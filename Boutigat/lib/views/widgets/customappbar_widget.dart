
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/views/profil/%20language_selector_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:get/get.dart';



class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String titleText;
  final IconData icon;
  final Color? dividercolor;
  final Color? backgroundColor;
  final AuthController authController = Get.find<AuthController>();

  CustomAppBar({
    Key? key,
    required this.titleText,
    required this.icon,
    this.dividercolor,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      backgroundColor: backgroundColor ?? AppColors.surface,
     title: Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: [
    Container(
      margin: EdgeInsets.symmetric(
        horizontal: MediaQuery.of(context).size.width * 0.05,
      ),
      child: Text(
        titleText,
        style: TextStyle(
          color: Theme.of(context).colorScheme.primary,
          fontSize: AppTextSizes.heading,
          fontWeight: AppFontWeights.bold,
        ),
      ),
    ),
    GestureDetector(
      onTap: () {
        if (!authController.isAuthenticated.value) {
          Get.to(() => LoginPage());
        } else {
           ZoomDrawer.of(context)!.toggle();
        }
      },
      child: Container(
        margin: EdgeInsets.only(
          right: MediaQuery.of(context).size.width * 0.04,
          left: MediaQuery.of(context).size.width * 0.04,
        ),
        child: authController.isAuthenticated.value
            ? CircleAvatar(
                radius: 22,
                backgroundColor: Colors.grey[300],
                child: Text(
                  '${authController.user?.firstName[0] ?? ''}${authController.user?.lastName[0] ?? ''}'.toUpperCase(),
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            : Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.surface,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  size: 15,
                  color: Theme.of(context).colorScheme.surface,
                ),
              ),
      ),
    ),
  ],
),

      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(1),
        child: Container(
          color: dividercolor,
          height: 1.0,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
