import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SafetyNoticeDialog extends StatelessWidget {
  const SafetyNoticeDialog({super.key});

  /// Affiche le dialog et renvoie true si l'utilisateur confirme
  static Future<bool?> show(BuildContext context) {
    return Get.dialog<bool>(
      const SafetyNoticeDialog(),
      barrierDismissible: false, // force une action explicite
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      titlePadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      contentPadding: const EdgeInsets.fromLTRB(24, 8, 24, 0),
      actionsPadding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
      title: Row(
        children: [
          Icon(Icons.shield_rounded, color: theme.colorScheme.primary),
          const SizedBox(width: 8),
          Text('safety_notice_title'.tr),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('safety_notice_body'.tr),
          const SizedBox(height: 12),
          const _Bullet(textKey: 'safety_notice_bullet_meet'),
          const _Bullet(textKey: 'safety_notice_bullet_check_item'),
          const _Bullet(textKey: 'safety_notice_bullet_no_advance'),
          const SizedBox(height: 8),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(result: false),
          child: Text('safety_notice_cancel'.tr),
        ),
      ElevatedButton.icon(
  onPressed: () => Get.back(result: true),
  icon: const Icon(Icons.arrow_forward_rounded, size: 18),
  label: Text('safety_notice_ok'.tr),
  style: ElevatedButton.styleFrom(
    backgroundColor: Theme.of(context).colorScheme.primary,
    foregroundColor: Theme.of(context).colorScheme.onPrimary,
    elevation: 1,
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    textStyle: const TextStyle(fontWeight: FontWeight.w700),
    minimumSize: const Size(0, 44), // hauteur confortable
  ),
)

      ],
    );
  }
}

class _Bullet extends StatelessWidget {
  final String textKey;
  const _Bullet({required this.textKey});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.check_circle_rounded, size: 18),
          const SizedBox(width: 8),
          Expanded(child: Text(textKey.tr)),
        ],
      ),
    );
  }
}
