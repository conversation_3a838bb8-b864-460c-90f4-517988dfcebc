import 'dart:ui';
import 'dart:io' show Platform;
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/badge_controller.dart';
import 'package:boutigak/views/profil/address_management_page.dart';
import 'package:boutigak/views/profil/boutigak/boutigak_user_page.dart';
import 'package:boutigak/views/profil/delete_account_page.dart';
import 'package:boutigak/views/profil/my_information_page.dart';
import 'package:boutigak/views/profil/my_items.dart';
import 'package:boutigak/views/profil/my_order_page.dart';
import 'package:boutigak/views/profil/password_page.dart';
import 'package:boutigak/views/profil/settings_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/navigation_bar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:lottie/lottie.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
 // Remplacez par votre page principale

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _navigateToHome();
  }

  void _navigateToHome() async {
    await Future.delayed(Duration(seconds: 3), () {
      Get.offAll(
  () => ZoomDrawerWrapper(),
  transition: Transition.noTransition,
);
});}

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Lottie.asset(
          'assets/lottie/Boutigaklogo.json',
          width: 400.w,
          height: 400.h,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}





// === Tes imports projet (pages/controllers/colors) ===
// import '...';

bool get _isIOS => !kIsWeb && Platform.isIOS;
bool get _isAndroid => !kIsWeb && Platform.isAndroid;

// =================== WRAPPER ===================
class ZoomDrawerWrapper extends StatelessWidget {
  final bool shouldOpenDrawer;
  final Widget? child;

  ZoomDrawerWrapper({this.shouldOpenDrawer = false, this.child});

  final _drawerController = ZoomDrawerController();

  @override
  Widget build(BuildContext context) {
    // ⚠️ Idéalement initialise ScreenUtil au root (MaterialApp). On laisse ici si tu le fais déjà.
    ScreenUtil.init(context, designSize: const Size(375, 812));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (shouldOpenDrawer) {
        Future.delayed(const Duration(milliseconds: 120), () { // léger délai = moins de jank
          _drawerController.open?.call();
        });
      }
    });

    return ZoomDrawer(
      controller: _drawerController,
      menuScreen: MenuScreen(controller: _drawerController),
      mainScreen: child ?? NavigationBarPage(),
      borderRadius: 24.r,
      // ✅ Android: pas d’ombre, slide un peu plus court; ✅ iOS: comme avant
      showShadow: _isIOS,
      angle: 0,
      slideWidth: MediaQuery.of(context).size.width * (_isAndroid ? 0.72 : 0.75),
      menuBackgroundColor: Theme.of(context).colorScheme.primary,
      openCurve: _isAndroid ? Curves.easeOutCubic : Curves.easeOut,
      closeCurve: _isAndroid ? Curves.easeInCubic : Curves.easeIn,
    );
  }
}

// =================== MENU SCREEN ===================
class MenuScreen extends StatelessWidget {
  final ZoomDrawerController controller;
  MenuScreen({super.key, required this.controller});

  final authController = Get.find<AuthController>();
  final badgeController = Get.find<BadgeController>();

  // ---------- Glass helpers ----------
  // iOS: BackdropFilter par tuile (look maximum)
  // Android: PAS de BackdropFilter par tuile (on garde un flou GLOBAL)
  Widget glassContainer({
    required Widget child,
    required BorderRadiusGeometry borderRadius,
    EdgeInsetsGeometry? margin,
    double opacityIOS = 0.08,
    double opacityAndroid = 0.08,
    double borderOpacity = 0.30,
    double sigmaIOS = 10,
    double sigmaAndroid = 0, // pas utilisé ici
  }) {
    final bgColor = Colors.white.withOpacity(_isIOS ? opacityIOS : opacityAndroid);
    final borderColor = Colors.white.withOpacity(borderOpacity);

    if (_isIOS) {
      // Style “full glass” par carte (iOS fluide grâce à Impeller)
      return ClipRRect(
        borderRadius: borderRadius,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: sigmaIOS, sigmaY: sigmaIOS),
          child: Container(
            margin: margin,
            decoration: BoxDecoration(
              color: bgColor,
              borderRadius: borderRadius,
              border: Border.all(color: borderColor, width: 1.2),
            ),
            child: child,
          ),
        ),
      );
    } else {
      // Android: léger glass sans BackdropFilter local
      return Container(
        margin: margin,
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: borderRadius,
          border: Border.all(color: borderColor, width: 1.2),
          // Pas de BoxShadow lourd ici
        ),
        child: child,
      );
    }
  }

  Widget customTile(String title, IconData icon, Widget page, {int badgeCount = 0}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
      child: glassContainer(
        borderRadius: BorderRadius.circular(12.r),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12.r),
            onTap: () {
              controller.close?.call();
              ZoomDrawer.of(Get.context!)?.close();
              Future.delayed(const Duration(milliseconds: 220), () {
                Get.to(() => page);
              });
            },
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
              child: Row(
                children: [
                  // Icône + badge
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      Icon(icon, size: 20.sp, color: Colors.white), // FaIcon -> Icon si possible
                      if (badgeCount > 0)
                        Positioned(
                          right: -6.w,
                          top: -6.h,
                          child: Container(
                            constraints: BoxConstraints(minWidth: 16.w),
                            height: 16.h,
                            padding: EdgeInsets.symmetric(horizontal: 4.w),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(color: Colors.white, width: 1.2),
                            ),
                            child: Center(
                              child: Text(
                                badgeCount > 99 ? '99+' : '$badgeCount',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 9.sp,
                                  height: 1.0,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(fontSize: 12.sp, color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildSocialTilesRow() {
    final socials = [
      {'icon': FontAwesomeIcons.whatsapp,       'url': 'https://wa.me/22238407840'},
      {'icon': FontAwesomeIcons.facebookF,      'url': 'https://www.facebook.com/share/17JmTu6VUW/?mibextid=wwXIfr'},
      {'icon': FontAwesomeIcons.snapchatGhost,  'url': 'https://www.snapchat.com/add/Boutigak.com'},
      {'icon': FontAwesomeIcons.tiktok,         'url': 'https://www.tiktok.com/@Boutigak.com'},
    ];

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: RepaintBoundary(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: socials.map((data) {
            final tile = Container(
              height: 45.w,
              margin: EdgeInsets.symmetric(horizontal: 4.w),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.08),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(8.r),
                  onTap: () async {
                    final uri = Uri.parse(data['url'] as String);

                    if (await canLaunchUrl(uri)) {
                      await launchUrl(uri, mode: LaunchMode.externalApplication);
                    } else {
                      Get.snackbar('Erreur', 'Impossible d’ouvrir le lien');
                    }
                  },
                  child: Center(
                    child: FaIcon(
                      data['icon'] as IconData,
                      size: 24.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            );

            // iOS: on peut garder un mini flou local pour les boutons sociaux
            return Expanded(
              child: _isIOS
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                        child: tile,
                      ),
                    )
                  : tile,
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget closeButton(BuildContext context) {
    final core = Container(
      width: 36.w,
      height: 36.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withOpacity(0.08),
        border: Border.all(color: Colors.white.withOpacity(0.3), width: 1.2),
      ),
      child: IconButton(
        padding: EdgeInsets.zero,
        icon: Icon(Icons.close, color: Colors.white, size: 24.sp),
        onPressed: () {
          controller.close?.call();
          ZoomDrawer.of(context)?.close();
        },
        splashRadius: 24.r,
      ),
    );

    // iOS: flou local sympa ; Android: petit sigma pour limiter le coût
    return ClipRRect(
      borderRadius: BorderRadius.circular(18.r),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: _isIOS ? 10 : 4, sigmaY: _isIOS ? 10 : 4),
        child: core,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bg = Theme.of(context).colorScheme.primary;

    // ==== État NON connecté ====
    if (authController.user == null) {
      return Scaffold(
        backgroundColor: bg,
        body: Stack(
          children: [
            // Android: UN flou global derrière tout le contenu
            if (_isAndroid)
              Positioned.fill(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                  child: const SizedBox.expand(),
                ),
              ),
            SafeArea(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 20.h),
                child: Column(
                  children: [
                    // Header
                    Padding(
                      padding: EdgeInsets.only(top: 16.h, bottom: 180.h, left: 16.w, right: 16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              closeButton(context),
                              Text(
                                '👋 ' + "hello".tr,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 32.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Logo
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: SizedBox(
                        width: 150,
                        child: ColorFiltered(
                          colorFilter: const ColorFilter.mode(
                            AppColors.surface,
                            BlendMode.srcATop,
                          ),
                          child: Image.asset(
                            'assets/images/biglogo_boutigak.png',
                            fit: BoxFit.contain,
                            filterQuality: FilterQuality.low,
                          ),
                        ),
                      ),
                    ),

                  // Bouton Login (glass)// Bouton Login (même pattern que Logout — simple et fiable)
Padding(
  padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 16.w),
  child: glassContainer(
    borderRadius: BorderRadius.circular(12.r),
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(12.r),
        onTap: () {
          controller.close?.call();
          ZoomDrawer.of(context)?.close();
          Future.delayed(const Duration(milliseconds: 220), () {
            Get.to(() => LoginPage());
          });
        },
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
          child: Row(
            children: [
              const FaIcon(
                FontAwesomeIcons.rightToBracket,
                color: Color(0xFFF4AE4E),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Text(
                  'login'.tr,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  ),
),


                    // Réseaux sociaux
                    buildSocialTilesRow(),

                    // Footer
                    Padding(
                      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 100.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Spacer(),
                          Text('powered_by'.tr,
                              style: TextStyle(fontSize: 12.sp, color: Theme.of(context).colorScheme.surface)),
                          Text('BÉCOD',
                              style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w900, color: Theme.of(context).colorScheme.onSurface)),
                          const Spacer(),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      child: Center(
                        child: Text(
                          'Version 1.0.0',
                          style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w300, color: Theme.of(context).colorScheme.surface),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    // ==== État CONNECTÉ ====
    return Scaffold(
      backgroundColor: bg,
      body: Stack(
        children: [
          if (_isAndroid)
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                child: const SizedBox.expand(),
              ),
            ),
          Obx(
            // ⬇️ Obx enveloppe SEULEMENT ce qui change (prénom/badges). Le layout reste stable.
            () => SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            closeButton(context),
                            Text(
                              '👋 ' + "hello".tr,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 32.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          '${authController.user?.firstName}!',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 28.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Liste des actions (RepaintBoundary pour limiter les repaints)
                  Expanded(
                    child: RepaintBoundary(
                      child: ListView(
                        padding: EdgeInsets.only(top: 30.h),
                        children: [
                          customTile(
                            'my_orders'.tr,
                            FontAwesomeIcons.box,
                            MyOrdersPage(),
                            badgeCount: badgeController.getModuleCount('user-order'),
                          ),
                          customTile(
                            'my_items'.tr,
                            FontAwesomeIcons.scroll,
                            MyItemsPage(),
                            badgeCount: badgeController.getModuleCount('items'),
                          ),
                          customTile(
                            'boutigak'.tr,
                            FontAwesomeIcons.store,
                            BoutigakUserPage(),
                            badgeCount: badgeController.getModuleCount('store-order'),
                          ),
                          customTile(
                            'settings'.tr,
                            FontAwesomeIcons.gear,
                            SettingsPage(),
                          ),

                          // Logout
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 50.h, horizontal: 16.w),
                            child: glassContainer(
                              borderRadius: BorderRadius.circular(12.r),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(12.r),
                                  onTap: () => authController.logout(),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                                    child: Row(
                                      children: [
                                        const FaIcon(FontAwesomeIcons.rightFromBracket, color: Color(0xFFF4AE4E)),
                                        SizedBox(width: 16.w),
                                        Expanded(
                                          child: Text(
                                            'log_out'.tr,
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Réseaux sociaux
                  buildSocialTilesRow(),

                  // Footer
                  Padding(
                    padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 15.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Spacer(),
                        Text('powered_by'.tr,
                            style: TextStyle(fontSize: 12.sp, color: Theme.of(context).colorScheme.surface)),
                        Text('BÉCOD',
                            style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w900, color: Theme.of(context).colorScheme.onSurface)),
                        const Spacer(),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    child: Center(
                      child: Text(
                        'Version 1.0.0',
                        style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w300, color: Theme.of(context).colorScheme.surface),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
