import 'dart:ui';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/item_widget.dart';
import 'package:boutigak/views/widgets/matterport_webview_page.dart';
import 'package:boutigak/views/widgets/snapchat_item_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:shimmer/shimmer.dart';

class ItemBottomSheetContent extends StatefulWidget {
  final Item item;

  const ItemBottomSheetContent({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  State<ItemBottomSheetContent> createState() => _ItemBottomSheetContentState();
}

class _ItemBottomSheetContentState extends State<ItemBottomSheetContent> {
  final PageController pageController = PageController();
  final ConversationController conversationController = Get.put(ConversationController());
  final ItemController itemController = Get.find<ItemController>();
  final AuthController authController = Get.put(AuthController());

  int currentPage = 0;
  bool isFavorited = false;

  @override
  void initState() {
    super.initState();
    itemController.selectItem(widget.item);
    itemController.fetchItemById(widget.item.id!).then((_) {
      final updatedItem = itemController.selectedItem.value;
      if (updatedItem != null) {
        setState(() {
          isFavorited = updatedItem.isLiked;
        });
      }
    });
  }

  void _showPaymentBottomSheet() {
    showDialog(
      context: context,
      builder: (_) => FractionallySizedBox(
        heightFactor: 0.8,
        child: CapturableItemWidget(
          imageUrl: '${widget.item.images.first}',
          title: widget.item.title,
          brand: widget.item.brandName,
          ownerName: widget.item.userName,
          price: widget.item.price,
          itemId: widget.item.id!,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isLoading = itemController.isLoading.value;
      final errorMessage = itemController.errorMessage.value;
      final updatedItem = itemController.selectedItem.value;

      if (isLoading) {
        return SizedBox(
          height: double.infinity,
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: double.infinity,
                  height: MediaQuery.of(context).size.height * .47,
                  color: Colors.grey[300],
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              width: 200,
                              height: 22,
                              color: Colors.grey[300],
                            ),
                          ),
                          const Spacer(),
                          Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              width: 30,
                              height: 30,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: 140,
                          height: 18,
                          color: Colors.grey[300],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Align(
                        alignment: Alignment.centerRight,
                        child: Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 80,
                            height: 14,
                            color: Colors.grey[300],
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: 120,
                          height: 20,
                          color: Colors.grey[300],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: double.infinity,
                          height: 60,
                          color: Colors.grey[300],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        margin: const EdgeInsets.symmetric(vertical: 8.0),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: Colors.grey.shade300, width: 1),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.08),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade100,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(10),
                                  topRight: Radius.circular(10),
                                ),
                              ),
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  width: 80,
                                  height: 16,
                                  color: Colors.grey[300],
                                ),
                              ),
                            ),
                            Divider(height: 1, color: Colors.grey.shade300, thickness: 1),
                            ...List.generate(5, (i) => Column(
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      width: 120,
                                      height: 40,
                                      color: Colors.grey.shade100,
                                      alignment: Alignment.centerLeft,
                                      padding: const EdgeInsets.symmetric(horizontal: 12),
                                      child: Shimmer.fromColors(
                                        baseColor: Colors.grey[300]!,
                                        highlightColor: Colors.grey[100]!,
                                        child: Container(
                                          width: 60,
                                          height: 12,
                                          color: Colors.grey[300],
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 12),
                                        child: Shimmer.fromColors(
                                          baseColor: Colors.grey[300]!,
                                          highlightColor: Colors.grey[100]!,
                                          child: Container(
                                            height: 12,
                                            color: Colors.grey[200],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                if (i < 4)
                                  Divider(height: 1, color: Colors.grey.shade300, thickness: 1),
                              ],
                            )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 30.0, left: 25, right: 25, top: 10),
                child: Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 60,
                            height: 12,
                            color: Colors.grey[300],
                          ),
                        ),
                        const SizedBox(height: 6),
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 80,
                            height: 18,
                            color: Colors.grey[300],
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 200,
                        height: 50,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: Colors.grey[300],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }

      if (errorMessage.isNotEmpty) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.red,
                ),
                SizedBox(height: 16),
                Text(
                  'Failed to load item details',
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => itemController.fetchItemById(widget.item.id!),
                  child: Text('Retry'),
                ),
              ],
            ),
          ),
        );
      }

      if (updatedItem == null) {
        return SizedBox(
          width: double.infinity,
          height: MediaQuery.of(context).size.height,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CupertinoActivityIndicator(radius: 20),
                SizedBox(height: 16),
                Text('Loading...'),
              ],
            ),
          ),
        );
      }

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Container(
                width: double.infinity,
                height: MediaQuery.of(context).size.height * .47,
                child: ImageSlider(
                  pageController: pageController,
                  photos: updatedItem.images.map((image) => '$image').toList(),
                  currentPage: currentPage,
                  onPageChanged: (int page) => setState(() => currentPage = page),
                  borderRadius: 0,
                ),
              ),
              Positioned(
                top: 60,
                right: 10,
                child: ClipOval(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                    child: Container(
                      color: AppColors.onBackground.withOpacity(0.2),
                      child: IconButton(
                        iconSize: 20,
                        icon: Icon(FontAwesomeIcons.upRightFromSquare, color: AppColors.background),
                        onPressed: _showPaymentBottomSheet,
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 60,
                left: 10,
                child: ClipOval(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                    child: Container(
                      color: AppColors.onBackground.withOpacity(0.3),
                      child: IconButton(
                        iconSize: 20,
                        icon: Icon(FontAwesomeIcons.chevronDown, color: AppColors.background),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ),
                  ),
                ),
              ),
              if (updatedItem.matterportLink != null && updatedItem.matterportLink!.isNotEmpty)
                Positioned(
                  bottom: 40,
                  right: 10,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MatterportView(
                              matterportLink: updatedItem.matterportLink!,
                            ),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(20),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(FontAwesomeIcons.cube, color: AppColors.background, size: 24),
                          const SizedBox(width: 6),
                          Text('3d_view'.tr, style: TextStyle(color: AppColors.background, fontSize: 14, fontWeight: FontWeight.bold)),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.4,
            child: SingleChildScrollView(
              padding: const EdgeInsets.only(top: 0, left: 0, right: 0, bottom: 0),
              child: InfoSection(
                item: updatedItem,
                isFavorited: isFavorited,
                toggleFavorite: () => setState(() => isFavorited = !isFavorited),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 100.0, left: 25, right: 25, top: 10),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "price".tr + "(" + "mru".tr + ")",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).disabledColor,
                      ),
                    ),
                    Text(
                      "${updatedItem.price.toStringAsFixed(0)} ",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Container(
                  width: 200,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onSurface,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 5,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: TextButton(
                    onPressed: () async {
                      if (authController.isAuthenticated.value) {
                        final success = await conversationController.createDiscussion(updatedItem.id!);
                        if (success != null) {
                          Get.to(() => ConversationPage(
                                itemId: updatedItem.id!,
                                item: updatedItem.toJson(),
                                discussionId: success['discussion']['id'],
                                interlocutor: updatedItem.userName!,
                                phoneNumber: success['discussion']['interlocutor_phone'],
                              ));
                        }
                      } else {
                        Get.to(() => LoginPage());
                      }
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        "make_an_offer".tr,
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }
}

// You can reuse InfoSection from item_widget.dart or keep your own.
