import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/connectivity_controlleur.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ConnectivityPopup {
  /// Affiche le popup si l'app est hors-ligne.
  /// Optionnel: fournissez un [onRetry] si vous avez déjà une méthode de vérification.
  static void show(BuildContext context, {Future<bool> Function()? onRetry}) {
    final connectivityController = Get.find<ConnectivityController>();

    // Ne rien afficher si en ligne, ou si un autre dialog est déjà ouvert
    if (connectivityController.isConnected.value) return;
    if (Get.isDialogOpen == true) return;

    // Fermer automatiquement quand la connexion revient
    ever<bool>(connectivityController.isConnected, (connected) {
      if (connected && Get.isDialogOpen == true) {
        Get.back(); // ferme le dialog
        Get.snackbar('connection_restored'.tr, 'back_online_message'.tr,
            snackPosition: SnackPosition.BOTTOM, duration: const Duration(seconds: 2));
      }
    });

    Get.dialog(
      WillPopScope(
        onWillPop: () async => false, // désactive le bouton back
        child: Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.symmetric(horizontal: 24),
          child: _ConnectivityCard(
            onRetry: () async {
              // 1) Si une fonction custom est fournie, on l’utilise
              if (onRetry != null) {
                final ok = await onRetry();
                connectivityController.isConnected.value = ok;
                return;
              }
              // 2) Sinon, on fait un "ping" rapide pour tester
              try {
                final result = await InternetAddress.lookup('one.one.one.one');
                final ok = result.isNotEmpty && result.first.rawAddress.isNotEmpty;
                connectivityController.isConnected.value = ok;
              } catch (_) {
                connectivityController.isConnected.value = false;
              }
            },
            onClose: () {
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              }
            },
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }
}

class _ConnectivityCard extends StatefulWidget {
  final Future<void> Function() onRetry;
  final VoidCallback onClose;
  const _ConnectivityCard({required this.onRetry, required this.onClose, Key? key}) : super(key: key);

  @override
  State<_ConnectivityCard> createState() => _ConnectivityCardState();
}

class _ConnectivityCardState extends State<_ConnectivityCard> {
  bool _retrying = false;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final edgeColor = AppColors.primary.withOpacity(0.45);
    final bg = AppColors.primary.withOpacity(0.24);

    return Center(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 14, sigmaY: 14),
          child: Container(
            width: 420,
            padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
            decoration: BoxDecoration(
              color: bg,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: edgeColor),
              boxShadow: [
                BoxShadow(
                  blurRadius: 40,
                  offset: const Offset(0, 20),
                  color: AppColors.primary.withOpacity(0.50),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 56,
                  width: 56,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.red.withOpacity(0.12),
                    border: Border.all(color: Colors.red.withOpacity(0.25)),
                  ),
                  child: const Icon(Icons.wifi_off_rounded, size: 28, color: Colors.red),
                ),
                const SizedBox(height: 16),
                Text(
  'no_internet_title'.tr,
  textAlign: TextAlign.center,
  style: Theme.of(context).textTheme.titleLarge?.copyWith(
        color: Colors.white,
        fontWeight: FontWeight.w700,
      ),
),
const SizedBox(height: 8),
Text(
  'no_internet_message'.tr,
  textAlign: TextAlign.center,
  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: Colors.white70, // texte secondaire en blanc atténué
        height: 1.4,
      ),
),
const SizedBox(height: 18),
Row(
  mainAxisAlignment: MainAxisAlignment.center,
  children: [
    OutlinedButton(
      onPressed: _retrying
          ? null
          : () async {
              setState(() => _retrying = true);
              await widget.onRetry();
              if (mounted) setState(() => _retrying = false);
            },
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.white, // texte/icone bouton
        side: const BorderSide(color: Colors.white70),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      child: _retrying
          ? const SizedBox(
              width: 18,
              height: 18,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white, // spinner blanc
              ),
            )
          : Text('retry'.tr),
    ),
    const SizedBox(width: 12),
    TextButton(
      onPressed: _retrying ? null : widget.onClose,
      style: TextButton.styleFrom(
        foregroundColor: Colors.white, // texte bouton
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      child: Text('ok'.tr),
    ),
  ],
),

                  
                
              ],
            ),
          ),
        ),
      ),
    );
  }
}
