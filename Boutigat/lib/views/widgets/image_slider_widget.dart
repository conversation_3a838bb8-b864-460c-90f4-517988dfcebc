import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ImageSlider extends StatelessWidget {
  final PageController pageController;
  final List<String> photos;
  final int currentPage;
  final Function(int)? onPageChanged;
  final double borderRadius;
  final bool isFullscreenEnabled;
  final double? height;



  const ImageSlider({
    required this.pageController,
    required this.photos,
    required this.currentPage,
    this.onPageChanged,
    this.borderRadius = 0,
    this.isFullscreenEnabled = true,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final double sliderHeight = height ?? 250;
   

    return SizedBox(
      height:  MediaQuery.of(context).size.height * .45,
      child: Stack(
        children: [
          PageView.builder(
            controller: pageController,
            itemCount: photos.length,
            onPageChanged: onPageChanged,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  if (isFullscreenEnabled) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => FullscreenImageGallery(
                          photos: photos,
                          initialPage: currentPage,
                        ),
                      ),
                    );
                  }
                },
                child: CachedImageWidget(
                  imageUrl: photos[index],
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(borderRadius),
                ),
              );
            },
          ),
          Positioned(
            bottom: 15,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(
                    photos.length,
                    (index) => Container(
                      margin: EdgeInsets.symmetric(horizontal: 4),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: currentPage == index
                            ? Colors.white
                            : Colors.white.withOpacity(0.5),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}



class FullscreenImageGallery extends StatefulWidget {
  final List<String> photos;
  final int initialPage;

  FullscreenImageGallery({
    super.key,
    required this.photos,
    this.initialPage = 0,
  });

  @override
  _FullscreenImageGalleryState createState() => _FullscreenImageGalleryState();
}

class _FullscreenImageGalleryState extends State<FullscreenImageGallery> {
  late PageController _pageController;
  late int _currentPage;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage;
    _pageController = PageController(initialPage: widget.initialPage);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Stack(
          children: [
            Positioned(
              top: -12,
              left: 0,
              child: IconButton(
                icon: Icon(Icons.arrow_back),
                onPressed: () {
                  Get.back(); 
                },
              ),
            ),
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildPageIndicator(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      body: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.vertical,
        itemCount: widget.photos.length,
        onPageChanged: (int page) {
          setState(() {
            _currentPage = page;
          });
        },
        itemBuilder: (context, index) {
          final photo = widget.photos[index];
          final isNetworkImage = photo.startsWith('http://') || photo.startsWith('https://');

          return  Stack(
  children: [
    Center(
  child: InteractiveViewer(
    child: CachedImageWidget(
      imageUrl: photo,
      fit: BoxFit.contain,
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
    ),
  ),
),

    Positioned(
      right: 12,
      top: null,
      bottom: null,
      child: Center(
        child: Container(
          margin: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.4),
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.4),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              widget.photos.length,
              (index) => Container(
                margin: EdgeInsets.symmetric(vertical: 3),
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _currentPage == index
                      ? Colors.white
                      : Colors.white.withOpacity(0.5),
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  ],
);

        },
      ),
    );
  }

Widget _buildPageIndicator() {
  double progress = (_currentPage + 1) / widget.photos.length;
  
  return Stack(
    children: [
      // Fond blanc avec des bords arrondis
      Container(
        width: 150.0,
        height: 20.0,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15.0),
        ),
      ),
      // Rectangle rempli pour indiquer la progression
      AnimatedContainer(
        duration: Duration(milliseconds: 300),
        width: 150.0 * progress, // La largeur change en fonction de la progression
        height: 20.0,
        decoration: BoxDecoration(
          color: AppColors.primary, // La couleur de progression est la couleur primaire
          borderRadius: BorderRadius.circular(15.0),
        ),
      ),
    ],
  );
}
}
