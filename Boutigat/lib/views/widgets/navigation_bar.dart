import 'dart:ui';

import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/badge_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/registerandlogin/register_page.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/customlogo_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/nav_controller.dart';
import 'package:boutigak/views/home/<USER>';
import 'package:boutigak/views/search/search_page.dart';
import 'package:boutigak/views/sell/sell_page.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:boutigak/views/boutigat_store/boutique_store_page.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';









class NavigationBarPage extends StatelessWidget {
  const NavigationBarPage({super.key});

  @override
  Widget build(BuildContext context) {
    final NavController navController = Get.find<NavController>();
    final AuthController authController = Get.find<AuthController>();
    final BadgeController badgeController = Get.find<BadgeController>();

Widget buildLoginPrompt(BuildContext context) {
  final primary = AppColors.primary;

  return Scaffold(
 
    body: Center( 
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min, 
         
          children: [
           
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: primary.withOpacity(0.08), 
                shape: BoxShape.circle,
                border: Border.all(color: primary.withOpacity(0.35)),
              ),
              child: Icon(
                FontAwesomeIcons.rightToBracket,
                color: primary,
                size: 50,
              ),
            ),
            const SizedBox(height: 16),

            // Titre
            Text(
              "login_required_title".tr, 
              style: TextStyle(
                color: primary,
                fontSize: 18,
                fontWeight: FontWeight.w800,
              ),
            ),
            const SizedBox(height: 8),

            // Sous-texte
            Text(
              "login_required_subtitle".tr, 
              textAlign: TextAlign.center,
              style: TextStyle(
                color: primary.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),

            // Bouton CTA
            ElevatedButton(
              onPressed: () => Get.to(() => LoginPage()),
              style: ElevatedButton.styleFrom(
                backgroundColor: primary.withOpacity(0.12),
                foregroundColor: primary,
                side: BorderSide(color: primary.withOpacity(0.35)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
                elevation: 0,
              ),
              child: Text("login_cta".tr),
            ),
          ],
        ),
      ),
    ),
  );
}



    return Obx(
      () => Scaffold(
        body: authController.tokenLoading.value
            ? const Center(child: CircularProgressIndicator())
            : Obx(() {
                final int currentTab = navController.tabIndex.value;

                if ((currentTab == 2 || currentTab == 3) &&
                    !authController.isAuthenticated.value) {
                  return buildLoginPrompt(context);
                } else {
                  return IndexedStack(
                    index: currentTab,
                    children: [
                      HomePage(),
                      SearchPage(),
                      Container(),
                      InboxPage(),
                      StorePage(),
                    ],
                  );
                }
              }),
        bottomNavigationBar: Obx(() {
          return Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  blurRadius: 10,
                  color: Colors.black.withOpacity(0.1),
                )
              ],
            ),
            child: Padding(
              padding:  EdgeInsets.only(
                  left: 16.w, right: 16.w, bottom: 22.h, top: 6.h),
              child: GNav(
                selectedIndex: navController.tabIndex.value,
                onTabChange: (index) {
                  if (index == 2) {
                    if (!authController.isAuthenticated.value) {
                      navController.changeTabIndex(2);
                    } else {
                      Get.to(
                        () => SellPage(),
                        transition: Transition.zoom,
                        duration: const Duration(milliseconds: 0),
                      );
                    }
                  } else if (index == 3) {
                    navController.changeTabIndex(index);
                    if (authController.isAuthenticated.value) {
                    }
                  } else if (index == 4){
                    navController.changeTabIndex(index);

                   final StoreController storeController = Get.find<StoreController>();

                  storeController.fetchRecomandedStores(refresh: true);

                  }
                  else {
                    navController.changeTabIndex(index);
                  }
                },
                haptic: true,
                curve: Curves.easeIn,
                duration: const Duration(milliseconds: 200),
                gap: 8.w,
                activeColor: AppColors.primary,
                iconSize: 20.w,
                padding:
                    EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.w),
tabs: [
  GButton(
   icon: Icons.circle,
   backgroundColor: AppColors.primary.withOpacity(.2),
    leading: Image.asset(
      navController.tabIndex.value == 0
          ? 'assets/images/home_solid.png'
          : 'assets/images/home.png',
        width: 18.w,
      height: 18.w,
    ),
    text: 'home'.tr,
  ),
  GButton(
   icon: Icons.circle,
   backgroundColor: AppColors.primary.withOpacity(.2),
    leading: Image.asset(
      navController.tabIndex.value == 1
          ? 'assets/images/search_solid.png'
          : 'assets/images/search.png',
      width: 18.w,
      height: 18.w,
    ),
    text: 'search'.tr,
  ),
  GButton(
   icon: Icons.circle,
   backgroundColor: AppColors.primary.withOpacity(.2),
    leading: Image.asset(
      navController.tabIndex.value == 2
          ? 'assets/images/add_solid.png'
          : 'assets/images/add.png',
      width: 18.w,
      height: 18.w,
    ),
    text: 'sell'.tr,
  ),
  GButton(
    icon: Icons.circle,
    backgroundColor: AppColors.primary.withOpacity(.2),
    leading: Obx(() {
      int badgeCount = badgeController.getModuleCount('messages') +
          badgeController.getModuleCount('notifications');

      final isSelected = navController.tabIndex.value == 3;
      final iconPath = isSelected
          ? 'assets/images/envelope_solid.png'
          : 'assets/images/envelope.png';

      Widget iconWidget = Image.asset(
        iconPath,
          width: 18.w,
      height: 18.w,
      );

      return AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (child, animation) =>
            ScaleTransition(scale: animation, child: child),
        child: badgeCount > 0
            ? Badge(
                key: ValueKey<int>(badgeCount),
                backgroundColor: Colors.red.shade100,
                offset: const Offset(8, -8),
                label: Text(
                  badgeCount > 9 ? '9+' : badgeCount.toString(),
                  style: TextStyle(
                    color: Colors.red.shade900,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                child: iconWidget,
              )
            : iconWidget,
      );
    }),
    text: 'inbox'.tr,
  ),
  GButton(
    icon: Icons.circle,
    backgroundColor: AppColors.primary.withOpacity(.2),
    leading: Image.asset(
      navController.tabIndex.value == 4
          ? 'assets/images/shop_solid.png'
          : 'assets/images/shop.png',
      width: 18.w,
      height: 18.w,
    ),
    text: 'stores'.tr,
  ),
],

              ),
            ),
          );
        }),
      ),
    );
  }
}
