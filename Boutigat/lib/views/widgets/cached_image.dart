import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:image/image.dart' as img;

import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:image/image.dart' as img;

class CachedImageWidget extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;

  const CachedImageWidget({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
  }) : super(key: key);

  @override
  State<CachedImageWidget> createState() => _CachedImageWidgetState();
}

class _CachedImageWidgetState extends State<CachedImageWidget> with AutomaticKeepAliveClientMixin {
  MemoryImage? _image;
  bool _loading = true;
  bool _hasError = false;
  bool _hasTransparentBackground = false;

  static final Map<String, MemoryImage> _imageCache = {};
  static final Map<String, bool> _transparencyCache = {};

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(covariant CachedImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    setState(() {
      _loading = true;
      _hasError = false;
    });

    try {
      MemoryImage imageProvider;
      Uint8List bytes;

      if (_imageCache.containsKey(widget.imageUrl)) {
        imageProvider = _imageCache[widget.imageUrl]!;
      } else {
        final file = await DefaultCacheManager().getSingleFile(widget.imageUrl);
        bytes = await file.readAsBytes();

        // 💡 Appel de compute pour détection transparente sans bloquer l'UI
        bool hasTransparency = false;
        if (_transparencyCache.containsKey(widget.imageUrl)) {
          hasTransparency = _transparencyCache[widget.imageUrl]!;
        } else {
          hasTransparency = await compute(_detectTransparencyIsolate, bytes);
          _transparencyCache[widget.imageUrl] = hasTransparency;
        }

        imageProvider = MemoryImage(bytes);
        _imageCache[widget.imageUrl] = imageProvider;

        if (mounted) {
          setState(() {
            _image = imageProvider;
            _loading = false;
            _hasTransparentBackground = hasTransparency;
          });
        }

        return;
      }

      // Si trouvé dans le cache
      bool hasTransparency = _transparencyCache[widget.imageUrl] ?? false;

      if (mounted) {
        setState(() {
          _image = imageProvider;
          _loading = false;
          _hasTransparentBackground = hasTransparency;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _loading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final double width = widget.width ?? 100;
    final double height = widget.height ?? 100;

    if (_loading) return _buildPlaceholder(width, height);
    if (_hasError || _image == null) return _buildError(width, height);

    final imageWidget = Image(
      image: _image!,
      width: width,
      height: height,
      fit: _hasTransparentBackground ? BoxFit.contain : widget.fit,
    );

    return ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300), // 💡 transition fluide
        child: Container(
          key: ValueKey(widget.imageUrl),
          width: width,
          height: height,
          color: _hasTransparentBackground ? Colors.grey[200] : null,
          alignment: Alignment.center,
          child: imageWidget,
        ),
      ),
    );
  }

 Widget _buildPlaceholder(double width, double height) {
  return Container(
    width: width,
    height: height,
    decoration: BoxDecoration(
      color: Colors.grey[300],
      borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
    ),
    child: Center(
      child: Image.asset(
        'assets/images/logo.png',
        width: (width * 0.4).clamp(40, 80),
        height: (height * 0.4).clamp(40, 80),
        fit: BoxFit.contain,
      ),
    ),
  );
}


  Widget _buildError(double width, double height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
      ),
      child: const Icon(Icons.error),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

// 💡 Fonction qui tourne dans un isolate pour détecter s'il y a de la transparence
bool _detectTransparencyIsolate(Uint8List bytes) {
  final image = img.decodeImage(bytes);
  if (image != null && image.numChannels == 4) {
    final pixels = image.getBytes();
    for (int i = 3; i < pixels.length; i += 40) {
      if (pixels[i] < 255) {
        return true;
      }
    }
  }
  return false;
}
