import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DeleteAccountPage extends StatefulWidget {
  const DeleteAccountPage({super.key});

  @override
  State<DeleteAccountPage> createState() => _DeleteAccountPageState();
}

class _DeleteAccountPageState extends State<DeleteAccountPage> {
  final AuthController authController = Get.find<AuthController>();

  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _reasonController   = TextEditingController();

  final RxBool _passwordVisible = false.obs;

  @override
  void dispose() {
    _passwordController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  Future<bool> _confirmDeleteDialog() async {
    return await showDialog<bool>(
          context: context,
          builder: (_) => AlertDialog(
            title: Text('confirm_delete_title'.tr),
            content: Text('confirm_delete_body'.tr),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('cancel'.tr),
              ),
              FilledButton(
                onPressed: () => Navigator.pop(context, true),
                style: FilledButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                ),
                child: Text('delete'.tr),
              ),
            ],
          ),
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Dégradé rouge cohérent
    final Color c1 = theme.colorScheme.error;                      // base
    final Color c2 = Color.lerp(theme.colorScheme.error, Colors.black, 0.12)!; // un poil plus sombre

    final border = theme.dividerColor.withOpacity(0.25);

    return Scaffold(
      backgroundColor: theme.colorScheme.error,
      appBar: AppBar(
        title: Text('delete_account'.tr),
        centerTitle: true,
        backgroundColor: theme.colorScheme.error,
        foregroundColor: theme.colorScheme.onError,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // ===== Fond dégradé rouge en haut (comme Settings/Language) =====
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [c1, c2],
                ),
              ),
            ),
          ),

          // ===== Contenu scrollable =====
          CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // ----- Header translucide danger -----
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                  child: Material(
                    color: Colors.white.withOpacity(0.10),
                    elevation: 0,
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.white.withOpacity(0.25)),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Halo icône
                          Container(
                            width: 56,
                            height: 56,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white.withOpacity(0.28),
                                  Colors.white.withOpacity(0.12),
                                ],
                              ),
                              border: Border.all(color: Colors.white.withOpacity(0.35)),
                            ),
                            child: Icon(Icons.report_gmailerrorred_outlined,
                                color: Colors.white, size: 26),
                          ),
                          const SizedBox(width: 12),
                          // Titre + sous-titre
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'delete_header_title'.tr, // ex: "Suppression de compte"
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w800,
                                    fontSize: 16,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'delete_header_subtitle'.tr, // ex: "Cette action est irréversible."
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.85),
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // ----- Surface blanche arrondie -----
              SliverFillRemaining(
                hasScrollBody: false,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.06),
                        blurRadius: 20,
                        offset: const Offset(0, -6),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                    child: Obx(() {
                      final loading = authController.isLoading.value;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // ===== Carte formulaire =====
                          Material(
                            color: theme.colorScheme.surface,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                              side: BorderSide(color: border),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(12),
                              child: Column(
                                children: [
                                  // Mot de passe
                                  Obx(() => TextField(
                                        controller: _passwordController,
                                        readOnly: loading,
                                        obscureText: !_passwordVisible.value,
                                        textInputAction: TextInputAction.next,
                                        decoration: InputDecoration(
                                          labelText: 'password'.tr,
                                          hintText: 'enter_password_hint'.tr,
                                          filled: true,
                                          isDense: true,
                                          fillColor: theme.colorScheme.surfaceVariant.withOpacity(0.4),
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(12),
                                            borderSide: BorderSide(color: theme.dividerColor),
                                          ),
                                          prefixIcon: const Icon(Icons.lock_outline),
                                          suffixIcon: IconButton(
                                            tooltip: _passwordVisible.value
                                                ? 'hide_password'.tr
                                                : 'show_password'.tr,
                                            icon: Icon(
                                              _passwordVisible.value
                                                  ? Icons.visibility
                                                  : Icons.visibility_off,
                                            ),
                                            onPressed: () => _passwordVisible.toggle(),
                                          ),
                                        ),
                                      )),

                                  const SizedBox(height: 12),

                                  // Raison
                                  TextField(
                                    controller: _reasonController,
                                    readOnly: loading,
                                    maxLines: 5,
                                    minLines: 3,
                                    maxLength: 300,
                                    textInputAction: TextInputAction.done,
                                    decoration: InputDecoration(
                                      labelText: 'reason_for_deletion'.tr,
                                      hintText: 'reason_hint'.tr,
                                      counterText: '',
                                      filled: true,
                                      isDense: true,
                                      fillColor: theme.colorScheme.surfaceVariant.withOpacity(0.4),
                                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(color: theme.dividerColor),
                                      ),
                                      prefixIcon: const Icon(Icons.comment_outlined),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // ===== Alerte / Warning =====
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.errorContainer,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: theme.colorScheme.error.withOpacity(0.25)),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(Icons.warning_amber_rounded,
                                    color: theme.colorScheme.onErrorContainer),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'delete_warning'.tr,
                                    style: TextStyle(
                                      color: theme.colorScheme.onErrorContainer,
                                      fontSize: 13,
                                      height: 1.35,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const Spacer(),

                          // ===== Bouton supprimer (danger) =====
                          SizedBox(
                            width: double.infinity,
                            height: 48,
                            child: FilledButton.icon(
                              style: FilledButton.styleFrom(
                                backgroundColor: theme.colorScheme.error,
                                foregroundColor: theme.colorScheme.onError,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              onPressed: loading
                                  ? null
                                  : () async {
                                      final pwd = _passwordController.text.trim();
                                      final why = _reasonController.text.trim();

                                      if (pwd.isEmpty || why.isEmpty) {
                                        Get.snackbar(
                                          'error'.tr,
                                          'fill_all_fields'.tr,
                                          snackPosition: SnackPosition.BOTTOM,
                                        );
                                        return;
                                      }

                                      final sure = await _confirmDeleteDialog();
                                      if (!sure) return;

                                      FocusScope.of(context).unfocus();

                                      final ok = await authController.deleteAccount(pwd, why);
                                      if (!ok) {
                                        Get.snackbar(
                                          'error'.tr,
                                          'account_deletion_failed'.tr,
                                          snackPosition: SnackPosition.BOTTOM,
                                        );
                                      }
                                      // Si ok: deleteAccount gère la redirection (logout)
                                    },
                              icon: loading
                                  ? const SizedBox(
                                      width: 18,
                                      height: 18,
                                      child: CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  : const Icon(Icons.delete_forever),
                              label: Text(loading ? 'deleting'.tr : 'delete_my_account'.tr),
                            ),
                          ),
                        ],
                      );
                    }),
                  ),
                ),
              ),
            ],
          ),

          // ===== Overlay de chargement fin (barre) =====
          Obx(() {
            if (!authController.isLoading.value) return const SizedBox.shrink();
            return IgnorePointer(
              ignoring: true,
              child: AnimatedOpacity(
                opacity: 1,
                duration: const Duration(milliseconds: 150),
                child: Container(
                  color: Colors.black.withOpacity(0.05),
                  alignment: Alignment.topCenter,
                  padding: const EdgeInsets.only(top: 8),
                  child: const LinearProgressIndicator(minHeight: 2),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
