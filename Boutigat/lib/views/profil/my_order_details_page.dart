import 'dart:developer';
import 'dart:ui';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/widgets/payment_screenshot.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class OrderDetailsPage extends StatefulWidget {
  final Order order;
  const OrderDetailsPage({super.key, required this.order});

  @override
  State<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  bool _isCancelLoading = false;
  bool _isPayLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final orderController = Get.find<OrderController>();

    final double delivery =
        double.tryParse(widget.order.deliveryCharge ?? '0') ?? 0.0;
    final double productsTotal = (widget.order.totalOrders ?? 0).toDouble();
    final double total = productsTotal + delivery;


     Future<void> _callNumber(String phone) async {
      final uri = Uri(scheme: 'tel', path: phone.replaceAll(' ', ''));
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        Get.snackbar(
          'error'.tr.isEmpty ? 'Error' : 'error'.tr,
          'cannot_place_call'.tr.isEmpty
              ? 'Cannot place a call on this device.'
              : 'cannot_place_call'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }

    log('order_user ${widget.order.toJson()}');
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Bande primaire en arrière-plan
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 230,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.9)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ),

          SafeArea(
            bottom: false,
            child: Column(
              children: [
                // ======= Header Glass (avec photo + menu) =======
             Padding(
  padding: const EdgeInsets.fromLTRB(12, 8, 12, 10),
  child: _GlassHeader(
    title: '${'order'.tr} #${widget.order.orderId ?? '—'}',
    // onCancelTap: null, // ❌ On supprime pour éviter le doublon
    extraMenuItemsBuilder: (context) => <PopupMenuEntry>[
      if (!_isCancelLoading &&
          !widget.order.isPaid &&
          widget.order.status != 'CANCELLED')
        PopupMenuItem(
          child: Row(
            children: [
              const Icon(Icons.cancel_outlined, size: 18),
              const SizedBox(width: 8),
              Text('cancel_order'.tr),
            ],
          ),
          onTap: () {
            Future.delayed(const Duration(milliseconds: 10), () {
              if (!_isCancelLoading &&
                  !widget.order.isPaid &&
                  widget.order.status != 'CANCELLED') {
                Get.defaultDialog(
                  title: 'cancel_order'.tr,
                  middleText: 'cancel_order_confirm'.tr,
                  textCancel: 'no'.tr,
                  textConfirm: 'yes'.tr,
                  confirmTextColor: Colors.white,
                  onConfirm: () async {
                    setState(() => _isCancelLoading = true);
                    await orderController.changeOrderStatus(
                        widget.order.orderId!, 'CANCELLED');
                    setState(() => _isCancelLoading = false);
                    Get.back();
                    Get.back();
                  },
                );
              }
            });
          },
        ),

      // ✅ Nouveau : numéro du store
      if ((widget.order.userPhone ?? '').isNotEmpty)
        PopupMenuItem(
          child: Row(
            children: [
              const Icon(Icons.phone, size: 18, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(widget.order.userPhone!,
                  style: const TextStyle(fontWeight: FontWeight.w500)),
            ],
          ),
          onTap: () {
            Future.delayed(const Duration(milliseconds: 10), () {
              final phone = widget.order.userPhone!;
              final uri = Uri(scheme: 'tel', path: phone.replaceAll(' ', ''));
              launchUrl(uri);
            });
          },
        ),
    ],
  ),
),


                // ======= Barre de statut (glass) =======
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: _GlassStatusBar(
  currentStatus: widget.order.status,
  isCOD: widget.order.isCashOnDelivery, 
  isPaid: widget.order.isPaid,
),

                ),
                const SizedBox(height: 10),

                // ======= Corps blanc arrondi =======
                Expanded(
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(22)),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: CustomScrollView(
                            physics: const BouncingScrollPhysics(),
                            slivers: [
                              // (3) Remplace StoreCard -> simple texte si COD

                              SliverPadding(
                                padding:
                                    const EdgeInsets.fromLTRB(18, 26, 18, 10),
                                sliver: SliverToBoxAdapter(
                                  child: Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          // Texte "N products from STORE" avec N et STORE en noir/gras
                                          ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            child: widget.order.storeImage !=
                                                    null
                                                ? Image.network(
                                                    widget.order.storeImage!,
                                                    width: 50,
                                                    height: 50,
                                                    fit: BoxFit.cover)
                                                : Container(
                                                    width: 50,
                                                    height: 50,
                                                    color: Colors.white
                                                        .withOpacity(0.18),
                                                    child: const Icon(
                                                        Icons.storefront,
                                                        color: Colors.white)),
                                          ),
                                          const SizedBox(width: 10),

                                          // Badge COD (à droite, optionnel)
                                          if (widget
                                              .order.isCashOnDelivery) ...[
                                            Row(
                                              children: [
                                                const SizedBox(width: 10),
                                                const Icon(
                                                    Icons
                                                        .local_shipping_outlined,
                                                    color: Colors.blueGrey,
                                                    size: 18),
                                                const SizedBox(width: 6),
                                                Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 10,
                                                      vertical: 6),
                                                  decoration: BoxDecoration(
                                                    color: Colors.blueGrey
                                                        .withOpacity(0.08),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            999),
                                                    border: Border.all(
                                                        color: Colors.blueGrey
                                                            .withOpacity(0.28)),
                                                  ),
                                                  child: Text(
                                                    'cash_on_delivery'
                                                        .tr, // ou 'cod_short'.tr si tu préfères "COD"
                                                    style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      color: Colors.black,
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            )
                                          ],
                                        ],
                                      ),
                        
                                      const SizedBox(height: 16),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: RichText(
                                              text: TextSpan(
                                                style: TextStyle(
                                                    fontSize: 16,
                                                    color:
                                                        Colors.grey.shade600),
                                                children: [
                                                  TextSpan(
                                                    text:
                                                        '${widget.order.items.length}',
                                                    style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                  TextSpan(
                                                      text:
                                                          ' ${'products'.tr} ${'from'.tr} '),
                                                  TextSpan(
                                                    text: widget
                                                            .order.storeName ??
                                                        'store'.tr,
                                                    style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              if (widget.order.modifiedByStore &&
                                  widget.order.modifications.isNotEmpty)
                                SliverPadding(
                                  padding:
                                      const EdgeInsets.fromLTRB(14, 6, 14, 10),
                                  sliver: SliverToBoxAdapter(
                                    child: _UpdatesCard(
                                      title: 'order_updates'.tr,
                                      lines: widget.order.modifications
                                          .map(_modificationLine)
                                          .where((s) => s.isNotEmpty)
                                          .toList(),
                                    ),
                                  ),
                                ),

                              // Liste des items
                              SliverPadding(
                                padding:
                                    const EdgeInsets.fromLTRB(14, 0, 14, 140),
                                sliver: SliverList.separated(
                                  itemCount: widget.order.items.length,
                                  separatorBuilder: (_, __) =>
                                      const SizedBox(height: 10),
                                  itemBuilder: (_, i) => _ItemRow(
                                      orderItem: widget.order.items[i]),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // ======= Résumé collant =======
                        _BottomSummaryBar(
                          productsTotal: productsTotal,
                          delivery: delivery,
                          total: total,
                          isPaid: widget.order.isPaid,
                          isCOD: widget.order.isCashOnDelivery,
                          canPay: widget.order.status == 'ACCEPTED' &&
                              !widget.order.isPaid &&
                              !widget.order.isCashOnDelivery,
                          canCancel: !widget.order.isPaid &&
                              widget.order.status != 'CANCELLED',
                          onPay: _isPayLoading
                              ? null
                              : () async {
                                  setState(() => _isPayLoading = true);
                                  final result =
                                      await Get.to(() => PaymentWidget(
                                            amount: total,
                                            orderId: widget.order.orderId,
                                            isOrderPayment: true,
                                            isPaid: false,
                                            storeId: widget.order.storeId,
                                            initialAmount: total,
                                          ));
                                  setState(() => _isPayLoading = false);
                                  if (result == true) Get.back();
                                },
                          onCancel: _isCancelLoading
                              ? null
                              : () {
                                  Get.defaultDialog(
                                    title: 'cancel_order'.tr,
                                    middleText: 'cancel_order_confirm'.tr,
                                    textCancel: 'no'.tr,
                                    textConfirm: 'yes'.tr,
                                    confirmTextColor: Colors.white,
                                    onConfirm: () async {
                                      setState(() => _isCancelLoading = true);
                                      await orderController.changeOrderStatus(
                                          widget.order.orderId!, 'CANCELLED');
                                      setState(() => _isCancelLoading = false);
                                      Get.back(); // dialog
                                      Get.back(); // page
                                    },
                                  );
                                },
                          onViewReceipt: (widget.order.isPaid &&
                                  widget.order.paymentScreenshot != null)
                              ? () => _showPaymentScreenshotDialog(context)
                              : null,
                          isPayLoading: _isPayLoading,
                          isCancelLoading: _isCancelLoading,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // ================= Helpers =================

  String _modificationLine(OrderModification m) {
    switch (m.type) {
      case 'quantity_changed':
        if (m.oldQuantity != null && m.newQuantity != null) {
          return 'mod_qty_changed_line'.trParams({
            'item': m.itemName,
            'from': '${m.oldQuantity}',
            'to': '${m.newQuantity}'
          });
        }
        return '';
      case 'removed':
        return 'mod_removed_line'.trParams({'item': m.itemName});
      case 'added':
        return 'mod_added_line'
            .trParams({'item': m.itemName, 'qty': '${m.newQuantity ?? 1}'});
      default:
        return '';
    }
  }

  void _showPaymentScreenshotDialog(BuildContext context) {
    final imageUrl = '${widget.order.paymentScreenshot}';

    showGeneralDialog(
      context: context,
      barrierLabel: 'payment_screenshot',
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.25),
      pageBuilder: (_, __, ___) => const SizedBox.shrink(),
      transitionBuilder: (ctx, anim, __, ___) {
        // Zoom + fade
        final curved =
            CurvedAnimation(parent: anim, curve: Curves.easeOutCubic);
        return Transform.scale(
          scale: 0.95 + 0.05 * curved.value,
          child: Opacity(
            opacity: curved.value,
            child: _PaymentScreenshotDialog(imageUrl: imageUrl),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 220),
    );
  }
}

/* ===================== Widgets “Glass & Cards” ===================== */

class _GlassHeader extends StatelessWidget {
  final String title;

  final VoidCallback? onCancelTap;
  final List<PopupMenuEntry> Function(BuildContext)? extraMenuItemsBuilder;

  const _GlassHeader({
    required this.title,
    this.onCancelTap,
    this.extraMenuItemsBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Container(
        height: 64,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.22),
          border: Border.all(color: Colors.white.withOpacity(0.35)),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            const SizedBox(width: 6),
            _GlassIconButton(
                icon: Icons.arrow_back_ios_new_rounded,
                onTap: () => Get.back()),
            const SizedBox(width: 8),

            // Avatar du store

            // Titre centré
            Expanded(
              child: Text(
                title,
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 18),
              ),
            ),
            const SizedBox(width: 10),
            // Menu 3 points
            _GlassMenuButton(
              itemsBuilder: (ctx) {
                final list = <PopupMenuEntry>[
                  if (onCancelTap != null)
                    PopupMenuItem(
                      child: Row(
                        children: [
                          const Icon(Icons.cancel_outlined, size: 18),
                          const SizedBox(width: 8),
                          Text('cancel_order'.tr),
                        ],
                      ),
                      onTap: () => Future.microtask(() => onCancelTap!()),
                    ),
                ];
                if (extraMenuItemsBuilder != null)
                  list.addAll(extraMenuItemsBuilder!(ctx));
                return list;
              },
            ),
            const SizedBox(width: 6),
          ],
        ),
      ),
    );
  }
}

class _GlassIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _GlassIconButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: InkWell(
          onTap: onTap,
          child: SizedBox(
              width: 40, height: 40, child: Icon(icon, color: Colors.white)),
        ),
      ),
    );
  }
}

class _GlassMenuButton extends StatelessWidget {
  final List<PopupMenuEntry> Function(BuildContext) itemsBuilder;
  const _GlassMenuButton({required this.itemsBuilder});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: PopupMenuButton(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          itemBuilder: itemsBuilder,
          color: Colors.white,
          elevation: 4,
        ),
      ),
    );
  }
}


class _GlassStatusBar extends StatelessWidget {
  final String? currentStatus;
  final bool isCOD;
  final bool isPaid; // ✅ Nouveau paramètre

  const _GlassStatusBar({
    required this.currentStatus,
    this.isCOD = false,
    this.isPaid = false,
  });

  // Flux normal avec paiement
  static const _flowWithPayment = <String>[
    'PENDING',
    'ACCEPTED',
    'WAITING_PAYMENT',
    'DELIVERED',
  ];

  // Flux simplifié si COD
  static const _flowWithoutPayment = <String>[
    'PENDING',
    'ACCEPTED',
    'DELIVERED',
  ];

  int _currentIndex(String? s, List<String> flow) {
    var key = (s ?? 'PENDING').toUpperCase();

    // ✅ Si payé → forcer "WAITING_PAYMENT"
    if (isPaid && flow.contains('WAITING_PAYMENT')) {
      key = 'WAITING_PAYMENT';
    }

    if (key == 'COMPLETED') return flow.indexOf('DELIVERED');
    return flow.indexOf(key).clamp(0, flow.length - 1);
  }

  IconData _iconFor(String key) {
    switch (key) {
      case 'PENDING':
        return Icons.hourglass_bottom_rounded;
      case 'ACCEPTED':
        return Icons.check_circle_outline;
      case 'WAITING_PAYMENT':
        return Icons.account_balance_wallet_outlined;
      case 'DELIVERED':
      case 'COMPLETED':
        return Icons.local_shipping_outlined;
      case 'CANCELLED':
        return Icons.cancel_outlined;
      default:
        return Icons.info_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isCancelled = (currentStatus ?? '').toUpperCase() == 'CANCELLED';

    final flow = isCOD ? _flowWithoutPayment : _flowWithPayment;

    if (isCancelled) {
      return _glass(
        child: Row(
          children: [
            _StepChip(
              labelKey: 'PENDING',
              showLabel: false,
              icon: _iconFor('PENDING'),
              active: true,
              color: Colors.white,
              iconSize: 22,
            ),
            Expanded(child: _connector(active: false)),
            _StepChip(
              labelKey: 'CANCELLED',
              showLabel: true,
              icon: _iconFor('CANCELLED'),
              active: true,
              color: Colors.white,
              iconSize: 22,
            ),
          ],
        ),
      );
    }

    final idx = _currentIndex(currentStatus, flow);

    return _glass(
      child: Row(
        children: [
       
   
      for (int i = 0; i < flow.length; i++) ...[
        _StepChip(
          labelKey: flow[i],
          showLabel: (i == idx) || (i == idx + 1), // actuel + suivant
          icon: _iconFor(flow[i]),
          active: i <= idx,
          color: Colors.white,
          iconSize: 20,
          emphasize: i == idx,                     // gras seulement pour l’actuel
          labelOpacity: (i == idx) ? 1.0 : 0.85,   // légère atténuation pour le suivant
        ),
        if (i != flow.length - 1) _connector(active: i < idx),
      ],
   

        ],
      ),
    );
  }

  Widget _glass({required Widget child}) {
    return ClipRRect(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.18),
          border: Border.all(color: Colors.white.withOpacity(0.35)),
        ),
        child: child,
      ),
    );
  }

  Widget _connector({required bool active}) {
    return Expanded(
      child: Container(
        height: 2,
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: active ? Colors.white : Colors.white60,
          borderRadius: BorderRadius.circular(999),
        ),
      ),
    );
  }
}
class _StepChip extends StatelessWidget {
  final String labelKey;
  final bool showLabel;
  final IconData icon;
  final bool active;
  final Color color;
  final double iconSize;

  final bool emphasize;      // texte en gras si true
  final double labelOpacity; // opacité du label

  const _StepChip({
    required this.labelKey,
    required this.showLabel,
    required this.icon,
    required this.active,
    required this.color,
    this.iconSize = 18,
    this.emphasize = false,
    this.labelOpacity = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    final iconColor = active ? color : color.withOpacity(0.7);
    final textStyle = TextStyle(
      color: color.withOpacity(labelOpacity),
      fontWeight: emphasize ? FontWeight.w700 : FontWeight.w500,
      fontSize: 12,
    );

    final iconWidget = Icon(icon, size: iconSize, color: iconColor);

    if (!showLabel) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Tooltip(message: labelKey.tr, child: ExcludeSemantics(child: iconWidget)),
        ],
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        iconWidget,
        const SizedBox(width: 6),
        Flexible(
          child: Text(
            labelKey.tr,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: textStyle,
          ),
        ),
      ],
    );
  }
}


/* ===================== Items ===================== */

class _UpdatesCard extends StatelessWidget {
  final String title;
  final List<String> lines;
  const _UpdatesCard({required this.title, required this.lines});

  @override
  Widget build(BuildContext context) {
    if (lines.isEmpty) return const SizedBox.shrink();
    return Container(
      decoration: BoxDecoration(
        color: Colors.blueGrey.withOpacity(0.06),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: Colors.blueGrey.withOpacity(0.25)),
      ),
      padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(children: [
          const Icon(Icons.info_outline_rounded,
              color: Colors.orange, size: 18),
          const SizedBox(width: 6),
          Text(title, style: const TextStyle(fontWeight: FontWeight.w700)),
        ]),
        const SizedBox(height: 8),
        ...lines.map((l) => Padding(
              padding: const EdgeInsets.only(bottom: 6),
              child:
                  Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                const Text('•  ', style: TextStyle(height: 1.2)),
                Expanded(
                    child:
                        Text(l, style: TextStyle(color: Colors.grey.shade800))),
              ]),
            )),
      ]),
    );
  }
}

class _ItemRow extends StatelessWidget {
  final OrderItem orderItem;
  const _ItemRow({required this.orderItem});

  @override
  Widget build(BuildContext context) {
    // Use localized data directly from orderItem instead of fetching
    final itemName = orderItem.getTitle();
    final itemDescription = orderItem.getDescription();
    final itemPrice = orderItem.price ?? 0.0;
    final lineTotal = itemPrice * orderItem.quantity;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: Colors.grey.withOpacity(0.12)),
      ),
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: orderItem.images != null && orderItem.images!.isNotEmpty
                ? Image.network(orderItem.images![0],
                    width: 70, height: 80, fit: BoxFit.cover)
                : Container(
                    width: 70,
                    height: 80,
                    color: Colors.grey.shade200,
                    child: Icon(Icons.image, color: Colors.grey.shade600)),
          ),
          const SizedBox(width: 12),
          Expanded(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(itemName,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 6),
              Wrap(spacing: 8, runSpacing: 6, children: [
                _MiniPill(
                    text:
                        '${'price'.tr}: ${itemPrice.toStringAsFixed(2)} ${'mru'.tr}'),
                _MiniPill(text: '${'quantity'.tr}: ${orderItem.quantity}'),
              ]),
            ]),
          ),
          const SizedBox(width: 8),
          Text('${lineTotal.toStringAsFixed(2)} ${'mru'.tr}',
              style: const TextStyle(fontWeight: FontWeight.w700)),
        ],
      ),
    );
  }
}

/* ===================== Bottom summary ===================== */

class _MiniPill extends StatelessWidget {
  final String text;
  const _MiniPill({required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(999),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Text(text,
          style: TextStyle(fontSize: 11, color: Colors.grey.shade800)),
    );
  }
}

class _BottomSummaryBar extends StatelessWidget {
  final double productsTotal;
  final double delivery;
  final double total;
  final bool isPaid;
  final bool isCOD;
  final bool canPay;
  final bool canCancel;
  final VoidCallback? onPay;
  final VoidCallback? onCancel;
  final VoidCallback? onViewReceipt;
  final bool isPayLoading;
  final bool isCancelLoading;

  const _BottomSummaryBar({
    required this.productsTotal,
    required this.delivery,
    required this.total,
    required this.isPaid,
    required this.isCOD,
    required this.canPay,
    required this.canCancel,
    required this.onPay,
    required this.onCancel,
    required this.onViewReceipt,
    required this.isPayLoading,
    required this.isCancelLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 16,
      shadowColor: Colors.black.withOpacity(0.12),
      child: SafeArea(
        top: false,
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border(top: BorderSide(color: Colors.grey.shade200))),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _row('products_total'.tr,
                  '${productsTotal.toStringAsFixed(2)} ${'mru'.tr}'),
              const SizedBox(height: 8),
              _row('delivery_charge'.tr,
                  '${delivery.toStringAsFixed(2)} ${'mru'.tr}'),
              const SizedBox(height: 8),
              _row('total'.tr, '${total.toStringAsFixed(2)} ${'mru'.tr}',
                  bold: true, color: AppColors.primary),
              const SizedBox(height: 12),
              if (isPaid && onViewReceipt != null) ...[
                ElevatedButton(
                  onPressed: onViewReceipt,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 12),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                  ),
                  child: Text('view_payment_screenshot'.tr),
                ),
              ],
              Row(
                children: [
                  if (canPay)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: isPayLoading ? null : onPay,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black87,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10)),
                        ),
                        child: isPayLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor:
                                        AlwaysStoppedAnimation(Colors.white)))
                            : Text('pay_now'.tr),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _row(String label, String value, {bool bold = false, Color? color}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label),
        Text(value,
            style: TextStyle(
                fontWeight: bold ? FontWeight.w800 : FontWeight.w600,
                color: color))
      ],
    );
  }
}

class _PaymentScreenshotDialog extends StatefulWidget {
  final String imageUrl;
  const _PaymentScreenshotDialog({required this.imageUrl});

  @override
  State<_PaymentScreenshotDialog> createState() =>
      _PaymentScreenshotDialogState();
}

class _PaymentScreenshotDialogState extends State<_PaymentScreenshotDialog> {
  final TransformationController _transformationController =
      TransformationController();
  TapDownDetails? _doubleTapDetails;
  bool _isLoading = true;
  bool _isError = false;

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  void _onDoubleTap() {
    // Toggle zoom between 1x and 2.2x around the tap point
    final position = _doubleTapDetails!.localPosition;
    const zoom = 2.2;

    final current = _transformationController.value;
    final isZoomed = current.getMaxScaleOnAxis() > 1.01;

    if (isZoomed) {
      _transformationController.value = Matrix4.identity();
    } else {
      final toScene = _transformationController.toScene(position);
      final m = Matrix4.identity()
        ..translate(-toScene.dx * (zoom - 1), -toScene.dy * (zoom - 1))
        ..scale(zoom);
      _transformationController.value = m;
    }
    setState(() {});
  }

  void _retry() {
    setState(() {
      _isError = false;
      _isLoading = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onDark = true; // on force un header clair sur fond flouté

    return SafeArea(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // ---- Backdrop blur
                BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                  child: Container(color: Colors.white.withOpacity(0.08)),
                ),

                // ---- Card container
                Container(
                  width: double.infinity,
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.98,
                    maxHeight: MediaQuery.of(context).size.height * 0.92,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.black.withOpacity(0.12),
                          blurRadius: 22,
                          offset: const Offset(0, 10))
                    ],
                  ),
                  child: Column(
                    children: [
                      // ===== Header “glass”
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10),
                        decoration: BoxDecoration(
                          color: onDark
                              ? theme.colorScheme.primary.withOpacity(0.9)
                              : Colors.white.withOpacity(0.85),
                          border: Border(
                            bottom: BorderSide(
                                color: Colors.black.withOpacity(0.06)),
                          ),
                        ),
                        child: Row(
                          children: [
                            // Close
                            _HeaderIconButton(
                              icon: Icons.close_rounded,
                              color: Colors.white,
                              onTap: () => Navigator.of(context).maybePop(),
                            ),

                            const SizedBox(width: 8),

                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                'payment_screenshot'.tr,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w800,
                                  fontSize: 16,
                                  decoration: TextDecoration.none,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // ===== Image zone (zoomable)
                      Expanded(
                        child: Container(
                          color: Colors.grey.shade50,
                          child: GestureDetector(
                            onDoubleTapDown: (d) => _doubleTapDetails = d,
                            onDoubleTap: _onDoubleTap,
                            child: InteractiveViewer(
                              minScale: 1,
                              maxScale: 4,
                              transformationController:
                                  _transformationController,
                              child: Image.network(
                                widget.imageUrl,
                                fit: BoxFit.contain,
                                // Loading
                                loadingBuilder: (ctx, child, prog) {
                                  if (prog == null) {
                                    // loaded
                                    if (_isLoading) {
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                        if (mounted)
                                          setState(() => _isLoading = false);
                                      });
                                    }
                                    return child;
                                  }
                                  return _ModernLoader(
                                    onDark: false,
                                    label: 'loading_screenshot'.tr,
                                  );
                                },
                                // Error
                                errorBuilder: (_, __, ___) {
                                  if (!_isError) {
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      if (mounted)
                                        setState(() => _isError = true);
                                    });
                                  }
                                  return _ModernError(
                                    title: 'failed_to_load_screenshot'.tr,
                                    onRetry: _retry,
                                    onClose: () =>
                                        Navigator.of(context).maybePop(),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // ---- Soft gradient edges (subtil)
                IgnorePointer(
                  ignoring: true,
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.transparent,
                          Colors.black12
                        ],
                        stops: [0, .85, 1],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// ===================== Header Icon Button =====================

class _HeaderIconButton extends StatelessWidget {
  final IconData icon;
  final Color color;
  final String? tooltip;
  final VoidCallback onTap;
  const _HeaderIconButton(
      {required this.icon,
      required this.color,
      required this.onTap,
      this.tooltip});

  @override
  Widget build(BuildContext context) {
    final btn = InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Icon(icon, size: 22, color: color),
      ),
    );

    return Tooltip(
      message: tooltip ?? '',
      child: Material(
        color: Colors.white.withOpacity(0.10),
        borderRadius: BorderRadius.circular(10),
        child: btn,
      ),
    );
  }
}

// ===================== Modern Loader (blanc possible) =====================

class _ModernLoader extends StatelessWidget {
  final bool onDark; // si true -> loader blanc
  final String? label;
  const _ModernLoader({this.onDark = false, this.label});

  @override
  Widget build(BuildContext context) {
    final color = onDark ? Colors.white : Colors.black87;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // anneau épais + ombre
            SizedBox(
              width: 36,
              height: 36,
              child: CircularProgressIndicator(
                strokeWidth: 3.2,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ),
            if (label != null) ...[
              const SizedBox(height: 10),
              Text(label!,
                  style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.none)),
            ],
          ],
        ),
      ),
    );
  }
}

// ===================== Modern Error =====================

class _ModernError extends StatelessWidget {
  final String title;
  final VoidCallback onRetry;
  final VoidCallback onClose;
  const _ModernError(
      {required this.title, required this.onRetry, required this.onClose});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(18.0),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.red.withOpacity(0.15)),
            borderRadius: BorderRadius.circular(14),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 14,
                  offset: const Offset(0, 6))
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline_rounded,
                  color: Colors.red.shade400, size: 34),
              const SizedBox(height: 10),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: Colors.grey.shade800,
                    fontSize: 15,
                    fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  OutlinedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh_rounded, size: 18),
                    label: Text('retry'.tr),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.black87,
                      side: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: onClose,
                    icon: const Icon(Icons.close_rounded, size: 18),
                    label: Text('close'.tr),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
