import 'dart:developer';
import 'dart:ui';
import 'package:boutigak/controllers/status_controller.dart';
import 'package:boutigak/data/models/status.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/sell/sell_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/matterport_webview_page.dart';
import 'package:boutigak/views/widgets/snapchat_item_widget.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';

import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import '/constants/app_colors.dart';
import 'package:boutigak/views/widgets/item_widget.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:boutigak/controllers/badge_controller.dart';
class MyItemsPage extends StatefulWidget {
  const MyItemsPage({Key? key}) : super(key: key);

  @override
  State<MyItemsPage> createState() => _MyItemsPageState();
}

class _MyItemsPageState extends State<MyItemsPage> {
  final ItemController itemController = Get.put(ItemController());
  final StatusController statusController = Get.put(StatusController());
  final BadgeController badgeController = Get.find<BadgeController>();
  late ScrollController _scrollController;

  void _navigateToHome() {
    Get.offAll(() => ZoomDrawerWrapper(), transition: Transition.noTransition);
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      statusController.fetchMyItems();
    }
  }

  Future<void> _onPullToRefresh() async {
    await statusController.fetchMyItems(refresh: true);
  }

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController()..addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await statusController.fetchMyItems(refresh: true);
      badgeController.resetBadge('items');
    });
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Fond vert dégradé
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.92),
                    theme.colorScheme.primary.withOpacity(0.90),
                  ],
                ),
              ),
            ),
          ),

          SafeArea(
            bottom: false,
            child: Obx(() {
              final items = statusController.myItems;
              final isLoading = statusController.isLoading.value;
              final isLoadingMore = statusController.isLoadingMore.value;

              if (isLoading) {
                return Column(
                  children: const [
                    _GlassHeader(title: 'my_items'),
                    Expanded(child: MyItemsShimmerList()),
                  ],
                );
              }

              return CustomScrollView(
                controller: _scrollController,
                physics: const BouncingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics(),
                ),
                slivers: [
                  CupertinoSliverRefreshControl(
                    onRefresh: _onPullToRefresh,
                    builder: (context, refreshState, pulledExtent,
                        refreshTriggerPullDistance, refreshIndicatorExtent) {
                      return const Center(
                        child: CupertinoActivityIndicator(
                          radius: 14,
                          color: Colors.white,
                        ),
                      );
                    },
                  ),

                  const SliverToBoxAdapter(
                    child: _GlassHeader(title: 'my_items'),
                  ),

                  // ===== OPTION A : Empty state SUR LE FOND VERT =====
                  if (items.isEmpty)
                    SliverFillRemaining(
                      hasScrollBody: false,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 100),
                        child: _EmptyState(
                          title: 'no_items_title'.tr,
                          subtitle: 'no_items_subtitle'.tr,
                          ctaText: 'back_home'.tr.isEmpty
                              ? 'Back to Home'
                              : 'back_home'.tr,
                          onCta: _navigateToHome,
                        ),
                      ),
                    )
                  // ===== Liste dans une carte blanche UNIQUEMENT s'il y a des items =====
                  else
                    SliverToBoxAdapter(
                      child: LayoutBuilder(
                        builder: (context, _) {
                          final padding = MediaQuery.of(context).padding;
                          final viewportH =
                              MediaQuery.of(context).size.height - padding.top;

                          return ConstrainedBox(
                            constraints: BoxConstraints(minHeight: viewportH),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(24),
                                ),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const SizedBox(height: 12),
                                  ...List.generate(
                                    items.length,
                                    (index) => Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 4,
                                      ),
                                      child: MyItemWidget(status: items[index]),
                                    ),
                                  ),
                                  if (isLoadingMore)
                                    const Padding(
                                      padding: EdgeInsets.all(16),
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    ),
                                  const SizedBox(height: 12),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }
}

class _EmptyState extends StatelessWidget {
  final String title;
  final String subtitle;
  final String ctaText;
  final VoidCallback onCta;

  const _EmptyState({
    required this.title,
    required this.subtitle,
    required this.ctaText,
    required this.onCta,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 56, 24, 24),
      child: Column(
        children: [
          // Illustration placeholder
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.18),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.35)),
            ),
            child: const Icon(Icons.receipt_long, color: Colors.white, size: 56),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.w800),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.white70),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onCta,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white.withOpacity(0.18),
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white30),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
              elevation: 0,
            ),
            child: Text(ctaText),
          ),
        ],
      ),
    );
  }
}

class _GlassHeader extends StatelessWidget {
  final String title;
  const _GlassHeader({required this.title});

    void _navigateToHome() async {
    Get.offAll(
      () => ZoomDrawerWrapper(),
      transition: Transition.noTransition,
    );
  }


  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.25),
              border: Border.all(color: Colors.white.withOpacity(0.35)),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const SizedBox(width: 6),
                _GlassIconButton(
                  icon: Icons.arrow_back_ios_new_rounded,
                  onTap: () => _navigateToHome(),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title.tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(width: 46), // équilibre visuel avec le bouton retour
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _GlassIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _GlassIconButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: InkWell(
          onTap: onTap,
          child: SizedBox(
            width: 40,
            height: 40,
            child: Icon(icon, color: Colors.white),
          ),
        ),
      ),
    );
  }
}



class MyItemDetailModal extends StatefulWidget {
  final Item item;
  final PageController pageController;
  final int currentPage;
  final bool isFavorited;
  final ItemController itemController;

  const MyItemDetailModal({
    Key? key,
    required this.item,
    required this.pageController,
    required this.currentPage,
    required this.isFavorited,
    required this.itemController,
  }) : super(key: key);

  @override
  State<MyItemDetailModal> createState() => _MyItemDetailModalState();
}

class _MyItemDetailModalState extends State<MyItemDetailModal> {
  late int currentPage;
  late bool isFavorited;

  @override
  void initState() {
    super.initState();
    currentPage = widget.currentPage;
    isFavorited = widget.isFavorited;

    // Charger les détails à jour
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.itemController.fetchItemById(widget.item.id!);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isLoading = widget.itemController.isLoading.value;
      final updatedItem = widget.itemController.selectedItem.value;

      if (isLoading || updatedItem == null) {
        return const _MyItemDetailSkeleton();
      }

      return Column(
        children: [
          // --- Slider d’images + actions flottantes ---
          Stack(
            children: [
              Container(
                width: double.infinity,
                height: MediaQuery.of(context).size.height * .47,
                color: Theme.of(context).colorScheme.surface,
                child: ImageSlider(
                  pageController: widget.pageController,
                  photos: updatedItem.images.map((image) => '$image').toList(),
                  currentPage: currentPage,
                  onPageChanged: (int page) => setState(() => currentPage = page),
                  borderRadius: 0,
                ),
              ),
              // Bouton ouvrir dans une nouvelle page
              Positioned(
                top: 60,
                right: 10,
                child: ClipOval(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                    child: Container(
                      color: AppColors.onBackground.withOpacity(0.2),
                      child: IconButton(
                        iconSize: 20,
                        icon: Icon(FontAwesomeIcons.upRightFromSquare, color: AppColors.background),
                        onPressed: () {
                          // TODO: ton action
                        },
                      ),
                    ),
                  ),
                ),
              ),
              // Bouton fermer
              Positioned(
                top: 60,
                left: 10,
                child: ClipOval(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                    child: Container(
                      color: AppColors.onBackground.withOpacity(0.3),
                      child: IconButton(
                        iconSize: 20,
                        icon: Icon(FontAwesomeIcons.chevronDown, color: AppColors.background),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ),
                  ),
                ),
              ),
              // Bouton 3D Matterport (si dispo)
              if (updatedItem.matterportLink != null && updatedItem.matterportLink!.isNotEmpty)
                Positioned(
                  bottom: 40,
                  right: 10,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MatterportView(
                              matterportLink: updatedItem.matterportLink!,
                            ),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(20),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(FontAwesomeIcons.cube, color: AppColors.background, size: 24),
                          const SizedBox(width: 6),
                          Text(
                            '3d_view'.tr,
                            style: const TextStyle(
                              color: AppColors.background,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),

          // --- Section scrollable ---
          Expanded(
            child: SingleChildScrollView(
              child: InfoSectionMyItems(item: updatedItem),
            ),
          ),

          // --- Prix + boutons (responsive) ---
          SafeArea( // respecte l’encoche & la barre gestuelle
            top: false,
            child: Padding(
              padding: EdgeInsets.only(
                bottom: 12 + MediaQuery.of(context).padding.bottom,
                left: 16,
                right: 16,
                top: 10,
              ),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final isCompact = constraints.maxWidth < 420;
                  const btnHeight = 50.0;

                  final priceWidget = _PriceBlock(
                    price: (updatedItem.price ?? 0).toStringAsFixed(0),
                  );

                  final editBtn = SizedBox(
                    height: btnHeight,
                    child: TextButton(
                      onPressed: () => Get.to(() => SellPage(itemId: updatedItem.id!)),
                      style: TextButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.onSurface,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                      ),
                      child: Text(
                        "edit".tr,
                        style: const TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );

                  final deleteBtn = SizedBox(
                    height: btnHeight,
                    child: TextButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (_) => AlertDialog(
                            title: Text("confirm_deletion".tr),
                            content: Text("delete_confirmation_message".tr),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: Text("cancel".tr),
                              ),
                              ElevatedButton(
                                onPressed: () {
                                  widget.itemController.deleteItem(updatedItem.id!);
                                  Navigator.pop(context); // ferme dialog
                                  Navigator.pop(context); // ferme modal
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.error,
                                ),
                                child: Text("delete".tr),
                              ),
                            ],
                          ),
                        );
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: AppColors.error,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                      ),
                      child: Text(
                        "delete".tr,
                        style: const TextStyle(
                          color: AppColors.surface,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );

                  if (isCompact) {
                    // 📱 Écrans étroits : prix au-dessus, boutons en pleine largeur
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        priceWidget,
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(child: editBtn),
                            const SizedBox(width: 10),
                            Expanded(child: deleteBtn),
                          ],
                        ),
                      ],
                    );
                  } else {
                    // 🖥️ Écrans larges : tout sur une ligne
                    return Row(
                      children: [
                        priceWidget,
                        const Spacer(),
                        SizedBox(width: 160, child: editBtn),
                        const SizedBox(width: 12),
                        SizedBox(width: 160, child: deleteBtn),
                      ],
                    );
                  }
                },
              ),
            ),
          ),
        ],
      );
    });
  }
}

// --------- Skeleton qui MIMIQUE le contenu final ---------

class _MyItemDetailSkeleton extends StatelessWidget {
  const _MyItemDetailSkeleton();

  @override
  Widget build(BuildContext context) {
    final base = Colors.grey[300]!;
    final highlight = Colors.grey[100]!;

    Widget box({double h = 14, double w = double.infinity, BorderRadius? r}) {
      return Container(
        height: h,
        width: w,
        decoration: BoxDecoration(
          color: base,
          borderRadius: r ?? BorderRadius.circular(8),
        ),
      );
    }

    return Column(
      children: [
        // Slider skeleton + actions
        Stack(
          children: [
            Shimmer.fromColors(
              baseColor: base,
              highlightColor: highlight,
              child: Container(
                width: double.infinity,
                height: MediaQuery.of(context).size.height * .47,
                color: base,
              ),
            ),
            Positioned(
              top: 60,
              right: 10,
              child: Shimmer.fromColors(
                baseColor: base,
                highlightColor: highlight,
                child: Container(
                  width: 40, height: 40,
                  decoration: BoxDecoration(
                    color: base, shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            Positioned(
              top: 60,
              left: 10,
              child: Shimmer.fromColors(
                baseColor: base,
                highlightColor: highlight,
                child: Container(
                  width: 40, height: 40,
                  decoration: BoxDecoration(
                    color: base, shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            // Petit pill en bas à droite (simule bouton 3D)
            Positioned(
              bottom: 40,
              right: 10,
              child: Shimmer.fromColors(
                baseColor: base,
                highlightColor: highlight,
                child: Container(
                  height: 36,
                  padding: const EdgeInsets.symmetric(horizontal: 50),
                  decoration: BoxDecoration(
                    color: base, borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),

        // Section scrollable (structure identique à InfoSectionMyItems)
        Expanded(
          child: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Shimmer.fromColors(
                baseColor: base,
                highlightColor: highlight,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Titre (2 lignes)
                    box(h: 18, w: double.infinity),
                    const SizedBox(height: 8),
                    box(h: 18, w: MediaQuery.of(context).size.width * 0.6),

                    const SizedBox(height: 8),

                    // by + username
                    Row(
                      children: [
                        box(h: 14, w: 40),
                        const SizedBox(width: 6),
                        box(h: 14, w: 80),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // date (à droite)
                    Row(
                      children: [
                        const Spacer(),
                        box(h: 14, w: 90),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // "Description" titre
                    box(h: 16, w: 120),
                    const SizedBox(height: 8),
                    // paragraphes
                    box(h: 14, w: double.infinity),
                    const SizedBox(height: 6),
                    box(h: 14, w: double.infinity),
                    const SizedBox(height: 6),
                    box(h: 14, w: MediaQuery.of(context).size.width * 0.7),

                    const SizedBox(height: 24),

                    // Bloc détails
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          box(h: 16, w: 90), // "details"
                          const SizedBox(height: 12),
                          // lignes détails (label 100px + value)
                          ...List.generate(5, (i) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 6),
                              child: Row(
                                children: [
                                  Container(
                                    width: 100, // même largeur que le label réel
                                    alignment: Alignment.centerLeft,
                                    child: box(h: 14, w: 80),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(child: box(h: 14, w: double.infinity)),
                                ],
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // Bas — skeleton prix + 2 boutons
        SafeArea(
          top: false,
          child: Padding(
            padding: EdgeInsets.only(
              bottom: 12 + MediaQuery.of(context).padding.bottom,
              left: 16, right: 16, top: 10,
            ),
            child: LayoutBuilder(
              builder: (context, c) {
                final isCompact = c.maxWidth < 420;
                final btn = (double w) => Shimmer.fromColors(
                      baseColor: base,
                      highlightColor: highlight,
                      child: Container(
                        height: 50, width: w,
                        decoration: BoxDecoration(
                          color: base, borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    );
                final price = Shimmer.fromColors(
                  baseColor: base,
                  highlightColor: highlight,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(height: 12, width: 90, color: base),
                      const SizedBox(height: 6),
                      Container(height: 18, width: 80, color: base),
                    ],
                  ),
                );

                if (isCompact) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      price,
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(child: btn(double.infinity)),
                          const SizedBox(width: 10),
                          Expanded(child: btn(double.infinity)),
                        ],
                      ),
                    ],
                  );
                } else {
                  return Row(
                    children: [
                      price,
                      const Spacer(),
                      SizedBox(width: 160, child: btn(160)),
                      const SizedBox(width: 12),
                      SizedBox(width: 160, child: btn(160)),
                    ],
                  );
                }
              },
            ),
          ),
        ),
      ],
    );
  }
}

// --------- Sous-widgets ---------

class _PriceBlock extends StatelessWidget {
  final String price;
  const _PriceBlock({required this.price});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "price".tr + " (" + "mru".tr + ")",
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).disabledColor,
          ),
        ),
        Text(
          price,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}

class InfoSectionMyItems extends StatelessWidget {
  final Item item;

  const InfoSectionMyItems({Key? key, required this.item}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final onSurface = Theme.of(context).colorScheme.onSurface;
 final locale = Get.locale?.languageCode ?? 'en';
final createdAtText = item.createdAt != null
    ? timeago.format(item.createdAt!, locale: locale)
    : 'unknown'.tr;

    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title + User info
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Titre limité en lignes (évite overflow mobiles)
                    Text(
                      item.getTitle(),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          "by".tr + " ",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: onSurface,
                          ),
                        ),
                        Flexible(
                          child: Text(
                            item.userName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).disabledColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Date (à droite)
          Row(
            children: [
              const Spacer(),
             
Text(
  createdAtText,
  style: TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.bold,
    color: Theme.of(context).primaryColor,
  ),
),

            ],
          ),

          const SizedBox(height: 16),

          // Description
          Text(
            "description".tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            item.getDescription(),
            style: const TextStyle(fontSize: 16),
          ),

          const SizedBox(height: 24),

          // Details
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: _DetailsList(item: item),
          ),
        ],
      ),
    );
  }
}

class _DetailsList extends StatelessWidget {
  final Item item;
  const _DetailsList({required this.item});

  @override
  Widget build(BuildContext context) {
    final onSurface = Theme.of(context).colorScheme.onSurface;

    List<Widget> rows = [
      _buildDetailRow("details".tr, null, header: true, onSurface: onSurface),
      const SizedBox(height: 12),
      _buildDetailRow("condition".tr, item.condition, onSurface: onSurface),
      _buildDetailRow("category".tr, item.categoryName ?? "unknown".tr, onSurface: onSurface),
      _buildDetailRow("brand".tr, item.brandName ?? "unknown".tr, onSurface: onSurface),
    ];

    if (item.categoryItemDetails.isNotEmpty) {
      rows.addAll(item.categoryItemDetails.map<Widget>((detail) {
        String label = detail.labelEn;
        if (Get.locale?.languageCode == 'ar') {
          label = detail.labelAr;
        } else if (Get.locale?.languageCode == 'fr') {
          label = detail.labelFr;
        }
        return _buildDetailRow(label, detail.value.toString(), onSurface: onSurface);
      }));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rows,
    );
  }

  Widget _buildDetailRow(String label, String? value, {bool header = false, required Color onSurface}) {
    if (header) {
      return Text(
        label,
        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: onSurface),
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          // Label à largeur fixe pour aligner (même que skeleton)
          SizedBox(
            width: 100,
            child: Text(label, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              value ?? "unknown".tr,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }
}


class MyItemWidget extends StatefulWidget {
  final Status status;

  const MyItemWidget({
    Key? key,
    required this.status,
  }) : super(key: key);

  @override
  _MyItemWidgetState createState() => _MyItemWidgetState();
}

class _MyItemWidgetState extends State<MyItemWidget> {
  late ItemController itemController;

  @override
  void initState() {
    super.initState();
    itemController = Get.find<ItemController>();
  }

  void _openMyItemDetailModal(BuildContext context, Status status) async {
    final itemId = int.parse(status.id);
    
    // Reset pour forcer le shimmer
    itemController.selectedItem.value = null;
    await itemController.fetchItemById(itemId);

    showModalBottomSheet(
      backgroundColor: Theme.of(context).colorScheme.surface,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (_) {
        return MyItemDetailModal(
          item: itemController.selectedItem.value!,
          pageController: PageController(),
          currentPage: 0,
          isFavorited: false,
          itemController: itemController,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _openMyItemDetailModal(context, widget.status),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0.4, 0.4),
            ),
          ],
        ),
        child: Row(
          children: [
            CachedImageWidget(
              imageUrl: '${widget.status.images.first}',
              width: 70,
              height: 80,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(8.0),
            ),
            const SizedBox(width: 8.0),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Première ligne : titre + Help
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          widget.status.title,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      HelpButton(),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Deuxième ligne : statut et action
                  Row(
                    children: [
                      _buildStatusWidget(widget.status),
                      const Spacer(),
                      if (widget.status.status == "REJECTED")
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            widget.status.rejectionReason ?? 'rejected'.tr,
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )
                      else if (widget.status.isPaid != null &&
                          !widget.status.isPaid &&
                          widget.status.status != "REJECTED" &&
                          widget.status.status != "APPROVED")
                        GestureDetector(
                          onTap: () {
                            Get.to(() => PaymentWidget(
                                  amount: double.parse(
                                      widget.status.categoryPrice ?? '0'),
                                  itemId: int.parse(widget.status.id),
                                  isOrderPayment: false,
                                  isPaid: false,
                                  initialAmount: double.parse(
                                      widget.status.categoryPrice ?? '0'),
                                ));
                          },
                          child: Container(
                            width: 50,
                            height: 30,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                'pay'.tr,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),

                        // show image screenshot 
                      if (widget.status.isPaid && widget.status.paymentScreenshot != null  )
                        GestureDetector(
                          onTap: () {
                            log('payment screenshot ${widget.status.paymentScreenshot}');
                            _showPaymentScreenshotDialog(context, widget.status.paymentScreenshot!);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            // width: 50,
                            height: 30,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                'payment_screenshot'.tr,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

    void _showPaymentScreenshotDialog(BuildContext context , String imageUrl) {

    showGeneralDialog(
      context: context,
      barrierLabel: 'payment_screenshot',
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.25),
      pageBuilder: (_, __, ___) => const SizedBox.shrink(),
      transitionBuilder: (ctx, anim, __, ___) {
        // Zoom + fade
        final curved =
            CurvedAnimation(parent: anim, curve: Curves.easeOutCubic);
        return Transform.scale(
          scale: 0.95 + 0.05 * curved.value,
          child: Opacity(
            opacity: curved.value,
            child: _PaymentScreenshotDialog(imageUrl: imageUrl),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 220),
    );
  }

  Widget _buildStatusWidget(Status status) {
    Widget buildStatusBox(
        {required IconData icon, required String text, required Color color}) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 5),
            Text(
              text.tr, // ✅ traduction
              style: TextStyle(
                  fontSize: 12, fontWeight: FontWeight.bold, color: color),
            ),
          ],
        ),
      );
    }

    if (status.status == "APPROVED") {
      if (status.isPromoted) {
        return buildStatusBox(
            icon: Icons.star,
            text: "approved_and_promoted",
            color: Colors.green);
      }
      return buildStatusBox(
          icon: Icons.check_circle, text: "approved", color: Colors.green);
    } else if (status.status == "REJECTED") {
      return buildStatusBox(
          icon: Icons.cancel, text: "rejected", color: Colors.red);
    } else if (status.isPaid != null && status.isPaid == false) {
      return buildStatusBox(
          icon: Icons.attach_money,
          text: "awaiting_payment",
          color: Colors.orange);
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              width: 14,
              height: 14,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            ),
            const SizedBox(width: 5),
            Text(
              "under_review".tr,
              style: const TextStyle(
                  fontSize: 12, fontWeight: FontWeight.bold, color: Colors.blue),
            ),
          ],
        ),
      );
    }
  }
}







class _PaymentScreenshotDialog extends StatefulWidget {
  final String imageUrl;
  const _PaymentScreenshotDialog({required this.imageUrl});

  @override
  State<_PaymentScreenshotDialog> createState() =>
      _PaymentScreenshotDialogState();
}

class _PaymentScreenshotDialogState extends State<_PaymentScreenshotDialog> {
  final TransformationController _transformationController =
      TransformationController();
  TapDownDetails? _doubleTapDetails;
  bool _isLoading = true;
  bool _isError = false;

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  void _onDoubleTap() {
    // Toggle zoom between 1x and 2.2x around the tap point
    final position = _doubleTapDetails!.localPosition;
    const zoom = 2.2;

    final current = _transformationController.value;
    final isZoomed = current.getMaxScaleOnAxis() > 1.01;

    if (isZoomed) {
      _transformationController.value = Matrix4.identity();
    } else {
      final toScene = _transformationController.toScene(position);
      final m = Matrix4.identity()
        ..translate(-toScene.dx * (zoom - 1), -toScene.dy * (zoom - 1))
        ..scale(zoom);
      _transformationController.value = m;
    }
    setState(() {});
  }

  void _retry() {
    setState(() {
      _isError = false;
      _isLoading = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onDark = true; // on force un header clair sur fond flouté

    return SafeArea(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // ---- Backdrop blur
                BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                  child: Container(color: Colors.white.withOpacity(0.08)),
                ),

                // ---- Card container
                Container(
                  width: double.infinity,
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.98,
                    maxHeight: MediaQuery.of(context).size.height * 0.92,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.black.withOpacity(0.12),
                          blurRadius: 22,
                          offset: const Offset(0, 10))
                    ],
                  ),
                  child: Column(
                    children: [
                      // ===== Header “glass”
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10),
                        decoration: BoxDecoration(
                          color: onDark
                              ? theme.colorScheme.primary.withOpacity(0.9)
                              : Colors.white.withOpacity(0.85),
                          border: Border(
                            bottom: BorderSide(
                                color: Colors.black.withOpacity(0.06)),
                          ),
                        ),
                        child: Row(
                          children: [
                            // Close
                            _HeaderIconButton(
                              icon: Icons.close_rounded,
                              color: Colors.white,
                              onTap: () => Navigator.of(context).maybePop(),
                            ),

                            const SizedBox(width: 8),

                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                'payment_screenshot'.tr,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w800,
                                  fontSize: 16,
                                  decoration: TextDecoration.none,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // ===== Image zone (zoomable)
                      Expanded(
                        child: Container(
                          color: Colors.grey.shade50,
                          child: GestureDetector(
                            onDoubleTapDown: (d) => _doubleTapDetails = d,
                            onDoubleTap: _onDoubleTap,
                            child: InteractiveViewer(
                              minScale: 1,
                              maxScale: 4,
                              transformationController:
                                  _transformationController,
                              child: Image.network(
                                widget.imageUrl,
                                fit: BoxFit.contain,
                                // Loading
                                loadingBuilder: (ctx, child, prog) {
                                  if (prog == null) {
                                    // loaded
                                    if (_isLoading) {
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                        if (mounted)
                                          setState(() => _isLoading = false);
                                      });
                                    }
                                    return child;
                                  }
                                  return _ModernLoader(
                                    onDark: false,
                                    label: 'loading_screenshot'.tr,
                                  );
                                },
                                // Error
                                errorBuilder: (_, __, ___) {
                                  if (!_isError) {
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      if (mounted)
                                        setState(() => _isError = true);
                                    });
                                  }
                                  return _ModernError(
                                    title: 'failed_to_load_screenshot'.tr,
                                    onRetry: _retry,
                                    onClose: () =>
                                        Navigator.of(context).maybePop(),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // ---- Soft gradient edges (subtil)
                IgnorePointer(
                  ignoring: true,
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.transparent,
                          Colors.black12
                        ],
                        stops: [0, .85, 1],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}




class HelpButton extends StatelessWidget {
  const HelpButton({super.key});

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: const Text('Besoin d’aide ?'),
        content: const Text(
          'Contactez-nous si vous avez un problème, ou si vous souhaitez promouvoir votre annonce pour qu’elle apparaisse en premier.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              const phoneNumber = '0022236666688';
              final url = Uri.parse("https://wa.me/$phoneNumber");

              if (await canLaunchUrl(url)) {
                await launchUrl(url, mode: LaunchMode.externalApplication);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text("Impossible d'ouvrir WhatsApp.")),
                );
              }
            },
            icon: Icon(Icons.phone),
            label: const Text("Contactez-nous"),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showHelpDialog(context),
      child: Container(
        width: 18,
        height: 18,
        decoration: BoxDecoration(
          color: Colors.grey[700], // gris foncé
          shape: BoxShape.circle,
        ),
        child: const Center(
          child: Text(
            '?',
            style: TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}


class _HeaderIconButton extends StatelessWidget {
  final IconData icon;
  final Color color;
  final String? tooltip;
  final VoidCallback onTap;
  const _HeaderIconButton(
      {required this.icon,
      required this.color,
      required this.onTap,
      this.tooltip});

  @override
  Widget build(BuildContext context) {
    final btn = InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Icon(icon, size: 22, color: color),
      ),
    );

    return Tooltip(
      message: tooltip ?? '',
      child: Material(
        color: Colors.white.withOpacity(0.10),
        borderRadius: BorderRadius.circular(10),
        child: btn,
      ),
    );
  }
}

// ===================== Modern Loader (blanc possible) =====================

class _ModernLoader extends StatelessWidget {
  final bool onDark; // si true -> loader blanc
  final String? label;
  const _ModernLoader({this.onDark = false, this.label});

  @override
  Widget build(BuildContext context) {
    final color = onDark ? Colors.white : Colors.black87;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // anneau épais + ombre
            SizedBox(
              width: 36,
              height: 36,
              child: CircularProgressIndicator(
                strokeWidth: 3.2,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ),
            if (label != null) ...[
              const SizedBox(height: 10),
              Text(label!,
                  style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.none)),
            ],
          ],
        ),
      ),
    );
  }
}

// ===================== Modern Error =====================

class _ModernError extends StatelessWidget {
  final String title;
  final VoidCallback onRetry;
  final VoidCallback onClose;
  const _ModernError(
      {required this.title, required this.onRetry, required this.onClose});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(18.0),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.red.withOpacity(0.15)),
            borderRadius: BorderRadius.circular(14),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 14,
                  offset: const Offset(0, 6))
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline_rounded,
                  color: Colors.red.shade400, size: 34),
              const SizedBox(height: 10),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: Colors.grey.shade800,
                    fontSize: 15,
                    fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  OutlinedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh_rounded, size: 18),
                    label: Text('retry'.tr),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.black87,
                      side: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: onClose,
                    icon: const Icon(Icons.close_rounded, size: 18),
                    label: Text('close'.tr),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}


class MyItemsShimmerList extends StatelessWidget {
  const MyItemsShimmerList({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(12, 12, 12, 16),
      itemCount: 6,
      itemBuilder: (context, index) => const _MyItemShimmerCard(),
    );
  }
}
class _MyItemShimmerCard extends StatelessWidget {
  const _MyItemShimmerCard();

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.white.withOpacity(0.30),
      highlightColor: Colors.white.withOpacity(0.10),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.18),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white24),
        ),
        child: Row(
          children: [
            // Image shimmer
            Container(
              width: 70,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            const SizedBox(width: 12),

            // Texte shimmer
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Titre
                  Container(
                    height: 14,
                    width: 180,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 10),

                  // Status bar
                  Row(
                    children: [
                      Container(height: 12, width: 80, color: Colors.white),
                      const SizedBox(width: 8),
                      Container(height: 12, width: 40, color: Colors.white),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),

            // Prix / action
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(height: 14, width: 60, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 12, width: 40, color: Colors.white),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
