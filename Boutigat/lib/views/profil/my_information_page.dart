import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/data/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

/// =======================
/// CONTROLLER
/// =======================
class UpdateProfileController extends GetxController {
  final isLoading = false.obs;
  final showErrorMessage = ''.obs;

  final AuthController authController = Get.find<AuthController>();

  bool validateProfile(String firstName, String lastName) {
    final f = firstName.trim();
    final l = lastName.trim();

    if (f.isEmpty || l.isEmpty) {
      showErrorMessage.value = 'name_fields_cannot_be_empty'.tr;
      return false;
    }
    // Optionnel : longueur mini
    if (f.length < 2 || l.length < 2) {
      showErrorMessage.value = 'name_fields_min_len'.tr;
      return false;
    }
    return true;
  }

  /// Retourne true si succès. L’UI décide ensuite (vider champs, pop, snackbar…)
  Future<bool> updateProfile(String firstName, String lastName) async {
    isLoading.value = true;
    showErrorMessage.value = '';

    if (!validateProfile(firstName, lastName)) {
      isLoading.value = false;
      return false;
    }

    try {
      final success = await AuthService.updateProfile(
        firstName: firstName.trim(),
        lastName: lastName.trim(),
      );

      if (success) {
        await authController.getAndSaveUser();
        return true;
      } else {
        showErrorMessage.value = 'profile_update_error'.tr;
        return false;
      }
    } catch (e) {
      showErrorMessage.value = 'profile_update_error'.tr + ' ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}

/// =======================
/// PAGE (UI MODERNISÉE)
/// =======================
/// import 'package:flutter/material.dart';

class MyInformationPage extends StatefulWidget {
  final String firstName;
  final String lastName;

  const MyInformationPage({
    super.key,
    required this.firstName,
    required this.lastName,
  });

  @override
  State<MyInformationPage> createState() => _MyInformationPageState();
}

class _MyInformationPageState extends State<MyInformationPage> {
  final UpdateProfileController c = Get.put(UpdateProfileController());

  late final TextEditingController _firstCtrl;
  late final TextEditingController _lastCtrl;

  final _firstFocus = FocusNode();
  final _lastFocus  = FocusNode();

  bool _dirty = false;
  bool get _valid =>
      _firstCtrl.text.trim().isNotEmpty &&
      _lastCtrl.text.trim().isNotEmpty;

  @override
  void initState() {
    super.initState();
    _firstCtrl = TextEditingController(text: widget.firstName);
    _lastCtrl  = TextEditingController(text: widget.lastName);

    void _markDirty() {
      final dirty = _firstCtrl.text.trim() != widget.firstName.trim() ||
                    _lastCtrl.text.trim()  != widget.lastName.trim();
      if (dirty != _dirty) setState(() => _dirty = dirty);
    }

    _firstCtrl.addListener(_markDirty);
    _lastCtrl.addListener(_markDirty);
  }

  @override
  void dispose() {
    _firstCtrl.dispose();
    _lastCtrl.dispose();
    _firstFocus.dispose();
    _lastFocus.dispose();
    super.dispose();
  }

  String _initials(String first, String last) {
    final f = first.trim().isNotEmpty ? first.trim()[0] : '';
    final l = last.trim().isNotEmpty  ? last.trim()[0]  : '';
    final s = (f + l).toUpperCase();
    return s.isEmpty ? 'U' : s;
  }

  Future<void> _submit() async {
    if (!mounted || !(_valid && !_isBusy)) return;
    FocusScope.of(context).unfocus();

    final ok = await c.updateProfile(_firstCtrl.text.trim(), _lastCtrl.text.trim());
    if (!mounted) return;

    if (ok) {
      Get.back(result: true); // ✅ la page précédente peut refetch conditionnellement
      Get.snackbar('success'.tr, 'profile_updated'.tr,
          snackPosition: SnackPosition.BOTTOM, margin: const EdgeInsets.all(12));
    } else {
      Get.snackbar('error'.tr, 'something_wrong'.tr,
          snackPosition: SnackPosition.BOTTOM,
          margin: const EdgeInsets.all(12),
          backgroundColor: Colors.red,
          colorText: Colors.white);
    }
  }

  bool get _isBusy => c.isLoading.value;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final pad   = EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 16.h);
    final br    = 12.r;
    final fill  = theme.colorScheme.surfaceVariant.withOpacity(0.4);
    final border= theme.dividerColor.withOpacity(0.25);

    final first = _firstCtrl.text;
    final last  = _lastCtrl.text;
    final initials = _initials(first, last);

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      appBar: AppBar(
        title: Text('user_name'.tr),
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // ====== Fond dégradé (cohérent Settings/Language) ======
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.95),
                  ],
                ),
              ),
            ),
          ),

          // ====== Contenu scrollable avec header cohérent ======
          CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // ----- Bandeau header (avatar + titre) -----
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 12.h),
                  child: Material(
                    color: Colors.white.withOpacity(0.10),
                    elevation: 0,
                    borderRadius: BorderRadius.circular(16.r),
                    child: Container(
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(color: Colors.white.withOpacity(0.25)),
                      ),
                      child: Row(
                        children: [
                          // Avatar initials avec halo (même style)
                          Container(
                            width: 56.w,
                            height: 56.w,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white.withOpacity(0.28),
                                  Colors.white.withOpacity(0.12),
                                ],
                              ),
                              border: Border.all(color: Colors.white.withOpacity(0.35)),
                            ),
                            child: Text(
                              initials,
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w800,
                                fontSize: 18.sp,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                          SizedBox(width: 12.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Titre + sous-titre (i18n déjà présents chez toi)
                                Text(
                                  'profile_header_title'.tr, // ex: "Modifier vos informations"
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.95),
                                    fontWeight: FontWeight.w800,
                                    fontSize: 16.sp,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: 4.h),
                                Text(
                                  'profile_header_subtitle'.tr, // ex: "Mettez à jour votre nom et prénom"
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.85),
                                    fontSize: 12.sp,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // ----- Surface blanche arrondie (comme les autres pages) -----
              SliverFillRemaining(
                hasScrollBody: false,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24.r),
                      topRight: Radius.circular(24.r),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.06),
                        blurRadius: 20,
                        offset: const Offset(0, -6),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: pad,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // ------- Carte formulaire -------
                        Material(
                          color: theme.colorScheme.surface,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16.r),
                            side: BorderSide(color: border),
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(12.w),
                            child: Column(
                              children: [
                                // ------- Prénom -------
                                Obx(() => TextField(
                                      focusNode: _firstFocus,
                                      controller: _firstCtrl,
                                      readOnly: _isBusy,
                                      textCapitalization: TextCapitalization.words,
                                      textInputAction: TextInputAction.next,
                                      onSubmitted: (_) => _lastFocus.requestFocus(),
                                      decoration: InputDecoration(
                                        labelText: 'first_name'.tr,
                                        hintText: 'first_name_hint'.tr,
                                        prefixIcon: const Icon(Icons.person_outline),
                                        suffixIcon: (_firstCtrl.text.isNotEmpty && !_isBusy)
                                            ? IconButton(
                                                icon: const Icon(Icons.clear),
                                                onPressed: () => _firstCtrl.clear(),
                                              )
                                            : null,
                                        filled: true,
                                        isDense: true,
                                        fillColor: fill,
                                        contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 14.h),
                                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(br)),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(br),
                                          borderSide: BorderSide(color: Theme.of(context).dividerColor),
                                        ),
                                      ),
                                    )),

                                SizedBox(height: 12.h),

                                // ------- Nom -------
                                Obx(() => TextField(
                                      focusNode: _lastFocus,
                                      controller: _lastCtrl,
                                      readOnly: _isBusy,
                                      textCapitalization: TextCapitalization.words,
                                      textInputAction: TextInputAction.done,
                                      onEditingComplete: _submit,
                                      decoration: InputDecoration(
                                        labelText: 'last_name'.tr,
                                        hintText: 'last_name_hint'.tr,
                                        prefixIcon: const Icon(Icons.badge_outlined),
                                        suffixIcon: (_lastCtrl.text.isNotEmpty && !_isBusy)
                                            ? IconButton(
                                                icon: const Icon(Icons.clear),
                                                onPressed: () => _lastCtrl.clear(),
                                              )
                                            : null,
                                        filled: true,
                                        isDense: true,
                                        fillColor: fill,
                                        contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 14.h),
                                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(br)),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(br),
                                          borderSide: BorderSide(color: Theme.of(context).dividerColor),
                                        ),
                                      ),
                                    )),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(height: 16.h),

                        // ------- Erreur globale -------
                        Obx(() => AnimatedSize(
                              duration: const Duration(milliseconds: 200),
                              child: c.showErrorMessage.value.isEmpty
                                  ? const SizedBox.shrink()
                                  : Container(
                                      padding: EdgeInsets.all(12.w),
                                      decoration: BoxDecoration(
                                        color: theme.colorScheme.errorContainer,
                                        borderRadius: BorderRadius.circular(10.r),
                                        border: Border.all(color: theme.colorScheme.error.withOpacity(0.30)),
                                      ),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Icon(Icons.error_outline, color: theme.colorScheme.onErrorContainer),
                                          SizedBox(width: 8.w),
                                          Expanded(
                                            child: Text(
                                              c.showErrorMessage.value,
                                              style: TextStyle(
                                                color: theme.colorScheme.onErrorContainer,
                                                fontSize: 13.sp,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                            )),

                        SizedBox(height: 12.h),

                        // ------- Bouton Save -------
                        Obx(() {
                          final canSave = _dirty && _valid && !_isBusy;
                          return SizedBox(
                            width: double.infinity,
                            height: 48.h,
                            child: FilledButton.icon(
                              onPressed: canSave ? _submit : null,
                              icon: _isBusy
                                  ? SizedBox(
                                      width: 18.w,
                                      height: 18.w,
                                      child: const CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  : const Icon(Icons.save_outlined),
                              label: Text(_isBusy ? 'loading'.tr : 'save'.tr),
                              style: FilledButton.styleFrom(
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
                              ),
                            ),
                          );
                        }),

                        const Spacer(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          // ====== Overlay loading fin (barre fine) ======
          Obx(() {
            if (!_isBusy) return const SizedBox.shrink();
            return IgnorePointer(
              ignoring: true,
              child: AnimatedOpacity(
                opacity: 1,
                duration: const Duration(milliseconds: 150),
                child: Container(
                  color: Colors.black.withOpacity(0.05),
                  alignment: Alignment.topCenter,
                  padding: EdgeInsets.only(top: 8.h),
                  child: const LinearProgressIndicator(minHeight: 2),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}

/// =======================
/// WIDGETS
/// =======================
class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final t = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('profile_header_title'.tr, style: t.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
        SizedBox(height: 4.h),
        Text(
          'profile_header_subtitle'.tr,
          style: t.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onSurfaceVariant),
        ),
      ],
    );
  }
}
