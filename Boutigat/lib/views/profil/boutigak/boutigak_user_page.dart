// Dart

import 'dart:ui';

// Flutter & 3rd-party
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/item.dart';

import 'package:boutigak/views/profil/boutigak/creation_store_page.dart';
import 'package:boutigak/views/profil/boutigak/edit_store_page.dart';
import 'package:boutigak/views/profil/boutigak/my_store_orders_page.dart';
import 'package:boutigak/views/profil/boutigak/mystore_page.dart';
import 'package:boutigak/views/profil/boutigak/payment_provider_page.dart';
import 'package:boutigak/views/profil/boutigak/store_social_media_link_page.dart';

import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';

import 'package:shimmer/shimmer.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
// <-- un seul import, avec alias
import 'package:badges/badges.dart' as badges; 

// App (adapte les chemins à ton projet)
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/controllers/badge_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:url_launcher/url_launcher.dart';



// =======================
//  Abonnement: statut + helpers
// =======================
enum SubStatus { loading, active, inactive }

bool _isNowWithin(DateTime? start, DateTime? end) {
  if (start == null || end == null) return false;
  final now = DateTime.now();
  final endInclusive = DateTime(end.year, end.month, end.day, 23, 59, 59, 999);
  return (now.isAfter(start) || now.isAtSameMomentAs(start)) &&
         (now.isBefore(endInclusive) || now.isAtSameMomentAs(endInclusive));
}

SubStatus _resolveSubscriptionStatusFromAuth(AuthController auth) {
  if (auth.tokenLoading.value) return SubStatus.loading;
  final u = auth.user;
  if (u == null) return SubStatus.loading;

  if (auth.hasSubscription.value == true) {
    if (u.subscriptionsStartDate != null || u.subscriptionsEndDate != null) {
      return _isNowWithin(u.subscriptionsStartDate, u.subscriptionsEndDate)
          ? SubStatus.active
          : SubStatus.inactive;
    }
    return SubStatus.active;
  }

  final bool flag = u.hasSubscription == true;
  if (flag) {
    if (u.subscriptionsStartDate != null || u.subscriptionsEndDate != null) {
      return _isNowWithin(u.subscriptionsStartDate, u.subscriptionsEndDate)
          ? SubStatus.active
          : SubStatus.inactive;
    }
    return SubStatus.active;
  }

  return SubStatus.inactive;
}

// ============================================================
//                       PAGE PRINCIPALE
// ============================================================
class BoutigakUserPage extends StatefulWidget {
  const BoutigakUserPage({super.key});

  @override
  State<BoutigakUserPage> createState() => _BoutigakUserPageState();
}

class _BoutigakUserPageState extends State<BoutigakUserPage> {
  final AuthController authController = Get.find<AuthController>();
  final StoreController storeController =
      Get.isRegistered<StoreController>() ? Get.find<StoreController>() : Get.put(StoreController());

  // Boot/loading contrôlé pour afficher le shimmer au lancement et pendant refresh
  final RxBool _pageBootLoading = true.obs;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _refreshUserAndStore(); // premier fetch -> shimmer garanti
    });
  }

  // ---------- REFRESH centralisé (appelé au boot et au retour des pages) ----------
  Future<void> _refreshUserAndStore() async {
    try {
      _pageBootLoading.value = true; // start shimmer
      await authController.getAndSaveUser();
      if (authController.user?.hasStore == true) {
        await storeController.fetchedMyStoreInformation();
      } else {
        storeController.myStore.value = null; // état propre si pas de store
      }
    } catch (e) {
      debugPrint('refresh error: $e');
    } finally {
      _pageBootLoading.value = false; // stop shimmer
    }
  }
  // -------------------------------------------------------------------------------

 
  @override
  @override
Widget build(BuildContext context) {
  final theme  = Theme.of(context);
  final isDark = theme.brightness == Brightness.dark;

  // Couleurs shimmer
  final shimmerBase = isDark ? Colors.white.withOpacity(0.10) : Colors.grey.shade300;
  final shimmerHigh = isDark ? Colors.white.withOpacity(0.25) : Colors.grey.shade100;
  final tileStroke  = theme.dividerColor.withOpacity(0.20);

  return Obx(() {
    final store      = storeController.myStore.value;
    final storeName = (() {
  final n = (store?.name ?? '').trim();
  return n.isEmpty ? 'Boutigak' : n;
})();

    final subStatus  = _resolveSubscriptionStatusFromAuth(authController);
    final subActive  = subStatus == SubStatus.active;

    // même logique de loading que l'ancien AppBar
    final bool appBarLoading =
        _pageBootLoading.value ||
        authController.tokenLoading.value ||
        authController.user == null ||
        subStatus == SubStatus.loading ||
        (authController.user?.hasStore == true && store == null);

    return Scaffold(
      extendBodyBehindAppBar: true, // clé pour l'effet verre sur fond
      body: Stack(
        children: [
          // ===== Fond dégradé "vert" (prend theme.colorScheme.primary)
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.92),
                    theme.colorScheme.primary.withOpacity(0.90),
                  ],
                ),
              ),
            ),
          ),

          SafeArea(
            top: true,
            bottom: false,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
              slivers: [
                // iOS pull-to-refresh (optionnel mais cohérent avec le reste de l’app)
                CupertinoSliverRefreshControl(
                  onRefresh: _refreshUserAndStore,
                  builder: (context, refreshState, pulledExtent, refreshTriggerPullDistance, refreshIndicatorExtent) {
                    return const Center(child: CupertinoActivityIndicator(radius: 14, color: Colors.white));
                  },
                ),

                // ===== AppBar effet verre
                SliverToBoxAdapter(
                  child: _UserGlassHeader(
                    title: storeName,
                    loading: appBarLoading,
                    shimmerBase: shimmerBase,
                    shimmerHigh: shimmerHigh,
                    onBackTap: () => Get.back(),
                    // Trailing : bouton "Edit" seulement si store + sub actif
                    trailing: (!appBarLoading && store != null && subActive)
                        ? _GlassCircleButton(
                            icon: Icons.edit,
                            onTap: () => Get.to(() => EditStorePage()),
                          )
                        : null,
                  ),
                ),

                // ===== Bottom sheet blanc (coins haut arrondis) qui contient TOUT le body
              // ⬇️ Remplace ENTIEREMENT ce SliverToBoxAdapter par ce bloc
SliverToBoxAdapter(
  child: LayoutBuilder(
    builder: (context, _) {
      final padding  = MediaQuery.of(context).padding;
      final viewport = MediaQuery.of(context).size.height - padding.top;

      return SizedBox(
        height: viewport, // 🔒 borne la hauteur du sheet
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: _buildMainContent(
              context: context,
              shimmerBase: shimmerBase,
              shimmerHigh: shimmerHigh,
              tileStroke: tileStroke,
            ),
          ),
        ),
      );
    },
  ),
),


              ],
            ),
          ),
        ],
      ),
    );
  });
}
Widget _buildMainContent({
  required BuildContext context,
  required Color shimmerBase,
  required Color shimmerHigh,
  required Color tileStroke,
}) {
  final user        = authController.user;
  final myStore     = storeController.myStore.value;
  final hasStore    = user?.hasStore == true;
  final subStatus   = _resolveSubscriptionStatusFromAuth(authController);
  final subIsActive = subStatus == SubStatus.active;

  final bool isBootLoading =
      _pageBootLoading.value ||
      authController.tokenLoading.value ||
      user == null ||
      (hasStore && myStore == null);

  if (isBootLoading || subStatus == SubStatus.loading) {
    return _PageSkeleton(base: shimmerBase, high: shimmerHigh, tileStroke: tileStroke);
  }

  if (!subIsActive) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20),
      child: _NoSubscriptionState(
        onSubscribe: () => Get.to(() => StoreSubscriptionPage()),
        onRefresh: _refreshUserAndStore,
      ),
    );
  }

  if (myStore == null) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20),
      child: _SubscribedNoStoreState(
        onCreate: () => Get.to(() => StoreInformations()),
        onRefresh: _refreshUserAndStore,
      ),
    );
  }

  // Abonnement actif + store présent
  return _BoutigakUserContent(store: myStore);
}

}

class _UserGlassHeader extends StatelessWidget {
  final String title;
  final bool loading;
  final Color shimmerBase;
  final Color shimmerHigh;
  final VoidCallback? onBackTap;
  final Widget? trailing;

  const _UserGlassHeader({
    required this.title,
    required this.loading,
    required this.shimmerBase,
    required this.shimmerHigh,
    this.onBackTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.25),
              border: Border.all(color: Colors.white.withOpacity(0.35)),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const SizedBox(width: 6),
                _GlassCircleButton(
                  icon: Icons.arrow_back_ios_new_rounded,
                  onTap: onBackTap ?? () => Get.back(),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: loading
                      ? _ShimmerBar(
                          width: 160,
                          height: 18,
                          base: shimmerBase,
                          high: shimmerHigh,
                          radius: 8,
                        )
                      : Text(
                          title,
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                            fontSize: 18,
                          ),
                        ),
                ),
                if (trailing != null) ...[
                  trailing!,
                  const SizedBox(width: 6),
                ] else
                  const SizedBox(width: 44), // équilibre visuel avec le bouton back
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _GlassCircleButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onTap;
  const _GlassCircleButton({required this.icon, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: 44,
        height: 44,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.20),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.35)),
        ),
        child: Icon(icon, color: Colors.white, size: 18),
      ),
    );
  }
}

// ============================================================
//           État “Abonné actif MAIS pas de store” (nouveau)
// ============================================================
class _NoSubscriptionState extends StatelessWidget {
  final VoidCallback onSubscribe;
  final Future<void> Function() onRefresh;
  const _NoSubscriptionState({required this.onSubscribe, required this.onRefresh});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
       
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 640),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeOut,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: border),
                  boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Hero icon avec halo
                    Container(
                      width: 72,
                      height: 72,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            theme.colorScheme.primary.withOpacity(0.12),
                            theme.colorScheme.primary.withOpacity(0.06),
                          ],
                        ),
                      ),
                      child: Icon(FontAwesomeIcons.solidBellSlash, size: 30, color: theme.colorScheme.primary),
                    ),
                    const SizedBox(height: 16),

                    Text(
                      "subscription_required".trParams({"fallback": "Abonnement requis"}),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w800,
                        color: onSurface,
                        letterSpacing: 0.2,
                      ),
                    ),
                    const SizedBox(height: 8),

                    Text(
                      "subscription_desc".trParams({"fallback": "Active ton abonnement pour déverrouiller les commandes, les paiements et plus encore."}),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.4,
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),

                    const SizedBox(height: 18),

                    // ---- Avantages Boutigak
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "benefits_title".trParams({"fallback": "Pourquoi choisir Boutigak ?"}),
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w800, color: onSurface),
                      ),
                    ),
                    const SizedBox(height: 10),

                    const _BenefitRow(
                      icon: Icons.storefront,
                      titleKey: "benefit_online_store",
                      titleFallback: "Boutique en ligne prête en quelques minutes",
                      descKey: "benefit_online_store_desc",
                      descFallback: "Crée et personnalise ta boutique sans compétences techniques.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.link,
                      titleKey: "benefit_share_link",
                      titleFallback: "Lien public qui liste tous tes articles",
                      descKey: "benefit_share_link_desc",
                      descFallback: "Partage un lien unique pour présenter tout ton catalogue.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.shopping_cart_checkout,
                      titleKey: "benefit_online_orders",
                      titleFallback: "Commandes en ligne",
                      descKey: "benefit_online_orders_desc",
                      descFallback: "Reçois et gère les commandes en temps réel depuis ton tableau de bord.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.payments,
                      titleKey: "benefit_online_payments",
                      titleFallback: "Paiements en ligne sécurisés",
                      descKey: "benefit_online_payments_desc",
                      descFallback: "Accepte les paiements (carte / mobile money) en toute sécurité.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.analytics_outlined,
                      titleKey: "benefit_analytics",
                      titleFallback: "Statistiques & suivi des ventes",
                      descKey: "benefit_analytics_desc",
                      descFallback: "Visualise les performances : vues, paniers, ventes et tendances.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.share_outlined,
                      titleKey: "benefit_social",
                      titleFallback: "Partage social en 1 clic",
                      descKey: "benefit_social_desc",
                      descFallback: "Diffuse tes produits sur WhatsApp, Instagram et Facebook.",
                    ),

                    const SizedBox(height: 18),
                    Container(height: 1, width: 64, color: border),
                    const SizedBox(height: 18),

                    Wrap(
                      spacing: 12,
                      runSpacing: 10,
                      alignment: WrapAlignment.center,
                      children: [
                        OutlinedButton.icon(
                          onPressed: onRefresh,
                          icon: const Icon(Icons.refresh, size: 18),
                          label: Text("refresh".trParams({"fallback": "Rafraîchir"})),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                            side: BorderSide(color: border),
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: onSubscribe,
                      
                          label: Text("subscribe_now".trParams({"fallback": "S’abonner maintenant"})),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                            elevation: 0,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}


/// Une ligne “avantage” avec icône, titre et description.
class _BenefitRow extends StatelessWidget {
  final IconData icon;
  final String titleKey;
  final String titleFallback;
  final String descKey;
  final String descFallback;

  const _BenefitRow({
    required this.icon,
    required this.titleKey,
    required this.titleFallback,
    required this.descKey,
    required this.descFallback,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final border = theme.dividerColor.withOpacity(0.2);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: border),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icône dans un petit carré doux
          Container(
            width: 36,
            height: 36,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: theme.colorScheme.primary.withOpacity(0.10),
            ),
            child: Icon(icon, size: 20, color: theme.colorScheme.primary),
          ),
          const SizedBox(width: 12),

          // Titre + description
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  titleKey.trParams({"fallback": titleFallback}),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: theme.colorScheme.onSurface.withOpacity(0.95),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  descKey.trParams({"fallback": descFallback}),
                  style: TextStyle(
                    fontSize: 13,
                    height: 1.35,
                    color: theme.colorScheme.onSurface.withOpacity(0.72),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}


class _SubscribedNoStoreState extends StatelessWidget {
  final VoidCallback onCreate;
  final Future<void> Function() onRefresh;

  const _SubscribedNoStoreState({
    required this.onCreate,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Hero icon avec halo
              Container(
                width: 72,
                height: 72,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.orange.withOpacity(0.14),
                      Colors.orange.withOpacity(0.07),
                    ],
                  ),
                ),
                child: Icon(FontAwesomeIcons.storeSlash, size: 30, color: Colors.orange),
              ),
              const SizedBox(height: 16),

              Text(
                "sub_active_no_store_title".trParams({"fallback": "Tu as un abonnement actif, mais pas encore de store"}),
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w800,
                  color: onSurface,
                  letterSpacing: 0.2,
                ),
              ),
              const SizedBox(height: 8),

              Text(
                "sub_active_no_store_desc".trParams({
                  "fallback": "Crée ton store pour commencer à ajouter des produits et recevoir des commandes."
                }),
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  height: 1.4,
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),

              const SizedBox(height: 18),
              Container(height: 1, width: 64, color: border),
              const SizedBox(height: 18),

              // Boutons d’action
              Wrap(
                spacing: 12,
                runSpacing: 10,
                alignment: WrapAlignment.center,
                children: [
                  OutlinedButton.icon(
                    onPressed: onRefresh,
                    icon: const Icon(Icons.refresh, size: 18),
                    label: Text("refresh".trParams({"fallback": "Rafraîchir"})),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                      side: BorderSide(color: border),
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: onCreate,
                   
                    label: Text("create_store".trParams({"fallback": "Créer mon store"})),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                      elevation: 0,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}



/* ===========================
   Shimmer primitives utilisées dans l'app bar
   (tu as déjà les tiennes pour la page, on garde juste ces deux)
=========================== */

class _ShimmerBar extends StatelessWidget {
  final double width;
  final double height;
  final double radius;
  final Color base;
  final Color high;
  const _ShimmerBar({
    required this.width,
    required this.height,
    required this.base,
    required this.high,
    this.radius = 6,
  });

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (rect) => LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [base, high, base],
        stops: const [0.1, 0.3, 0.6],
      ).createShader(rect),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: base,
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
    );
  }
}

class _ShimmerPill extends StatelessWidget {
  final double width;
  final double height;
  final Color base;
  final Color high;
  const _ShimmerPill({
    required this.width,
    this.height = 28,
    required this.base,
    required this.high,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8, left: 8),
      child: ShaderMask(
        shaderCallback: (rect) => LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [base, high, base],
          stops: const [0.1, 0.3, 0.6],
        ).createShader(rect),
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: base,
            borderRadius: BorderRadius.circular(999),
          ),
        ),
      ),
    );
  }
}

/* ============================================================
 *                        CONTENU "OK"
 * ============================================================ */
class _BoutigakUserContent extends StatelessWidget {
  final dynamic store;
  const _BoutigakUserContent({required this.store});

  @override
  Widget build(BuildContext context) {
   
    final followersCount = store?.followersCount ?? 0;
    final opening = store?.openingTime;
    final closing = store?.closingTime;
    final images = store?.images ?? const [];
    final firstImage = images.isNotEmpty ? (images.first ?? "") : "";

    final hasBadgeCtrl = Get.isRegistered<BadgeController>();
    final badgeController = hasBadgeCtrl ? Get.find<BadgeController>() : null;

    return Column(
      children: [
        // ====== Header réel ======
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: SizedBox(
                  width: 120,
                  height: 120,
                  child: firstImage.isEmpty
                      ? _imagePlaceholder(context, width: 120, height: 120)
                      : CachedImageWidget(
                          imageUrl: firstImage,
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          borderRadius: BorderRadius.circular(12),
                        ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {},
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        backgroundColor: Colors.black,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                      child: Text(
                        "$followersCount " + "followers".tr,
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                      ),
                    ),
                     SizedBox(height: 16.h),
                    _OutlineInfo(
                      icon: Icons.access_time,
                      label: (opening != null && closing != null && opening.isNotEmpty && closing.isNotEmpty)
                          ? "$opening - $closing"
                          : "—",
                    ),
                 
                    // Obx(() {
                    //   final isOpen = storeController.isStoreOpen.value;
                    //   final color = isOpen ? Colors.orange : Colors.green;
                    //   return _OutlineAction(
                    //     icon: isOpen ? Icons.store : Icons.store_mall_directory_outlined,
                    //     label: isOpen ? "close_store".tr : "open_store".tr,
                    //     color: color,
                    //     onTap: () => storeController.toggleStoreStatus(!isOpen),
                    //   );
                    // }),
                  ],
                ),
              ),
            ],
          ),
        ),

        // ====== Menu réel ======
      // ...
ListView(
  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
  shrinkWrap: true,
  physics: const NeverScrollableScrollPhysics(), // ❗ le scroll est géré par le CustomScrollView parent
  children: [
    _ModernTile(
      title: 'list_of_products'.tr,
      icon: FontAwesomeIcons.list,
      onTap: () => Get.to(() => StoreProductPage()),
    ),
    _OrdersTile(
      title: 'store_orders'.tr,
      icon: FontAwesomeIcons.cartShopping,
      badgeController: badgeController,
      onTap: () {
        Get.to(() => MyStoreOrdersPage());
        badgeController?.resetBadge('store-order');
      },
    ),
    _ModernTile(
      title: 'payment_method'.tr,
      icon: FontAwesomeIcons.creditCard,
      onTap: () => Get.to(() => PaymentProvidersPage()),
    ),
    _ModernTile(
      title: 'store_location'.tr,
      icon: FontAwesomeIcons.locationDot,
      onTap: () => Get.to(() => const StoreLocationManagementPage()),
    ),
    _ModernTile(
      title: 'subscription'.tr,
      icon: FontAwesomeIcons.solidBell,
      onTap: () => Get.to(() => StoreSubscriptionPage()),
    ),
    _ModernTile(
      title: 'social_media'.tr,
      icon: FontAwesomeIcons.at,
      onTap: () => Get.to(() => SocialMediaLinksPage(storeId: store?.id ?? 0)),
    ),
  ],
),

      ],
    );
  }

  Widget _imagePlaceholder(BuildContext context, {double width = 120, double height = 120}) {
    final theme = Theme.of(context);
    return Container(
      width: width,
      height: height,
      color: theme.colorScheme.surfaceVariant.withOpacity(0.25),
      alignment: Alignment.center,
      child: Icon(Icons.image_not_supported, color: theme.disabledColor),
    );
  }
}

  // ========= Helpers visuels =========



/* ============================================================
 *                   SKELETON / SHIMMER WIDGETS
 *   - Couleurs passées depuis la page (base/high) pour cohérence
 *   - Formes/tailles identiques aux widgets réels
 * ============================================================ */

class _PageSkeleton extends StatelessWidget {
  final Color base;
  final Color high;
  final Color tileStroke;
  const _PageSkeleton({required this.base, required this.high, required this.tileStroke});

  @override
  Widget build(BuildContext context) {
    // Reproduit la page: header + liste de 6 tuiles
    return Column(
      children: [
        // ----- Header skeleton -----
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _ShimmerBox(width: 120, height: 120, radius: 12, base: base, high: high),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _ShimmerPill(width: 110, height: 32, base: base, high: high),
                    const SizedBox(height: 10),
                    _ShimmerOutlined(width: 120, height: 36, base: base, high: high),
                  ],
                ),
              ),
            ],
          ),
        ),
        // ----- Menu skeleton -----
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            itemCount: 6,
            itemBuilder: (_, __) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 6),
                child: _CardSkeleton(
                  base: base,
                  high: high,
                  stroke: tileStroke,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class _CardSkeleton extends StatelessWidget {
  final Color base;
  final Color high;
  final Color stroke;
  const _CardSkeleton({required this.base, required this.high, required this.stroke});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Theme.of(context).colorScheme.surface,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
        side: BorderSide(color: stroke),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 14.h, horizontal: 14.w),
        child: Row(
          children: [
            _ShimmerBox(width: 40.w, height: 40.w, radius: 12.r, base: base, high: high),
            SizedBox(width: 12.w),
            Expanded(child: _ShimmerBar(width: double.infinity, height: 16, base: base, high: high, radius: 8)),
            SizedBox(width: 12.w),
            _ShimmerCircle(size: 22, base: base, high: high),
          ],
        ),
      ),
    );
  }
}

// --- Primitives (boîte, barre, pill, outlined) ---

class _ShimmerBox extends StatelessWidget {
  final double? width;
  final double? height;
  final double radius;
  final Color base;
  final Color high;
  const _ShimmerBox({this.width, this.height, this.radius = 12, required this.base, required this.high});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1150),
      direction: ShimmerDirection.ltr,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: base, // important: pas de blanc dur
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
    );
  }
}


class _ShimmerCircle extends StatelessWidget {
  final double size;
  final Color base;
  final Color high;
  const _ShimmerCircle({required this.size, required this.base, required this.high});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1150),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(color: base, shape: BoxShape.circle),
      ),
    );
  }
}

class _ShimmerOutlined extends StatelessWidget {
  final double width;
  final double height;
  final Color base;
  final Color high;
  const _ShimmerOutlined({required this.width, required this.height, required this.base, required this.high});

  @override
  Widget build(BuildContext context) {
    final border = Theme.of(context).dividerColor.withOpacity(0.4);
    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1150),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: base,
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(color: border),
        ),
      ),
    );
  }
}

// ===================== TILES =====================

class _ModernTile extends StatelessWidget {
  final String title;
  final IconData icon;
  final int badgeCount;
  final VoidCallback onTap;

  const _ModernTile({
    required this.title,
    required this.icon,
    required this.onTap,
    this.badgeCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: _CardContainer(
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 14.h, horizontal: 14.w),
            child: Row(
              children: [
                Container(
                  width: 40.w,
                  height: 40.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color: theme.colorScheme.primary.withOpacity(0.1),
                  ),
                  child: FaIcon(icon, size: 18.sp, color: theme.colorScheme.primary),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (badgeCount > 0)
                  badges.Badge(
                    badgeAnimation: const badges.BadgeAnimation.scale(),
                    position: badges.BadgePosition.topEnd(top: -4, end: -2),
                    badgeStyle: badges.BadgeStyle(
                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
                      badgeColor: Colors.red.shade100,
                    ),
                    badgeContent: Text(
                      badgeCount > 9 ? '9+' : '$badgeCount',
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    child: const SizedBox(width: 20, height: 20),
                  ),
                SizedBox(width: 8.w),
                Icon(Icons.chevron_right, size: 22.sp, color: theme.disabledColor),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// tuile commandes (sécurise l’accès au BadgeController)
class _OrdersTile extends StatelessWidget {
  final String title;
  final IconData icon;
  final BadgeController? badgeController;
  final VoidCallback onTap;

  const _OrdersTile({
    required this.title,
    required this.icon,
    required this.onTap,
    this.badgeController,
  });

  @override
  Widget build(BuildContext context) {
    final count = badgeController?.getModuleCount('store-order') ?? 0;
    return _ModernTile(title: title, icon: icon, onTap: onTap, badgeCount: count);
  }
}

// ===================== Reusable UI bits =====================

class _CardContainer extends StatelessWidget {
  final Widget child;
  const _CardContainer({required this.child});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Material(
      color: theme.colorScheme.surface,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
        side: BorderSide(color: theme.dividerColor.withOpacity(0.2)),
      ),
      child: child,
    );
  }
}

class _OutlineInfo extends StatelessWidget {
  final IconData icon;
  final String label;
  const _OutlineInfo({required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(color: theme.dividerColor.withOpacity(0.4)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12.sp, color: onSurface),
          SizedBox(width: 8.w),
          Text(
            label,
            style: TextStyle(color: onSurface, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }
}

class _OutlineAction extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;
  const _OutlineAction({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton.icon(
      onPressed: onTap,
      
      label: Text(label, style: TextStyle(color: color, fontWeight: FontWeight.w600)),
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        backgroundColor: Theme.of(context).colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
          side: BorderSide(color: color.withOpacity(0.5)),
        ),
      ),
    );
  }
}



















































































class StoreSubscriptionPage extends StatelessWidget {
  final AuthController authController = Get.find<AuthController>();

  StoreSubscriptionPage({super.key});

  // ====== helper commun pour WhatsApp ======
  Future<void> _openWhatsApp() async {
    const phone = "22238407840"; // ← remplace par ton numéro (sans +)
    final url = Uri.parse("https://wa.me/$phone");
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      Get.snackbar(
        'error'.tr,
        'WhatsApp not installed or URL invalid',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('subscription_title'.tr, ),
       
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Obx(() {
            final user = authController.user;
            final hasSubscription = authController.hasSubscription.value;

            // ——— Loading / état initial : skeleton
            if (user == null && hasSubscription == false) {
              return const _SubscriptionSkeleton();
            }

            // ——— Pas d’abonnement (UI carte améliorée + redirection WhatsApp)
            if (user == null || !hasSubscription) {
              return _EmptySubscription(
                onAction: _openWhatsApp,
              );
            }

            // ——— Abonné mais dates manquantes
            final startDate = user.subscriptionsStartDate;
            final endDate = user.subscriptionsEndDate;
            if (startDate == null || endDate == null) {
              return _DataMissing(onAction: () async {
                await authController.getAndSaveUser();
              });
            }

            // ——— Détails d’abonnement
            return _SubscriptionDetailsCard(
              startDate: startDate,
              endDate: endDate,
              onRenew: _openWhatsApp, // même action WhatsApp
            );
          }),
        ),
      ),
    );
  }
}

// ================== UI components ==================

class _SubscriptionDetailsCard extends StatelessWidget {
  final DateTime startDate;
  final DateTime endDate;
  final VoidCallback onRenew;

  const _SubscriptionDetailsCard({
    required this.startDate,
    required this.endDate,
    required this.onRenew,
  });

  String _fmtDate(BuildContext context, DateTime d) {
    final ml = MaterialLocalizations.of(context);
    return ml.formatFullDate(d);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final now = DateTime.now();

    final totalDaysRaw = endDate.difference(startDate).inDays;
    final totalDays = totalDaysRaw <= 0 ? 1 : totalDaysRaw;
    final leftDaysRaw = endDate.difference(now).inDays;
    final remainingDays = leftDaysRaw > 0 ? leftDaysRaw : 0;
    final usedDays = (now.isBefore(startDate)) ? 0 : now.difference(startDate).inDays.clamp(0, totalDays);
    final consumed = (usedDays / totalDays).clamp(0.0, 1.0);

    final isActive = now.isBefore(endDate);
    final isExpiringSoon = remainingDays < 60;

    final startText = _fmtDate(context, startDate);
    final endText   = _fmtDate(context, endDate);

    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 640),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hero + titre + badge
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary.withOpacity(0.16),
                          AppColors.primary.withOpacity(0.08),
                        ],
                      ),
                    ),
                    child: Icon(Icons.subscriptions, color: AppColors.primary),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'subscription_overview'.tr,
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.w800, color: onSurface),
                    ),
                  ),
                  _StatusChip(
                    label: isActive ? 'status_active'.tr : 'status_expired'.tr,
                    color: isActive ? Colors.green : Colors.red,
                    icon: isActive ? Icons.verified_user : Icons.error_outline,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Dates
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _DateColumn(title: 'start_date'.tr, date: startText),
                  _DateColumn(title: 'end_date'.tr,   date: endText),
                ],
              ),
              const SizedBox(height: 16),

              // Barre de progression (part consommée)
              _ProgressBar(
                progress: consumed,
                leadingLabel: 'consumed'.trParams({'days': usedDays.toString()}),
                trailingLabel: 'total_days'.trParams({'days': totalDays.toString()}),
              ),
              const SizedBox(height: 12),

              Align(
                alignment: Alignment.centerRight,
                child: Text(
                  'time_remaining'.trParams({'days': remainingDays.toString()}),
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(height: 16),

              if (isActive && isExpiringSoon)
                _AlertCard(
                  text: 'renew_soon_notice'.tr,
                  color: Colors.orange,
                  icon: Icons.notifications_active_outlined,
                ),
              if (!isActive)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: _AlertCard(
                    text: 'expired_notice'.tr,
                    color: Colors.red,
                    icon: Icons.report_gmailerrorred_outlined,
                  ),
                ),

              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: onRenew,
                      icon: const Icon(Icons.autorenew),
                      label: Text(isActive ? 'renew_now'.tr : 'renew_subscription'.tr),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.surface,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        elevation: 0,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text('subscription_tips_title'.tr, style: const TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 6),
              _TipLine(text: 'subscription_tip_visibility'.tr),
              _TipLine(text: 'subscription_tip_reminder'.tr),
            ],
          ),
        ),
      ),
    );
  }
}

class _StatusChip extends StatelessWidget {
  final String label;
  final Color color;
  final IconData icon;
  const _StatusChip({required this.label, required this.color, required this.icon});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.12),
        borderRadius: BorderRadius.circular(999),
        border: Border.all(color: color.withOpacity(0.4)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 6),
          Text(label, style: TextStyle(color: color, fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
}

class _DateColumn extends StatelessWidget {
  final String title;
  final String date;
  const _DateColumn({required this.title, required this.date});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
        const SizedBox(height: 4),
        Text(date, style: TextStyle(fontSize: 12, color: theme.hintColor)),
      ],
    );
  }
}

class _ProgressBar extends StatelessWidget {
  final double progress; // 0..1 consommé
  final String leadingLabel;
  final String trailingLabel;
  const _ProgressBar({required this.progress, required this.leadingLabel, required this.trailingLabel});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final consumed = progress.clamp(0.0, 1.0);
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Stack(
            children: [
              Container(height: 12, color: theme.dividerColor.withOpacity(0.25)),
              FractionallySizedBox(
                widthFactor: consumed,
                child: Container(
                  height: 12,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.primary, AppColors.primary.withOpacity(0.7)],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 6),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(leadingLabel, style: TextStyle(color: theme.hintColor, fontSize: 12)),
            Text(trailingLabel, style: TextStyle(color: theme.hintColor, fontSize: 12)),
          ],
        ),
      ],
    );
  }
}

class _AlertCard extends StatelessWidget {
  final String text;
  final MaterialColor color;
  final IconData icon;

  const _AlertCard({
    required this.text,
    required this.color,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.12),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.4)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: TextStyle(color: color.shade700, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}

class _TipLine extends StatelessWidget {
  final String text;
  const _TipLine({required this.text});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Icon(Icons.check_circle_outline, size: 16, color: theme.hintColor),
        const SizedBox(width: 6),
        Expanded(child: Text(text)),
      ],
    );
  }
}

// ====== Empty Subscription (UI carte + WhatsApp) ======
class _EmptySubscription extends StatelessWidget {
  final VoidCallback onAction; // WhatsApp
  const _EmptySubscription({required this.onAction});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Hero icon avec halo
              Container(
                width: 72,
                height: 72,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary.withOpacity(0.12),
                      AppColors.primary.withOpacity(0.06),
                    ],
                  ),
                ),
                child: Icon(Icons.subscriptions_outlined, size: 30, color: AppColors.primary),
              ),
              const SizedBox(height: 16),

              Text(
                'no_subscription_title'.tr,
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.w800, color: onSurface),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'no_subscription_subtitle'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14, height: 1.4, color: theme.colorScheme.onSurface.withOpacity(0.7)),
              ),
              const SizedBox(height: 18),
              Container(height: 1, width: 64, color: border),
              const SizedBox(height: 18),

              ElevatedButton.icon(
                onPressed: onAction,
                icon:  Icon(FontAwesomeIcons.whatsapp,),
                label: Text('get_subscription'.tr),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.surface,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  elevation: 0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ====== Data Missing (UI carte) ======
class _DataMissing extends StatelessWidget {
  final VoidCallback onAction;
  const _DataMissing({required this.onAction});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 72,
                height: 72,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      Colors.orange.withOpacity(0.14),
                      Colors.orange.withOpacity(0.07),
                    ],
                  ),
                ),
                child: const Icon(Icons.info_outline, size: 30, color: Colors.orange),
              ),
              const SizedBox(height: 16),
              Text('missing_data_title'.tr,
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.w800, color: onSurface),
                  textAlign: TextAlign.center),
              const SizedBox(height: 8),
              Text('missing_data_subtitle'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, height: 1.4, color: theme.colorScheme.onSurface.withOpacity(0.7))),
              const SizedBox(height: 18),
              Container(height: 1, width: 64, color: border),
              const SizedBox(height: 18),
              OutlinedButton.icon(
                onPressed: onAction,
                icon: const Icon(Icons.refresh),
                label: Text('retry'.tr),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  side: BorderSide(color: border),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ====== Skeleton (style carte + shimmer) ======
class _SubscriptionSkeleton extends StatelessWidget {
  const _SubscriptionSkeleton();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final base = isDark ? Colors.white.withOpacity(0.10) : Colors.grey.shade300;
    final high = isDark ? Colors.white.withOpacity(0.25) : Colors.grey.shade100;
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    Widget bar(double w, double h, {double r = 10}) => Shimmer.fromColors(
          baseColor: base,
          highlightColor: high,
          period: const Duration(milliseconds: 1100),
          child: Container(width: w, height: h, decoration: BoxDecoration(color: base, borderRadius: BorderRadius.circular(r))),
        );

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 640),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // header (icon + title + chip)
              Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(shape: BoxShape.circle, color: base.withOpacity(0.6)),
                  ),
                  const SizedBox(width: 12),
                  Expanded(child: bar(180, 18, r: 6)),
                  const SizedBox(width: 10),
                  bar(110, 28, r: 999),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [bar(120, 12, r: 6), bar(120, 12, r: 6)],
              ),
              const SizedBox(height: 16),
              bar(double.infinity, 12),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [bar(120, 10, r: 6), bar(100, 10, r: 6)],
              ),
              const SizedBox(height: 16),
              bar(double.infinity, 44, r: 12),
            ],
          ),
        ),
      ),
    );
  }
}


// ==========================
// ==========================
// Store Location Management — version harmonisée
// ==========================
class StoreLocationManagementPage extends StatefulWidget {
  const StoreLocationManagementPage({super.key});

  @override
  State<StoreLocationManagementPage> createState() =>
      _StoreLocationManagementPageState();
}

class _StoreLocationManagementPageState extends State<StoreLocationManagementPage> {
  final StoreController storeController = Get.find<StoreController>();

  bool _initialLoading = true;

  @override
  void initState() {
    super.initState();
    // Chargement initial après le premier frame (comme AddressManagementPage)
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() => _initialLoading = true);
      await storeController.fetchMyStoreLocations();
      if (mounted) setState(() => _initialLoading = false);
    });
  }
Future<bool> _getCurrentLocation() async {
  try {
    // 1) Services activés ?
    final serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      Get.snackbar(
        'location_services_off'.tr,
        'enable_gps_desc'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }

    // 2) Permissions
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      Get.snackbar(
        'location_permission'.tr,
        'permission_denied_desc'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }

    // 3) Position courante
    final position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );

    // 4) On stocke dans le controller
    storeController.updateStorePosition(
      gmaps.LatLng(position.latitude, position.longitude),
    );
    return true;
  } catch (e) {
    debugPrint('Error getting location: $e');
    Get.snackbar('error'.tr, 'location_error'.tr,
        snackPosition: SnackPosition.BOTTOM);
    return false;
  }
}


  Future<void> _openAddOrEdit({required bool isEditing}) async {
    await _getCurrentLocation();
    final saved = await Get.to(
      () => AddStoreLocationPage(isEditing: isEditing),
      fullscreenDialog: true,
    );
    if (saved == true) {
      await storeController.fetchMyStoreLocations();
    }
  }

  Future<void> _onRefresh() async {
    await storeController.fetchMyStoreLocations();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final c1 = theme.colorScheme.primary;
    final c2 = theme.colorScheme.primary.withOpacity(0.95);

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      appBar: AppBar(
        title: Text('store_location'.tr, style: const TextStyle(fontWeight: FontWeight.w700)),
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // ===== Fond dégradé (cohérent avec AddressManagementPage) =====
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [c1, c2],
                ),
              ),
            ),
          ),

          // ===== Contenu scrollable =====
          Obx(() {
            // force Rx reads
            final hasLocation = storeController.myStoreLocations.isNotEmpty;
            final isLoading = storeController.isLoadingStoreAdresses.value;

            return CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                CupertinoSliverRefreshControl(onRefresh: _onRefresh),

                // ----- Header translucide (même style) -----
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                    child: Material(
                      color: Colors.white.withOpacity(0.10),
                      elevation: 0,
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.white.withOpacity(0.25)),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Halo + icône
                            Container(
                              width: 56,
                              height: 56,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withOpacity(0.28),
                                    Colors.white.withOpacity(0.12),
                                  ],
                                ),
                                border: Border.all(color: Colors.white.withOpacity(0.35)),
                              ),
                              child: const Icon(Icons.store_mall_directory_outlined,
                                  color: Colors.white, size: 26),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'store_location_header_title'.tr, // ex: "Localisation de votre boutique"
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w800,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'store_location_header_subtitle'.tr, // ex: "Positionnez votre boutique sur la carte"
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.85),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // ----- Surface blanche + contenu -----
                SliverToBoxAdapter(
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 12, 16, 120),
                      child: (_initialLoading || isLoading)
                          ? const _ShimmerAddressListBody(itemCount: 1)
                          : (!hasLocation
                              ? _EmptyStoreLocation(
                                  onAdd: () => _openAddOrEdit(isEditing: false),
                                )
                              : ListView.separated(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemCount: 1, // une seule localisation boutique
                                  separatorBuilder: (_, __) => const SizedBox(height: 10),
                                  itemBuilder: (context, index) {
                                    final loc = storeController.myStoreLocations.first;
                                    return _StoreAddressCard(
                                      name: (loc.name?.isNotEmpty ?? false)
                                          ? loc.name!
                                          : 'store_name'.tr,
                                      address: (loc.address?.isNotEmpty ?? false)
                                          ? loc.address!
                                          : 'address'.tr,
                                      onEdit: () => _openAddOrEdit(isEditing: true),
                                    );
                                  },
                                )),
                    ),
                  ),
                ),

                // Remplissage pour éviter de voir le dégradé sous la surface
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Container(color: theme.colorScheme.surface),
                ),
              ],
            );
          }),
        ],
      ),

      // ===== FAB centré (ajouter / modifier localisation) =====
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Obx(() {
        final hasLocation = storeController.myStoreLocations.isNotEmpty;
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 52),
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(14),
                    ),
                  ),
                  onPressed: () => _openAddOrEdit(isEditing: hasLocation),
                  icon: Icon(hasLocation
                      ? Icons.edit_location_alt_outlined
                      : Icons.add_location_alt_outlined),
                  label: Text(hasLocation
                      ? 'edit_store_location'.tr
                      : 'add_store_location'.tr),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
class _ShimmerAddressListBody extends StatelessWidget {
  const _ShimmerAddressListBody({this.itemCount = 6});

  final int itemCount;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        itemCount,
        (_) => const _ShimmerAddressCard(),
      ),
    );
  }
}
class _ShimmerAddressCard extends StatelessWidget {
  const _ShimmerAddressCard();
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.black12),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerBox(width: 44, height: 44, radius: 12),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: const [
                  ShimmerBox(width: double.infinity, height: 14, radius: 6),
                  SizedBox(height: 8),
                  ShimmerBox(width: 180, height: 12, radius: 6),
                ],
              ),
            ),
            const SizedBox(width: 8),
            ShimmerBox(width: 28, height: 28, radius: 8),
          ],
        ),
      ),
    );
  }
}

class ShimmerBox extends StatelessWidget {
  final double width;
  final double height;
  final double radius;
  const ShimmerBox({
    required this.width,
    required this.height,
    this.radius = 8,
  });
  @override
  Widget build(BuildContext context) {
    return _Shimmer(
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
    );
  }
}

class _Shimmer extends StatefulWidget {
  final Widget child;
  const _Shimmer({required this.child});
  @override
  State<_Shimmer> createState() => _ShimmerState();
}

class _ShimmerState extends State<_Shimmer>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1400),
    )..repeat();
  }
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final double slide = _controller.value * 2 - 1; // -1 → 1
        return ShaderMask(
          shaderCallback: (rect) {
            return LinearGradient(
              begin: Alignment(-1 - slide, 0),
              end: Alignment(1 + slide, 0),
              colors: [
                Colors.grey.shade300,
                Colors.grey.shade100,
                Colors.grey.shade300,
              ],
              stops: const [0.1, 0.3, 0.4],
            ).createShader(rect);
          },
          blendMode: BlendMode.srcATop,
          child: child,
        );
      },
      child: widget.child,
    );
  }
}

// ====== Carte (identique style AddressCard) ======
class _StoreAddressCard extends StatelessWidget {
  final String name;
  final String address;
  final VoidCallback onEdit;

  const _StoreAddressCard({
    required this.name,
    required this.address,
    required this.onEdit,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Material(
        color: AppColors.surface,
        elevation: 0,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onEdit,
          child: Container(
            padding: const EdgeInsets.all(14),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.black12),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.blueGrey.withOpacity(.08),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.location_on_outlined),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(name,
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.w700)),
                      const SizedBox(height: 4),
                      Text(
                        address,
                        style: TextStyle(color: Colors.black.withOpacity(.7)),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  visualDensity: VisualDensity.compact,
                  onPressed: onEdit,
                  icon: const Icon(Icons.more_horiz),
                  tooltip: 'edit'.tr,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// ====== Empty state boutique (style cohérent) ======
class _EmptyStoreLocation extends StatelessWidget {
  final VoidCallback onAdd;
  const _EmptyStoreLocation({required this.onAdd});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: 88,
            width: 88,
            decoration: BoxDecoration(
              color: Colors.blueGrey.withOpacity(.08),
              borderRadius: BorderRadius.circular(24),
            ),
            child: const Icon(Icons.storefront_outlined, size: 36),
          ),
          const SizedBox(height: 16),
          Text(
            'no_store_location_yet'.tr, // ex: "Aucune localisation de boutique"
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
          ),
          const SizedBox(height: 8),
          Text(
            'add_your_store_location'.tr, // ex: "Ajoutez la position de votre boutique pour être visible"
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.black.withOpacity(.7)),
          ),
          const SizedBox(height: 24),
          OutlinedButton.icon(
            onPressed: onAdd,
            icon: const Icon(Icons.add_location_alt_outlined),
            label: Text('add_store_location'.tr),
            style: OutlinedButton.styleFrom(
              foregroundColor: theme.colorScheme.primary,
              side: BorderSide(color: theme.colorScheme.primary.withOpacity(.35)),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          ),
        ],
      ),
    );
  }
}





class AddStoreLocationPage extends StatefulWidget {
  final bool isEditing;

  /// Optionnel : passer une position initiale depuis l'écran parent
  final gmaps.LatLng? initialLatLng;

  const AddStoreLocationPage({
    required this.isEditing,
    this.initialLatLng,
    super.key,
  });

  @override
  State<AddStoreLocationPage> createState() => _AddStoreLocationPageState();
}

class _AddStoreLocationPageState extends State<AddStoreLocationPage> {
  final _formKey = GlobalKey<FormState>();
  final storeController = Get.find<StoreController>();

  late final TextEditingController nameController;
  late final TextEditingController addressController;

  gmaps.GoogleMapController? _mapController;

  /// Position courante choisie par l'utilisateur (centre de la carte)
  late gmaps.LatLng _selectedLatLng;

  /// Fallback : centre de Nouakchott
  static const gmaps.LatLng _fallbackNouakchott = gmaps.LatLng(18.0735, -15.9582);

  gmaps.LatLng _resolveInitialLatLng() {
    // 1) priorité à la position passée par le parent
    if (widget.initialLatLng != null) return widget.initialLatLng!;

    // 2) si on édite et qu'on a déjà une localisation en base
    if (storeController.myStoreLocations.isNotEmpty) {
      final loc = storeController.myStoreLocations.first;
      final lat = loc.latitude;
      final lng = loc.longitude;
      if (lat != null && lng != null) {
        return gmaps.LatLng(lat, lng);
      }
    }

    // 3) fallback Nouakchott
    return _fallbackNouakchott;
  }

  @override
  void initState() {
    super.initState();

    final existing = storeController.myStoreLocations.isNotEmpty
        ? storeController.myStoreLocations.first
        : null;

    nameController = TextEditingController(text: existing?.name ?? '');
    addressController = TextEditingController(text: existing?.address ?? '');

    _selectedLatLng = _resolveInitialLatLng();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    nameController.dispose();
    addressController.dispose();
    super.dispose();
  }

  Future<void> _recenterToDevice() async {
    try {
      final enabled = await Geolocator.isLocationServiceEnabled();
      if (!enabled) {
        Get.snackbar('location_services_off'.tr, 'enable_gps_desc'.tr,
            snackPosition: SnackPosition.BOTTOM);
        return;
      }
      var perm = await Geolocator.checkPermission();
      if (perm == LocationPermission.denied) {
        perm = await Geolocator.requestPermission();
      }
      if (perm == LocationPermission.denied ||
          perm == LocationPermission.deniedForever) {
        Get.snackbar('location_permission'.tr, 'permission_denied_desc'.tr,
            snackPosition: SnackPosition.BOTTOM);
        return;
      }

      final p = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      final target = gmaps.LatLng(p.latitude, p.longitude);
      setState(() => _selectedLatLng = target);
      await _mapController?.animateCamera(gmaps.CameraUpdate.newLatLng(target));
    } catch (e) {
      Get.snackbar('error'.tr, 'location_error'.tr,
          snackPosition: SnackPosition.BOTTOM);
    }
  }

  bool _saving = false;

 Future<void> _save() async {
  final formOk = _formKey.currentState?.validate() ?? false;
  if (!formOk) return;

  final name = nameController.text.trim();
  final address = addressController.text.trim();

  setState(() => _saving = true);
  try {
    // Si ta méthode est Future<void>, garde await. Si elle est void, enlève-le.
    await storeController.saveMyStoreLocation(
      name: name,
      address: address,
      latitude: _selectedLatLng.latitude,
      longitude: _selectedLatLng.longitude,
    );

    // 🔁 Rafraîchir la liste AVANT de sortir
    await storeController.fetchMyStoreLocations();

    if (!mounted) return;
    Get.back(result: true); // on retourne true pour éventuels usages côté parent
  } catch (e) {
    Get.snackbar('error'.tr, 'location_error'.tr, snackPosition: SnackPosition.BOTTOM);
  } finally {
    if (mounted) setState(() => _saving = false);
  }
}



  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isEditing ? 'edit_store_location'.tr : 'add_store_location'.tr),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Instructions
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
            child: Text(
              'map_instructions'.tr, // "Déplacez la carte pour positionner votre boutique."
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ),

          // Carte Google
          Expanded(
            child: Stack(
              children: [
                gmaps.GoogleMap(
                  initialCameraPosition: gmaps.CameraPosition(
                    target: _selectedLatLng,
                    zoom: 14,
                  ),
                  myLocationEnabled: true,
                  myLocationButtonEnabled: false,
                  zoomControlsEnabled: false,
                  onMapCreated: (c) => _mapController = c,
                  onCameraMove: (camera) {
                    _selectedLatLng = camera.target; // local only
                  },
                ),
                // Pin centré
                IgnorePointer(
                  child: Center(
                    child: Icon(
                      Icons.location_on,
                      size: 36,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
                // Bouton "Ma position"
                Positioned(
                  right: 12,
                  bottom: 12,
                  child: FloatingActionButton.extended(
                    heroTag: 'recenter_gps',
                    onPressed: _recenterToDevice,
                    icon: const Icon(Icons.my_location),
                    label: Text('use_current_location'.tr),
                  ),
                ),
              ],
            ),
          ),

          // Formulaire + bouton Enregistrer
          SafeArea(
            top: false,
            child: Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.06),
                    blurRadius: 12,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Nom boutique
                    TextFormField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: 'store_name'.tr,
                        hintText: 'store_name'.tr,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      validator: (v) =>
                          (v == null || v.trim().isEmpty) ? 'field_required'.tr : null,
                      textInputAction: TextInputAction.next,
                    ),
                    const SizedBox(height: 12),

                    // Adresse
                    TextFormField(
                      controller: addressController,
                      decoration: InputDecoration(
                        labelText: 'address'.tr,
                        hintText: 'address'.tr,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      validator: (v) =>
                          (v == null || v.trim().isEmpty) ? 'field_required'.tr : null,
                      textInputAction: TextInputAction.done,
                    ),
                    const SizedBox(height: 12),

                    SizedBox(
                      width: double.infinity,
                      height: 52,
                      child: ElevatedButton.icon(
                        onPressed: _saving ? null : _save,
                        icon: _saving
                            ? const SizedBox(
                                width: 18, height: 18,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.check),
                        label: Text(_saving ? 'saving'.tr : 'save'.tr),
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(14),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// ==== Champ réutilisable style moderne ====
class _ModernTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String hint;
  final IconData icon;
  final int maxLength;
  final int maxLines;
  final String? Function(String value)? validator;

  const _ModernTextField({
    super.key,
    required this.controller,
    required this.label,
    required this.hint,
    required this.icon,
    this.maxLength = 50,
    this.maxLines = 1,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setSt) {
        controller.removeListener(() {});
        controller.addListener(() => setSt(() {}));

        return TextFormField(
          controller: controller,
          maxLines: maxLines,
          maxLength: maxLength,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
            prefixIcon: Icon(icon),
            suffixIcon: controller.text.isEmpty
                ? null
                : IconButton(
                    icon: const Icon(Icons.close_rounded),
                    tooltip: 'clear',
                    onPressed: () {
                      controller.clear();
                      setSt(() {});
                    },
                  ),
            counterText: '${controller.text.length}/$maxLength',
            filled: true,
            fillColor: Colors.white,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: BorderSide(color: Colors.black12.withOpacity(.15)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: BorderSide(color: AppColors.primary, width: 1.6),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: const BorderSide(color: Colors.red),
            ),
          ),
          validator: (v) {
            final text = v?.trim() ?? '';
            if (validator != null) return validator!(text);
            if (text.isEmpty) return 'missing_info'.tr;
            return null;
          },
        );
      },
    );
  }
   
}
/* ===========================
   Traductions à ajouter (FR/EN/AR)
   - 'store_location' : 'Localisation du magasin' / 'Store location' / 'موقع المتجر'
   - 'no_store_location' : 'Aucune localisation de magasin définie' / 'No store location set' / 'لا يوجد موقع متجر محدد'
   - 'add_store_location_hint' : 'Ajoutez la localisation du magasin pour que les clients vous trouvent.' / 'Add your store location so customers can find you.' / 'أضف موقع متجرك ليتسنى للعملاء العثور عليك.'
   - 'store_name' : 'Nom du magasin' / 'Store name' / 'اسم المتجر'
   - 'address' : 'Adresse' / 'Address' / 'العنوان'
   - 'save' : 'Enregistrer' / 'Save' / 'حفظ'
   - 'update' : 'Mettre à jour' / 'Update' / 'تحديث'
   - 'saved' : 'Enregistrée' / 'Saved' / 'تم الحفظ'
   - 'address_saved' : 'Adresse enregistrée avec succès' / 'Address saved successfully' / 'تم حفظ العنوان بنجاح'
   - 'error' : 'Erreur' / 'Error' / 'خطأ'
   - 'location_error' : 'Échec de l’obtention de la position' / 'Failed to get current location' / 'فشل الحصول على الموقع الحالي'
   - 'missing_info' : 'Informations manquantes' / 'Missing information' / 'معلومات ناقصة'
   - 'fill_all_fields' : 'Veuillez remplir tous les champs' / 'Please fill in all fields' / 'يرجى ملء جميع الحقول'
=========================== */




 Widget buildShimmerContent(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Shimmer for image section
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: double.infinity,
            height: MediaQuery.of(context).size.height * .47,
            color: Colors.grey[300],
          ),
        ),

        // Shimmer for scrollable content section
        Expanded(
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(15)),
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and heart shimmer
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: 200,
                          height: 22,
                          color: Colors.grey[300],
                        ),
                      ),
                      const Spacer(),
                      Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: 30,
                          height: 30,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Store info shimmer
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      width: 140,
                      height: 18,
                      color: Colors.grey[300],
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Date shimmer
                  Align(
                    alignment: Alignment.centerRight,
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 80,
                        height: 14,
                        color: Colors.grey[300],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Description shimmer
                  Column(
                    children: List.generate(3, (index) => Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: double.infinity,
                          height: 16,
                          color: Colors.grey[300],
                        ),
                      ),
                    )),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Bottom section shimmer
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
          ),
          child: Padding(
            padding: const EdgeInsets.only(
                bottom: 30.0, left: 25, right: 25, top: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Quantity section shimmer
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 80,
                        height: 16,
                        color: Colors.grey[300],
                      ),
                    ),
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 100,
                        height: 35,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(15),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                
                // Price and buttons shimmer
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 80,
                            height: 12,
                            color: Colors.grey[300],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 100,
                            height: 18,
                            color: Colors.grey[300],
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 95,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 95,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }