
import 'package:boutigak/controllers/payment_controller.dart';
import 'package:boutigak/data/models/payment_provider.dart';
import 'package:boutigak/data/services/payment_provider_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
// ========================= PaymentProvidersPage (améliorée) =========================
class PaymentProvidersPage extends StatefulWidget {
  const PaymentProvidersPage({super.key});

  @override
  State<PaymentProvidersPage> createState() => _PaymentProvidersPageState();
}

class _PaymentProvidersPageState extends State<PaymentProvidersPage> {
  final PaymentController paymentController = Get.put(PaymentController());

  // Force l'affichage du skeleton pendant le tout premier chargement,
  // même si le controller démarre avec isLoading = false.
  bool _firstLoad = true;

  @override
  void initState() {
    super.initState();
    // On déclenche le prefetch sur la prochaine micro-tâche pour garantir
    // un 1er frame avec _firstLoad = true -> shimmer visible immédiatement.
    Future.microtask(_prefetch);
  }

  Future<void> _prefetch() async {
    try {
      setState(() => _firstLoad = true);
      await Future.wait([
        paymentController.fetchPaymentProviders(),
        paymentController.fetchStorePaymentProviders(),
      ]);
    } finally {
      if (mounted) setState(() => _firstLoad = false);
    }
  }

  Future<void> _refresh() async {
    await paymentController.fetchStorePaymentProviders();
  }

  void _openAddDialog() {
    showDialog(
      context: context,
      builder: (_) => AddProviderDialog(paymentController: paymentController),
    ).then((_) {
      // au retour du dialog, on rafraîchit la liste
      _refresh();
    });
  }

  void _openEditDialog(StorePaymentProvider provider) {
    showDialog(
      context: context,
      builder: (_) => EditProviderDialog(
        paymentController: paymentController,
        provider: provider,
      ),
    ).then((_) {
      _refresh();
    });
  }

  void _confirmDelete(StorePaymentProvider provider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Text('delete_confirm_title'.tr),
          content: Text('delete_confirm_body'.trParams({'name': provider.providerName})),
          actionsPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('cancel'.tr),
            ),
            FilledButton.tonal(
              onPressed: () async {
                Navigator.of(context).pop();
                final ok = await paymentController.deletePaymentProvider(provider.id!);
                if (ok) {
                  Get.snackbar('success'.tr, 'delete_success'.tr,
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.green, colorText: Colors.white);
                  _refresh();
                } else {
                  Get.snackbar('error'.tr, 'delete_fail'.tr,
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.red, colorText: Colors.white);
                }
              },
              style: FilledButton.styleFrom(foregroundColor: Colors.red),
              child: Text('delete'.tr),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('payment_methods'.tr,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w600)),
        centerTitle: true,
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _openAddDialog,
        icon: const Icon(Icons.add),
        label: Text('add_method'.tr),
        backgroundColor: Colors.black87,
      ),
      body: SafeArea(
        child: Obx(() {
          final isLoading = paymentController.isLoading.value;

          // 👉 priorité au firstLoad pour afficher le skeleton sur le 1er rendu
          if (_firstLoad || isLoading) {
            return const _ProvidersSkeleton();
          }

          final items = paymentController.storePaymentProviders;

          // 👉 état vide SEULEMENT après le premier chargement terminé
          if (items.isEmpty) {
            return _EmptyState(onAddTap: _openAddDialog);
          }

          // iOS-like pull-to-refresh avec callback explicite
          return CustomScrollView(
            slivers: [
              CupertinoSliverRefreshControl(onRefresh: _refresh),
              SliverPadding(
                padding: const EdgeInsets.fromLTRB(16, 12, 16, 100),
                sliver: SliverList.separated(
                  itemCount: items.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 10),
                  itemBuilder: (_, i) {
                    final p = items[i];
                    return _ProviderCard(
                      provider: p,
                      onEdit: () => _openEditDialog(p),
                      onDelete: () => _confirmDelete(p),
                    );
                  },
                ),
              ),
            ],
          );
        }),
      ),
    );
  }
}


// ========================= UI Bits =========================

class _ProviderCard extends StatelessWidget {
  final StorePaymentProvider provider;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const _ProviderCard({
    required this.provider,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);

    return Material(
      color: theme.colorScheme.surface,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: theme.dividerColor.withOpacity(0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: SizedBox(
                width: 56,
                height: 56,
                child: (provider.providerLogo ?? '').isEmpty
                    ? Container(
                        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                        child: Icon(Icons.payment, color: theme.disabledColor),
                      )
                    : Image.network(
                        provider.providerLogo!,
                        fit: BoxFit.cover,
                        errorBuilder: (_, __, ___) =>
                            Icon(Icons.payment, size: 32, color: theme.disabledColor),
                      ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DefaultTextStyle(
                style: TextStyle(color: onSurface),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      provider.providerName,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
                    ),
                    const SizedBox(height: 16),
                    if ((provider.phoneNumber ?? '').isNotEmpty)
                      Row(
                        children: [
                          const Icon(Icons.phone, size: 16),
                          const SizedBox(width: 6),
                          Text(provider.phoneNumber!, style: TextStyle(color: theme.hintColor)),
                        ],
                      ),
                      const SizedBox(height: 16),
                    if ((provider.paymentCode ?? '').isNotEmpty)
                      Row(
                        children: [
                          const Icon(Icons.qr_code_2, size: 16),
                          const SizedBox(width: 6),
                          Text(provider.paymentCode!, style: TextStyle(color: theme.hintColor)),
                        ],
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 6),
            _ActionButtons(onEdit: onEdit, onDelete: onDelete),
          ],
        ),
      ),
    );
  }
}

class _ActionButtons extends StatelessWidget {
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  const _ActionButtons({required this.onEdit, required this.onDelete});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      spacing: 8,
      children: [
        OutlinedButton.icon(
          onPressed: onEdit,
          icon: const Icon(Icons.edit, size: 16),
          label: Text('edit'.tr),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
            foregroundColor: theme.colorScheme.primary,
            side: BorderSide(color: theme.colorScheme.primary.withOpacity(0.4)),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        ),
        OutlinedButton.icon(
          onPressed: onDelete,
          icon: const Icon(Icons.delete_outline, size: 16),
          label: Text('delete'.tr),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
            foregroundColor: Colors.red,
            side: BorderSide(color: Colors.red.withOpacity(0.4)),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        ),
      ],
    );
  }
}

class _EmptyState extends StatelessWidget {
  final VoidCallback onAddTap;
  const _EmptyState({required this.onAddTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.credit_card_off, size: 72, color: theme.disabledColor),
            const SizedBox(height: 12),
            Text('empty_title'.tr,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            const SizedBox(height: 8),
            Text('empty_subtitle'.tr,
                textAlign: TextAlign.center, style: TextStyle(color: theme.hintColor)),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: onAddTap,
              icon: const Icon(Icons.add),
              label: Text('add_method'.tr),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black87,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ================ Skeleton (shimmer) cohérent avec la page ================
// ================ Skeleton (shimmer) 1:1 avec _ProviderCard ================
class _ProvidersSkeleton extends StatelessWidget {
  const _ProvidersSkeleton();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final base = isDark ? Colors.white.withOpacity(0.10) : Colors.grey.shade300;
    final high = isDark ? Colors.white.withOpacity(0.25) : Colors.grey.shade100;

    return ListView.separated(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
      itemCount: 6,
      separatorBuilder: (_, __) => const SizedBox(height: 10),
      itemBuilder: (_, __) {
        return Material(
          color: theme.colorScheme.surface,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: theme.dividerColor.withOpacity(0.2)),
          ),
          child: Padding(
            padding: const EdgeInsets.all(14), // = _ProviderCard padding
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // avatar/logo 56x56 radius 10
                _ShBox(width: 56, height: 56, radius: 10, base: base, high: high),

                const SizedBox(width: 12),

                // bloc texte (titre + phone + code)
                Expanded(
                  child: DefaultTextStyle(
                    style: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.9)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Titre (16px, w700) -> barre ~60% largeur
                        _ShBar(width: 210, height: 18, radius: 6, base: base, high: high),

                        const SizedBox(height: 16), // même que la card

                        // row phone: icon 16 + 6 + label
                        Row(
                          children: [
                            _ShIcon(size: 16, base: base, high: high),
                            const SizedBox(width: 6),
                            _ShBar(width: 140, height: 13, radius: 6, base: base, high: high),
                          ],
                        ),

                        const SizedBox(height: 16), // même que la card

                        // row code: icon 16 + 6 + label
                        Row(
                          children: [
                            _ShIcon(size: 16, base: base, high: high),
                            const SizedBox(width: 6),
                            _ShBar(width: 120, height: 13, radius: 6, base: base, high: high),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(width: 6),

                // actions à droite (deux boutons outlined empilés)
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _ShBar(width: 110, height: 36, radius: 10, base: base, high: high),
                    const SizedBox(height: 8),
                    _ShBar(width: 100, height: 36, radius: 10, base: base, high: high),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// --- Primitives shimmer (mêmes timings/couleurs) ---

class _ShBox extends StatelessWidget {
  final double width, height, radius;
  final Color base, high;
  const _ShBox({
    required this.width,
    required this.height,
    required this.radius,
    required this.base,
    required this.high,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1150),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(color: base, borderRadius: BorderRadius.circular(radius)),
      ),
    );
  }
}

class _ShBar extends StatelessWidget {
  final double width, height, radius;
  final Color base, high;
  const _ShBar({
    required this.width,
    required this.height,
    required this.radius,
    required this.base,
    required this.high,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1150),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(color: base, borderRadius: BorderRadius.circular(radius)),
      ),
    );
  }
}

class _ShIcon extends StatelessWidget {
  final double size;
  final Color base, high;
  const _ShIcon({required this.size, required this.base, required this.high});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1150),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(color: base, shape: BoxShape.circle),
      ),
    );
  }
}

// ========================= Dialogs & Forms (UI polis) =========================
class PaymentForm extends StatefulWidget {
  final PaymentController paymentController;
  const PaymentForm({super.key, required this.paymentController});

  @override
  State<PaymentForm> createState() => _PaymentFormState();
}

class _PaymentFormState extends State<PaymentForm> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _phoneCtrl;
  late final TextEditingController _codeCtrl;

  final FocusNode _phoneFocus = FocusNode();
  final FocusNode _codeFocus = FocusNode();

  // pour activer/désactiver le bouton
  bool _canSubmit = false;
  bool _submitting = false;

  @override
  void initState() {
    super.initState();
    _phoneCtrl = TextEditingController();
    _codeCtrl = TextEditingController();
    _phoneCtrl.addListener(_revalidate);
    _codeCtrl.addListener(_revalidate);
  }

  @override
  void dispose() {
    _phoneCtrl.removeListener(_revalidate);
    _codeCtrl.removeListener(_revalidate);
    _phoneCtrl.dispose();
    _codeCtrl.dispose();
    _phoneFocus.dispose();
    _codeFocus.dispose();
    super.dispose();
  }

  void _revalidate() {
    final ok = _formKey.currentState?.validate() ?? false;
    if (ok != _canSubmit) {
      setState(() => _canSubmit = ok);
    }
  }

  String? _phoneValidator(String? v) {
    final value = (v ?? '').trim();
    final code = _codeCtrl.text.trim();

    // Règle globale: au moins un des deux champs doit être rempli
    if (value.isEmpty && code.isEmpty) {
      return 'form_at_least_one'.tr; // "Veuillez remplir au moins un champ"
    }

    if (value.isEmpty) return null;

    // Basique: 7–12 chiffres après le préfixe (tu peux ajuster à ton besoin)
    final digitsOnly = value.replaceAll(RegExp(r'\D'), '');
    if (digitsOnly.length < 7 || digitsOnly.length > 12) {
      return 'phone_invalid'.tr; // "Numéro de téléphone invalide"
    }
    return null;
  }

  String? _codeValidator(String? v) {
    final value = (v ?? '').trim();
    final phone = _phoneCtrl.text.trim();

    // Règle globale: au moins un des deux champs doit être rempli
    if (value.isEmpty && phone.isEmpty) {
      return 'form_at_least_one'.tr;
    }
    // Option: longueur mini
    if (value.isNotEmpty && value.length < 3) {
      return 'code_invalid'.tr; // "Code trop court"
    }
    return null;
  }

  Future<void> _submit() async {
    if (!(_formKey.currentState?.validate() ?? false)) return;

    setState(() => _submitting = true);
    try {
      final body = {
        'provider_id': int.parse(widget.paymentController.selectedProvider.value),
        'payment_code': _codeCtrl.text.trim(),
        'phone_number': _phoneCtrl.text.trim(),
      };

      final ok = await PaymentService.addPaymentProvider(body);
      if (ok) {
        await widget.paymentController.fetchStorePaymentProviders();
        Get.snackbar('success'.tr, 'add_success'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green, colorText: Colors.white);
        Get.back();
      } else {
        Get.snackbar('error'.tr, 'add_fail'.tr, snackPosition: SnackPosition.BOTTOM);
      }
    } finally {
      if (mounted) setState(() => _submitting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // -------- Provider selector --------
            Obx(() => DropdownButtonFormField<String>(
                  value: widget.paymentController.selectedProvider.value.isEmpty
                      ? null
                      : widget.paymentController.selectedProvider.value,
                  hint: Text('select_method'.tr),
                  onChanged: (v) {
                    if (v != null) {
                      widget.paymentController.selectedProvider.value = v;
                      _revalidate();
                    }
                  },
                  items: widget.paymentController.paymentProviders.map((provider) {
                    return DropdownMenuItem<String>(
                      value: provider.id.toString(),
                      child: Row(
                        children: [
                          if ((provider.logoUrl ?? '').isNotEmpty)
                            ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: Image.network(provider.logoUrl!, width: 24, height: 24, fit: BoxFit.cover),
                            ),
                          const SizedBox(width: 8),
                          Text(provider.name),
                        ],
                      ),
                    );
                  }).toList(),
                  decoration: InputDecoration(
                    labelText: 'provider_label'.tr,
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  ),
                  validator: (v) =>
                      (v == null || v.isEmpty) ? 'select_method'.tr : null, // obligatoire
                )),
            const SizedBox(height: 16),

            // -------- Phone --------
            TextFormField(
              controller: _phoneCtrl,
              focusNode: _phoneFocus,
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.next,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9+\s-]')),
                LengthLimitingTextInputFormatter(16),
              ],
              decoration: InputDecoration(
                labelText: 'phone_label'.tr,
                hintText: 'phone_hint'.tr,
                prefixIcon: const Icon(Icons.phone_outlined),
                prefixText: '+222 ', // adapte si tu veux le rendre dynamique
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              ),
              onFieldSubmitted: (_) => _codeFocus.requestFocus(),
              validator: _phoneValidator,
            ),
            const SizedBox(height: 16),

            // -------- Payment code --------
            TextFormField(
              controller: _codeCtrl,
              focusNode: _codeFocus,
              textInputAction: TextInputAction.done,
              decoration: InputDecoration(
                labelText: 'code_label'.tr,
                hintText: 'code_hint'.tr,
                prefixIcon: const Icon(Icons.qr_code_2),
                suffixIcon: IconButton(
                  tooltip: 'scan_code'.tr,
                  icon: const Icon(Icons.qr_code_scanner),
                  onPressed: () async {
                    // TODO: branche ici ton scanner (ex: mobile_scanner / qr_code_scanner)
                    // final result = await Get.to(() => const QrScanPage());
                    // if (result != null) setState(() => _codeCtrl.text = result);
                  },
                ),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              ),
              validator: _codeValidator,
              onEditingComplete: _submit,
            ),
            const SizedBox(height: 20),

            // -------- Submit --------
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: (!_canSubmit || _submitting) ? null : _submit,
                icon: _submitting
                    ? const SizedBox(
                        width: 18, height: 18,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.save_outlined),
                label: Text(_submitting ? 'saving'.tr : 'save'.tr),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black87,
                  disabledBackgroundColor: theme.disabledColor.withOpacity(0.2),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}


class AddProviderDialog extends StatelessWidget {
  final PaymentController paymentController;
  const AddProviderDialog({super.key, required this.paymentController});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: PaymentForm(paymentController: paymentController),
      ),
    );
  }
}

class EditProviderDialog extends StatelessWidget {
  final PaymentController paymentController;
  final StorePaymentProvider provider;
  const EditProviderDialog({super.key, required this.paymentController, required this.provider});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: EditPaymentForm(paymentController: paymentController, provider: provider),
      ),
    );
  }
}

class EditPaymentForm extends StatelessWidget {
  final PaymentController paymentController;
  final StorePaymentProvider provider;
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController paymentCodeController = TextEditingController();

  EditPaymentForm({super.key, required this.paymentController, required this.provider}) {
    phoneController.text = provider.phoneNumber ?? '';
    paymentCodeController.text = provider.paymentCode ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('edit_title'.trParams({'name': provider.providerName}),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(color: Colors.grey.shade100, borderRadius: BorderRadius.circular(12)),
            child: Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: SizedBox(
                    width: 42, height: 42,
                    child: (provider.providerLogo ?? '').isEmpty
                        ? const Icon(Icons.payment, size: 28, color: Colors.grey)
                        : Image.network(provider.providerLogo!, fit: BoxFit.cover,
                          errorBuilder: (_, __, ___) => const Icon(Icons.payment, size: 28, color: Colors.grey),
                        ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(child: Text(provider.providerName, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600))),
              ],
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: phoneController,
            decoration: InputDecoration(
              labelText: 'phone_label'.tr,
              hintText: 'phone_hint'.tr,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            ),
            keyboardType: TextInputType.phone,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: paymentCodeController,
            decoration: InputDecoration(
              labelText: 'code_label'.tr,
              hintText: 'code_hint'.tr,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            ),
          ),
          const SizedBox(height: 22),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Get.back(),
                  child: Text('cancel'.tr),
                  style: OutlinedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 14)),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () async {
                    if (phoneController.text.isEmpty && paymentCodeController.text.isEmpty) {
                      Get.snackbar('error'.tr, 'form_at_least_one'.tr,
                          snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.red, colorText: Colors.white);
                      return;
                    }
                    final ok = await paymentController.updatePaymentProvider(
                      provider.id!, paymentCodeController.text, phoneController.text,
                    );
                    if (ok) {
                      Get.snackbar('success'.tr, 'update_success'.tr,
                          snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.green, colorText: Colors.white);
                      Get.back();
                    } else {
                      Get.snackbar('error'.tr, 'update_fail'.tr,
                          snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.red, colorText: Colors.white);
                    }
                  },
                  icon: const Icon(Icons.save_outlined),
                  label: Text('update'.tr),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black87,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
