import 'dart:io';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
class StoreInformations extends StatefulWidget {
  @override
  _StoreInformationsState createState() => _StoreInformationsState();
}

class _StoreInformationsState extends State<StoreInformations> {
  final StoreController storeController = Get.put(StoreController());
  final TextEditingController fromDateController = TextEditingController();
  final TextEditingController toDateController = TextEditingController();

  TimeOfDay? openingTime;
  TimeOfDay? closingTime;
  bool isTimePickerOpen = false;

  @override
  void initState() {
    super.initState();

    if (storeController.storeTypes.isEmpty) {
      storeController.fetchStoreTypes();
    }

    storeController.openingHours.value =
        storeController.openingHours.value.isNotEmpty ? storeController.openingHours.value : '09:00';

    storeController.closingHours.value =
        storeController.closingHours.value.isNotEmpty ? storeController.closingHours.value : '21:00';
  }

  Future<void> _selectTime(BuildContext context, bool isOpening) async {
    if (isTimePickerOpen) return;
    isTimePickerOpen = true;

    final picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (picked != null) {
      final formattedTime = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';

      if (isOpening) {
        storeController.openingHours.value = formattedTime;
        setState(() => openingTime = picked);
      } else {
        storeController.closingHours.value = formattedTime;
        setState(() => closingTime = picked);
      }
    }

    isTimePickerOpen = false;
  }

  InputDecoration inputDecoration(String labelText) {
    return InputDecoration(
      labelText: labelText,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(14)),
      isDense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
    );
  }

  void _saveStore() async {
    if (storeController.storeTypes.isEmpty) {
      Get.snackbar('error'.tr, 'something_went_wrong'.tr, snackPosition: SnackPosition.BOTTOM);
      return;
    }

    if (storeController.type.value.isEmpty) {
      Get.snackbar('error'.tr, 'store_type'.tr, snackPosition: SnackPosition.BOTTOM);
      return;
    }

    showLoadingDialog(context);

    try {
      await storeController.createStore();
      Navigator.of(context).pop(); // fermer le loader
      Get.back(); // retour
    } catch (_) {
      Navigator.of(context).pop();
      Get.snackbar('error'.tr, 'something_went_wrong'.tr, snackPosition: SnackPosition.BOTTOM);
    }
  }

  void showLoadingDialog(BuildContext context) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) => Container(
        color: Colors.black.withOpacity(0.4),
        child: const Center(
          child: CupertinoTheme(
            data: CupertinoThemeData(brightness: Brightness.dark),
            child: CupertinoActivityIndicator(radius: 15),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final c1 = theme.colorScheme.primary;
    final c2 = theme.colorScheme.primary.withOpacity(0.95);

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      appBar: AppBar(
        title: Text('store_creation'.tr),
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // ===== Dégradé d’arrière-plan (cohérent avec tes autres pages) =====
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [c1, c2],
                ),
              ),
            ),
          ),

          Obx(() {
            final isInitialLoading = storeController.isLoading.isTrue && storeController.storeTypes.isEmpty;

            return CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // ----- Header translucide -----
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                    child: Material(
                      color: Colors.white.withOpacity(0.10),
                      elevation: 0,
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.white.withOpacity(0.25)),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              width: 56,
                              height: 56,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withOpacity(0.28),
                                    Colors.white.withOpacity(0.12),
                                  ],
                                ),
                                border: Border.all(color: Colors.white.withOpacity(0.35)),
                              ),
                              child: const Icon(Icons.store_mall_directory_outlined, color: Colors.white, size: 26),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'store_creation'.tr,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w800,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    // tu peux remplacer par une clé i18n propre si tu veux
                                    "store_create_subtitle_1".tr,
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.85),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // ----- Surface blanche arrondie + formulaire -----
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.06),
                          blurRadius: 20,
                          offset: const Offset(0, -6),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                      child: isInitialLoading
                          ? const Center(child: CircularProgressIndicator())
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // ===== Carte image + nom =====
                                Material(
                                  color: theme.colorScheme.surface,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        // ⬇️ Garde exactement le format 200x80
                                        UploadStoreImagePage(),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text('store_name'.tr,
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w700,
                                                    color: theme.colorScheme.primary,
                                                  )),
                                              const SizedBox(height: 6),
                                              TextField(
                                                decoration: inputDecoration(""),
                                                onChanged: (value) => storeController.name.value = value,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 12),

                                // ===== Description =====
                                Material(
                                  color: theme.colorScheme.surface,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text('store_description'.tr,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w700,
                                              color: theme.colorScheme.primary,
                                            )),
                                        const SizedBox(height: 6),
                                        TextField(
                                          decoration: inputDecoration(""),
                                          maxLines: 5,
                                          textInputAction: TextInputAction.done,
                                          onEditingComplete: () => FocusScope.of(context).unfocus(),
                                          onChanged: (value) => storeController.description.value = value,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 12),

                                // ===== Type de boutique =====
                                Material(
                                  color: theme.colorScheme.surface,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: DropdownButtonFormField<String>(
                                      decoration: inputDecoration('store_type'.tr),
                                      value: storeController.type.value.isNotEmpty ? storeController.type.value : null,
                                      items: storeController.storeTypes.map((storeType) {
                                        return DropdownMenuItem<String>(
                                          value: storeType.id.toString(),
                                          child: Row(
                                            children: [
                                              const Icon(Icons.category_outlined, size: 18),
                                              const SizedBox(width: 8),
                                              Text(storeType.name),
                                            ],
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (newValue) {
                                        if (newValue != null) {
                                          storeController.type.value = newValue;
                                        }
                                      },
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 12),

                                // ===== Horaires =====
                                Material(
                                  color: theme.colorScheme.surface,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4),
                                    child: Column(
                                      children: [
                                        ListTile(
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                                          leading: Container(
                                            width: 40,
                                            height: 40,
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(10),
                                              color: theme.colorScheme.primary.withOpacity(0.08),
                                            ),
                                            child: Icon(Icons.access_time, color: theme.colorScheme.primary),
                                          ),
                                          title: Text('opening_time'.tr),
                                          subtitle: Obx(() => Text(
                                                storeController.openingHours.value.isNotEmpty
                                                    ? storeController.openingHours.value
                                                    : 'select'.tr,
                                                style: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.7)),
                                              )),
                                          onTap: () => _selectTime(context, true),
                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                          child: const _ModernDivider(),
                                        ),
                                        ListTile(
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                                          leading: Container(
                                            width: 40,
                                            height: 40,
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(10),
                                              color: theme.colorScheme.primary.withOpacity(0.08),
                                            ),
                                            child: Icon(Icons.timelapse, color: theme.colorScheme.primary),
                                          ),
                                          title: Text('closing_time'.tr),
                                          subtitle: Obx(() => Text(
                                                storeController.closingHours.value.isNotEmpty
                                                    ? storeController.closingHours.value
                                                    : 'select'.tr,
                                                style: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.7)),
                                              )),
                                          onTap: () => _selectTime(context, false),
                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                const Spacer(),

                                // ===== Bouton Enregistrer =====
                                SizedBox(
                                  width: double.infinity,
                                  child: CustomButton(
                                    text: "save".tr,
                                    onPressed: _saveStore,
                                  ),
                                ),
                                 const SizedBox(height: 12),
                              ],
                            ),
                    ),
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }
}

class UploadStoreImagePage extends StatefulWidget {
  @override
  _UploadStoreImagePageState createState() => _UploadStoreImagePageState();
}

class _UploadStoreImagePageState extends State<UploadStoreImagePage> {
  File? image1;

  final StoreController storeController = Get.find<StoreController>();
  final picker = ImagePicker();

  Future<void> pickImage() async {
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        image1 = File(pickedFile.path);
        storeController.addBoutiquePicture(pickedFile.path);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Align(
      alignment: Alignment.centerLeft,
      child: Stack(
        children: [
          // ⬇️ On garde exactement ce format (200x80) comme demandé
          ClipRRect(
            borderRadius: BorderRadius.circular(14),
            child: Container(
              width: 200,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                border: Border.all(color: theme.dividerColor.withOpacity(0.25)),
                borderRadius: BorderRadius.circular(14),
              ),
              child: image1 == null
                  ? InkWell(
                      onTap: pickImage,
                      child: Container(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.add_a_photo, color: theme.colorScheme.primary),
                            const SizedBox(width: 8),
                            Text(
                              'upload'.trParams({'fallback': 'Upload'}),
                              style: TextStyle(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : Image.file(image1!, fit: BoxFit.cover),
            ),
          ),

          if (image1 != null)
            Positioned(
              right: 2,
              top: 2,
              child: Material(
                color: Colors.white.withOpacity(0.9),
                shape: const CircleBorder(),
                child: InkWell(
                  customBorder: const CircleBorder(),
                  onTap: () {
                    setState(() {
                      image1 = null;
                      storeController.boutiquePictures.clear();
                    });
                  },
                  child: const Padding(
                    padding: EdgeInsets.all(4.0),
                    child: Icon(Icons.close, color: Colors.red, size: 18),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _ModernDivider extends StatelessWidget {
  const _ModernDivider({this.indent = 0, this.endIndent = 0});
  final double indent;
  final double endIndent;

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Container(
      height: 0.5,
      margin: EdgeInsetsDirectional.only(start: indent, end: endIndent),
      color: c.outlineVariant.withOpacity(0.3), // fin et net
    );
  }
}