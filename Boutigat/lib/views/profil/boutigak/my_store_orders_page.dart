import 'dart:developer';
import 'dart:io' show Platform;
import 'dart:ui';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:boutigak/views/profil/store_order_validation_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';

class StoreOrderDetailsPage extends StatefulWidget {
  final Order order;
  const StoreOrderDetailsPage({super.key, required this.order});

  @override
  State<StoreOrderDetailsPage> createState() => _StoreOrderDetailsPageState();
}

class _StoreOrderDetailsPageState extends State<StoreOrderDetailsPage> {
  bool _isCancelLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final orderCtl = Get.find<OrderController>();

    final double delivery =
        double.tryParse(widget.order.deliveryCharge ?? '0') ?? 0.0;
    final double productsTotal = (widget.order.totalOrders ?? 0).toDouble();
    final double total = productsTotal + delivery;

    
    Future<void> _callNumber(String phone) async {
      final uri = Uri(scheme: 'tel', path: phone.replaceAll(' ', ''));
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        Get.snackbar(
          'error'.tr.isEmpty ? 'Error' : 'error'.tr,
          'cannot_place_call'.tr.isEmpty
              ? 'Cannot place a call on this device.'
              : 'cannot_place_call'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Bande primaire en haut
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 230,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.9)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ),

          SafeArea(
            bottom: false,
            child: Column(
              children: [
                // Header glass
                Padding(
                  padding: const EdgeInsets.fromLTRB(12, 8, 12, 10),
                  child: _GlassHeader(
                    title: '${'order'.tr} #${widget.order.orderId ?? '—'}',
                    onCancelTap: (_isCancelLoading ||
                            widget.order.isPaid ||
                            widget.order.status == 'CANCELLED')
                        ? null
                        : () {
                            Get.defaultDialog(
                              title: 'cancel_order'.tr,
                              middleText: 'cancel_order_confirm'.tr,
                              textCancel: 'no'.tr,
                              textConfirm: 'yes'.tr,
                              confirmTextColor: Colors.white,
                              onConfirm: () async {
                                setState(() => _isCancelLoading = true);
                                await orderCtl.changeOrderStatus(
                                    widget.order.orderId!, 'CANCELLED');
                                setState(() => _isCancelLoading = false);
                                Get.back(); // dialog
                                Get.back(); // page
                              },
                            );
                          },
                    extraMenuItemsBuilder: (ctx) => <PopupMenuEntry>[
                      // Valider (si PENDING)
                      PopupMenuItem(
                        enabled: widget.order.status == 'PENDING',
                        child: Row(children: [
                          const Icon(Icons.verified_outlined, size: 18),
                          const SizedBox(width: 8),
                          Text('validate'.tr.isEmpty
                              ? 'Validate'
                              : 'validate'.tr),
                        ]),
                        onTap: () {
                          Future.delayed(const Duration(milliseconds: 10), () {
                            if (widget.order.status == 'PENDING') {
                              Get.to(() => StoreOrderValidationPage(
                                  order: widget.order));
                            }
                          });
                        },
                      ),
                    ],
                  ),
                ),

                // Barre de statut
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: _GlassStatusBar(currentStatus: widget.order.status,
                    isCOD: widget.order.isCashOnDelivery, 
  isPaid: widget.order.isPaid,),
                ),
                const SizedBox(height: 10),

                // Corps blanc arrondi
                Expanded(
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(22)),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: CustomScrollView(
                            physics: const BouncingScrollPhysics(),
                            slivers: [
                              // En-tête: client + COD
                              SliverPadding(
                                padding:
                                    const EdgeInsets.fromLTRB(14, 18, 14, 12),
                                sliver: SliverToBoxAdapter(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                          color:
                                              Colors.black12.withOpacity(0.06)),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                          14, 14, 10, 14),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Avatar rond
                                          Container(
                                            width: 48,
                                            height: 48,
                                            decoration: BoxDecoration(
                                              color: Colors.black12
                                                  .withOpacity(0.06),
                                              shape: BoxShape.circle,
                                            ),
                                            child: const Icon(
                                                Icons.person_outline_rounded,
                                                color: Colors.black87),
                                          ),
                                          const SizedBox(width: 12),

                                          // Infos client
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                // Nom + badge COD
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: Text(
                                                        (() {
                                                          final fn = widget
                                                                  .order
                                                                  .userFirstName ??
                                                              '';
                                                          final ln = widget
                                                                  .order
                                                                  .userLastName ??
                                                              '';
                                                          final full =
                                                              '$fn $ln'.trim();
                                                          return full.isEmpty
                                                              ? ('customer'
                                                                      .tr
                                                                      .isEmpty
                                                                  ? 'Customer'
                                                                  : 'customer'
                                                                      .tr)
                                                              : full;
                                                        })(),
                                                        maxLines: 1,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: const TextStyle(
                                                            fontWeight:
                                                                FontWeight.w700,
                                                            fontSize: 16),
                                                      ),
                                                    ),
                                                    if (widget
                                                        .order.isCashOnDelivery)
                                                      Container(
                                                        margin: const EdgeInsets
                                                            .only(left: 8),
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                horizontal: 10,
                                                                vertical: 6),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.blueGrey
                                                              .withOpacity(
                                                                  0.08),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      999),
                                                          border: Border.all(
                                                              color: Colors
                                                                  .blueGrey
                                                                  .withOpacity(
                                                                      0.28)),
                                                        ),
                                                        child: Text(
                                                          'cash_on_delivery'.tr,
                                                          style:
                                                              const TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w700,
                                                                  fontSize: 12),
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                                const SizedBox(height: 8),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    if ((widget.order
                                                                .userPhone ??
                                                            '')
                                                        .isNotEmpty)
                                                      InkWell(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                        onTap: () =>
                                                            _callNumber(widget
                                                                .order
                                                                .userPhone!),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  vertical: 4),
                                                          child: Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              const Icon(
                                                                  FontAwesomeIcons
                                                                      .phone,
                                                                  size: 18,
                                                                  color: Colors
                                                                      .black54),
                                                              const SizedBox(
                                                                  width: 6),
                                                              Text(
                                                                widget.order
                                                                    .userPhone!,
                                                                style:
                                                                    const TextStyle(
                                                                  color: Colors
                                                                      .black87,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    if ((widget.order.isPaid ||
                                                            widget.order
                                                                .isCashOnDelivery) &&
                                                        widget.order
                                                                .deliveryLatitude !=
                                                            null &&
                                                        widget.order
                                                                .deliveryLongitude !=
                                                            null)
                                                      Container(
                                                        height: 40,
                                                        width: 40,
                                                        margin: EdgeInsets
                                                            .symmetric(
                                                                horizontal: 3),
                                                        decoration:
                                                            BoxDecoration(
                                                          border: Border.all(
                                                              color: Colors.grey
                                                                  .shade300),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                        ),
                                                        child: Material(
                                                          color: Colors
                                                              .transparent,
                                                          child: InkWell(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        8),
                                                            onTap: () {
                                                              // ferme le menu parent si besoin

                                                              showDialog(
                                                                context:
                                                                    context,
                                                                builder: (_) =>
                                                                    FractionallySizedBox(
                                                                  heightFactor:
                                                                      0.6,
                                                                  child:
                                                                      DeliveryMapDialog(
                                                                    latitude: widget
                                                                        .order
                                                                        .deliveryLatitude!,
                                                                    longitude: widget
                                                                        .order
                                                                        .deliveryLongitude!,
                                                                    subtitle: widget
                                                                            .order
                                                                            .deliveryAddress ??
                                                                        '',
                                                                  ),
                                                                ),
                                                              );
                                                            },
                                                            child: Center(
                                                              child: Icon(
                                                                Icons
                                                                    .place_outlined,
                                                                color: AppColors
                                                                    .onSurface,
                                                                size: 22,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                  ],
                                                ),

                                                // Téléphone cliquable
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                              // Modifications (si dispo)
                              if (widget.order.modifiedByStore &&
                                  widget.order.modifications.isNotEmpty)
                                SliverPadding(
                                  padding:
                                      const EdgeInsets.fromLTRB(14, 6, 14, 10),
                                  sliver: SliverToBoxAdapter(
                                    child: _UpdatesCard(
                                      title: 'order_updates'.tr,
                                      lines: widget.order.modifications
                                          .map(_storeModificationLine)
                                          .where((s) => s.isNotEmpty)
                                          .toList(),
                                    ),
                                  ),
                                ),

                              // Items
                              SliverPadding(
                                padding:
                                    const EdgeInsets.fromLTRB(14, 0, 14, 140),
                                sliver: SliverList.separated(
                                  itemCount: widget.order.items.length,
                                  separatorBuilder: (_, __) =>
                                      const SizedBox(height: 10),
                                  itemBuilder: (_, i) => _StoreItemRow(
                                      orderItem: widget.order.items[i]),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Résumé collant (boutons adaptés au store)
                        _BottomSummaryBar(
                          productsTotal: productsTotal,
                          delivery: delivery,
                          total: total,
                          isPaid: widget.order.isPaid,
                          isCOD: widget.order.isCashOnDelivery,
                          canPay: false, // le store ne paie pas :)
                          canCancel: !widget.order.isPaid &&
                              widget.order.status != 'CANCELLED',
                          onPay: null,
                          onCancel: _isCancelLoading
                              ? null
                              : () {
                                  Get.defaultDialog(
                                    title: 'cancel_order'.tr,
                                    middleText: 'cancel_order_confirm'.tr,
                                    textCancel: 'no'.tr,
                                    textConfirm: 'yes'.tr,
                                    confirmTextColor: Colors.white,
                                    onConfirm: () async {
                                      setState(() => _isCancelLoading = true);
                                      await orderCtl.changeOrderStatus(
                                          widget.order.orderId!, 'CANCELLED');
                                      setState(() => _isCancelLoading = false);
                                      Get.back(); // dialog
                                      Get.back(); // page
                                    },
                                  );
                                },
                          onViewReceipt: (widget.order.isPaid &&
                                  widget.order.paymentScreenshot != null)
                              ? () => _showPaymentScreenshotDialog(context)
                              : null,
                          isPayLoading: false,
                          isCancelLoading: _isCancelLoading,
                        ),
                        const SizedBox(height: 0),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Mappage textes de modifications côté store
  String _storeModificationLine(OrderModification m) {
    switch (m.type) {
      case 'quantity_changed':
        if (m.oldQuantity != null && m.newQuantity != null) {
          return 'mod_qty_changed_line'.trParams({
            'item': m.itemName,
            'from': '${m.oldQuantity}',
            'to': '${m.newQuantity}'
          });
        }
        return '';
      case 'removed':
        return 'mod_removed_line'.trParams({'item': m.itemName});
      case 'added':
        return 'mod_added_line'
            .trParams({'item': m.itemName, 'qty': '${m.newQuantity ?? 1}'});
      default:
        return '';
    }
  }

  void _showPaymentScreenshotDialog(BuildContext context) {
    final imageUrl = '${widget.order.paymentScreenshot}';
    showGeneralDialog(
      context: context,
      barrierLabel: 'payment_screenshot',
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.25),
      pageBuilder: (_, __, ___) => const SizedBox.shrink(),
      transitionBuilder: (ctx, anim, __, ___) {
        final curved =
            CurvedAnimation(parent: anim, curve: Curves.easeOutCubic);
        return Transform.scale(
          scale: 0.95 + 0.05 * curved.value,
          child: Opacity(
              opacity: curved.value,
              child: _PaymentScreenshotDialog(imageUrl: imageUrl)),
        );
      },
      transitionDuration: const Duration(milliseconds: 220),
    );
  }
}

/* ===================== Items (store) ===================== */

class _StoreItemRow extends StatelessWidget {
  final OrderItem orderItem;
  const _StoreItemRow({required this.orderItem});

  @override
  Widget build(BuildContext context) {
    log('order_item ${orderItem.toJson()}');
    // Use localized data directly from orderItem instead of fetching
    final itemName = orderItem.getTitle();
    final itemDescription = orderItem.getDescription();
    final itemPrice = orderItem.price ?? 0.0;
    final lineTotal = itemPrice * orderItem.quantity;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: Colors.grey.withOpacity(0.12)),
      ),
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: orderItem.images != null && orderItem.images!.isNotEmpty
                ? Image.network(orderItem.images![0].toString(),
                    width: 70, height: 80, fit: BoxFit.cover)
                : Container(
                    width: 70,
                    height: 80,
                    color: Colors.grey.shade200,
                    child: Icon(Icons.image, color: Colors.grey.shade600)),
          ),
          const SizedBox(width: 12),
          Expanded(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(itemName,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 6),
              Wrap(spacing: 8, runSpacing: 6, children: [
                _MiniPill(
                    text:
                        '${'price'.tr}: ${itemPrice.toStringAsFixed(2)} ${'mru'.tr}'),
                _MiniPill(text: '${'quantity'.tr}: ${orderItem.quantity}'),
              ]),
            ]),
          ),
          const SizedBox(width: 8),
          Text('${lineTotal.toStringAsFixed(2)} ${'mru'.tr}',
              style: const TextStyle(fontWeight: FontWeight.w700)),
        ],
      ),
    );
  }
}

class MyStoreOrdersPage extends StatefulWidget {
  const MyStoreOrdersPage({super.key});

  @override
  State<MyStoreOrdersPage> createState() => _MyStoreOrdersPageState();
}

class _MyStoreOrdersPageState extends State<MyStoreOrdersPage> {
  final OrderController orderController = Get.put(OrderController());

  final _fromDateCtrl = TextEditingController();
  final _toDateCtrl = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      orderController.fetchMyStoreOrders(); // appel initial
    });
  }

  Future<void> _onPullToRefresh() async {
    await orderController.fetchMyStoreOrders(
      fromDate: _fromDateCtrl.text.isEmpty ? null : _fromDateCtrl.text,
      toDate: _toDateCtrl.text.isEmpty ? null : _toDateCtrl.text,
      paymentStatus: orderController.paymentStatusFilter.value.isEmpty
          ? null
          : orderController.paymentStatusFilter.value,
    );
  }

  void _openFilters() => _showFilterDialog(context);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Fond dégradé
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.92),
                    theme.colorScheme.primary.withOpacity(0.90),
                  ],
                ),
              ),
            ),
          ),

          SafeArea(
            top: true,
            bottom: false,
            child: Obx(() {
              if (orderController.isLoading.isTrue) {
                return Column(
                  children: const [
                    StoreGlassHeader(title: 'store_orders'),
                    Expanded(child: StoreOrdersShimmerList()),
                  ],
                );
              }

              final orders = orderController.myStoreOrders;

              return CustomScrollView(
                physics: const BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                slivers: [
                  // Pull-to-refresh iOS
                  CupertinoSliverRefreshControl(
                    onRefresh: _onPullToRefresh,
                    builder: (context, refreshState, pulledExtent,
                        refreshTriggerPullDistance, refreshIndicatorExtent) {
                      return const Center(
                          child: CupertinoActivityIndicator(
                              radius: 14, color: Colors.white));
                    },
                  ),

                  SliverToBoxAdapter(
                    child: StoreGlassHeader(
                      title: 'store_orders',
                      onBackTap: () => Get.back(),
                      onFilterTap:
                          _openFilters, // <-- ouvre ton dialog existant
                    ),
                  ),

                  if (orders.isEmpty)
                    SliverToBoxAdapter(
                      child: StoreEmptyState(
                        title: 'no_store_orders_title'.tr.isEmpty
                            ? 'No store orders yet'
                            : 'no_store_orders_title'.tr,
                        subtitle: 'no_store_orders_subtitle'.tr.isEmpty
                            ? 'When a customer orders from your store, you will see it here.'
                            : 'no_store_orders_subtitle'.tr,
                      ),
                    )
                  else
                    SliverToBoxAdapter(
                      child: LayoutBuilder(
                        builder: (context, _) {
                          final padding = MediaQuery.of(context).padding;
                          final viewportH =
                              MediaQuery.of(context).size.height - padding.top;

                          return ConstrainedBox(
                            constraints: BoxConstraints(minHeight: viewportH),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(24)),
                              ),
                              child: ListView.separated(
                                padding:
                                    const EdgeInsets.fromLTRB(12, 12, 12, 16),
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount: orders.length,
                                separatorBuilder: (_, __) =>
                                    const SizedBox(height: 8),
                                itemBuilder: (context, i) {
                                  final o = orders[i];
                                  final delivery = double.tryParse(
                                          o.deliveryCharge ?? '0') ??
                                      0.0;
                                  final total =
                                      (o.totalOrders ?? 0).toDouble() +
                                          delivery;
                                  return StoreOrderCard(
                                    order: o,
                                    total: total,
                                    onTap: () => Get.to(
                                      () => StoreOrderDetailsPage(order: o),
                                      transition: Transition.rightToLeft,
                                    ),
                                  );
                                },
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }

// À mettre DANS _MyStoreOrdersPageState
  Widget _dateField(BuildContext ctx, String label, TextEditingController c) {
    final theme = Theme.of(ctx);

    return TextFormField(
      controller: c,
      readOnly: true,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: '$label ${'date'.tr.isEmpty ? 'Date' : 'date'.tr}',
        labelStyle: const TextStyle(color: Colors.white70),
        hintText: 'yyyy-mm-dd',
        hintStyle: const TextStyle(color: Colors.white54),
        filled: true,
        fillColor: Colors.white.withOpacity(0.12),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.30)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.white),
        ),
        suffixIcon: IconButton(
          icon: const Icon(Icons.calendar_today, color: Colors.white),
          onPressed: () async {
            final now = DateTime.now();
            final initial =
                (c.text.isNotEmpty) ? DateTime.tryParse(c.text) ?? now : now;

            final d = await showDatePicker(
              context: ctx,
              initialDate: initial,
              firstDate: DateTime(2020),
              lastDate: now,
              builder: (context, child) {
                // Thème du date picker (boutons/accents)
                final cs = Theme.of(context).colorScheme;
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: cs.copyWith(
                      primary: theme.colorScheme.primary,
                      onPrimary: Colors.white,
                      surface: Colors.white,
                      onSurface: Colors.black87,
                    ),
                    dialogBackgroundColor: Colors.white,
                  ),
                  child: child!,
                );
              },
            );

            if (d != null) {
              c.text = d.toIso8601String().split('T').first; // yyyy-mm-dd
            }
          },
        ),
      ),
    );
  }

  /* ───────────── Dialog Filtre (réutilise la logique existante) ───────────── */
  void _showFilterDialog(BuildContext context) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (ctx) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 16, sigmaY: 16),
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.92),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white.withOpacity(0.2)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.25),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  )
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // ===== Header =====
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 14),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.12),
                      border: Border(
                        bottom:
                            BorderSide(color: Colors.white.withOpacity(0.2)),
                      ),
                    ),
                    child: Text(
                      'filter_orders'.tr.isEmpty
                          ? 'Filter Orders'
                          : 'filter_orders'.tr,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        fontSize: 18,
                      ),
                    ),
                  ),

                  // ===== Contenu =====
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _dateField(ctx, 'from'.tr.isEmpty ? 'From' : 'from'.tr,
                            _fromDateCtrl),
                        const SizedBox(height: 16),
                        _dateField(
                            ctx, 'to'.tr.isEmpty ? 'To' : 'to'.tr, _toDateCtrl),
                        const SizedBox(height: 16),
                        Obx(() => DropdownButtonFormField<String>(
                              dropdownColor:
                                  theme.colorScheme.primary.withOpacity(0.95),
                              value: orderController.paymentStatusFilter.value,
                              items: [
                                DropdownMenuItem(
                                  value: '',
                                  child: Text(
                                      'all'.tr.isEmpty ? 'All' : 'all'.tr,
                                      style:
                                          const TextStyle(color: Colors.white)),
                                ),
                                DropdownMenuItem(
                                  value: 'paid',
                                  child: Text(
                                      'paid'.tr.isEmpty ? 'Paid' : 'paid'.tr,
                                      style:
                                          const TextStyle(color: Colors.white)),
                                ),
                                DropdownMenuItem(
                                  value: 'unpaid',
                                  child: Text(
                                      'unpaid'.tr.isEmpty
                                          ? 'Unpaid'
                                          : 'unpaid'.tr,
                                      style:
                                          const TextStyle(color: Colors.white)),
                                ),
                              ],
                              onChanged: (val) => orderController
                                  .paymentStatusFilter.value = val ?? '',
                              decoration: InputDecoration(
                                labelText: 'payment'.tr.isEmpty
                                    ? 'Payment'
                                    : 'payment'.tr,
                                labelStyle:
                                    const TextStyle(color: Colors.white70),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(
                                      color: Colors.white.withOpacity(0.3)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(color: Colors.white),
                                ),
                              ),
                              style: const TextStyle(color: Colors.white),
                              iconEnabledColor: Colors.white,
                            )),
                      ],
                    ),
                  ),

                  // ===== Actions =====
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    child: Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.white,
                              side: BorderSide(
                                  color: Colors.white.withOpacity(0.6)),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                            ),
                            onPressed: () {
                              _fromDateCtrl.clear();
                              _toDateCtrl.clear();
                              orderController.paymentStatusFilter.value = '';
                              orderController.fetchMyStoreOrders();
                              Navigator.pop(ctx);
                            },
                            child:
                                Text('clear'.tr.isEmpty ? 'Clear' : 'clear'.tr),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: theme.colorScheme.primary,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                            ),
                            onPressed: () {
                              orderController.fetchMyStoreOrders(
                                fromDate: _fromDateCtrl.text,
                                toDate: _toDateCtrl.text,
                                paymentStatus:
                                    orderController.paymentStatusFilter.value,
                              );
                              Navigator.pop(ctx);
                            },
                            child:
                                Text('apply'.tr.isEmpty ? 'Apply' : 'apply'.tr),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/* ============================= Widgets “Store” (liste) ============================= */
class StoreGlassHeader extends StatelessWidget {
  final String title;
  final VoidCallback? onBackTap;
  final VoidCallback? onFilterTap;

  const StoreGlassHeader({
    super.key,
    required this.title,
    this.onBackTap,
    this.onFilterTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.25),
              border: Border.all(color: Colors.white.withOpacity(0.35)),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const SizedBox(width: 6),

                // Bouton retour (glass)
                _GlassIconButton(
                  icon: Icons.arrow_back_ios_new_rounded,
                  onTap: onBackTap ?? () => Get.back(),
                ),

                const SizedBox(width: 8),

                // Titre centré
                Expanded(
                  child: Text(
                    title.tr,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                    ),
                  ),
                ),

                // Bouton Filters (glass), même style que retour
                _GlassIconButton(
                  icon: Icons.filter_list_rounded,
                  onTap: onFilterTap ?? () {},
                ),

                const SizedBox(width: 6),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class StoreOrdersShimmerList extends StatelessWidget {
  const StoreOrdersShimmerList({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 16),
      itemCount: 6,
      itemBuilder: (context, index) => const _StoreShimmerCard(),
    );
  }
}

class _StoreShimmerCard extends StatelessWidget {
  const _StoreShimmerCard();

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.white.withOpacity(0.30),
      highlightColor: Colors.white.withOpacity(0.10),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.18),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white24),
        ),
        child: Row(
          children: [
            Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12))),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(height: 14, width: 180, color: Colors.white),
                    const SizedBox(height: 10),
                    Row(children: [
                      Container(height: 12, width: 60, color: Colors.white),
                      const SizedBox(width: 8),
                      Container(height: 12, width: 40, color: Colors.white),
                    ]),
                  ]),
            ),
            const SizedBox(width: 12),
            Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
              Container(height: 14, width: 70, color: Colors.white),
              const SizedBox(height: 8),
              Container(height: 12, width: 40, color: Colors.white),
            ]),
          ],
        ),
      ),
    );
  }
}

class StoreEmptyState extends StatelessWidget {
  final String title;
  final String subtitle;
  const StoreEmptyState(
      {super.key, required this.title, required this.subtitle});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 56, 24, 24),
      child: Column(
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.18),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.35)),
            ),
            child: const Icon(Icons.store_mall_directory_rounded,
                color: Colors.white, size: 56),
          ),
          const SizedBox(height: 16),
          Text(title,
              style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w800)),
          const SizedBox(height: 8),
          Text(subtitle,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.white70)),
        ],
      ),
    );
  }
}

class StoreOrderCard extends StatelessWidget {
  final Order order;
  final double total;
  final VoidCallback onTap;

  const StoreOrderCard(
      {super.key,
      required this.order,
      required this.total,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    final t = Theme.of(context);
    final statusColor = _statusColor(order.status);

    final orderId = order.orderId?.toString() ?? '—';
    final customerName =
        '${order.userFirstName ?? ''} ${order.userLastName ?? ''}'
                .trim()
                .isEmpty
            ? ('customer'.tr.isEmpty ? 'Customer' : 'customer'.tr)
            : '${order.userFirstName ?? ''} ${order.userLastName ?? ''}'.trim();

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: t.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.black12.withOpacity(0.1)),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Vignette / statut
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.10),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: statusColor.withOpacity(0.35)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(12), // espace autour de l’image
                  child: Image.asset(
                    'assets/images/box.png', // <-- ton asset colis
                    fit: BoxFit.contain,
                    // facultatif si tu veux le teinter
                  ),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // Infos
            Expanded(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(children: [
                      Expanded(
                        child: Text(
                          '#$orderId • $customerName',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              fontSize: 15, fontWeight: FontWeight.w700),
                        ),
                      ),
                    ]),
                    const SizedBox(height: 6),
                    Wrap(spacing: 6, runSpacing: 6, children: [
                      _Chip(label: (order.status ?? 'N/A'), color: statusColor),
                      if (order.isPaid)
                        _Chip(
                            label: 'paid'.tr.isEmpty ? 'Paid' : 'paid'.tr,
                            color: Colors.green),
                      if (order.isCashOnDelivery)
                        _Chip(
                            label: 'cash_on_delivery'.tr,
                            color: Colors.blueGrey),
                    ]),
                  ]),
            ),

            const SizedBox(width: 12),

            // Total
            Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
              Text(
                  '${total.toStringAsFixed(2)} ${'mru'.tr.isEmpty ? 'mru' : 'mru'.tr}',
                  style: const TextStyle(
                      fontWeight: FontWeight.w800, fontSize: 16)),
            ]),
          ],
        ),
      ),
    );
  }
}

class _Chip extends StatelessWidget {
  final String label;
  final Color color;
  const _Chip({required this.label, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.10),
        border: Border.all(color: color.withOpacity(0.45)),
        borderRadius: BorderRadius.circular(999),
      ),
      child: Text(
        label.tr,
        style:
            TextStyle(fontSize: 11, fontWeight: FontWeight.w700, color: color),
      ),
    );
  }
}

Color _statusColor(String? status) {
  switch (status) {
    case 'PENDING':
      return Colors.grey;
    case 'ACCEPTED':
      return Colors.blue;
    case 'CANCELLED':
      return Colors.red;
    case 'WAITING_PAYMENT':
      return Colors.deepOrange;
    case 'DELIVERED':
    case 'COMPLETED':
      return Colors.green;
    default:
      return Colors.black26;
  }
}

class _GlassIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _GlassIconButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: InkWell(
          onTap: onTap,
          child: SizedBox(
            width: 40,
            height: 40,
            child: Icon(icon, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

class _BottomSummaryBar extends StatelessWidget {
  final double productsTotal;
  final double delivery;
  final double total;
  final bool isPaid;
  final bool isCOD;
  final bool canPay;
  final bool canCancel;
  final VoidCallback? onPay;
  final VoidCallback? onCancel;
  final VoidCallback? onViewReceipt;
  final bool isPayLoading;
  final bool isCancelLoading;

  const _BottomSummaryBar({
    required this.productsTotal,
    required this.delivery,
    required this.total,
    required this.isPaid,
    required this.isCOD,
    required this.canPay,
    required this.canCancel,
    required this.onPay,
    required this.onCancel,
    required this.onViewReceipt,
    required this.isPayLoading,
    required this.isCancelLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 16,
      shadowColor: Colors.black.withOpacity(0.12),
      child: SafeArea(
        top: false,
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border(top: BorderSide(color: Colors.grey.shade200))),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _row('products_total'.tr,
                  '${productsTotal.toStringAsFixed(2)} ${'mru'.tr}'),
              const SizedBox(height: 8),
              _row('delivery_charge'.tr,
                  '${delivery.toStringAsFixed(2)} ${'mru'.tr}'),
              const SizedBox(height: 8),
              _row('total'.tr, '${total.toStringAsFixed(2)} ${'mru'.tr}',
                  bold: true, color: AppColors.primary),
              const SizedBox(height: 12),
              if (isPaid && onViewReceipt != null) ...[
                ElevatedButton(
                  onPressed: onViewReceipt,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 12),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                  ),
                  child: Text('view_payment_screenshot'.tr),
                ),
              ],
              Row(
                children: [
                  if (canPay)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: isPayLoading ? null : onPay,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black87,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10)),
                        ),
                        child: isPayLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor:
                                        AlwaysStoppedAnimation(Colors.white)))
                            : Text('pay_now'.tr),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _row(String label, String value, {bool bold = false, Color? color}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label),
        Text(value,
            style: TextStyle(
                fontWeight: bold ? FontWeight.w800 : FontWeight.w600,
                color: color))
      ],
    );
  }
}

class _MiniPill extends StatelessWidget {
  final String text;
  const _MiniPill({required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(999),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Text(text,
          style: TextStyle(fontSize: 11, color: Colors.grey.shade800)),
    );
  }
}

class _PaymentScreenshotDialog extends StatefulWidget {
  final String imageUrl;
  const _PaymentScreenshotDialog({required this.imageUrl});

  @override
  State<_PaymentScreenshotDialog> createState() =>
      _PaymentScreenshotDialogState();
}

class _PaymentScreenshotDialogState extends State<_PaymentScreenshotDialog> {
  final TransformationController _transformationController =
      TransformationController();
  TapDownDetails? _doubleTapDetails;
  bool _isLoading = true;
  bool _isError = false;

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  void _onDoubleTap() {
    // Toggle zoom between 1x and 2.2x around the tap point
    final position = _doubleTapDetails!.localPosition;
    const zoom = 2.2;

    final current = _transformationController.value;
    final isZoomed = current.getMaxScaleOnAxis() > 1.01;

    if (isZoomed) {
      _transformationController.value = Matrix4.identity();
    } else {
      final toScene = _transformationController.toScene(position);
      final m = Matrix4.identity()
        ..translate(-toScene.dx * (zoom - 1), -toScene.dy * (zoom - 1))
        ..scale(zoom);
      _transformationController.value = m;
    }
    setState(() {});
  }

  void _retry() {
    setState(() {
      _isError = false;
      _isLoading = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onDark = true; // on force un header clair sur fond flouté

    return SafeArea(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // ---- Backdrop blur
                BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                  child: Container(color: Colors.white.withOpacity(0.08)),
                ),

                // ---- Card container
                Container(
                  width: double.infinity,
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.98,
                    maxHeight: MediaQuery.of(context).size.height * 0.92,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.black.withOpacity(0.12),
                          blurRadius: 22,
                          offset: const Offset(0, 10))
                    ],
                  ),
                  child: Column(
                    children: [
                      // ===== Header “glass”
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10),
                        decoration: BoxDecoration(
                          color: onDark
                              ? theme.colorScheme.primary.withOpacity(0.9)
                              : Colors.white.withOpacity(0.85),
                          border: Border(
                            bottom: BorderSide(
                                color: Colors.black.withOpacity(0.06)),
                          ),
                        ),
                        child: Row(
                          children: [
                            // Close
                            _HeaderIconButton(
                              icon: Icons.close_rounded,
                              color: Colors.white,
                              onTap: () => Navigator.of(context).maybePop(),
                            ),

                            const SizedBox(width: 8),

                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                'payment_screenshot'.tr,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w800,
                                  fontSize: 16,
                                  decoration: TextDecoration.none,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // ===== Image zone (zoomable)
                      Expanded(
                        child: Container(
                          color: Colors.grey.shade50,
                          child: GestureDetector(
                            onDoubleTapDown: (d) => _doubleTapDetails = d,
                            onDoubleTap: _onDoubleTap,
                            child: InteractiveViewer(
                              minScale: 1,
                              maxScale: 4,
                              transformationController:
                                  _transformationController,
                              child: Image.network(
                                widget.imageUrl,
                                fit: BoxFit.contain,
                                // Loading
                                loadingBuilder: (ctx, child, prog) {
                                  if (prog == null) {
                                    // loaded
                                    if (_isLoading) {
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                        if (mounted)
                                          setState(() => _isLoading = false);
                                      });
                                    }
                                    return child;
                                  }
                                  return _ModernLoader(
                                    onDark: false,
                                    label: 'loading_screenshot'.tr,
                                  );
                                },
                                // Error
                                errorBuilder: (_, __, ___) {
                                  if (!_isError) {
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      if (mounted)
                                        setState(() => _isError = true);
                                    });
                                  }
                                  return _ModernError(
                                    title: 'failed_to_load_screenshot'.tr,
                                    onRetry: _retry,
                                    onClose: () =>
                                        Navigator.of(context).maybePop(),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // ---- Soft gradient edges (subtil)
                IgnorePointer(
                  ignoring: true,
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.transparent,
                          Colors.black12
                        ],
                        stops: [0, .85, 1],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _UpdatesCard extends StatelessWidget {
  final String title;
  final List<String> lines;
  const _UpdatesCard({required this.title, required this.lines});

  @override
  Widget build(BuildContext context) {
    if (lines.isEmpty) return const SizedBox.shrink();
    return Container(
      decoration: BoxDecoration(
        color: Colors.blueGrey.withOpacity(0.06),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: Colors.blueGrey.withOpacity(0.25)),
      ),
      padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(children: [
          const Icon(Icons.info_outline_rounded,
              color: Colors.orange, size: 18),
          const SizedBox(width: 6),
          Text(title, style: const TextStyle(fontWeight: FontWeight.w700)),
        ]),
        const SizedBox(height: 8),
        ...lines.map((l) => Padding(
              padding: const EdgeInsets.only(bottom: 6),
              child:
                  Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                const Text('•  ', style: TextStyle(height: 1.2)),
                Expanded(
                    child:
                        Text(l, style: TextStyle(color: Colors.grey.shade800))),
              ]),
            )),
      ]),
    );
  }
}

class _ModernLoader extends StatelessWidget {
  final bool onDark; // si true -> loader blanc
  final String? label;
  const _ModernLoader({this.onDark = false, this.label});

  @override
  Widget build(BuildContext context) {
    final color = onDark ? Colors.white : Colors.black87;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // anneau épais + ombre
            SizedBox(
              width: 36,
              height: 36,
              child: CircularProgressIndicator(
                strokeWidth: 3.2,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ),
            if (label != null) ...[
              const SizedBox(height: 10),
              Text(label!,
                  style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.none)),
            ],
          ],
        ),
      ),
    );
  }
}

// ===================== Modern Error =====================

class _ModernError extends StatelessWidget {
  final String title;
  final VoidCallback onRetry;
  final VoidCallback onClose;
  const _ModernError(
      {required this.title, required this.onRetry, required this.onClose});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(18.0),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.red.withOpacity(0.15)),
            borderRadius: BorderRadius.circular(14),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 14,
                  offset: const Offset(0, 6))
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline_rounded,
                  color: Colors.red.shade400, size: 34),
              const SizedBox(height: 10),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: Colors.grey.shade800,
                    fontSize: 15,
                    fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  OutlinedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh_rounded, size: 18),
                    label: Text('retry'.tr),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.black87,
                      side: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: onClose,
                    icon: const Icon(Icons.close_rounded, size: 18),
                    label: Text('close'.tr),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _HeaderIconButton extends StatelessWidget {
  final IconData icon;
  final Color color;
  final String? tooltip;
  final VoidCallback onTap;
  const _HeaderIconButton(
      {required this.icon,
      required this.color,
      required this.onTap,
      this.tooltip});

  @override
  Widget build(BuildContext context) {
    final btn = InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Icon(icon, size: 22, color: color),
      ),
    );

    return Tooltip(
      message: tooltip ?? '',
      child: Material(
        color: Colors.white.withOpacity(0.10),
        borderRadius: BorderRadius.circular(10),
        child: btn,
      ),
    );
  }
}

class _GlassHeader extends StatelessWidget {
  final String title;

  final VoidCallback? onCancelTap;
  final List<PopupMenuEntry> Function(BuildContext)? extraMenuItemsBuilder;

  const _GlassHeader({
    required this.title,
    this.onCancelTap,
    this.extraMenuItemsBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Container(
        height: 64,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.22),
          border: Border.all(color: Colors.white.withOpacity(0.35)),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            const SizedBox(width: 6),
            _GlassIconButton(
                icon: Icons.arrow_back_ios_new_rounded,
                onTap: () => Get.back()),
            const SizedBox(width: 8),

            // Avatar du store

            // Titre centré
            Expanded(
              child: Text(
                title,
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 18),
              ),
            ),
            const SizedBox(width: 10),
            // Menu 3 points
            _GlassMenuButton(
              itemsBuilder: (ctx) {
                final list = <PopupMenuEntry>[
                  if (onCancelTap != null)
                    PopupMenuItem(
                      child: Row(
                        children: [
                          const Icon(Icons.cancel_outlined, size: 18),
                          const SizedBox(width: 8),
                          Text('cancel_order'.tr),
                        ],
                      ),
                      onTap: () => Future.microtask(() => onCancelTap!()),
                    ),
                ];
                if (extraMenuItemsBuilder != null)
                  list.addAll(extraMenuItemsBuilder!(ctx));
                return list;
              },
            ),
            const SizedBox(width: 6),
          ],
        ),
      ),
    );
  }
}

class _GlassMenuButton extends StatelessWidget {
  final List<PopupMenuEntry> Function(BuildContext) itemsBuilder;
  const _GlassMenuButton({required this.itemsBuilder});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: PopupMenuButton(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          itemBuilder: itemsBuilder,
          color: Colors.white,
          elevation: 4,
        ),
      ),
    );
  }
}

class _GlassStatusBar extends StatelessWidget {
  final String? currentStatus;
  final bool isCOD;
  final bool isPaid; // ✅ Nouveau paramètre

  const _GlassStatusBar({
    required this.currentStatus,
    this.isCOD = false,
    this.isPaid = false,
  });

  // Flux normal avec paiement
  static const _flowWithPayment = <String>[
    'PENDING',
    'ACCEPTED',
    'WAITING_PAYMENT',
    'DELIVERED',
  ];

  // Flux simplifié si COD
  static const _flowWithoutPayment = <String>[
    'PENDING',
    'ACCEPTED',
    'DELIVERED',
  ];

  int _currentIndex(String? s, List<String> flow) {
    var key = (s ?? 'PENDING').toUpperCase();

    // ✅ Si payé → forcer "WAITING_PAYMENT"
    if (isPaid && flow.contains('WAITING_PAYMENT')) {
      key = 'WAITING_PAYMENT';
    }

    if (key == 'COMPLETED') return flow.indexOf('DELIVERED');
    return flow.indexOf(key).clamp(0, flow.length - 1);
  }

  IconData _iconFor(String key) {
    switch (key) {
      case 'PENDING':
        return Icons.hourglass_bottom_rounded;
      case 'ACCEPTED':
        return Icons.check_circle_outline;
      case 'WAITING_PAYMENT':
        return Icons.account_balance_wallet_outlined;
      case 'DELIVERED':
      case 'COMPLETED':
        return Icons.local_shipping_outlined;
      case 'CANCELLED':
        return Icons.cancel_outlined;
      default:
        return Icons.info_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isCancelled = (currentStatus ?? '').toUpperCase() == 'CANCELLED';

    final flow = isCOD ? _flowWithoutPayment : _flowWithPayment;

    if (isCancelled) {
      return _glass(
        child: Row(
          children: [
            _StepChip(
              labelKey: 'PENDING',
              showLabel: false,
              icon: _iconFor('PENDING'),
              active: true,
              color: Colors.white,
              iconSize: 22,
            ),
            Expanded(child: _connector(active: false)),
            _StepChip(
              labelKey: 'CANCELLED',
              showLabel: true,
              icon: _iconFor('CANCELLED'),
              active: true,
              color: Colors.white,
              iconSize: 22,
            ),
          ],
        ),
      );
    }

    final idx = _currentIndex(currentStatus, flow);

    return _glass(
      child: Row(
        children: [
       
   
      for (int i = 0; i < flow.length; i++) ...[
        _StepChip(
          labelKey: flow[i],
          showLabel: (i == idx) || (i == idx + 1), // actuel + suivant
          icon: _iconFor(flow[i]),
          active: i <= idx,
          color: Colors.white,
          iconSize: 20,
          emphasize: i == idx,                     // gras seulement pour l’actuel
          labelOpacity: (i == idx) ? 1.0 : 0.85,   // légère atténuation pour le suivant
        ),
        if (i != flow.length - 1) _connector(active: i < idx),
      ],
   

        ],
      ),
    );
  }

  Widget _glass({required Widget child}) {
    return ClipRRect(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.18),
          border: Border.all(color: Colors.white.withOpacity(0.35)),
        ),
        child: child,
      ),
    );
  }

  Widget _connector({required bool active}) {
    return Expanded(
      child: Container(
        height: 2,
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: active ? Colors.white : Colors.white60,
          borderRadius: BorderRadius.circular(999),
        ),
      ),
    );
  }
}

class _StepChip extends StatelessWidget {
  final String labelKey;
  final bool showLabel;
  final IconData icon;
  final bool active;
  final Color color;
  final double iconSize;

  final bool emphasize;      
  final double labelOpacity; 

  const _StepChip({
    required this.labelKey,
    required this.showLabel,
    required this.icon,
    required this.active,
    required this.color,
    this.iconSize = 18,
    this.emphasize = false,
    this.labelOpacity = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    final iconColor = active ? color : color.withOpacity(0.7);
    final textStyle = TextStyle(
      color: color.withOpacity(labelOpacity),
      fontWeight: emphasize ? FontWeight.w700 : FontWeight.w500,
      fontSize: 12,
    );

    final iconWidget = Icon(icon, size: iconSize, color: iconColor);

    if (!showLabel) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Tooltip(message: labelKey.tr, child: ExcludeSemantics(child: iconWidget)),
        ],
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        iconWidget,
        const SizedBox(width: 6),
        Flexible(
          child: Text(
            labelKey.tr,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: textStyle,
          ),
        ),
      ],
    );
  }
}

class DeliveryMapDialog extends StatelessWidget {
  final double latitude;
  final double longitude;

  final String subtitle; // ex: adresse texte

  const DeliveryMapDialog({
    Key? key,
    required this.latitude,
    required this.longitude,
    required this.subtitle,
  }) : super(key: key);

  Future<void> _openGoogleMaps() async {
    final url = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _openWaze() async {
    final url =
        Uri.parse('https://waze.com/ul?ll=$latitude,$longitude&navigate=yes');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _openAppleMaps() async {
    final url = Uri.parse('http://maps.apple.com/?daddr=$latitude,$longitude');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Widget _buildMapOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Container(
      height: 45,
      margin: EdgeInsets.symmetric(vertical: 6),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              children: [
                FaIcon(icon, size: 20, color: Colors.black),
                SizedBox(width: 12),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (subtitle.trim().isNotEmpty) ...[
              SizedBox(height: 4),
              Text(
                subtitle,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12, color: Colors.black54),
              ),
            ],
            SizedBox(height: 16),
            _buildMapOption(
              icon: FontAwesomeIcons.mapLocationDot,
              label: 'Google Maps',
              onTap: _openGoogleMaps,
            ),
            _buildMapOption(
              icon: FontAwesomeIcons.waze,
              label: 'Waze',
              onTap: _openWaze,
            ),
            if (Platform.isIOS)
              _buildMapOption(
                icon: Icons.map,
                label: 'Apple Maps',
                onTap: _openAppleMaps,
              ),
          ],
        ),
      ),
    );
  }
}
