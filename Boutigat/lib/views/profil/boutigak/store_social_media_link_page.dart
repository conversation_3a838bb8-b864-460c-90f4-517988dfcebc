import 'package:boutigak/controllers/social_media_controller.dart';
import 'package:boutigak/data/models/social_media_link.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';


class SocialMediaLinksPage extends StatelessWidget {
  final int storeId;
  const SocialMediaLinksPage({super.key, required this.storeId});

  @override
  Widget build(BuildContext context) {
    final c = Get.put(SocialMediaController(storeId));

    return Scaffold(
      appBar: AppBar(title: Text('social_store_title'.tr)),
      body: Obx(() {
        if (c.isLoading.value && c.links.isEmpty) {
          return const SocialMediaLinksShimmer();
        }

        if (c.isError.value) {
          return Center(
            child: TextButton(onPressed: c.fetchLinks, child: Text('retry'.tr)),
          );
        }

        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: ListView(
            padding: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 120.h),
            children: [
              _QuickRow(c: c),
              SizedBox(height: 20.h),
              _SectionTitle('configure_links_title'.tr),
              SizedBox(height: 12.h),
              _WhatsAppField(controller: c),
              SizedBox(height: 12.h),
              _TikTokField(controller: c),
              SizedBox(height: 12.h),
              _FacebookField(controller: c),
              SizedBox(height: 12.h),
              _SnapchatField(controller: c),
            ],
          ),
        );
      }),
      bottomNavigationBar: Obx(() {
        final saving = c.isLoading.value;          // n'importe quel chargement
        final changed = _hasChanges(c);            // modifications locales non enregistrées

        // Si pas de changement ET pas de chargement -> on masque le bouton
        if (!changed && !saving) return const SizedBox.shrink();

        return SafeArea(
          minimum: EdgeInsets.all(16.w),
          child: SizedBox(
            width: double.infinity,
            height: 48.h,
            child: FilledButton.icon(
              onPressed: saving
                  ? null
                  : () async {
                      // on déclenche l’enregistrement uniquement si changes
                      if (changed) {
                        final ok = await c.saveAll();
                        if (ok) {
                          Get.snackbar('success'.tr, 'links_saved'.tr);
                        } else {
                          Get.snackbar('error'.tr, 'save_failed'.tr);
                        }
                      }
                    },
              icon: saving
                  ? const SizedBox(
                      width: 18,
                      height: 18,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.save),
              label: Text(saving ? 'saving_in_progress'.tr : 'save'.tr),
              style: FilledButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
          ),
        );
      }),
    );
  }
}

/* =========================
   Helpers d’UI unifiés
   ========================= */

/// Icône uniforme pour les champs (même largeur/hauteur, centré)
Widget _inputIcon({
  IconData? faIcon,
  Color? color,
  String? assetPath,
}) {
  return SizedBox(
    width: 44, // largeur fixe du prefix
    height: 44,
    child: Center(
      child: assetPath != null
          ? Image.asset(
              assetPath,
              width: 20,
              height: 20,
              fit: BoxFit.contain,
            )
          : FaIcon(faIcon!, size: 18, color: color),
    ),
  );
}

// Retourne l'URL existante pour une plateforme, sinon null
String? _existingUrl(SocialMediaController c, String platform) {
  for (final l in c.links) {
    if (l.platform.toLowerCase() == platform.toLowerCase()) {
      return l.link;
    }
  }
  return null;
}

// Détecte s'il y a des changements par rapport aux liens existants
bool _hasChanges(SocialMediaController c) {
  // URLs actuelles voulues selon les champs
  final String? currWa = c.whatsappPhone.value.trim().isEmpty
      ? null
      : c.composeWhatsAppUrl(c.whatsappPhone.value);
  final String? currTt = c.tiktokUser.value.trim().isEmpty
      ? null
      : c.composeTikTokUrl(c.tiktokUser.value);
  final String? currFb = c.facebookUser.value.trim().isEmpty
      ? null
      : c.composeFacebookUrl(c.facebookUser.value);
  final String? currSc = c.snapchatUser.value.trim().isEmpty
      ? null
      : c.composeSnapchatUrl(c.snapchatUser.value);

  // URLs existantes dans la liste
  final String? oldWa = _existingUrl(c, 'whatsapp');
  final String? oldTt = _existingUrl(c, 'tiktok');
  final String? oldFb = _existingUrl(c, 'facebook');
  final String? oldSc = _existingUrl(c, 'snapchat');

  // Un changement = différence de valeur (null vs non-null ou chaîne différente)
  bool diff(String? a, String? b) => a != b;

  return diff(currWa, oldWa) ||
      diff(currTt, oldTt) ||
      diff(currFb, oldFb) ||
      diff(currSc, oldSc);
}

/// InputDecoration améliorée + contraintes identiques pour prefix
InputDecoration _prettyInput(BuildContext context,
    {required String label, String? hint, Widget? prefixIcon, String? helper}) {
  return InputDecoration(
    labelText: label,
    hintText: hint,
    helperText: helper,
    filled: true,
    isDense: true,
    fillColor: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.4),
    contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 14.h),
    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12.r)),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12.r),
      borderSide: BorderSide(color: Theme.of(context).dividerColor),
    ),
    prefixIcon: prefixIcon,
    prefixIconConstraints: const BoxConstraints(
      minWidth: 44,
      minHeight: 44,
      maxWidth: 48, // légère marge
    ),
  );
}

Future<bool> _confirmDeletePlatformDialog(
    BuildContext context, String localizedPlatform) async {
  return await showDialog<bool>(
        context: context,
        builder: (_) => AlertDialog(
          title: Text('delete_link_title'.tr),
          content: Text('delete_link_content'.trArgs([localizedPlatform])),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('cancel'.tr)),
            FilledButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text('delete'.tr)),
          ],
        ),
      ) ??
      false;
}

/* =========================
   Rangée d’icônes rapides
   ========================= */

class _QuickRow extends StatelessWidget {
  final SocialMediaController c;
  const _QuickRow({required this.c});

  @override
  Widget build(BuildContext context) {
    final items = <Map<String, dynamic>>[];

    if (c.whatsappPhone.value.trim().isNotEmpty) {
      items.add({
        'platform': 'whatsapp',
        'icon': FontAwesomeIcons.whatsapp,
        'url': c.composeWhatsAppUrl(c.whatsappPhone.value),
        'color': const Color(0xFF25D366),
      });
    }
    if (c.facebookUser.value.trim().isNotEmpty) {
      items.add({
        'platform': 'facebook',
        'icon': FontAwesomeIcons.facebookF,
        'url': c.composeFacebookUrl(c.facebookUser.value),
        'color': const Color(0xFF1877F2),
      });
    }
    if (c.snapchatUser.value.trim().isNotEmpty) {
      items.add({
        'platform': 'snapchat',
        'icon': 'snapchat',
        'url': c.composeSnapchatUrl(c.snapchatUser.value),
        'color': const Color(0xFFFFFC00),
      });
    }
    if (c.tiktokUser.value.trim().isNotEmpty) {
      items.add({
        'platform': 'tiktok',
        'icon': FontAwesomeIcons.tiktok,
        'url': c.composeTikTokUrl(c.tiktokUser.value),
        'color': const Color(0xFF000000),
      });
    }

    if (items.isEmpty) {
      return Text('quick_actions_empty'.tr,
          style: Theme.of(context).textTheme.bodyMedium);
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: items.map((data) {
        return Expanded(
          child: Container(
            height: 50.w,
            margin: EdgeInsets.symmetric(horizontal: 4.w),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(10.r),
                onLongPress: () async {
                  final platform = data['platform'] as String; // 'whatsapp' | ...
                  final localizedPlatform = () {
                    switch (platform) {
                      case 'whatsapp':
                        return 'platform_whatsapp'.tr;
                      case 'tiktok':
                        return 'platform_tiktok'.tr;
                      case 'facebook':
                        return 'platform_facebook'.tr;
                      case 'snapchat':
                        return 'platform_snapchat'.tr;
                      default:
                        return platform;
                    }
                  }();

                  final yes = await _confirmDeletePlatformDialog(
                      context, localizedPlatform);
                  if (yes) {
                    await c.deletePlatform(platform); // déclenche fetchLinks()
                    // Vide le champ associé pour rafraîchir l'UI immédiatement :
                    switch (platform) {
                      case 'whatsapp':
                        c.whatsappPhone.value = '';
                        break;
                      case 'tiktok':
                        c.tiktokUser.value = '';
                        break;
                      case 'facebook':
                        c.facebookUser.value = '';
                        break;
                      case 'snapchat':
                        c.snapchatUser.value = '';
                        break;
                    }
                  }
                },
                onTap: () async {
                  final uri = Uri.parse(data['url']);
                  if (await canLaunchUrl(uri)) {
                    await launchUrl(uri,
                        mode: LaunchMode.externalApplication);
                  } else {
                    Get.snackbar('error'.tr, 'cannot_open_link'.tr);
                  }
                },
                child: Center(
                  child: data['icon'] == 'snapchat'
                      ? Image.asset('assets/images/snapchat.png',
                          width: 22.w, height: 22.w)
                      : FaIcon(data['icon'], size: 20.sp, color: data['color']),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}

/* =========================
   Champs spécifiques
   ========================= */

class _WhatsAppField extends StatefulWidget {
  final SocialMediaController controller;
  const _WhatsAppField({required this.controller});

  @override
  State<_WhatsAppField> createState() => _WhatsAppFieldState();
}

class _WhatsAppFieldState extends State<_WhatsAppField> {
  late TextEditingController _txt;
  late FocusNode _focusNode;
  bool _isKeyboardVisible = false;

  @override
  void initState() {
    super.initState();
    _txt = TextEditingController(
      text: widget.controller.whatsappPhone.value,
    );
    _focusNode = FocusNode();

    _focusNode.addListener(() {
      setState(() {
        _isKeyboardVisible = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _txt.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final preview = widget.controller.whatsappPhone.value.isEmpty
          ? null
          : widget.controller
              .composeWhatsAppUrl(widget.controller.whatsappPhone.value);

      return TextFormField(
        controller: _txt,
        focusNode: _focusNode,
        keyboardType: const TextInputType.numberWithOptions(
            signed: false, decimal: false),
        textInputAction: TextInputAction.done,
        onEditingComplete: () => FocusScope.of(context).unfocus(),
        onChanged: (v) async {
          var input = v.trim();

          if (input.startsWith("00")) {
            input = "+${input.substring(2)}";
          }
          if (!input.startsWith("+") && input.isNotEmpty) {
            input = "+$input";
          }

          widget.controller.whatsappPhone.value = input;

          _txt.value = TextEditingValue(
            text: input,
            selection: TextSelection.collapsed(offset: input.length),
          );

          if (input.isEmpty &&
              widget.controller.existsPlatform('whatsapp')) {
            final yes = await _confirmDeletePlatformDialog(
                context, 'platform_whatsapp'.tr);
            if (yes) await widget.controller.deletePlatform('whatsapp');
          }
        },
        decoration: _prettyInput(
          context,
          label: 'wa_label'.tr,
          hint: 'wa_hint'.tr,
          helper: preview ?? 'wa_helper_fallback'.tr,
          prefixIcon: _inputIcon(
            faIcon: FontAwesomeIcons.whatsapp,
            color: const Color(0xFF25D366),
          ),
        ).copyWith(
          suffixIcon: _isKeyboardVisible
              ? Padding(
                  padding: const EdgeInsetsDirectional.only(end: 6),
                  child: TextButton(
                    onPressed: () => FocusScope.of(context).unfocus(),
                    child: Text('done'.tr),
                  ),
                )
              : null,
        ),
      );
    });
  }
}

class _TikTokField extends StatefulWidget {
  final SocialMediaController controller;
  const _TikTokField({required this.controller});

  @override
  State<_TikTokField> createState() => _TikTokFieldState();
}

class _TikTokFieldState extends State<_TikTokField> {
  late final TextEditingController _txt;

  @override
  void initState() {
    super.initState();
    _txt = TextEditingController(text: widget.controller.tiktokUser.value);
  }

  @override
  void dispose() {
    _txt.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // si la valeur Rx a changé (ex: suppression), on synchronise le champ
      final desired = widget.controller.tiktokUser.value;
      if (_txt.text != desired) {
        _txt.value = TextEditingValue(
          text: desired,
          selection: TextSelection.collapsed(offset: desired.length),
        );
      }

      final u = desired.trim();
      final preview = u.isEmpty ? null : widget.controller.composeTikTokUrl(u);

      return TextFormField(
        controller: _txt,
        onChanged: (v) async {
          final cleaned = v.replaceAll('@', '');
          widget.controller.tiktokUser.value = cleaned;
          if (cleaned.isEmpty &&
              widget.controller.existsPlatform('tiktok')) {
            final yes = await _confirmDeletePlatformDialog(
                context, 'platform_tiktok'.tr);
            if (yes) await widget.controller.deletePlatform('tiktok');
          }
        },
        decoration: _prettyInput(
          context,
          label: 'tt_label'.tr,
          hint: 'tt_hint'.tr,
          helper: preview,
          prefixIcon: _inputIcon(
            faIcon: FontAwesomeIcons.tiktok,
            color: Colors.black,
          ),
        ),
      );
    });
  }
}

class _FacebookField extends StatefulWidget {
  final SocialMediaController controller;
  const _FacebookField({required this.controller});

  @override
  State<_FacebookField> createState() => _FacebookFieldState();
}

class _FacebookFieldState extends State<_FacebookField> {
  late final TextEditingController _txt;

  @override
  void initState() {
    super.initState();
    _txt = TextEditingController(text: widget.controller.facebookUser.value);
  }

  @override
  void dispose() {
    _txt.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final desired = widget.controller.facebookUser.value;
      if (_txt.text != desired) {
        _txt.value = TextEditingValue(
          text: desired,
          selection: TextSelection.collapsed(offset: desired.length),
        );
      }

      final u = desired.trim();
      final preview = u.isEmpty ? null : widget.controller.composeFacebookUrl(u);

      return TextFormField(
        controller: _txt,
        onChanged: (v) async {
          widget.controller.facebookUser.value = v;
          if (v.trim().isEmpty &&
              widget.controller.existsPlatform('facebook')) {
            final yes = await _confirmDeletePlatformDialog(
                context, 'platform_facebook'.tr);
            if (yes) await widget.controller.deletePlatform('facebook');
          }
        },
        decoration: _prettyInput(
          context,
          label: 'fb_label'.tr,
          hint: 'fb_hint'.tr,
          helper: preview,
          prefixIcon: _inputIcon(
            faIcon: FontAwesomeIcons.facebookF,
            color: const Color(0xFF1877F2),
          ),
        ),
      );
    });
  }
}

class _SnapchatField extends StatelessWidget {
  final SocialMediaController controller;
  const _SnapchatField({required this.controller});

  @override
  Widget build(BuildContext context) {
    final txt = TextEditingController(text: controller.snapchatUser.value);

    return Obx(() {
      final u = controller.snapchatUser.value.trim();
      final preview = u.isEmpty ? null : controller.composeSnapchatUrl(u);

      return TextFormField(
        controller: txt,
        onChanged: (v) async {
          controller.snapchatUser.value = v;
          if (v.trim().isEmpty &&
              controller.existsPlatform('snapchat')) {
            final yes = await _confirmDeletePlatformDialog(
                context, 'platform_snapchat'.tr);
            if (yes) await controller.deletePlatform('snapchat');
          }
        },
        decoration: _prettyInput(
          context,
          label: 'sc_label'.tr,
          hint: 'sc_hint'.tr,
          helper: preview,
          prefixIcon: _inputIcon(
            assetPath: 'assets/images/snapchat.png',
          ),
        ),
      );
    });
  }
}

/* =========================
   Titre de section
   ========================= */

class _SectionTitle extends StatelessWidget {
  final String text;
  const _SectionTitle(this.text, {super.key});

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: Theme.of(context)
          .textTheme
          .titleMedium
          ?.copyWith(fontWeight: FontWeight.w600),
    );
  }
}

/* =========================
   Shimmer
   ========================= */

class SocialMediaLinksShimmer extends StatelessWidget {
  const SocialMediaLinksShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    // Couleurs adaptées au thème
    final base = Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.38);
    final highlight =
        Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.18);

    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: highlight,
      period: const Duration(milliseconds: 1200),
      child: ListView(
        padding: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 120.h),
        children: [
          // --- Rangée d’icônes (4 cases) ---
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(4, (i) {
              return Expanded(
                child: Container(
                  height: 50.w,
                  margin: EdgeInsets.symmetric(horizontal: 4.w),
                  decoration: BoxDecoration(
                    color: base,
                    borderRadius: BorderRadius.circular(10.r),
                    border:
                        Border.all(color: Theme.of(context).dividerColor),
                  ),
                ),
              );
            }),
          ),
          SizedBox(height: 20.h),

          // --- Titre section ---
          _line(width: 160.w, height: 16.h, radius: 6.r),

          SizedBox(height: 14.h),

          // --- Champ WhatsApp ---
          const _FieldShimmer(hasHelper: true),

          SizedBox(height: 12.h),

          // --- Champ TikTok ---
          const _FieldShimmer(hasHelper: true),

          SizedBox(height: 12.h),

          // --- Champ Facebook ---
          const _FieldShimmer(hasHelper: true),

          SizedBox(height: 12.h),

          // --- Champ Snapchat ---
          const _FieldShimmer(hasHelper: true),

          // Espace pour le bouton
          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  // Petite ligne (ex: label / helper / titre)
  Widget _line(
      {required double width,
      required double height,
      required double radius}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white, // sera “shimmerisé”
        borderRadius: BorderRadius.circular(radius),
      ),
    );
  }
}

/// Reproduit un TextField stylé : label, input rempli avec prefix, helper
class _FieldShimmer extends StatelessWidget {
  final bool hasHelper;
  const _FieldShimmer({this.hasHelper = false});

  @override
  Widget build(BuildContext context) {
    final br = 12.r;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Container(
          width: 120.w,
          height: 14.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(6.r),
          ),
        ),
        SizedBox(height: 8.h),

        // Input (prefix 44x44 + champ)
        Container(
          height: 48.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(br),
            border: Border.all(color: Theme.of(context).dividerColor),
          ),
          child: Row(
            children: [
              // prefix placeholder
              Container(
                width: 44,
                height: 48.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(br),
                    bottomLeft: Radius.circular(br),
                  ),
                ),
              ),
              // Champ
              Expanded(
                child: Container(
                  height: 48.h,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.zero,
                      bottomRight: Radius.zero,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Helper (prévisualisation URL)
        if (hasHelper) ...[
          SizedBox(height: 6.h),
          Container(
            width: double.infinity,
            height: 10.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6.r),
            ),
          ),
        ],
      ],
    );
  }
}
