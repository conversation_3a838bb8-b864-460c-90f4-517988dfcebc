import 'dart:io';
import 'dart:ui';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/controllers/sell_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/utils/color_utils.dart';
import 'package:boutigak/views/boutigat_store/orderdeatails_page.dart';
import 'package:boutigak/views/profil/boutigak/boutigak_user_page.dart';
import 'package:boutigak/views/sell/sell_widgets.dart';
import 'package:boutigak/views/widgets/boutigak_loading_widget.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/upload_progress_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import 'package:palette_generator/palette_generator.dart';
import 'package:timeago/timeago.dart' as timeago;


class StoreProductPage extends StatefulWidget {
  @override
  _StoreProductPageState createState() => _StoreProductPageState();
}

class _StoreProductPageState extends State<StoreProductPage> {
  final StoreController storeController = Get.put(StoreController());
  final double expandedHeight = 300.0;

  Color? dominantColor = AppColors.surface;
  Color? surfaceColor = AppColors.surface;
  String? currentImageUrl;

  late final ItemController itemController;


  @override
  void initState() {
    super.initState();

    // Init contrôleurs comme dans StoreDetailsPage
    itemController = Get.put(ItemController());


    // Charger favoris & items
    Future.wait([
      storeController.fetchMyFavoriteCategories(),
      storeController.fetchMyStoreItems(),
    ]).then((_) {
      storeController.selectAllCategories();
    });

    // Charger l'image et couleur dominante
    _initializeStore();
  }

  Future<void> _initializeStore() async {
    await storeController.fetchedMyStoreInformation();

    final store = storeController.myStore.value;
    if (store != null && store.images.isNotEmpty) {
      currentImageUrl = store.images.first;
      await _updateDominantColor(currentImageUrl!);
    }
  }

  bool isDarkColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance < 0.5;
  }

  Future<void> _updateDominantColor(String imageUrl) async {
    final color = await getMostFrequentColor(imageUrl);
    setState(() {
      dominantColor = color;
      surfaceColor = isDarkColor(color) ? Colors.white : Colors.black;
    });
  }

  @override
  Widget build(BuildContext context) {
    final store = storeController.myStore.value;

    return Scaffold(
      body: store != null && store.images.isNotEmpty
          ? NestedScrollView(
              headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
                return [
                  SliverAppBar(
                     leading: Padding(
  padding: const EdgeInsets.all( 8.0),
  child: Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: () => Navigator.of(context).pop(),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        height: 36, // ✅ Taille visible
        width: 36,
        decoration: BoxDecoration(
          color: Colors.white,
         
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Icon(
            Icons.arrow_back,
            size: 20, // ✅ Icône plus petite
            color: Colors.black,
          ),
        ),
      ),
    ),
  ),
),
                    expandedHeight: expandedHeight,
                    pinned: true,
                    floating: true,
                    elevation: 0,
                    backgroundColor: dominantColor ?? AppColors.primary,
                    flexibleSpace: FlexibleSpaceBar(
                      collapseMode: CollapseMode.pin,
                      background: Stack(
                        fit: StackFit.expand,
                        children: [
                          Image.network('${store.images.first}', fit: BoxFit.cover),
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withOpacity(0.1),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    actions: [

                      
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: TextButton(
                          
                          onPressed: () {},
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            
                            foregroundColor: AppColors.onBackground,
                          ),
                          child: Text("${store.followersCount} Followers"),
                        ),
                      ),
                     
                    ],
                  ),
                  SliverPersistentHeader(
                    delegate: _SliverAppBarDelegate(
                      child: FavoriteCategoryListWidget(storeId: store.id!),
                      height: 62,
                    ),
                    pinned: true,
                  ),
                ];
              },
              body: Container(
                color: const Color(0xFFf2f3f5),
                child: Column(
                  children: [
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: () async {
                          // Refresh store data with pull-to-refresh
                          await Future.wait([
                            storeController.refreshStoreItems(),
                            storeController.fetchedMyStoreInformation(),
                            storeController.fetchMyFavoriteCategories(),
                          ]);
                          storeController.selectAllCategories();
                        },
                        child: Obx(() {
                          final selectedCat = storeController.selectedCategory.value;
                          if (selectedCat == null) {
                            return MyStoreitemsListViewWidget(
                              category: null,
                              items: storeController.myItems,
                              storeImage: store.images.first,
                            );
                          } else {
                            final filteredItems = storeController.myItems
                                .where((item) => item.categoryId == selectedCat.id)
                                .toList();
                            return MyStoreitemsListViewWidget(
                              category: selectedCat,
                              items: filteredItems,
                              storeImage: store.images.first,
                            );
                          }
                        }),
                      ),
                    ),
                  ],
                ),
              ),
            )
          : const Center(child: CircularProgressIndicator()),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Get.to(() => SellStorePage());

          // Only refresh if item was successfully added
          if (result == true) {
            // Use the new refresh method for smoother updates
            await Future.wait([
              storeController.refreshStoreItems(),
              storeController.fetchedMyStoreInformation(),
            ]);

            // Update dominant color if store image changed
            final updatedStore = storeController.myStore.value;
            if (updatedStore != null && updatedStore.images.isNotEmpty) {
              await _updateDominantColor(updatedStore.images.first);
            }

            storeController.selectAllCategories();
          }
        },
        backgroundColor: AppColors.background,
        tooltip: 'Ajouter un item',
        child: Icon(Icons.add, color: AppColors.primary),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}



class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _SliverAppBarDelegate({required this.child, required this.height});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return oldDelegate.child != child || oldDelegate.height != height;
  }
}











class FavoriteCategoryListWidget extends StatefulWidget {
  final int storeId;

  const FavoriteCategoryListWidget({
    Key? key,
    required this.storeId,
  }) : super(key: key);

  @override
  _FavoriteCategoryListWidgetState createState() => _FavoriteCategoryListWidgetState();
}

class _FavoriteCategoryListWidgetState extends State<FavoriteCategoryListWidget> {
  final StoreController storeController = Get.find<StoreController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      storeController.fetchStoreFavoriteCategories(widget.storeId.toString());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      color: Theme.of(context).colorScheme.surface,
      child: Obx(() {
        final categories = storeController.FavoriteCategories;

        return ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: categories.length + 2, // +1 for "All", +1 for "+"
          itemBuilder: (context, index) {
            // 🟡 Bouton "All" au début
            if (index == 0) {
              final isSelected = storeController.selectedCategory.value == null;
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: TextButton(
                  onPressed: () {
                    storeController.selectAllCategories();
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.onSurface,
                  ),
                  child: Text(
                    'All',
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              );
            }

            // 🟡 Dernier = bouton "+"
            if (index == categories.length + 1) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Container(
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.add, color: Colors.black),
                    onPressed: () {
                      Get.to(() => CategorySelectionPage(controller: storeController));
                    },
                  ),
                ),
              );
            }

            // 🟡 Catégorie favorite
            final category = categories[index - 1]; // -1 à cause de "All"
            final isSelected = storeController.selectedCategory.value == category;

            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: TextButton(
                onPressed: () {
                  storeController.selectCategory(
                    category,
                    widget.storeId.toString(),
                  );
                },
                onLongPress: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
  title: Text('delete_category_title'.tr),
  content: Text('delete_category_message'.tr),
  actions: [
    TextButton(
      onPressed: () => Navigator.of(context).pop(),
      child: Text('cancel'.tr),
    ),
    TextButton(
      onPressed: () async {
        Navigator.of(context).pop();
        storeController.deleteStoreFavoriteCategory(category.id!);
        setState(() {});
      },
      child: Text('delete'.tr, style: const TextStyle(color: Colors.red)),
    ),
  ],
);

                    },
                  );
                },
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.onSurface,
                ),
                child: Text(
                  category.getTitle(),
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}



class SellStorePage extends StatefulWidget {
  final int? itemId;

  const SellStorePage({Key? key, this.itemId}) : super(key: key);

  @override
  _SellStorePageState createState() => _SellStorePageState();
}

class _SellStorePageState extends State<SellStorePage> {
  final ItemController itemController = Get.put(ItemController(), permanent: true);
  final PhotoActionsController photoController =
      Get.put(PhotoActionsController(Get.find<ItemController>()), permanent: true);
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final RxString uploadStatus = "Preparing upload...".obs;
  final RxDouble uploadProgress = 0.0.obs;
  final RxBool showValidationErrors = false.obs;

  @override
  void initState() {
    super.initState();
    if (widget.itemId != null) {
      itemController.loadItemForEdit(widget.itemId!);
    }
  }

  @override
  void dispose() {
    itemController.clearItemData();
    photoController.clearPhotoActionData();
    itemController.clearFields();
    showValidationErrors.value = false;
    super.dispose();
  }

  void showUploadProgressDialog(BuildContext context, RxString status, RxDouble progress) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Obx(() => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              UploadProgressWidget(
                progress: progress.value,
                status: status.value,
                isComplete: progress.value >= 1.0,
              ),
            ],
          ),
        ),
      )),
    );
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
        title: Text(
          "add item to your store",
          style: TextStyle(
            color: Theme.of(context).colorScheme.surface,
            fontSize: AppTextSizes.heading,
          ),
        ),
        elevation: 0,
        backgroundColor: AppColors.primary,
      ),
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            children: [
              // Main content - widgets handle their own shimmer loading
              PhotoActionsWidget(),
              Obx(() {
                // Show error if no image and validation errors should be shown
                if (photoController.photos.isEmpty && showValidationErrors.value) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Text(
                      "Veuillez ajouter au moins une photo.",
                      style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                    ),
                  );
                }
                return SizedBox.shrink();
              }),
              SizedBox(height: 20),
              ItemDetailsEntryMainLanguage(formKey: _formKey, showValidationErrors: showValidationErrors),
              SizedBox(height: 20),
              ItemDetailsEntryArabic(formKey: _formKey, showValidationErrors: showValidationErrors),
              SizedBox(height: 20),
              ItemDetailsFormWidget(formKey: _formKey, showValidationErrors: showValidationErrors),

              // Category details section - this widget handles its own reactivity
              // For store items, we allow editing category details
              Obx(() {
                if (itemController.selectedCategoryDetails.isEmpty) {
                  return const Center(child: Text(""));
                }
                return CategoryDetailsWidget(formKey: _formKey, showValidationErrors: showValidationErrors);
              }),

            // Upload progress and button section
            Obx(() {
                return Column(
                  children: [
                    if (uploadProgress.value > 0)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                        child: UploadProgressWidget(
                          progress: uploadProgress.value,
                          status: uploadStatus.value,
                          isComplete: uploadProgress.value >= 1.0,
                        ),
                      ),
                    SizedBox(height: 10),
                    CustomButton(
                      text: widget.itemId != null ? "update_item".tr : "upload_item".tr,
                      onPressed: () async {
                        // Enable validation error display
                        showValidationErrors.value = true;

                        // Force rebuild of all form fields to show validation
                        setState(() {});

                        // Wait a frame for the UI to update
                        await Future.delayed(Duration(milliseconds: 100));

                        // Check form validation
                        bool isFormValid = _formKey.currentState!.validate();

                        // Check additional validations
                        bool hasPhotos = photoController.photos.isNotEmpty;
                        bool hasCategory = itemController.categoryID.value.isNotEmpty;

                        if (!isFormValid || !hasPhotos || !hasCategory) {
                          // Show error message
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text("Veuillez remplir tous les champs obligatoires."),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                          return;
                        }

                        uploadProgress.value = 0.0;
                        uploadStatus.value = "Preparing upload...";

                        // Show popup loader dialog
                        if (mounted) {
                          showUploadProgressDialog(context, uploadStatus, uploadProgress);
                        }

                        if (widget.itemId != null) {
                          bool isStoreItem = itemController.selectedItem.value?.storeId != null;

                          if (isStoreItem) {
                            uploadProgress.value = 0.1;
                            uploadStatus.value = "Validating data...";
                            await Future.delayed(Duration(milliseconds: 200));

                            uploadProgress.value = 0.2;
                            uploadStatus.value = "Preparing images...";
                            await Future.delayed(Duration(milliseconds: 200));

                            uploadStatus.value = "Updating item...";

                            await itemController.updateStoreItemWithProgress(widget.itemId!, (progress) {
                              // Map the actual upload progress to 20% - 95% range
                              uploadProgress.value = 0.2 + (progress * 0.75);
                              if (progress > 0.5) {
                                uploadStatus.value = "Finalizing update...";
                              }
                            });

                            uploadProgress.value = 1.0;
                            uploadStatus.value = "Update complete!";
                          } else {
                            await itemController.updateItem(widget.itemId!);
                          }

                          // Brief success display
                          await Future.delayed(Duration(milliseconds: 500));

                          // Close the popup dialog
                          if (mounted) {
                            Navigator.of(context).pop();
                          }

                          // Clear form data
                          itemController.clearItemData();
                          photoController.clearPhotoActionData();
                          itemController.clearFields();

                          // Reset progress
                          uploadProgress.value = 0.0;
                          uploadStatus.value = "";

                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text("item_updated_successfully".tr),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }

                          // Store refresh is now handled by the calling widget
                          Get.back(result: true);
                        } else {
                          try {
                            // Start with realistic progress updates
                            uploadProgress.value = 0.1;
                            uploadStatus.value = "Validating data...";
                            await Future.delayed(Duration(milliseconds: 200));

                            uploadProgress.value = 0.2;
                            uploadStatus.value = "Preparing images...";
                            await Future.delayed(Duration(milliseconds: 200));

                            uploadStatus.value = "Uploading to store...";

                            await itemController.postStoreItemWithProgress((progress) {
                              // Map the actual upload progress to 20% - 95% range
                              uploadProgress.value = 0.2 + (progress * 0.75);
                              if (progress > 0.5) {
                                uploadStatus.value = "Finalizing...";
                              }
                            });

                            uploadProgress.value = 1.0;
                            uploadStatus.value = "Upload complete!";

                            if (itemController.isItemUploaded.value) {
                              // Brief success display then immediate navigation
                              await Future.delayed(Duration(milliseconds: 500));

                              // Close the popup dialog
                              if (mounted) {
                                Navigator.of(context).pop();
                              }

                              // Clear form data
                              itemController.clearItemData();
                              photoController.clearPhotoActionData();
                              itemController.clearFields();

                              // Reset progress
                              uploadProgress.value = 0.0;
                              uploadStatus.value = "";

                              // Show success message
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text("item_uploaded_successfully".tr),
                                    backgroundColor: Colors.green,
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              }

                              // Store refresh is now handled by the calling widget
                              Get.back(result: true);
                            }
                          } catch (e) {
                            uploadStatus.value = "Upload failed. Please try again.";

                            // Close the popup dialog
                            if (mounted) {
                              Navigator.of(context).pop();
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text("Error uploading item: ${e.toString()}"),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        }
                      },
                    ),
                  ],
                );
              }),
              SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }
}

class CategorySelectionPage extends StatefulWidget {
  final StoreController controller;
  final Category? initialParent;

  const CategorySelectionPage({
    Key? key,
    required this.controller,
    this.initialParent,
  }) : super(key: key);

  /// Helper: ouvrir en **un seul** bottom sheet (coins arrondis)
  static Future<void> show(
    BuildContext context, {
    required StoreController controller,
    Category? parent,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      builder: (_) => ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
        child: FractionallySizedBox(
          heightFactor: 0.98,
          child: Material(
            color: Theme.of(context).colorScheme.surface,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const _FavGrabber(),
                Expanded(
                  child: CategorySelectionPage(
                    controller: controller,
                    initialParent: parent,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  State<CategorySelectionPage> createState() => _CategorySelectionPageState();
}

class _CategorySelectionPageState extends State<CategorySelectionPage> {
  Category? _currentParent;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocus = FocusNode();
  String _query = '';

  @override
  void initState() {
    super.initState();
    _currentParent = widget.initialParent;
    if (_currentParent == null) {
      widget.controller.fetchCategories();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocus.dispose();
    super.dispose();
  }

  void _onSearchChanged(String value) {
    setState(() => _query = value.trim().toLowerCase());
  }

  @override
  Widget build(BuildContext context) {
    final title = _currentParent == null
        ? 'select_category'.tr
        : 'select_subcategory_of'.trParams({'name': _currentParent!.getTitle()});

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Theme.of(context).colorScheme.surface,
        leadingWidth: 48,
        leading: _currentParent == null
            ? IconButton(
                icon: const Icon(Icons.close),
                tooltip: 'close'.tr,
                onPressed: () => Navigator.of(context).maybePop(),
              )
            : IconButton(
                icon: const Icon(Icons.arrow_back_ios_new_rounded),
                tooltip: 'back'.tr,
                onPressed: () {
                  setState(() {
                    _currentParent = _findParentOf(_currentParent);
                  });
                },
              ),
        title: Text(title),
      ),
      body: Column(
        children: [
          // --- Barre de recherche (style iOS) ---
          Padding(
            padding: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 10.h),
            child: CupertinoTextField(
              controller: _searchController,
              focusNode: _searchFocus,
              placeholder: 'search'.tr,
              prefix: IconButton(
                icon: Icon(Icons.search,
                    color: Theme.of(context).colorScheme.onSurface),
                onPressed: () => _onSearchChanged(_searchController.text),
              ),
              suffix: IconButton(
                icon: Icon(Icons.clear,
                    color: Theme.of(context).colorScheme.onSurface),
                onPressed: () {
                  _searchController.clear();
                  _onSearchChanged('');
                  _searchFocus.unfocus();
                },
              ),
              onChanged: _onSearchChanged,
              onSubmitted: (v) {
                _onSearchChanged(v);
                _searchFocus.unfocus();
              },
            ),
          ),
          const _FavModernDivider(),

          Expanded(child: _buildBody()),
        ],
      ),
    );
  }

  Widget _buildBody() {
    // Racine: on observe le chargement via Obx
    if (_currentParent == null) {
      return Obx(() {
        final isLoading = widget.controller.isLoading.value;
        final all = widget.controller.categories;

        if (all.isEmpty && isLoading) {
          return const _FavCategorySkeleton();
        }

        final root = all.where((c) => c.parentId == null).toList();
        final list = _filter(root);

        if (list.isEmpty) {
          return _FavEmptyState(
            title: 'no_category_found'.tr,
            subtitle: _query.isEmpty
                ? 'choose_category_to_continue'.tr
                : 'try_another_keyword'.tr,
            icon: Icons.inventory_2_outlined,
          );
        }

        return _FavCategoryListView(
          categories: list,
          onTapParent: (cat) => setState(() => _currentParent = cat),
          onSelectLeaf: _selectLeaf,
        );
      });
    }

    // Sous-catégories (déjà sous l'objet parent)
    final children = _currentParent!.children;
    final list = _filter(children);

    if (list.isEmpty) {
      return _FavEmptyState(
        title: 'no_category_found'.tr,
        subtitle: _query.isEmpty
            ? 'choose_category_to_continue'.tr
            : 'try_another_keyword'.tr,
        icon: Icons.inventory_2_outlined,
      );
    }

    return _FavCategoryListView(
      categories: list,
      onTapParent: (cat) => setState(() => _currentParent = cat),
      onSelectLeaf: _selectLeaf,
    );
  }

  List<Category> _filter(List<Category> src) {
    if (_query.isEmpty) return src;
    return src.where((c) => c.getTitle().toLowerCase().contains(_query)).toList();
  }

  void _selectLeaf(Category cat) {
    // 👉 logique spécifique favoris
    widget.controller.addFavoriteCategory(cat.id!);
    Navigator.of(context).maybePop(); // ferme le seul sheet
  }

  /// Retrouver le parent d'une catégorie (depuis la liste globale du controller)
  Category? _findParentOf(Category? child) {
    if (child == null) return null;
    if (child.parentId == null) return null; // déjà à la racine
    for (final c in widget.controller.categories) {
      if (c.id == child.parentId) return c;
    }
    return null; // fallback: racine
  }
}

/// ===============================
/// LIST VIEW (design moderne)
/// ===============================
class _FavCategoryListView extends StatelessWidget {
  final List<Category> categories;
  final void Function(Category parent) onTapParent;
  final void Function(Category leaf) onSelectLeaf;

  const _FavCategoryListView({
    required this.categories,
    required this.onTapParent,
    required this.onSelectLeaf,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: categories.length,
      separatorBuilder: (_, __) => const _FavModernDivider(),
      itemBuilder: (_, i) {
        final cat = categories[i];
        final hasChildren = cat.children.isNotEmpty;

        return InkWell(
          onTap: () => hasChildren ? onTapParent(cat) : onSelectLeaf(cat),
          child: SizedBox(
            height: 52,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      cat.getTitle(),
                      style: Theme.of(context).textTheme.bodyMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: Center(
                      child: hasChildren
                          ? Icon(
                              Icons.chevron_right_rounded,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            )
                          : Icon(
                              Icons.radio_button_unchecked,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// ===============================
/// EMPTY + SKELETON
/// ===============================
class _FavEmptyState extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  const _FavEmptyState({
    required this.title,
    required this.subtitle,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final onVar = Theme.of(context).colorScheme.onSurfaceVariant;
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 64.sp, color: onVar),
            SizedBox(height: 12.h),
            Text(title, style: Theme.of(context).textTheme.titleSmall),
            SizedBox(height: 4.h),
            Text(
              subtitle,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall
                  ?.copyWith(color: onVar),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _FavCategorySkeleton extends StatelessWidget {
  const _FavCategorySkeleton();

  @override
  Widget build(BuildContext context) {
    final base =
        Theme.of(context).colorScheme.surfaceVariant.withOpacity(.35);
    return ListView.builder(
      itemCount: 10,
      itemBuilder: (_, i) => Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        height: 48.h,
        decoration: BoxDecoration(
          color: base,
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }
}

class _FavGrabber extends StatelessWidget {
  const _FavGrabber();

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 6),
      child: Center(
        child: Container(
          width: 42,
          height: 4,
          decoration: BoxDecoration(
            color: c.outlineVariant,
            borderRadius: BorderRadius.circular(999),
          ),
        ),
      ),
    );
  }
}

class _FavModernDivider extends StatelessWidget {
  const _FavModernDivider({this.indent = 0, this.endIndent = 0});
  final double indent;
  final double endIndent;

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Container(
      height: 0.5,
      margin: EdgeInsetsDirectional.only(start: indent, end: endIndent),
      color: c.outlineVariant.withOpacity(0.3),
    );
  }
}














class MyStoreitemsListViewWidget extends StatelessWidget {
  final Category? category;
  final List<Item> items;
  final String storeImage;

  MyStoreitemsListViewWidget({
    Key? key,
    this.category,
    required this.items,
    required this.storeImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final StoreController storeController = Get.find<StoreController>();

    // print('item id: ${items[0].id}');
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Obx(() {
      // Show loader when items are being fetched
      if (storeController.isLoadingItems.value) {
        return _buildShimmerEffect(context, screenWidth, sidePadding);
      }

      // Show "no items" message only after loading is complete
      if (items.isEmpty) {
        return Center(
          child: Text("No items available"),
        );
      }

      return _buildItemsList(context, screenWidth, sidePadding);
    });
  }

  Widget _buildShimmerEffect(
      BuildContext context, double screenWidth, double sidePadding) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Container(
                height: 20,
                width: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: _shimmerAnimation(),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: EdgeInsets.only(
              left: sidePadding, right: sidePadding, bottom: 20),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 344 / 517,
              crossAxisSpacing: sidePadding,
              mainAxisSpacing: 10,
            ),
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                return _buildShimmerItem(context, screenWidth);
              },
              childCount: 6, // Show 6 shimmer items
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerItem(BuildContext context, double screenWidth) {
    return SizedBox(
      width: screenWidth * 0.4388,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shimmer image placeholder
          Container(
            width: screenWidth * 0.4388,
            height: screenWidth * 0.5485,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: _shimmerAnimation(),
          ),
          SizedBox(height: 8),
          // Shimmer text placeholders
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 13,
                width: screenWidth * 0.3,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: _shimmerAnimation(),
              ),
              SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    height: 13,
                    width: 50,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: _shimmerAnimation(),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _shimmerAnimation() {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.3, end: 1.0),
      duration: Duration(milliseconds: 1000),
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.grey[300]!.withOpacity(0.3),
                Colors.grey[100]!.withOpacity(value),
                Colors.grey[300]!.withOpacity(0.3),
              ],
              stops: [0.0, 0.5, 1.0],
            ),
          ),
        );
      },
      onEnd: () {
        // Animation will automatically repeat due to the way TweenAnimationBuilder works
      },
    );
  }

  Widget _buildItemsList(
      BuildContext context, double screenWidth, double sidePadding) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                "${items.length} "+ "products".tr,
                style: const TextStyle(
                    fontSize: 16, fontWeight: FontWeight.normal),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: EdgeInsets.only(
              left: sidePadding, right: sidePadding, bottom: 20),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 344 / 560,
              crossAxisSpacing: sidePadding,
              mainAxisSpacing: 10,
            ),
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                return _buildItemWidget(context, items[index], screenWidth);
              },
              childCount: items.length,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemWidget(BuildContext context, Item item, double screenWidth) {
    return SizedBox(
      width: screenWidth * 0.4388,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              _showModalBottomSheet(context, item, storeImage);
            },
            child: Stack(
              children: [
                Container(
                  width: screenWidth * 0.4388,
                  height: screenWidth * 0.5485,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child:   CachedImageWidget(
                  imageUrl:
                      item.images.isNotEmpty ? '${item.images.first}' : '',
                  width: screenWidth * 0.4388,
                  height: screenWidth * 0.5485,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(8),
                ),
                ),
                if (item.hasPromotion ?? false)
                  Positioned(
                    top: 10,
                    left: 10,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                         borderRadius: BorderRadius.all( Radius.circular(5),),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.yellow,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(Icons.discount,
                                color: Theme.of(context).colorScheme.onSurface,
                                size: 15),
                          ),
                          SizedBox(width: 4),
                          Text(
                            "${item.promotionPercentage!.toStringAsFixed(0)}% Discount",
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontSize: 10,
                              fontWeight: AppFontWeights.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(height: screenWidth * 0.0407),
          Text(
            item.title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Row(
              mainAxisSize: MainAxisSize.min, // Évite de prendre tout l’espace
              children: [
                if (item.hasPromotion) ...[
                  Text(
                    '${(item.price / (1 - item.promotionPercentage! / 100)).toStringAsFixed(0)} mru',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: const Color.fromARGB(
                          255, 131, 131, 131), // Prix avant remise en gris
                      decoration: TextDecoration.lineThrough, // Barré
                    ),
                  ),
                  SizedBox(width: 5), // Espacement entre les prix
                ],
                Text(
                  '${item.price.toStringAsFixed(0)} mru',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary, // Prix après réduction
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addToOrder(Item item, int quantity, BuildContext context) {
    // Initialize OrderController with lazyPut to ensure it's only created when needed
    final OrderController orderController =
        Get.put(OrderController(initialOrderId: 1));
    final ItemController itemController = Get.put(ItemController());

    // Find if item exists in the order
    var existingOrderItem = orderController.items.firstWhereOrNull(
      (orderItem) => orderItem.itemId.value == item.id,
    );

    // If item exists, increment quantity
    if (existingOrderItem != null) {
      existingOrderItem.incrementQuantity();
    } else {
      // Add new item to order
      var newItem = OrderItemController(
        initialItemId: item.id!,
        initialQuantity: quantity,
      );
      orderController.addItem(newItem);
    }

    // Recalculate the total
    orderController.calculateTotal();

    Navigator.pop(context);
    orderController.update();
  }

  void _showModalBottomSheet(
      BuildContext context, Item item, String storeImage) {
    PageController pageController = PageController();
    int currentPage = 0;

    int quantity = 1;
    OrderController orderController =
        Get.put(OrderController(initialOrderId: 1));
    ItemController itemController = Get.put(ItemController());
    TextEditingController promotionController = TextEditingController();

    bool isLoading = true;

    showModalBottomSheet(
      backgroundColor: Theme.of(context).colorScheme.surface,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {

 if (isLoading) {
              Future.delayed(Duration(milliseconds: 1500), () {
                if (context.mounted) {
                  setState(() {
                    isLoading = false;
                  });
                }
              });
            }
                  if (isLoading) {
              return buildShimmerContent(context);
            }

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    Container(
                      width: double.infinity,
                      height: MediaQuery.of(context).size.height * .47,
                      child: ImageSlider(
                        pageController: pageController,
                        photos: item.images.map((image) => '$image').toList(),
                        currentPage: currentPage,
                        onPageChanged: (int page) =>
                            setState(() => currentPage = page),
                        borderRadius: 0,
                      ),
                    ),
                    Positioned(
                      top: 60,
                      right: 10,
                      child: ClipOval(
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                          child: Container(
                            color: AppColors.onBackground.withOpacity(0.2),
                            child: IconButton(
                              iconSize: 20,
                              icon: const Icon(
                                  FontAwesomeIcons.upRightFromSquare,
                                  color: AppColors.background),
                              onPressed: () => print('Share Icon Tapped!'),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 60,
                      left: 10,
                      child: ClipOval(
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                          child: Container(
                            color: AppColors.onBackground.withOpacity(0.3),
                            child: IconButton(
                              iconSize: 20,
                              icon: const Icon(FontAwesomeIcons.chevronDown,
                                  color: AppColors.background),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                // Scrollable info section
                Expanded(
                  child: Container(
                    width: double.infinity,
                    color: Colors.transparent,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.only(top: 0, left: 0, right: 0, bottom: 0),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(15)),
                        ),
                        child: MyStoreInfoSectionWidget(
                          boutiquePhotoUrl: storeImage,
                          item: item,
                          orderController: orderController,
                          dominantColor: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),

                // Bottom section with actions
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(
                        bottom: 30.0, left: 25, right: 25, top: 10),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Price (mru)",
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: Theme.of(context).disabledColor,
                                  ),
                                ),
                                Text(
                                  "${(item.price * quantity).toStringAsFixed(0)} ",
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface,
                                  ),
                                ),
                              ],
                            ),
                            Spacer(),
                            Row(
                              children: [
                                Container(
                                  width: 80,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        spreadRadius: 2,
                                        blurRadius: 5,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: TextButton(
                                    onPressed: () async {
                                      // Close the bottom sheet first for smoother navigation
                                      Navigator.of(context).pop();

                                      // Navigate to edit page and handle return
                                      final result = await Get.to(() => SellStorePage(itemId: item.id));

                                      // Refresh store data when returning from edit
                                      if (result == true) {
                                        final storeController = Get.find<StoreController>();
                                        // Use the new refresh method for smoother updates
                                        await storeController.refreshStoreItems();
                                        storeController.selectAllCategories();
                                      }
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                    ),
                                    child: Container(
                                      alignment: Alignment.center,
                                      child: const Text(
                                        "Edit",
                                        style: TextStyle(
                                          color: AppColors.surface,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10),
                                Container(
                                  width: 120,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        spreadRadius: 2,
                                        blurRadius: 5,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: TextButton(
                                    onPressed: () async {
                                      _showPromotionDialog(context, item);
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                    ),
                                    child: Container(
                                      alignment: Alignment.center,
                                      child: Text(
                                        item.hasPromotion
                                            ? "Promo: ${item.promotionPercentage}%" // Si promotion active, affiche le pourcentage
                                            : "Set Promotion", // Sinon affiche "Set Promotion"
                                        style: const TextStyle(
                                          color: AppColors.surface,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10),
                                Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: AppColors.error,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        spreadRadius: 2,
                                        blurRadius: 5,
                                        offset: Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: TextButton(
                                    onPressed: () async {
                                      showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return AlertDialog(
                                            title: const Text("Confirm Deletion"),
                                            content: const Text(
                                                "Are you sure you want to delete this item?"),
                                            actions: [
                                              TextButton(
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                },
                                                child: const Text("Cancel"),
                                              ),
                                              ElevatedButton(
                                                onPressed: () async {
                                                  // Close dialogs first
                                                  Navigator.of(context).pop(); // Ferme le dialog
                                                  Navigator.of(context).pop(); // Ferme le bottom sheet

                                                  // Delete item and refresh list
                                                  await itemController.deleteStoreItem(item.id!);
                                                  final storeController = Get.find<StoreController>();
                                                  await storeController.refreshStoreItems();
                                                  storeController.selectAllCategories();
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor: AppColors.error,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(15),
                                                  ),
                                                ),
                                                child: const Text(
                                                  "Delete",
                                                  style: TextStyle(
                                                      color: AppColors.surface),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                    ),
                                    child: Container(
                                      alignment: Alignment.center,
                                      child: const Icon(
                                        Icons.delete,
                                        color: AppColors.surface,
                                        size: 24,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

// 🎯 **Fonction qui affiche la boîte de dialogue pour entrer le pourcentage de promotion**
  void _showPromotionDialog(BuildContext context, Item item) {
    TextEditingController promotionController = TextEditingController();
    final StoreController storeController = Get.put(StoreController());
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text("Set Promotion"),
          content: TextField(
            controller: promotionController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(hintText: "Enter discount percentage"),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("Cancel"),
            ),
            TextButton(
              onPressed: () async {
                double? discount = double.tryParse(promotionController.text);
                if (discount != null && discount > 0) {
                  await storeController.setPromotionForItem(item.id!, discount);
                  Navigator.pop(context); // Fermer le dialogue après validation
                } else {
                  // Get.snakbar("Error", "Please enter a valid discount percentage");
                }
              },
              child: Text("Apply"),
            ),
            if (item.hasPromotion)
              Container(
                width: 140,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.redAccent,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextButton(
                  onPressed: () async {
                    await storeController.removePromotionForItem(item.id!);
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Container(
                    alignment: Alignment.center,
                    child: const Text(
                      "Remove Promotion",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

class MyStoreInfoSectionWidget extends StatefulWidget {
  final String boutiquePhotoUrl;
  final Item item;
  final OrderController orderController;

  final Color? dominantColor;

  MyStoreInfoSectionWidget({
    Key? key,
    required this.boutiquePhotoUrl,
    required this.item,
    required this.orderController,
    this.dominantColor,
  }) : super(key: key);

  @override
  _MyStoreInfoSectionWidgetState createState() =>
      _MyStoreInfoSectionWidgetState();
}

class _MyStoreInfoSectionWidgetState extends State<MyStoreInfoSectionWidget> {
  bool isDescriptionSelected = true;

  @override
  Widget build(BuildContext context) {
     final locale = Get.locale?.languageCode ?? 'en';
final createdAtText = widget.item.createdAt != null
    ? timeago.format(widget.item.createdAt!, locale: locale)
    : 'unknown'.tr;
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: ListView(
        shrinkWrap: true,
        primary: false,
        children: [
          // Title + Store info
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Titre + Vendeur
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Text(widget.item.title,
                    //     style: TextStyle(
                    //         fontSize: 18, fontWeight: FontWeight.bold)),
                    // const SizedBox(height: 4),

                        Row(
            children: [
              Container(
                width: 50.0,
                height: 50.0,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    image: DecorationImage(
                      image: NetworkImage(widget.boutiquePhotoUrl),
                      fit: BoxFit.cover,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: Offset(0.5, 0.5), // Position de l'ombre
                      ),
                    ]),
              ),
              SizedBox(width: 10),
              Expanded(
                child: Text(
                  widget.item.getTitle(),
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
         
            ],
          ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Date
          Row(
            children: [
              const Spacer(),
             Text(
  createdAtText,
  style: TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.bold,
    color: Theme.of(context).primaryColor,
  ),
),
            ],
          ),

          const SizedBox(height: 16),

          // Description Section
          Text(
            "Description".tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.item.getDescription(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.medium,
            ),
          ),

          const SizedBox(height: 24),

          // Details Section
           Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Details".tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 12),
                _buildDetailRow("Condition:", widget.item.condition),
                _buildDetailRow("Category:", widget.item.categoryName ?? "Unknown"),
                _buildDetailRow("Brand:", widget.item.brandName ?? "Unknown"),
                // Add category details if available
                if (widget.item.categoryItemDetails.isNotEmpty)
                  ...widget.item.categoryItemDetails.map<Widget>((detail) {
                    // Pick label based on locale, fallback to English
                    String label = detail.labelEn;
                    if (Get.locale?.languageCode == 'ar') {
                      label = detail.labelAr;
                    } else if (Get.locale?.languageCode == 'fr') {
                      label = detail.labelFr;
                    }
                    String value = detail.value.toString();
                    return _buildDetailRow("$label:", value);
                  }),
              ],
            ),
          ),
        ],
      ),
    );
  }

 

   Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

}

class InfoSectionWidget extends StatefulWidget {
  final String boutiquePhotoUrl;
  final Item item;
  // final OrderController orderController;
  final bool isFavorited;
  final Color? dominantColor;
  final void Function()? toggleFavorite;

  InfoSectionWidget({
    Key? key,
    required this.boutiquePhotoUrl,
    required this.item,
    //  required this.orderController,
    required this.isFavorited,
    required this.toggleFavorite,
    this.dominantColor,
  }) : super(key: key);

  @override
  _InfoSectionWidgetState createState() => _InfoSectionWidgetState();
}

class _InfoSectionWidgetState extends State<InfoSectionWidget> {
  bool isDescriptionSelected = true;

  @override
  Widget build(BuildContext context) {
    final locale = Get.locale?.languageCode ?? 'en';
final createdAtText = widget.item.createdAt != null
    ? timeago.format(widget.item.createdAt!, locale: locale)
    : 'unknown'.tr;
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: ListView(
        shrinkWrap: true,
        primary: false,
        children: [


    Row(
            children: [
              Container(
                width: 50.0,
                height: 50.0,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    image: DecorationImage(
                      image: NetworkImage(widget.boutiquePhotoUrl),
                      fit: BoxFit.cover,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: Offset(0.5, 0.5), // Position de l'ombre
                      ),
                    ]),
              ),
              SizedBox(width: 10),
              Expanded(
                child: Text(
                  widget.item.getTitle(),
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              IconButton(
                icon: Icon(
                  widget.isFavorited ? Icons.favorite : Icons.favorite_border,
                  size: 30,
                  color: Colors.red,
                ),
                onPressed: widget.toggleFavorite,
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Date
          Row(
            children: [
              const Spacer(),
             Text(
  createdAtText,
  style: TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.bold,
    color: Theme.of(context).primaryColor,
  ),
),
              
            ],
          ),

          const SizedBox(height: 16),

          // Description Section
          Text(
            "Description".tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.item.getDescription(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.medium,
            ),
          ),

          const SizedBox(height: 24),

          // Details Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Details".tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 12),
                _buildDetailRow("condition".tr, widget.item.condition),
                _buildDetailRow(
                    "category".tr, widget.item.categoryName ?? "Unknown"),
                _buildDetailRow("Brand:", widget.item.brandName ?? "Unknown"),
                // Add category details if available
                if (widget.item.categoryItemDetails.isNotEmpty)
                  ...widget.item.categoryItemDetails.map<Widget>((detail) {
                    // Pick label based on locale, fallback to English
                    String label = detail.labelEn;
                    if (Get.locale?.languageCode == 'ar') {
                      label = detail.labelAr;
                    } else if (Get.locale?.languageCode == 'fr') {
                      label = detail.labelFr;
                    }
                    String value = detail.value.toString();
                    return _buildDetailRow("$label:", value);
                  }),
              ],
            ),
          ),
    
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
} /* ───────────── Statut → Couleur ───────────── */




