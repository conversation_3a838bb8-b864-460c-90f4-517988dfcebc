import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/profil/boutigak/boutigak_user_page.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
class EditStorePage extends StatefulWidget {
  const EditStorePage({Key? key}) : super(key: key);

  @override
  State<EditStorePage> createState() => _EditStorePageState();
}

class _EditStorePageState extends State<EditStorePage> {
  final StoreController storeController = Get.find<StoreController>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController fromDateController = TextEditingController();
  final TextEditingController toDateController = TextEditingController();

  TimeOfDay? openingTime;
  TimeOfDay? closingTime;
  bool isTimePickerOpen = false;
  File? selectedImageFile;

  @override
  void initState() {
    super.initState();

    // Charger les types si nécessaire
    if (storeController.storeTypes.isEmpty) {
      storeController.fetchStoreTypes();
    }

    // Pré-remplir depuis la boutique actuelle
    final s = storeController.myStore.value;
    if (s != null) {
      storeController.name.value = s.name;
      storeController.description.value = s.description ?? '';
      storeController.type.value = s.typeId?.toString() ?? '';
      storeController.openingHours.value = s.openingTime ?? '09:00';
      storeController.closingHours.value = s.closingTime ?? '21:00';

      nameController.text = storeController.name.value;
      descriptionController.text = storeController.description.value;
    } else {
      // Valeurs par défaut si jamais
      storeController.openingHours.value = storeController.openingHours.value.isNotEmpty
          ? storeController.openingHours.value
          : '09:00';
      storeController.closingHours.value = storeController.closingHours.value.isNotEmpty
          ? storeController.closingHours.value
          : '21:00';
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
    fromDateController.dispose();
    toDateController.dispose();
    super.dispose();
  }

  InputDecoration inputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      isDense: true,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(14)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
    );
  }

  Future<void> _selectTime(BuildContext context, bool isOpeningTime) async {
    if (isTimePickerOpen) return;
    isTimePickerOpen = true;

    TimeOfDay initialTime;
    try {
      final src = isOpeningTime
          ? storeController.openingHours.value
          : storeController.closingHours.value;
      final parts = src.split(':');
      final h = int.tryParse(parts.first) ?? (isOpeningTime ? 9 : 21);
      final m = parts.length > 1 ? int.tryParse(parts[1]) ?? 0 : 0;
      initialTime = TimeOfDay(hour: h, minute: m);
    } catch (_) {
      initialTime = TimeOfDay(hour: isOpeningTime ? 9 : 21, minute: 0);
    }

    final pickedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (pickedTime != null) {
      final text = '${pickedTime.hour.toString().padLeft(2, '0')}:${pickedTime.minute.toString().padLeft(2, '0')}';
      setState(() {
        if (isOpeningTime) {
          openingTime = pickedTime;
          storeController.openingHours.value = text;
        } else {
          closingTime = pickedTime;
          storeController.closingHours.value = text;
        }
      });
    }

    isTimePickerOpen = false;
  }

  void _showLoadingDialog(BuildContext context) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) => Container(
        color: Colors.black.withOpacity(0.4),
        child: const Center(
          child: CupertinoTheme(
            data: CupertinoThemeData(brightness: Brightness.dark),
            child: CupertinoActivityIndicator(radius: 15),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final c1 = theme.colorScheme.primary;
    final c2 = theme.colorScheme.primary.withOpacity(0.95);

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      appBar: AppBar(
        title: Text('${'edit'.tr} ${'store'.tr}'),
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // ===== Dégradé cohérent =====
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [c1, c2],
                ),
              ),
            ),
          ),

          Obx(() {
            final isLoading = storeController.isLoading.isTrue && storeController.storeTypes.isEmpty;
            final myStore = storeController.myStore.value;
            final hasNetImage = (myStore?.images.isNotEmpty ?? false);

            return CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // ----- Header translucide -----
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                    child: Material(
                      color: Colors.white.withOpacity(0.10),
                      elevation: 0,
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.white.withOpacity(0.25)),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              width: 56,
                              height: 56,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withOpacity(0.28),
                                    Colors.white.withOpacity(0.12),
                                  ],
                                ),
                                border: Border.all(color: Colors.white.withOpacity(0.35)),
                              ),
                              child: const Icon(Icons.edit_note, color: Colors.white, size: 26),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${'edit'.tr} ${'store'.tr}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w800,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    "edit_store_subtitle_1".tr,
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.85),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // ----- Surface blanche + formulaire -----
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.06),
                          blurRadius: 20,
                          offset: const Offset(0, -6),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                      child: isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // ===== Carte Image + Nom =====
                                Material(
                                  color: theme.colorScheme.surface,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        // ⬇️ Garde le format 200x80
                                        Stack(
                                          children: [
                                            ClipRRect(
                                              borderRadius: BorderRadius.circular(14),
                                              child: Container(
                                                width: 200,
                                                height: 80,
                                                decoration: BoxDecoration(
                                                  color: Colors.grey[200],
                                                  border: Border.all(color: theme.dividerColor.withOpacity(0.25)),
                                                  borderRadius: BorderRadius.circular(14),
                                                ),
                                                child: selectedImageFile != null
                                                    ? Image.file(selectedImageFile!, fit: BoxFit.cover)
                                                    : hasNetImage
                                                        ? Image.network(
                                                            myStore!.images.first,
                                                            fit: BoxFit.cover,
                                                          )
                                                        : const Icon(Icons.store, size: 40, color: AppColors.primary),
                                              ),
                                            ),
                                            Positioned(
                                              right: 4,
                                              bottom: 4,
                                              child: Material(
                                                color: Colors.black.withOpacity(0.35),
                                                borderRadius: BorderRadius.circular(8),
                                                child: InkWell(
                                                  borderRadius: BorderRadius.circular(8),
                                                  onTap: () async {
                                                    final picker = ImagePicker();
                                                    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
                                                    if (pickedFile != null) {
                                                      setState(() {
                                                        selectedImageFile = File(pickedFile.path);
                                                        storeController.boutiquePictures.clear();
                                                        storeController.addBoutiquePicture(pickedFile.path);
                                                      });
                                                    }
                                                  },
                                                  child: const Padding(
                                                    padding: EdgeInsets.all(6.0),
                                                    child: Icon(Icons.add_a_photo, color: Colors.white, size: 18),
                                                  ),
                                                ),
                                              ),
                                            )
                                          ],
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'store_name'.tr,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w700,
                                                  color: theme.colorScheme.primary,
                                                ),
                                              ),
                                              const SizedBox(height: 6),
                                              TextField(
                                                controller: nameController,
                                                decoration: inputDecoration(''),
                                                onChanged: (v) => storeController.name.value = v,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 12),

                                // ===== Description =====
                                Material(
                                  color: theme.colorScheme.surface,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'store_description'.tr,
                                          style: TextStyle(
                                            fontWeight: FontWeight.w700,
                                            color: theme.colorScheme.primary,
                                          ),
                                        ),
                                        const SizedBox(height: 6),
                                        TextField(
                                          controller: descriptionController,
                                          decoration: inputDecoration(''),
                                          maxLines: 5,
                                          textInputAction: TextInputAction.done,
                                          onEditingComplete: () => FocusScope.of(context).unfocus(),
                                          onChanged: (v) => storeController.description.value = v,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 12),

                                // ===== Type de boutique =====
                                Material(
                                  color: theme.colorScheme.surface,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: DropdownButtonFormField<String>(
                                      decoration: inputDecoration('store_type'.tr),
                                      value: storeController.type.value.isNotEmpty
                                          ? storeController.type.value
                                          : null,
                                      items: storeController.storeTypes.map((type) {
                                        return DropdownMenuItem<String>(
                                          value: type.id.toString(),
                                          child: Row(
                                            children: [
                                              const Icon(Icons.category_outlined, size: 18),
                                              const SizedBox(width: 8),
                                              Text(type.name),
                                            ],
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (value) => storeController.type.value = value ?? '',
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 12),

                                // ===== Horaires =====
                                Material(
                                  color: theme.colorScheme.surface,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4),
                                    child: Column(
                                      children: [
                                        ListTile(
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                                          leading: Container(
                                            width: 40,
                                            height: 40,
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(10),
                                              color: theme.colorScheme.primary.withOpacity(0.08),
                                            ),
                                            child: Icon(Icons.access_time, color: theme.colorScheme.primary),
                                          ),
                                          title: Text('opening_time'.tr),
                                          subtitle: Obx(() => Text(
                                                storeController.openingHours.value,
                                                style: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.7)),
                                              )),
                                          onTap: () => _selectTime(context, true),
                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
                                        ),
                                        Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                          child: const _ModernDivider(),
                                        ),
                                        ListTile(
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                                          leading: Container(
                                            width: 40,
                                            height: 40,
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(10),
                                              color: theme.colorScheme.primary.withOpacity(0.08),
                                            ),
                                            child: Icon(Icons.timelapse, color: theme.colorScheme.primary),
                                          ),
                                          title: Text('closing_time'.tr),
                                          subtitle: Obx(() => Text(
                                                storeController.closingHours.value,
                                                style: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.7)),
                                              )),
                                          onTap: () => _selectTime(context, false),
                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                const Spacer(),

                                // ===== Bouton enregistrer =====
                                SizedBox(
                                  width: double.infinity,
                                  child: CustomButton(
                                    text: 'edit'.tr,
                                    onPressed: () async {
                                      _showLoadingDialog(context);
                                      await storeController.updateStore();
                                      if (mounted) Navigator.of(context).pop(); // close loader
                                      Get.back(); // retour
                                    },
                                  ),
                                ),
                                 const SizedBox(height: 12),
                              ],
                            ),
                    ),
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }
}

class _ModernDivider extends StatelessWidget {
  const _ModernDivider({this.indent = 0, this.endIndent = 0});
  final double indent;
  final double endIndent;

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Container(
      height: 0.5,
      margin: EdgeInsetsDirectional.only(start: indent, end: endIndent),
      color: c.outlineVariant.withOpacity(0.3), // fin et net
    );
  }
}