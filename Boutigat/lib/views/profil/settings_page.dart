import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/views/profil/address_management_page.dart';
import 'package:boutigak/views/profil/delete_account_page.dart';
import 'package:boutigak/views/profil/my_information_page.dart';
import 'package:boutigak/views/profil/password_page.dart';
import 'package:boutigak/views/profil/%20language_selector_page.dart';
import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final authController = Get.find<AuthController>();

  /// Refetch UNIQUEMENT quand on revient de MyInformationPage
  Future<void> _refreshUserAfterInfo() async {
    try {
      await authController.getAndSaveUser(); // ou getUser()
    } catch (e) {
      debugPrint('SettingsPage refresh user error: $e');
    } finally {
      if (mounted) setState(() {}); // rebuild pour nom/initiales
    }
  }

  /// Navigation générique (sans refresh)
Future<void> _goTo(Widget Function() pageBuilder) async {
  await Get.to(pageBuilder); // exactement comme Get.to(() => Page())
}

  /// Navigation vers MyInformationPage + refresh au retour
 Future<void> _goToInfoAndRefresh() async {
  final changed = await Get.to(() => MyInformationPage(
    firstName: authController.user?.firstName ?? '',
    lastName:  authController.user?.lastName  ?? '',
  ));
  if (changed == true) {
    await _refreshUserAfterInfo(); // refetch UNIQUEMENT si modifié
  }
}

  String _initials(String first, String last) {
    final f = first.isNotEmpty ? first[0] : '';
    final l = last.isNotEmpty ? last[0] : '';
    final s = (f + l).toUpperCase();
    return s.isEmpty ? 'U' : s;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      appBar: AppBar(
        title: Text('settings'.tr, style: TextStyle(fontSize: 18.sp)),
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // Dégradé de fond
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.95),
                  ],
                ),
              ),
            ),
          ),

          // Scroll sans “trou” en bas
          CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // Header profil (avec skeleton pendant le chargement)
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    SizedBox(height: 16.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Material(
                        color: Colors.white.withOpacity(0.10),
                        elevation: 0,
                        borderRadius: BorderRadius.circular(16.r),
                        child: Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.r),
                            border: Border.all(color: Colors.white.withOpacity(0.25)),
                          ),
                          child: Obx(() {
                            final isLoading = authController.tokenLoading.value || authController.user == null;

                            if (isLoading) {
                              return const _HeaderSkeleton();
                            }

                            final firstName = authController.user?.firstName ?? '';
                            final lastName  = authController.user?.lastName  ?? '';
                            final initials  = _initials(firstName, lastName);
                            final displayName = (firstName + ' ' + lastName).trim().isEmpty
                                ? 'user_name'.tr
                                : (firstName + ' ' + lastName).trim();

                            return Row(
                              children: [
                                // Avatar initials
                                Container(
                                  width: 56.w,
                                  height: 56.w,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Colors.white.withOpacity(0.28),
                                        Colors.white.withOpacity(0.12),
                                      ],
                                    ),
                                    border: Border.all(color: Colors.white.withOpacity(0.35)),
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    initials,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w800,
                                      fontSize: 18.sp,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 12.w),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        displayName,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w800,
                                          fontSize: 16.sp,
                                        ),
                                      ),
                                      SizedBox(height: 4.h),
                                      Text(
                                        'settings_subtitle'.tr, // "Manage your profile and preferences"
                                        style: TextStyle(
                                          color: Colors.white.withOpacity(0.8),
                                          fontSize: 12.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          }),
                        ),
                      ),
                    ),
                    SizedBox(height: 16.h),
                  ],
                ),
              ),

              // Section surface (remplit le reste de l'écran)
              SliverFillRemaining(
                hasScrollBody: false,
                child: _SettingsSurface(
                  onNameTap: _goToInfoAndRefresh, // ✅ refresh uniquement ici
                 onLanguageTap: () => _goTo(() => LanguageSelector()),
onAddressesTap: () => _goTo(() => AddressManagementPage()),
onPasswordTap:  () => _goTo(() => PasswordPage()),
onDeleteTap:    () => _goTo(() => DeleteAccountPage()),

                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Skeleton pour le header (avatar + 2 barres)
class _HeaderSkeleton extends StatelessWidget {
  const _HeaderSkeleton();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final base = isDark ? Colors.white.withOpacity(0.10) : Colors.white.withOpacity(0.35);
    final high = isDark ? Colors.white.withOpacity(0.25) : Colors.white.withOpacity(0.6);

    Widget shimmerBox({double? w, required double h, double r = 8}) {
      return Shimmer.fromColors(
        baseColor: base,
        highlightColor: high,
        period: const Duration(milliseconds: 1100),
        child: Container(
          width: w,
          height: h,
          decoration: BoxDecoration(
            color: base,
            borderRadius: BorderRadius.circular(r),
          ),
        ),
      );
    }

    return Row(
      children: [
        // Avatar circle
        Shimmer.fromColors(
          baseColor: base,
          highlightColor: high,
          period: const Duration(milliseconds: 1100),
          child: Container(
            width: 56.w,
            height: 56.w,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              shimmerBox(w: double.infinity, h: 14.h, r: 6),
              SizedBox(height: 8.h),
              shimmerBox(w: MediaQuery.of(context).size.width * 0.4, h: 12.h, r: 6),
            ],
          ),
        ),
      ],
    );
  }
}

/// Surface blanche contenant les cartes de réglages
class _SettingsSurface extends StatelessWidget {
  final VoidCallback onNameTap;
  final VoidCallback onLanguageTap;
  final VoidCallback onAddressesTap;
  final VoidCallback onPasswordTap;
  final VoidCallback onDeleteTap;

  const _SettingsSurface({
    required this.onNameTap,
    required this.onLanguageTap,
    required this.onAddressesTap,
    required this.onPasswordTap,
    required this.onDeleteTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme  = Theme.of(context);
    final shadow = Colors.black.withOpacity(0.06);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.r),
          topRight: Radius.circular(24.r),
        ),
        boxShadow: [BoxShadow(color: shadow, blurRadius: 20, offset: const Offset(0, -6))],
      ),
      child: Padding(
        padding: EdgeInsets.fromLTRB(12.w, 20.h, 12.w, 24.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _SectionHeader(title: 'general_section'.tr),

            _SettingCard(
              icon: FontAwesomeIcons.userCircle,
              title: 'user_name'.tr,
              onTap: onNameTap,
            ),

            _SettingCard(
              icon: FontAwesomeIcons.language,
              title: 'language'.tr,
              onTap: onLanguageTap,
            ),

            _SettingCard(
              icon: FontAwesomeIcons.mapMarkedAlt,
              title: 'my_addresses'.tr,
              onTap: onAddressesTap,
            ),

            _SettingCard(
              icon: FontAwesomeIcons.key,
              title: 'password'.tr,
              onTap: onPasswordTap,
            ),

            SizedBox(height: 8.h),
            _SectionHeader(title: 'account_section'.tr),

            _SettingCard(
              icon: FontAwesomeIcons.trashAlt,
              title: 'delete_account'.tr,
              onTap: onDeleteTap,
              accent: Colors.red,
            ),

            const Spacer(),
          ],
        ),
      ),
    );
  }
}

/// En-tête de section (petit titre)
/// En-tête de section (s’aligne automatiquement à gauche en LTR et à droite en RTL)
class _SectionHeader extends StatelessWidget {
  final String title;
  const _SectionHeader({required this.title});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Align(
      alignment: AlignmentDirectional.centerStart, // ← start = left en LTR, right en RTL
      child: Padding(
        padding: EdgeInsetsDirectional.fromSTEB(8.w, 8.h, 8.w, 8.h),
        child: Text(
          title,
          textAlign: TextAlign.start, // ← start = left en LTR, right en RTL
          style: TextStyle(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
            letterSpacing: 0.2,
            fontSize: 12.sp,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
    );
  }
}

/// Carte paramètre
class _SettingCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final Color? accent;

  const _SettingCard({
    required this.icon,
    required this.title,
    required this.onTap,
    this.accent,
  });

  @override
  Widget build(BuildContext context) {
    final theme     = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border    = theme.dividerColor.withOpacity(0.2);
    final color     = accent ?? theme.colorScheme.primary;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 4.w),
      child: Material(
        color: theme.colorScheme.surface,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
          side: BorderSide(color: border),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: onTap, // ✅ navigation OK
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 14.h, horizontal: 14.w),
            child: Row(
              children: [
                Container(
                  width: 40.w,
                  height: 40.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color: color.withOpacity(0.10),
                  ),
                  child: FaIcon(icon, size: 18.sp, color: color),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(Icons.chevron_right, size: 22.sp, color: theme.disabledColor),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
