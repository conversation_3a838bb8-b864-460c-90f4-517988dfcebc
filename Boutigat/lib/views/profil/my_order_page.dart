import 'dart:ui';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/oders.dart';
//import 'package:boutigak/data/services/snapchat_share.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/home/<USER>';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/profil/my_order_details_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:get/get.dart';
import 'package:boutigak/views/widgets/snapchat_item_widget.dart';
import 'package:flutter/services.dart';
import 'package:boutigak/views/widgets/payment_widget.dart';
import 'package:boutigak/controllers/badge_controller.dart';
import 'package:boutigak/views/widgets/order_modification_widget.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';



class MyOrdersPage extends StatefulWidget {
  final VoidCallback? onWillPop;

  const MyOrdersPage({Key? key, this.onWillPop}) : super(key: key);

  @override
  _MyOrdersPageState createState() => _MyOrdersPageState();
}

class _MyOrdersPageState extends State<MyOrdersPage> {
  final OrderController orderController = Get.put(OrderController());
  final BadgeController badgeController = Get.find<BadgeController>();
  late ScrollController _scrollController;

  void _navigateToHome() {
    Get.offAll(() => ZoomDrawerWrapper(), transition: Transition.noTransition);
  }

  void _onScroll() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      // User has reached the bottom, load more orders
      orderController.fetchedMyOrders();
    }
  }

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await orderController.fetchedMyOrders(refresh: true);
      badgeController.resetBadge('user-order');
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _onPullToRefresh() async {
    try {
      await orderController.fetchedMyOrders(refresh: true);
    } catch (e) {
      Get.snackbar(
        'error'.tr.isEmpty ? 'Error' : 'error'.tr,
        'refresh_orders_failed'.tr.isEmpty ? 'Failed to refresh orders: $e' : 'refresh_orders_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return WillPopScope(
      onWillPop: () async {
        widget.onWillPop?.call();
        return true;
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        body: Stack(
          children: [
            // ---- Dégradé de fond (haut vert) ----
            Positioned.fill(
              child: DecoratedBox(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.primary.withOpacity(0.92),
                      theme.colorScheme.primary.withOpacity(0.90),
                    ],
                  ),
                ),
              ),
            ),

            // ---- Contenu réactif ----
            SafeArea(
              top: true,
              bottom: false, // pas de padding bas => pas d'espace vert
              child: Obx(() {
                if (orderController.isLoading.isTrue) {
                  return Column(
                    children: const [
                      _GlassHeader(title: 'my_orders'),
                      Expanded(child: _OrdersShimmerList()),
                    ],
                  );
                }

                final orders = orderController.myOrders;
                final isLoadingMore = orderController.isLoadingMore.value;

                return CustomScrollView(
                  controller: _scrollController,
                  physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                  slivers: [
                    // IMPORTANT: le refresh control doit être le PREMIER sliver
                    CupertinoSliverRefreshControl(
  onRefresh: _onPullToRefresh,
  builder: (context, refreshState, pulledExtent, refreshTriggerPullDistance, refreshIndicatorExtent) {
    return const Center(
      child: CupertinoActivityIndicator(
        radius: 14,
        color: Colors.white,
      ),
    );
  },
),


                    const SliverToBoxAdapter(child: _GlassHeader(title: 'my_orders')),

                    if (orders.isEmpty)
                      SliverToBoxAdapter(
                        child: _EmptyState(
                          title: 'no_orders_title'.tr.isEmpty ? 'No orders yet' : 'no_orders_title'.tr,
                          subtitle: 'no_orders_subtitle'.tr.isEmpty
                              ? 'When you order from a store, it will appear here.'
                              : 'no_orders_subtitle'.tr,
                          ctaText: 'back_home'.tr.isEmpty ? 'Back to Home' : 'back_home'.tr,
                          onCta: _navigateToHome,
                        ),
                      )
                    else
                      // ===== Fond blanc arrondi qui remplit le bas (pas de SliverFillRemaining capricieux) =====
                      SliverToBoxAdapter(
                        child: LayoutBuilder(
                          builder: (context, _) {
                            // Remplit l’écran moins le padding top (déjà ajouté par SafeArea)
                            final padding = MediaQuery.of(context).padding;
                            final viewportH = MediaQuery.of(context).size.height - padding.top;

                            return ConstrainedBox(
                              constraints: BoxConstraints(minHeight: viewportH),
                              child: Container(
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Liste non scrollable -> un seul scroll global
                                    ListView.separated(
                                      padding: const EdgeInsets.fromLTRB(12, 12, 12, 16),
                                      physics: const NeverScrollableScrollPhysics(),
                                      shrinkWrap: true,
                                      itemCount: orders.length + (isLoadingMore ? 1 : 0),
                                      separatorBuilder: (_, index) => index == orders.length ? const SizedBox.shrink() : const SizedBox(height: 8),
                                      itemBuilder: (context, index) {
                                        if (index == orders.length) {
                                          // Show loading indicator at the bottom
                                          return Container(
                                            padding: EdgeInsets.all(16.0),
                                            alignment: Alignment.center,
                                            child: CircularProgressIndicator(
                                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                            ),
                                          );
                                        }

                                        final order = orders[index];
                                        final double delivery = double.tryParse(order.deliveryCharge ?? '0') ?? 0.0;
                                        final double total = (order.totalOrders ?? 0).toDouble() + delivery;

                                        return _OrderCard(
                                          order: order,
                                          total: total,
                                          onTap: () => Get.to(
                                            () => OrderDetailsPage(order: order),
                                            transition: Transition.rightToLeft,
                                          ),
                                        );
                                      },
                                    ),
                                    // aucune marge en bas → le blanc colle au bord inférieur
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}

/* ============================= UI widgets ============================= */

class _GlassHeader extends StatelessWidget {
  final String title;
  const _GlassHeader({required this.title});

    void _navigateToHome() async {
    Get.offAll(
      () => ZoomDrawerWrapper(),
      transition: Transition.noTransition,
    );
  }


  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.25),
              border: Border.all(color: Colors.white.withOpacity(0.35)),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const SizedBox(width: 6),
                _GlassIconButton(
                  icon: Icons.arrow_back_ios_new_rounded,
                  onTap: () => _navigateToHome(),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title.tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(width: 46), // équilibre visuel avec le bouton retour
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _GlassIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _GlassIconButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: InkWell(
          onTap: onTap,
          child: SizedBox(
            width: 40,
            height: 40,
            child: Icon(icon, color: Colors.white),
          ),
        ),
      ),
    );
  }
}
class _OrderCard extends StatelessWidget {
  final Order order;
  final double total;
  final VoidCallback onTap;

  const _OrderCard({
    required this.order,
    required this.total,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final t = Theme.of(context);
    final statusColor = _statusColor(order.status);
    final bool paid = order.isPaid;
    final bool modified = order.modifiedByStore;

    final orderId = order.orderId?.toString() ?? '—';
    final storeName = (order.storeName?.trim().isNotEmpty ?? false)
        ? order.storeName!.trim()
        : ('unknown_store'.tr.isNotEmpty ? 'unknown_store'.tr : 'Unknown Store');
   
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: t.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.black12.withOpacity(0.1)),
        
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // ===== Vignette + badge "modifié" positionné en haut-gauche =====
            SizedBox(
              width: 64,
              height: 64,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: order.storeImage != null
                        ? CachedImageWidget(
                            imageUrl: order.storeImage!,
                            width: 64,
                            height: 64,
                            fit: BoxFit.cover,
                            borderRadius: BorderRadius.circular(12),
                          )
                        : Image.asset(
                            'assets/default_store_image.png',
                            width: 64,
                            height: 64,
                            fit: BoxFit.cover,
                          ),
                  ),
                  if (modified)
                    Positioned(
                      top: 6,
                      left: 6,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.15), blurRadius: 6)],
                        ),
                        child: Text(
                          'modified'.tr.isEmpty ? 'Modified' : 'modified'.tr,
                          style: const TextStyle(fontSize: 9, fontWeight: FontWeight.w800, color: Colors.white),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 12),

            // Infos
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Nom boutique + ID
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          storeName,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w700),
                        ),
                      ),
                    
                    ],
                  ),
           
                  const SizedBox(height: 6),

                  // Chips statut / paid (on retire le chip "modified", déjà en badge)
                  Wrap(
                    spacing: 6,
                    runSpacing: 6,
                    children: [
                      _Chip(
                        label: (order.status ?? 'N/A').tr.isNotEmpty
                            ? (order.status ?? '').tr
                            : (order.status ?? 'N/A'),
                        color: statusColor,
                      ),
                      if (paid) _Chip(label: 'paid'.tr.isEmpty ? 'Paid' : 'paid'.tr, color: Colors.green),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),

            // Total + chevron
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                  Text('#$orderId', style: TextStyle(fontSize: 12, color: t.hintColor)),
                 const SizedBox(height: 6),
                Text(
                  '${total.toStringAsFixed(2)} ${'mru'.tr.isEmpty ? 'mru' : 'mru'.tr}',
                  style: const TextStyle(fontWeight: FontWeight.w800, fontSize: 16),
                ),
           
              
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _Chip extends StatelessWidget {
  final String label;
  final Color color;
  const _Chip({required this.label, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.10),
        border: Border.all(color: color.withOpacity(0.45)),
        borderRadius: BorderRadius.circular(999),
      ),
      child: Text(
        label,
        style: TextStyle(fontSize: 11, fontWeight: FontWeight.w700, color: color),
      ),
    );
  }
}

class _EmptyState extends StatelessWidget {
  final String title;
  final String subtitle;
  final String ctaText;
  final VoidCallback onCta;

  const _EmptyState({
    required this.title,
    required this.subtitle,
    required this.ctaText,
    required this.onCta,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 56, 24, 24),
      child: Column(
        children: [
          // Illustration placeholder
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.18),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.35)),
            ),
            child: const Icon(Icons.receipt_long, color: Colors.white, size: 56),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.w800),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.white70),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onCta,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white.withOpacity(0.18),
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white30),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
              elevation: 0,
            ),
            child: Text(ctaText),
          ),
        ],
      ),
    );
  }
}

class _OrdersShimmerList extends StatelessWidget {
  const _OrdersShimmerList();

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 16),
      itemCount: 6,
      itemBuilder: (context, index) => const _ShimmerOrderCard(),
    );
  }
}

class _ShimmerOrderCard extends StatelessWidget {
  const _ShimmerOrderCard();

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.white.withOpacity(0.30),
      highlightColor: Colors.white.withOpacity(0.10),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.18),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white24),
        ),
        child: Row(
          children: [
            // image
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            const SizedBox(width: 12),
            // texte
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(height: 14, width: 180, color: Colors.white),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      Container(height: 12, width: 60, color: Colors.white),
                      const SizedBox(width: 8),
                      Container(height: 12, width: 40, color: Colors.white),
                      const SizedBox(width: 8),
                      Container(height: 12, width: 50, color: Colors.white),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(height: 14, width: 70, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 12, width: 40, color: Colors.white),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/* ============================= Utils ============================= */

Color _statusColor(String? status) {
  switch (status) {
    case 'PENDING':
      return Colors.grey;
    case 'ACCEPTED':
      return Colors.blue;
    case 'CANCELLED':
      return Colors.red;
    case 'WAITING_PAYMENT':
      return Colors.deepOrange;
    case 'DELIVERED':
    case 'COMPLETED':
      return Colors.green;
    default:
      return Colors.black26;
  }
}
String _statusLabel(String? status) {
  // PENSE à normaliser au cas où l’API envoie "accepted" ou null
  final key = (status ?? 'UNKNOWN').toUpperCase();
  return key.tr;
}
