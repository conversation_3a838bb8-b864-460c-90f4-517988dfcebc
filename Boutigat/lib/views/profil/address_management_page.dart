import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/location.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';


import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

// Assume you already have these in your project
// import 'package:your_app/controllers/order_controller.dart';
// import 'package:your_app/theme/app_colors.dart';

import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shimmer/shimmer.dart';

// Assume you already have these in your project
// import 'package:your_app/controllers/order_controller.dart';
// import 'package:your_app/theme/app_colors.dart';

class AddressManagementPage extends StatefulWidget {
  const AddressManagementPage({super.key});

  @override
  State<AddressManagementPage> createState() => _AddressManagementPageState();
}

class _AddressManagementPageState extends State<AddressManagementPage> {
  final OrderController orderController = Get.find<OrderController>();

  bool _initialLoading = true;

  @override
  void initState() {
    super.initState();
    // Chargement initial après le premier frame
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() => _initialLoading = true);
      await orderController.fetchUserLocations();
      if (mounted) setState(() => _initialLoading = false);
    });
  }

  Future<void> _getCurrentLocation() async {
    try {
      final permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        Get.snackbar(
          'location_permission'.tr,
          'permission_denied_desc'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      orderController.updateLocation(LatLng(position.latitude, position.longitude));
    } catch (e) {
      debugPrint('Error getting location: $e');
      Get.snackbar('error'.tr, 'location_error'.tr, snackPosition: SnackPosition.BOTTOM);
    }
  }

  Future<void> _onRefresh() async {
    await orderController.fetchUserLocations();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final c1 = theme.colorScheme.primary;
    final c2 = theme.colorScheme.primary.withOpacity(0.95);

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      appBar: AppBar(
        title: Text('my_addresses'.tr, style: const TextStyle(fontWeight: FontWeight.w700)),
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // ===== Fond dégradé (cohérent Settings/Language) =====
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [c1, c2],
                ),
              ),
            ),
          ),

          // ===== Contenu scrollable =====
          Obx(() {
            // force la lecture Rx à chaque build
            final hasItems = orderController.userLocations.isNotEmpty;

            return CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                CupertinoSliverRefreshControl(onRefresh: _onRefresh),

                // ----- Header translucide -----
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                    child: Material(
                      color: Colors.white.withOpacity(0.10),
                      elevation: 0,
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.white.withOpacity(0.25)),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Halo + icône
                            Container(
                              width: 56,
                              height: 56,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withOpacity(0.28),
                                    Colors.white.withOpacity(0.12),
                                  ],
                                ),
                                border: Border.all(color: Colors.white.withOpacity(0.35)),
                              ),
                              child: const Icon(Icons.location_on_outlined, color: Colors.white, size: 26),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'addresses_header_title'.tr, // ex: "Gérer vos adresses"
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w800,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'addresses_header_subtitle'.tr, // ex: "Ajoutez et supprimez vos adresses de livraison"
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.85),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // ----- Surface (contenu) -----
               // ----- Surface blanche + contenu (remplace tout le bloc "Surface (contenu)") -----
SliverToBoxAdapter(
  child: Container(
    width: double.infinity,
    decoration: BoxDecoration(
      color: theme.colorScheme.surface,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(24),
        topRight: Radius.circular(24),
      ),
     
    ),
    child: Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 120),
      child: _initialLoading
          ? const _ShimmerAddressListBody() // ⬅️ shimmer version "non-sliver"
          : (!hasItems
              ? _EmptyAddresses(
                  onAdd: () async {
                    await _getCurrentLocation();
                    final saved = await Get.to(() => const AddAddressPage(), fullscreenDialog: true);
                    if (saved == true) {
                      await orderController.fetchUserLocations();
                    }
                  },
                )
              : ListView.separated(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: orderController.userLocations.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 10),
                  itemBuilder: (context, index) {
                    final location = orderController.userLocations[index];
                    return _AddressCard(
                      name: location.name ?? '',
                      address: location.address ?? '',
                      onDelete: () => _showDeleteDialog(context, location),
                    );
                  },
                )),
    ),
  ),
),
// ⬇️ AJOUTE CECI JUSTE APRÈS le SliverToBoxAdapter de la surface blanche
SliverFillRemaining(
  hasScrollBody: false,
  child: Container(
    color: Theme.of(context).colorScheme.surface, // fond blanc (ou surface)
  ),
),

              ],
            );
          }),
        ],
      ),

      // ===== FAB “Ajouter une adresse” =====
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 52),
                  backgroundColor:theme.colorScheme.primary, 
                  foregroundColor: theme.colorScheme.onPrimary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(14),
                  ),
                ),
                onPressed: () async {
                  await _getCurrentLocation();
                  final saved = await Get.to(() => const AddAddressPage(), fullscreenDialog: true);
                  if (saved == true) {
                    await orderController.fetchUserLocations();
                  }
                },
                icon: const Icon(Icons.add_location_alt_outlined),
                label: Text('add_address'.tr),
              ),
            ),
          ],
        ),
      ),
    );
  }

 void _showDeleteDialog(BuildContext context, Location location) {
  final theme = Theme.of(context);

  showModalBottomSheet(
    context: context,
    showDragHandle: true,
    isScrollControlled: false,
    backgroundColor: theme.colorScheme.surface,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (context) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header danger
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withOpacity(.08),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: theme.colorScheme.error.withOpacity(.25)),
              ),
              child: Row(
                children: [
                  Icon(Icons.delete_forever_rounded, color: theme.colorScheme.error),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      'delete_address'.tr,
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w800),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),

            // Chip avec le nom de l’adresse (si présent)
            if ((location.name ?? '').isNotEmpty)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  Chip(
                    label: Text(location.name!),
                    avatar: const Icon(Icons.place_outlined, size: 16),
                    backgroundColor: theme.colorScheme.primary.withOpacity(.08),
                    side: BorderSide(color: theme.colorScheme.primary.withOpacity(.25)),
                  ),
                ],
              ),

            const SizedBox(height: 8),
            Text(
              'are_you_sure_delete'.tr,
              style: TextStyle(color: theme.colorScheme.onSurface.withOpacity(.85)),
            ),

            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    label: Text('cancel'.tr),
                    style: OutlinedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 48),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.delete_outline),
                    label: Text('delete'.tr),
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 48),
                      backgroundColor: theme.colorScheme.error,
                      foregroundColor: theme.colorScheme.onError,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    ),
                    onPressed: () async {
                      // 1) ferme d’abord la feuille
                      Navigator.of(context).pop();

                      // 2) overlay pendant l’opération
                      await Get.showOverlay(
                        asyncFunction: () async {
                          final success = await orderController.deleteLocation(location.id!);
                          if (success) {
                            await orderController.fetchUserLocations();
                            Get.snackbar('deleted'.tr, 'address_deleted'.tr,
                                snackPosition: SnackPosition.BOTTOM);
                          } else {
                            Get.snackbar('error'.tr, 'something_went_wrong'.tr,
                                snackPosition: SnackPosition.BOTTOM);
                          }
                        },
                        loadingWidget: const Center(child: CircularProgressIndicator()),
                      );
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}

}

class _AddressCard extends StatelessWidget {
  final String name;
  final String address;
  final VoidCallback onDelete;
  const _AddressCard({
    required this.name,
    required this.address,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Material(
        color: AppColors.surface,
        elevation: 0,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {},
          child: Container(
            padding: const EdgeInsets.all(14),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.black12),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.blueGrey.withOpacity(.08),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.location_on_outlined),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        address,
                        style: TextStyle(
                          color: Colors.black.withOpacity(.7),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  visualDensity: VisualDensity.compact,
                  onPressed: onDelete,
                  icon: const Icon(Icons.more_horiz),
                  tooltip: 'delete'.tr,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _EmptyAddresses extends StatelessWidget {
  final VoidCallback onAdd;
  const _EmptyAddresses({required this.onAdd});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: 88,
            width: 88,
            decoration: BoxDecoration(
              color: Colors.blueGrey.withOpacity(.08),
              borderRadius: BorderRadius.circular(24),
            ),
            child: const Icon(Icons.map_outlined, size: 36),
          ),
          const SizedBox(height: 16),
          Text(
            'no_addresses_yet'.tr,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
          ),
          const SizedBox(height: 8),
          Text(
            'add_your_first_address'.tr,
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.black.withOpacity(.7)),
          ),
          const SizedBox(height: 24),
          OutlinedButton.icon(
            onPressed: onAdd,
            icon: const Icon(Icons.add_location_alt_outlined),
            label: Text('add_address'.tr),
          )
        ],
      ),
    );
  }
}

class AddAddressPage extends StatelessWidget {
  const AddAddressPage({super.key});

  static const _panelRadius = Radius.circular(20);

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();

    final TextEditingController nameController = TextEditingController();
    final TextEditingController addressController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Stack(
        children: [
          // Map
          Obx(
            () => GoogleMap(
              initialCameraPosition: CameraPosition(
                target: orderController.currentPosition.value,
                zoom: 15,
              ),
              onTap: (LatLng position) {
                orderController.updateLocation(position);
              },
              markers: {
                Marker(
                  markerId: const MarkerId('selected_location'),
                  position: orderController.currentPosition.value,
                ),
              },
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
            ),
          ),
      
          // Frosted back button
          Positioned(
            top: 60,
            left: 12,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(28),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(.2),
                    borderRadius: BorderRadius.circular(28),
                    border: Border.all(color: Colors.white.withOpacity(.3)),
                  ),
                  child: IconButton(
                    icon: const Icon(CupertinoIcons.xmark, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ),
            ),
          ),
      
          // Bottom panel
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(16, 10, 16, 16),
              decoration: const BoxDecoration(
                color: Color(0xFFF7F7F7),
                borderRadius: BorderRadius.vertical(top: _panelRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 12,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: Colors.black26,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Colors.blueGrey.withOpacity(.08),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(Icons.place_outlined),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'pin_your_location'.tr,
                          style: const TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
      
                  // Name
               Form(
  key: formKey,
  child: Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      // ... le header avec l'icône, etc.
      const SizedBox(height: 12),

      // ---- Nom de l’adresse (court, 30 chars) ----
      _ModernTextField(
        controller: nameController,
        label: 'location_name'.tr, // ex. "Nom de l'endroit"
        hint: 'Home / Office / Parents'.tr,
        icon: Icons.edit_outlined,
        maxLength: 30,
        action: TextInputAction.next,
        validator: (v) {
          if (v.isEmpty) return 'fill_all_fields'.tr;
          if (v.length < 2) return 'Le nom est trop court';
          return null;
        },
      ),
      const SizedBox(height: 12),

      // ---- Description / Adresse détaillée (multi-lignes, 120 chars) ----
      _ModernTextField(
        controller: addressController,
        label: 'address'.tr,
        hint: 'Rue, bâtiment, étage, repère…'.tr,
        icon: Icons.location_on_outlined,
        maxLength: 120,
        maxLines: 2,
        action: TextInputAction.done,
        onSubmitted: () {}, // optionnel
        validator: (v) {
          if (v.isEmpty) return 'fill_all_fields'.tr;
          if (v.length < 6) return 'Adresse trop courte';
          return null;
        },
      ),

      const SizedBox(height: 14),
                  const SizedBox(height: 12),
      
                 
      
                  // Save button
                  AnimatedBuilder(
                    animation: Listenable.merge([
                      nameController,
                      addressController,
                    ]),
                    builder: (context, _) {
                      final canSave = nameController.text.isNotEmpty &&
                          addressController.text.isNotEmpty;
                      return SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            minimumSize: const Size(double.infinity, 52),
                            backgroundColor: Colors.black,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(14),
                            ),
                          ),
                        onPressed: canSave
    ? () async {
        // 1) Copier les champs vers le controller
        orderController.newLocationName.value = nameController.text;
        orderController.newLocationAddress.value = addressController.text;

        // 2) Exécuter l'opération avec overlay (s'occupe d'ouvrir/fermer le loader)
        final bool ok = await Get.showOverlay(
          asyncFunction: () async {
            await orderController.saveNewLocation();
            return true;
          },
          loadingWidget: const Center(child: CircularProgressIndicator()),
      
        );

        // 3) Fermer la page et retourner "true" pour que la liste se refresh
        if (ok) {
          Navigator.pop(context, true);
          Get.snackbar('saved'.tr, 'address_saved'.tr,
              snackPosition: SnackPosition.BOTTOM);
        }
      }
    : null,


                          child: Text('save'.tr),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 14),
                ],
              ),
            ), ]
          ) ),
          )
        ],
      ),
    );
  }
}




class _ShimmerAddressCard extends StatelessWidget {
  const _ShimmerAddressCard();
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.black12),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _ShimmerBox(width: 44, height: 44, radius: 12),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: const [
                  _ShimmerBox(width: double.infinity, height: 14, radius: 6),
                  SizedBox(height: 8),
                  _ShimmerBox(width: 180, height: 12, radius: 6),
                ],
              ),
            ),
            const SizedBox(width: 8),
            _ShimmerBox(width: 28, height: 28, radius: 8),
          ],
        ),
      ),
    );
  }
}

class _ShimmerBox extends StatelessWidget {
  final double width;
  final double height;
  final double radius;
  const _ShimmerBox({
    required this.width,
    required this.height,
    this.radius = 8,
  });
  @override
  Widget build(BuildContext context) {
    return _Shimmer(
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
    );
  }
}

class _Shimmer extends StatefulWidget {
  final Widget child;
  const _Shimmer({required this.child});
  @override
  State<_Shimmer> createState() => _ShimmerState();
}

class _ShimmerState extends State<_Shimmer>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1400),
    )..repeat();
  }
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final double slide = _controller.value * 2 - 1; // -1 → 1
        return ShaderMask(
          shaderCallback: (rect) {
            return LinearGradient(
              begin: Alignment(-1 - slide, 0),
              end: Alignment(1 + slide, 0),
              colors: [
                Colors.grey.shade300,
                Colors.grey.shade100,
                Colors.grey.shade300,
              ],
              stops: const [0.1, 0.3, 0.4],
            ).createShader(rect);
          },
          blendMode: BlendMode.srcATop,
          child: child,
        );
      },
      child: widget.child,
    );
  }
}


// ==== Utilitaire réutilisable ====
class _ModernTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String hint;
  final IconData icon;
  final int maxLength;
  final int maxLines;
  final TextInputAction action;
  final String? Function(String value)? validator;
  final VoidCallback? onSubmitted;

  const _ModernTextField({
    required this.controller,
    required this.label,
    required this.hint,
    required this.icon,
    this.maxLength = 50,
    this.maxLines = 1,
    this.action = TextInputAction.next,
    this.validator,
    this.onSubmitted,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          maxLength: maxLength,
          textInputAction: action,
          onFieldSubmitted: (_) => onSubmitted?.call(),
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
            prefixIcon: Icon(icon),
            suffixIcon: controller.text.isEmpty
                ? null
                : IconButton(
                    icon: const Icon(Icons.close_rounded),
                    tooltip: 'clear',
                    onPressed: () {
                      controller.clear();
                      // force rebuild pour cacher le suffix
                      (context as Element).markNeedsBuild();
                    },
                  ),
            counterText: '${controller.text.length}/$maxLength',
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: BorderSide(color: Colors.black12.withOpacity(.15)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: BorderSide(color: AppColors.primary, width: 1.6),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: const BorderSide(color: Colors.red),
            ),
          ),
          style: theme.textTheme.bodyMedium,
          validator: (v) {
            final text = v?.trim() ?? '';
            if (validator != null) return validator!(text);
            if (text.isEmpty) return 'missing_info'.tr; // générique
            return null;
          },
        ),
      ],
    );
  }
}// ===== Body shimmer non-sliver (remplace _ShimmerAddressListBody) =====
class _ShimmerAddressListBody extends StatelessWidget {
  const _ShimmerAddressListBody({this.itemCount = 6});

  final int itemCount;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        itemCount,
        (_) => const _ShimmerAddressCard(),
      ),
    );
  }
}
