import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/data/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// =======================
/// CONTROLLER
/// =======================
class PasswordController extends GetxController {
  final oldPasswordVisible = false.obs;
  final newPasswordVisible = false.obs;
  final confirmPasswordVisible = false.obs;

  final isLoading = false.obs;
  final showErrorMessage = ''.obs;

  bool validatePasswords(String oldPassword, String newPassword, String confirmPassword) {
    if (newPassword != confirmPassword) {
      showErrorMessage.value = 'passwords_do_not_match'.tr;
      return false;
    }
    if (newPassword == oldPassword) {
      showErrorMessage.value = 'new_password_different'.tr;
      return false;
    }
    if (newPassword.length < 8) {
      showErrorMessage.value = 'new_password_8_chars'.tr;
      return false;
    }
    return true;
  }

  /// Retourne true si succès (utile pour vider les champs côté UI)
  Future<bool> changePassword(String oldPassword, String newPassword, String confirmPassword) async {
    isLoading.value = true;
    showErrorMessage.value = '';

    if (!validatePasswords(oldPassword, newPassword, confirmPassword)) {
      isLoading.value = false;
      return false;
    }

    try {
      // Remplace par ton service réel
      final success = await AuthService.changePassword(
        currentPassword: oldPassword,
        newPassword: newPassword,
        newPasswordConfirmation: confirmPassword,
      );

      if (success) {
        Get.snackbar('success'.tr, 'password_changed'.tr);
        return true;
      } else {
        showErrorMessage.value = 'password_change_error'.tr;
        return false;
      }
    } catch (e) {
      showErrorMessage.value = 'password_change_error'.tr + ' ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}

/// =======================
/// PAGE (UI MODERNISÉE)
/// =======================


class PasswordPage extends StatefulWidget {
  const PasswordPage({super.key});

  @override
  State<PasswordPage> createState() => _PasswordPageState();
}

class _PasswordPageState extends State<PasswordPage> {
  final PasswordController c = Get.put(PasswordController());

  final _oldCtrl     = TextEditingController();
  final _newCtrl     = TextEditingController();
  final _confirmCtrl = TextEditingController();

  @override
  void dispose() {
    _oldCtrl.dispose();
    _newCtrl.dispose();
    _confirmCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final c1 = theme.colorScheme.primary;
    final c2 = theme.colorScheme.primary.withOpacity(0.95);

    final br = 12.r;

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      appBar: AppBar(
        title: Text('change_password'.tr),
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // ===== Fond dégradé (cohérent avec tes autres pages) =====
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [c1, c2],
                ),
              ),
            ),
          ),

          // ===== Contenu scrollable =====
          CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // ----- Header translucide -----
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 12.h),
                  child: Material(
                    color: Colors.white.withOpacity(0.10),
                    elevation: 0,
                    borderRadius: BorderRadius.circular(16.r),
                    child: Container(
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(color: Colors.white.withOpacity(0.25)),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Halo + icône
                          Container(
                            width: 56.w,
                            height: 56.w,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white.withOpacity(0.28),
                                  Colors.white.withOpacity(0.12),
                                ],
                              ),
                              border: Border.all(color: Colors.white.withOpacity(0.35)),
                            ),
                            child: const Icon(Icons.lock_outline, color: Colors.white, size: 26),
                          ),
                          SizedBox(width: 12.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'password_header_title'.tr, // ex: "Modifier votre mot de passe"
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w800,
                                    fontSize: 16,
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                Text(
                                  'password_header_subtitle'.tr, // ex: "Assurez-vous d’utiliser un mot de passe robuste"
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.85),
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // ----- Surface blanche arrondie + formulaire -----
              SliverFillRemaining(
                hasScrollBody: false,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24.r),
                      topRight: Radius.circular(24.r),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.06),
                        blurRadius: 20,
                        offset: const Offset(0, -6),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 24.h),
                    child: Obx(() {
                      final loading = c.isLoading.value;
                      final fill = theme.colorScheme.surfaceVariant.withOpacity(0.4);

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // ===== Carte formulaire =====
                          Material(
                            color: theme.colorScheme.surface,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16.r),
                              side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                            ),
                            child: Padding(
                              padding: EdgeInsets.all(12.w),
                              child: Column(
                                children: [
                                  // -- Ancien mot de passe
                                  _PasswordField(
                                    controller: _oldCtrl,
                                    label: 'old_password'.tr,
                                    hint: 'old_password_hint'.tr,
                                    visible: c.oldPasswordVisible.value,
                                    onToggleVisibility: () => c.oldPasswordVisible.toggle(),
                                    borderRadius: br,
                                    readOnly: loading,
                                  ),

                                  SizedBox(height: 12.h),

                                  // -- Nouveau mot de passe
                                  _PasswordField(
                                    controller: _newCtrl,
                                    label: 'new_password'.tr,
                                    hint: 'new_password_hint'.tr,
                                    visible: c.newPasswordVisible.value,
                                    onToggleVisibility: () => c.newPasswordVisible.toggle(),
                                    borderRadius: br,
                                    readOnly: loading,
                                  ),

                                  SizedBox(height: 10.h),
                                  PasswordStrengthBar(passwordListenable: _newCtrl),
                                  SizedBox(height: 8.h),
                                  PasswordRequirements(passwordListenable: _newCtrl),

                                  SizedBox(height: 12.h),

                                  // -- Confirmation
                                  _PasswordField(
                                    controller: _confirmCtrl,
                                    label: 'confirm_password'.tr,
                                    hint: 'confirm_password_hint'.tr,
                                    visible: c.confirmPasswordVisible.value,
                                    onToggleVisibility: () => c.confirmPasswordVisible.toggle(),
                                    borderRadius: br,
                                    readOnly: loading,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 18.h),

                          // ===== Erreur globale =====
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 200),
                            child: c.showErrorMessage.value.isEmpty
                                ? const SizedBox.shrink()
                                : Container(
                                    key: const ValueKey('error-box'),
                                    padding: EdgeInsets.all(12.w),
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.errorContainer,
                                      borderRadius: BorderRadius.circular(10.r),
                                      border: Border.all(color: theme.colorScheme.error.withOpacity(.25)),
                                    ),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Icon(Icons.error_outline, color: theme.colorScheme.onErrorContainer),
                                        SizedBox(width: 8.w),
                                        Expanded(
                                          child: Text(
                                            c.showErrorMessage.value,
                                            style: TextStyle(
                                              color: theme.colorScheme.onErrorContainer,
                                              fontSize: 13.sp,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                          ),

                          const Spacer(),

                          // ===== Bouton enregistrer =====
                          SizedBox(
                            width: double.infinity,
                            height: 48.h,
                            child: FilledButton.icon(
                              onPressed: loading
                                  ? null
                                  : () async {
                                      FocusScope.of(context).unfocus();
                                      final ok = await c.changePassword(
                                        _oldCtrl.text.trim(),
                                        _newCtrl.text.trim(),
                                        _confirmCtrl.text.trim(),
                                      );
                                      if (ok) {
                                        _oldCtrl.clear();
                                        _newCtrl.clear();
                                        _confirmCtrl.clear();
                                        Get.snackbar('success'.tr, 'password_changed'.tr,
                                            snackPosition: SnackPosition.BOTTOM);
                                      }
                                    },
                              icon: loading
                                  ? SizedBox(
                                      width: 18.w,
                                      height: 18.w,
                                      child: const CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  : const Icon(Icons.lock_reset),
                              label: Text(loading ? 'loading'.tr : 'change_password'.tr),
                              style: FilledButton.styleFrom(
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
                              ),
                            ),
                          ),
                        ],
                      );
                    }),
                  ),
                ),
              ),
            ],
          ),

          // ===== Overlay de chargement fin (barre) =====
          Obx(() {
            if (!c.isLoading.value) return const SizedBox.shrink();
            return IgnorePointer(
              ignoring: true,
              child: AnimatedOpacity(
                opacity: 1,
                duration: const Duration(milliseconds: 150),
                child: Container(
                  color: Colors.black.withOpacity(0.04),
                  alignment: Alignment.topCenter,
                  padding: EdgeInsets.only(top: 8.h),
                  child: const LinearProgressIndicator(minHeight: 2),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}

/// =======================
/// WIDGETS UI
/// =======================
class _PasswordField extends StatelessWidget {
  const _PasswordField({
    required this.controller,
    required this.label,
    required this.hint,
    required this.visible,
    required this.onToggleVisibility,
    required this.borderRadius,
    this.readOnly = false,
  });

  final TextEditingController controller;
  final String label;
  final String hint;
  final bool visible;
  final VoidCallback onToggleVisibility;
  final double borderRadius;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final fill = theme.colorScheme.surfaceVariant.withOpacity(0.4);

    return TextField(
      controller: controller,
      obscureText: !visible,
      readOnly: readOnly,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        filled: true,
        isDense: true,
        fillColor: fill,
        contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 14.h),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(borderRadius)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(color: theme.dividerColor),
        ),
        prefixIcon: const Icon(Icons.password_outlined),
        suffixIcon: IconButton(
          tooltip: visible ? 'hide_password'.tr : 'show_password'.tr,
          icon: Icon(visible ? Icons.visibility : Icons.visibility_off),
          onPressed: onToggleVisibility,
        ),
      ),
    );
  }
}

/// Barre simple de robustesse
class PasswordStrengthBar extends StatelessWidget {
  const PasswordStrengthBar({super.key, required this.passwordListenable});
  final TextEditingController passwordListenable;

  int _score(String s) {
    var score = 0;
    if (s.length >= 8) score++;
    if (RegExp(r'[A-Z]').hasMatch(s)) score++;
    if (RegExp(r'[a-z]').hasMatch(s)) score++;
    if (RegExp(r'\d').hasMatch(s)) score++;
    if (RegExp(r'[!@#\$%^&*(),.?":{}|<>_\-\\/\[\]]').hasMatch(s)) score++;
    return score.clamp(0, 5);
  }

  String _label(int score) {
    switch (score) {
      case 0:
      case 1:
        return 'weak'.tr;
      case 2:
      case 3:
        return 'medium'.tr;
      default:
        return 'strong'.tr;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: passwordListenable,
      builder: (_, value, __) {
        final s = value.text;
        final score = _score(s);
        final percent = score / 5.0;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(6.r),
              child: LinearProgressIndicator(
                value: percent == 0 ? 0.02 : percent,
                minHeight: 8.h,
              ),
            ),
            SizedBox(height: 6.h),
            Row(
              children: [
                Icon(Icons.shield, size: 16.sp, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 6.w),
                Text(
                  '${'strength'.tr}: ${_label(score)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}

/// Liste des critères
class PasswordRequirements extends StatelessWidget {
  const PasswordRequirements({super.key, required this.passwordListenable});
  final TextEditingController passwordListenable;

  bool _hasUpper(String s) => RegExp(r'[A-Z]').hasMatch(s);
  bool _hasLower(String s) => RegExp(r'[a-z]').hasMatch(s);
  bool _hasDigit(String s) => RegExp(r'\d').hasMatch(s);
  bool _hasSymbol(String s) => RegExp(r'[!@#\$%^&*(),.?":{}|<>_\-\\/\[\]]').hasMatch(s);
  bool _hasLen(String s) => s.length >= 8;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: passwordListenable,
      builder: (_, value, __) {
        final s = value.text;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _RequirementRow(text: 'req_min_length'.tr, ok: _hasLen(s)),
            _RequirementRow(text: 'req_uppercase'.tr, ok: _hasUpper(s)),
            _RequirementRow(text: 'req_lowercase'.tr, ok: _hasLower(s)),
            _RequirementRow(text: 'req_digit'.tr, ok: _hasDigit(s)),
            _RequirementRow(text: 'req_symbol'.tr, ok: _hasSymbol(s)),
          ],
        );
      },
    );
  }
}

class _RequirementRow extends StatelessWidget {
  const _RequirementRow({required this.text, required this.ok});
  final String text;
  final bool ok;

  @override
  Widget build(BuildContext context) {
    final color = ok ? Colors.green : Theme.of(context).colorScheme.onSurfaceVariant;
    final icon = ok ? Icons.check_circle : Icons.radio_button_unchecked;
    return Padding(
      padding: EdgeInsets.only(bottom: 6.h),
      child: Row(
        children: [
          Icon(icon, size: 16.sp, color: color),
          SizedBox(width: 6.w),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: color),
            ),
          ),
        ],
      ),
    );
  }
}
