import 'dart:ui';

import 'package:boutigak/views/profil/address_management_page.dart';
import 'package:boutigak/views/profil/delete_account_page.dart';
import 'package:boutigak/views/profil/my_items.dart';
import 'package:boutigak/views/profil/settings_page.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:get/get.dart';

import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import 'boutigak/boutigak_user_page.dart';
import 'password_page.dart';

import 'my_information_page.dart';
import 'my_order_page.dart';

import 'privacy_police_page.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:boutigak/controllers/badge_controller.dart';


import 'dart:ui' show PlatformDispatcher;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
// Si tu utilises des tailles responsives (.h/.w/.sp), garde tes imports habituels.
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// Si tu as Shimmer déjà présent dans le projet :
import 'package:shimmer/shimmer.dart';

// import 'app_colors.dart'; // si tu as AppColors; je m'appuie surtout sur Theme pour la cohérence.

class LanguageSelector extends StatefulWidget {
  const LanguageSelector({Key? key}) : super(key: key);

  @override
  State<LanguageSelector> createState() => _LanguageSelectorState();
}

class _LanguageSelectorState extends State<LanguageSelector> {
  Locale? selectedLocale;
  final AuthController authController = Get.find<AuthController>();

  /// Options proposées (tu peux changer les drapeaux si tu veux)
  final List<_LangOption> _options = const [
    _LangOption(flag: '🇲🇷', labelKey: 'arabic', locale: Locale('ar', 'SA')),
    _LangOption(flag: '🇫🇷', labelKey: 'french', locale: Locale('fr', 'FR')),
    _LangOption(flag: '🇬🇧', labelKey: 'english', locale: Locale('en', 'GB')),
  ];

  @override
  void initState() {
    super.initState();
    _loadLocale();
  }

  bool _sameLanguage(Locale? a, Locale? b) {
    if (a == null || b == null) return false;
    return a.languageCode.toLowerCase() == b.languageCode.toLowerCase();
  }

  Future<void> _loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    String? languageCode = prefs.getString('language_code');
    String? countryCode  = prefs.getString('country_code');

    if (languageCode == null || countryCode == null) {
      // Fallback sur la langue de l’appareil
      final deviceLocale = PlatformDispatcher.instance.locale;
      languageCode = deviceLocale.languageCode;
      countryCode  = deviceLocale.countryCode ?? 'GB';
      await _saveLocale(Locale(languageCode, countryCode));
    }

    final loc = Locale(languageCode, countryCode);
    setState(() => selectedLocale = loc);
    Get.updateLocale(loc);
  }

  Future<void> _saveLocale(Locale locale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language_code', locale.languageCode);
    await prefs.setString('country_code', locale.countryCode ?? 'GB');
  }

  Future<void> _changeLanguage(Locale locale) async {
    // UI feedback rapide
    setState(() => selectedLocale = locale);

    // Persiste + GetX locale + notifie authController
    await _saveLocale(locale);
    Get.updateLocale(locale);
    authController.changeLanguage(locale.languageCode);

    // Snack discret
    final langName = _options
        .firstWhereOrNull((o) => _sameLanguage(o.locale, locale))
        ?.labelKey
        .tr;
    if (mounted) {
      Get.snackbar(
        'settings'.tr,
        'language_changed'.trParams({'lang': langName ?? locale.languageCode.toUpperCase()}),
        snackPosition: SnackPosition.BOTTOM,
        margin: const EdgeInsets.all(12),
        backgroundColor: Colors.black.withOpacity(0.75),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme     = Theme.of(context);
    final isLoading = selectedLocale == null;

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      appBar: AppBar(
        title: Text('select_language'.tr, style: const TextStyle(fontWeight: FontWeight.w700)),
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // Dégradé en fond (comme les autres pages)
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.95),
                  ],
                ),
              ),
            ),
          ),

          // Contenu
          CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                  child: Material(
                    color: Colors.white.withOpacity(0.10),
                    elevation: 0,
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.white.withOpacity(0.25)),
                      ),
                      child: isLoading
                          ? const _HeaderSkeleton()
                          : _HeaderPreview(
                              locale: selectedLocale!,
                              options: _options,
                              sameLanguage: _sameLanguage,
                            ),
                    ),
                  ),
                ),
              ),

              // Surface blanche avec les cartes de langue
              SliverFillRemaining(
                hasScrollBody: false,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.06),
                        blurRadius: 20,
                        offset: const Offset(0, -6),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(12, 20, 12, 24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        _SectionHeader(title: 'general_section'.tr), // “Général” (s’aligne RTL automatiquement)

                        if (isLoading) ...[
                          const _LangSkeletonCard(),
                          const SizedBox(height: 8),
                          const _LangSkeletonCard(),
                          const SizedBox(height: 8),
                          const _LangSkeletonCard(),
                        ] else ...[
                          for (final opt in _options) ...[
                            _LanguageCard(
                              option: opt,
                              selected: _sameLanguage(selectedLocale, opt.locale),
                              onTap: () => _changeLanguage(opt.locale),
                            ),
                            const SizedBox(height: 8),
                          ],
                        ],

                        const Spacer(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/* =========================
 *    WIDGETS D’INTERFACE
 * ========================= */

class _LangOption {
  final String flag;      // ex: "🇫🇷"
  final String labelKey;  // ex: "french"
  final Locale locale;    // ex: Locale('fr', 'FR')
  const _LangOption({required this.flag, required this.labelKey, required this.locale});
}

/// En-tête de section (RTL-aware)
class _SectionHeader extends StatelessWidget {
  final String title;
  const _SectionHeader({required this.title});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Align(
      alignment: AlignmentDirectional.centerStart, // start = left LTR / right RTL
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
        child: Text(
          title,
          textAlign: TextAlign.start,
          style: TextStyle(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
            letterSpacing: 0.2,
            fontSize: 12,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
    );
  }
}

/// Bandeau d’aperçu : langue actuelle (flag + nom)
class _HeaderPreview extends StatelessWidget {
  final Locale locale;
  final List<_LangOption> options;
  final bool Function(Locale?, Locale?) sameLanguage;

  const _HeaderPreview({
    required this.locale,
    required this.options,
    required this.sameLanguage,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final current = options.firstWhereOrNull((o) => sameLanguage(o.locale, locale));
    final title = current?.labelKey.tr ?? locale.languageCode.toUpperCase();
    final flag  = current?.flag ?? '🌐';

    return Row(
      children: [
        Container(
          width: 52,
          height: 52,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [Colors.white.withOpacity(0.28), Colors.white.withOpacity(0.12)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            border: Border.all(color: Colors.white.withOpacity(0.35)),
          ),
          child: Text(flag, style: const TextStyle(fontSize: 26)),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('select_language'.tr,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.95),
                    fontWeight: FontWeight.w800,
                    fontSize: 16,
                  )),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.85),
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Carte d’une langue
class _LanguageCard extends StatelessWidget {
  final _LangOption option;
  final bool selected;
  final VoidCallback onTap;

  const _LanguageCard({
    required this.option,
    required this.selected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme     = Theme.of(context);
    final border    = theme.dividerColor.withOpacity(0.2);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final selColor  = theme.colorScheme.primary;

    return Material(
      color: theme.colorScheme.surface,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: selected ? selColor.withOpacity(0.55) : border),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 14),
          child: Row(
            children: [
              // Flag container
              Container(
                width: 44,
                height: 44,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: selColor.withOpacity(0.08),
                ),
                child: Text(option.flag, style: const TextStyle(fontSize: 22)),
              ),
              const SizedBox(width: 12),

              // Label
              Expanded(
                child: Text(
                  option.labelKey.tr,
                  style: TextStyle(
                    fontSize: 14,
                    color: onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Trailing: check / chevron
              if (selected)
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: selColor.withOpacity(0.12),
                    shape: BoxShape.circle,
                    border: Border.all(color: selColor.withOpacity(0.5)),
                  ),
                  child: Icon(Icons.check, size: 16, color: selColor),
                )
              else
                Icon(
                  Directionality.of(context) == TextDirection.rtl
                      ? Icons.chevron_left
                      : Icons.chevron_right,
                  color: theme.disabledColor,
                ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Skeleton bandeau (header) : avatar + 2 barres
class _HeaderSkeleton extends StatelessWidget {
  const _HeaderSkeleton();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final base  = Colors.white.withOpacity(0.28);
    final high  = Colors.white.withOpacity(0.55);

    Widget bar(double w, double h, {double r = 8}) => Shimmer.fromColors(
          baseColor: base,
          highlightColor: high,
          period: const Duration(milliseconds: 1100),
          child: Container(width: w, height: h, decoration: BoxDecoration(color: base, borderRadius: BorderRadius.circular(r))),
        );

    return Row(
      children: [
        Shimmer.fromColors(
          baseColor: base,
          highlightColor: high,
          period: const Duration(milliseconds: 1100),
          child: Container(
            width: 52,
            height: 52,
            decoration: const BoxDecoration(shape: BoxShape.circle, color: Colors.white),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              bar(double.infinity, 14, r: 6),
              const SizedBox(height: 6),
              bar(MediaQuery.of(context).size.width * 0.35, 12, r: 6),
            ],
          ),
        ),
      ],
    );
  }
}

/// Skeleton d’une carte langue
class _LangSkeletonCard extends StatelessWidget {
  const _LangSkeletonCard();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final base  = theme.brightness == Brightness.dark ? Colors.white.withOpacity(0.10) : Colors.grey.shade300;
    final high  = theme.brightness == Brightness.dark ? Colors.white.withOpacity(0.25) : Colors.grey.shade100;

    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1100),
      child: Container(
        height: 72,
        decoration: BoxDecoration(
          color: base,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: theme.dividerColor.withOpacity(0.2)),
        ),
      ),
    );
  }
}
