import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:ui';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/payment_provider.dart'
    as models; // Add prefix here
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/services/payment_provider_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/profil/my_items.dart';
import 'package:boutigak/views/profil/my_order_page.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/navigation_bar.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:boutigak/controllers/payment_controller.dart';
import 'package:http/http.dart' as http;
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import 'dart:io';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/services.dart';


import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
// + tes autres imports (PaymentController, OrderController, ItemService, BankilyCheckoutPage, etc.)

class PaymentWidget extends StatefulWidget {
  double amount;
  final double initialAmount;
  final int? itemId;       // paiement d’un item (promo code possible)
  final int? orderId;      // paiement d’une commande
  final bool isOrderPayment;
  final bool isPaid;
  final int? storeId;

  PaymentWidget({
    super.key,
    required this.amount,
    required this.initialAmount,
    this.itemId,
    this.orderId,
    this.isOrderPayment = false,
    this.isPaid = false,
    this.storeId,
  });

  @override
  State<PaymentWidget> createState() => _PaymentWidgetState();
}

class _PaymentWidgetState extends State<PaymentWidget> {
  // GetX
  final PaymentController paymentController = Get.put(PaymentController());
  final OrderController orderController = Get.put(OrderController());

  final ImagePicker _picker = ImagePicker();

  // Providers
  List<dynamic> _providers = [];
  dynamic _selectedProvider;
  bool _isLoadingProviders = true;

  // Promo
  final _promoCtrl = TextEditingController();
  bool _isApplyingPromo = false;
  double? _discountedAmount;
  String? _promoMsg;
  bool _promoOk = false;

  // Upload
  XFile? _selectedImage;
  bool _isUploadingScreenshot = false;

  // ---------- Helpers provider (typed or Map) ----------
  bool _isStoreProvider(dynamic p) {


    log(' provider.... ${p}');
    try {
      return (p?.runtimeType.toString().contains('StorePayment') ?? false) ||
             (p is Map && (p['providerName'] != null || p['paymentCode'] != null));
    } catch (_) {
      return false;
    }


  }

  String _providerName(dynamic p) {
    try {
      if (_isStoreProvider(p)) {

        // print provider as json 

        
        try { return p.providerName as String; } catch (_) {}
      } else {
        try { return p.name as String; } catch (_) {}
      }
      if (p is Map) {
        return (p['providerName'] ?? p['name'] ?? '').toString();
      }
    } catch (_) {}
    return '';
  }

  String? _providerLogo(dynamic p) {
    try {
      if (_isStoreProvider(p)) {
        try { return p.providerLogo as String?; } catch (_) {}
      } else {
        try { return p.logoUrl as String?; } catch (_) {}
      }
      if (p is Map) {
        return (p['providerLogo'] ?? p['logoUrl'])?.toString();
      }
    } catch (_) {}
    return null;
  }

  String _providerCode(dynamic p) {
    try {
      if (_isStoreProvider(p)) {
        try { return (p.paymentCode ?? '') as String; } catch (_) {}
      } else {
        try { return p.providerCode as String; } catch (_) {}
      }
      if (p is Map) {
        return (p['paymentCode'] ?? p['providerCode'] ?? '').toString();
      }
    } catch (_) {}
    return '';
  }

  String? _providerPhone(dynamic p) {
    try {
      if (_isStoreProvider(p)) {
        try { return p.phoneNumber as String?; } catch (_) {}
      }
      if (p is Map) {
        return p['phoneNumber']?.toString();
      }
    } catch (_) {}
    return null;
  }

  int _providerId(dynamic p) {
    try {
      if (_isStoreProvider(p)) {
        try { return (p.providerId ?? 0) as int; } catch (_) {}
      } else {
        try { return p.id as int; } catch (_) {}
      }
      if (p is Map) {
        return (p['providerId'] ?? p['id'] ?? 0) as int;
      }
    } catch (_) {}
    return 0;
  }

  // ---------- Data ----------
  Future<void> _loadPaymentProviders() async {
    try {
      setState(() => _isLoadingProviders = true);
      if (widget.isOrderPayment && widget.storeId != null) {
        await paymentController.fetchStoreProvidersById(widget.storeId!);
        _providers = paymentController.storePaymentProviders;
      } else {
        await paymentController.fetchPaymentProviders();
        _providers = paymentController.paymentProviders;
      }
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        'providers_load_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red, colorText: Colors.white,
      );
    } finally {
      if (mounted) setState(() => _isLoadingProviders = false);
    }
  }

  // ---------- Promo ----------
  Future<void> _applyPromo() async {
    if (widget.itemId == null) return;
    final code = _promoCtrl.text.trim();
    if (code.isEmpty) {
      Get.snackbar('error'.tr, 'apply_promo_error_empty'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red, colorText: Colors.white);
      return;
    }
    setState(() { _isApplyingPromo = true; _promoMsg = null; });

    try {
      final res = await PaymentService.verifyPromoCode(
        promoCode: code,
        itemId: widget.itemId!,
        originalAmount: widget.amount,
      );

      final ok = res['success'] == true;
      final msg = (res['message'] ?? '').toString();
      final finalAmount = (res['final_amount'] ?? widget.amount).toDouble();

      setState(() {
        _isApplyingPromo = false;
        _promoOk = ok;
        _promoMsg = msg;
        _discountedAmount = ok ? finalAmount : null;
        if (ok) widget.amount = finalAmount;
      });

      Get.snackbar(
        ok ? 'success'.tr : 'error'.tr,
        msg.isEmpty ? (ok ? 'discount_applied'.tr : 'apply_promo_error_generic'.tr) : msg,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: ok ? Colors.green : Colors.red,
        colorText: Colors.white,
      );

      if (ok && finalAmount == 0) {
        if (widget.orderId != null) {
          Get.back(result: true);
        } else {
          Get.offAll(() => ZoomDrawerWrapper());
          Future.delayed(const Duration(milliseconds: 120), () {
            Get.to(() => MyItemsPage());
          });
        }
      }
    } catch (e) {
      setState(() { _isApplyingPromo = false; _promoOk = false; _promoMsg = 'apply_promo_error_generic'.tr; });
    }
  }

  // ---------- Upload ----------
  Future<void> _pickAndUploadScreenshot() async {
    if (_selectedProvider == null || _isUploadingScreenshot) return;

    final file = await _picker.pickImage(source: ImageSource.gallery);
    if (file == null) return;

    setState(() {
      _selectedImage = file;
      _isUploadingScreenshot = true;
    });

    try {
      final success = widget.orderId != null
          ? await orderController.orderPayment(
              providerId: _providerId(_selectedProvider),
              order_id: widget.orderId!,
              amount: widget.amount,
              paymentImage: File(file.path),
            )
          : await ItemService.addPayment(
              itemId: widget.itemId!,
              providerId: _providerId(_selectedProvider),
              amount: widget.amount,
              paymentType: 'photo',
              paymentImage: File(file.path),
            );

      if (success) {
        Get.snackbar('success'.tr, 'payment_success'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green, colorText: Colors.white);

        if (widget.orderId != null) {
          Get.offAll(() => ZoomDrawerWrapper(
                child: MyOrdersPage(),
                shouldOpenDrawer: false,
              ));
        } else {
          Get.offAll(() => ZoomDrawerWrapper());
          Future.delayed(const Duration(milliseconds: 120), () {
            Get.to(() => MyItemsPage());
          });
        }
      } else {
        Get.snackbar('error'.tr, 'payment_failed'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red, colorText: Colors.white);
      }
    } catch (e) {
      Get.snackbar('error'.tr, e.toString(),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red, colorText: Colors.white);
    } finally {
      if (mounted) setState(() => _isUploadingScreenshot = false);
    }
  }

  @override
  void initState() {
    super.initState();
    _loadPaymentProviders();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Palette claire “Settings-like”
    final bgTop = theme.colorScheme.primary;
    final bgMid = theme.colorScheme.primary.withOpacity(0.9);

    // -------- déjà payé --------
    if (widget.isPaid) {
      return Scaffold(
        extendBodyBehindAppBar: true,
        body: _GlassScaffold(
          title: 'checkout_title'.tr,
          bgTop: bgTop,
          bgMid: bgMid,
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 64),
                  const SizedBox(height: 12),
                  Text('already_paid_title'.tr,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: Colors.white)),
                  const SizedBox(height: 6),
                  Text('already_paid_subtitle'.tr,
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: Colors.white70)),
                ],
              ),
            ),
          ),
        ),
      );
    }

    // -------- écran normal --------
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: _GlassScaffold(
        title: 'checkout_title'.tr,
        bgTop: bgTop,
        bgMid: bgMid,
        child: SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _GlassSection(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('amount_to_pay'.tr,
                        style: const TextStyle(fontSize: 14, color: Colors.white70)),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        Text(
                          '${widget.amount.toStringAsFixed(2)} MRU',
                          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
                        ),
                        const Spacer(),
                        if (_discountedAmount != null) const _Badge(text: 'Discount'),
                      ],
                    ),
                    if (widget.itemId != null) ...[
                      const SizedBox(height: 14),
                      Divider(color: Colors.white24),
                      const SizedBox(height: 12),

                      Text('promo_code'.tr,
                          style: const TextStyle(fontWeight: FontWeight.w700, color: Colors.white)),
                      const SizedBox(height: 10),

                      Row(
                        children: [
                          Expanded(
                            child: _GlassTextField(
                              controller: _promoCtrl,
                              hintText: 'enter_promo_code'.tr,
                            ),
                          ),
                          const SizedBox(width: 10),
                          _PrimaryButton(
                            onPressed: _isApplyingPromo ? null : _applyPromo,
                            child: _isApplyingPromo
                                ? const SizedBox(
                                    width: 18, height: 18, child: CircularProgressIndicator(strokeWidth: 2))
                                : Text('apply'.tr,style: TextStyle(fontSize:12 ),),
                          ),
                        ],
                      ),

                      if (_promoMsg != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          _promoMsg!,
                          style: TextStyle(
                            color: _promoOk ? Colors.greenAccent : Colors.redAccent,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                      if (_discountedAmount != null) ...[
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('initial_price'.tr, style: const TextStyle(color: Colors.white70)),
                            Text(
                              '${widget.initialAmount.toStringAsFixed(2)} MRU',
                              style: const TextStyle(
                                decoration: TextDecoration.lineThrough,
                                color: Colors.white60,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('price_after_discount'.tr,
                                style: const TextStyle(fontWeight: FontWeight.w700, color: Colors.white)),
                            Text(
                              '${_discountedAmount!.toStringAsFixed(2)} MRU',
                              style: const TextStyle(
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 16),

              _GlassSection(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const _SectionTitle(title: 'payment_options'),
                    const SizedBox(height: 4),
                    const _SectionSub(textKey: 'payment_options_sub'),
                    const SizedBox(height: 14),

                    _GlassTile(
                      title: 'screenshot_payment',
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (_isLoadingProviders)
                            const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8),
                              child: Center(child: CircularProgressIndicator()),
                            )
                          else
                           Container(
  decoration: BoxDecoration(
    color: Colors.white.withOpacity(0.04),
    border: Border.all(color: Colors.white24),
    borderRadius: BorderRadius.circular(12),
  ),
  child: DropdownButtonHideUnderline(
    child: DropdownButton<dynamic>(
      dropdownColor: Colors.white,
      iconEnabledColor: Colors.white,
      isExpanded: true,
      value: _selectedProvider,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      hint: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: Text(
          'choose_payment_method'.tr,
          style: const TextStyle(color: Colors.white70),
        ),
      ),
      // --- Options quand le menu est ouvert (noir sur fond blanc)
      items: _providers.map((p) {
        final logo = _providerLogo(p);
        return DropdownMenuItem<dynamic>(
          value: p,
          child: Row(
            children: [
              (logo != null && logo.isNotEmpty)
                  ? Image.network(
                      logo,
                      width: 22,
                      height: 22,
                      errorBuilder: (_, __, ___) => const Icon(
                        Icons.payment,
                        size: 22,
                        color: Colors.black87,
                      ),
                    )
                  : const Icon(Icons.payment, size: 22, color: Colors.black87),
              const SizedBox(width: 10),
              Text(
                _providerName(p),
                style: const TextStyle(fontSize: 15, color: Colors.black87),
              ),
            ],
          ),
        );
      }).toList(),
      // --- Rendu quand le menu est fermé (blanc sur fond glass)
      selectedItemBuilder: (ctx) {
        return _providers.map((p) {
          final logo = _providerLogo(p);
          return Row(
            children: [
              (logo != null && logo.isNotEmpty)
                  ? Image.network(
                      logo,
                      width: 22,
                      height: 22,
                      errorBuilder: (_, __, ___) => const Icon(
                        Icons.payment,
                        size: 22,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.payment, size: 22, color: Colors.white),
              const SizedBox(width: 10),
              Text(
                _providerName(p),
                style: const TextStyle(fontSize: 15, color: Colors.white),
              ),
            ],
          );
        }).toList();
      },
      onChanged: (p) => setState(() => _selectedProvider = p),
    ),
  ),
),


                          if (_selectedProvider != null) ...[
                            const SizedBox(height: 10),
                            Wrap(
                              spacing: 8,
                              runSpacing: 6,
                              children: [
                                if (_providerCode(_selectedProvider).isNotEmpty)
                                  _InfoPill(
                                    icon: Icons.lock_outline,
                                    label: 'payment_code'.tr,
                                    value: _providerCode(_selectedProvider),
                                    onCopy: () => _copyToClipboard(_providerCode(_selectedProvider)),
                                  ),
                                if (_providerPhone(_selectedProvider) != null)
                                  _InfoPill(
                                    icon: Icons.phone_outlined,
                                    label: 'phone_number_short'.tr,
                                    value: _providerPhone(_selectedProvider)!,
                                    onCopy: () => _copyToClipboard(_providerPhone(_selectedProvider)!),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 6),
                            Text(
                              'make_payment_and_upload'.tr,
                              style: const TextStyle(color: Colors.white70),
                            ),
                            if (_selectedImage != null) ...[
                              const SizedBox(height: 10),
                              ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Image.file(
                                  File(_selectedImage!.path),
                                  height: 140,
                                  width: double.infinity,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ],
                          ],

                          const SizedBox(height: 12),
                          SizedBox(
                            width: double.infinity,
                            child: _PrimaryButton.icon(
                              onPressed: (_selectedProvider == null || _isUploadingScreenshot)
                                  ? null
                                  : _pickAndUploadScreenshot,
                              icon: _isUploadingScreenshot
                                  ? const SizedBox(
                                      width: 18, height: 18, child: CircularProgressIndicator(strokeWidth: 2))
                                  : const Icon(Icons.upload_file, color: Colors.white, size: 18),
                              label: Text(
                                _isUploadingScreenshot ? 'uploading'.tr : 'upload_screenshot'.tr,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    if (widget.itemId != null) ...[
                      const SizedBox(height: 12),
                      _GlassTile(
                        title: 'pay_with_ebankily_title',
                        child: SizedBox(
                          width: double.infinity,
                          child: _PrimaryButton(
                            onPressed: () {
                              showDialog(
                                context: context,
                                barrierDismissible: true,
                                builder: (_) => BankilyCheckoutPage(
                                  amount: widget.amount,
                                  itemId: widget.itemId!,
                                ),
                              );
                            },
                            child: Text('pay_with_ebankily_button'.tr,
                                style: const TextStyle(color: Colors.white)),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // <<< AVAIT ÉTÉ MAL POSITIONNÉ : maintenant à l'intérieur de la classe >>>
  Future<void> _copyToClipboard(String v) async {
    await Clipboard.setData(ClipboardData(text: v));
    Get.snackbar('copied'.tr, 'copied_desc'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.black87, colorText: Colors.white);
  }
}

/* ======================= Glass / Settings-like UI ======================= */

class _GlassScaffold extends StatelessWidget {
  final String title;
  final Widget child;
  final Color bgTop;
  final Color bgMid;

  const _GlassScaffold({
    required this.title,
    required this.child,
    required this.bgTop,
    required this.bgMid,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Dégradé de fond
        Positioned.fill(
          child: DecoratedBox(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [bgTop, bgMid, bgMid],
              ),
            ),
          ),
        ),

        // Contenu + header flouté
        SafeArea(
          top: true,
          bottom: false,
          child: Column(
            children: [
              const SizedBox(height: 8),
              // Header “verre dépoli”
              Padding(
                padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
                    child: Container(
                      height: 56,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.25),
                        border: Border.all(color: Colors.white.withOpacity(0.35)),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(width: 8),
                          _GlassIconButton(
                            icon: Icons.arrow_back_ios_new_rounded,
                            onTap: () => Get.back(),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              title,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w700,
                                fontSize: 18,
                              ),
                            ),
                          ),
                          const SizedBox(width: 48), // équilibre visuel avec le bouton retour
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // Corps
              Expanded(child: child),
            ],
          ),
        ),
      ],
    );
  }
}

class _GlassIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _GlassIconButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: InkWell(
          onTap: onTap,
          child: const SizedBox(
            width: 40,
            height: 40,
            child: Icon(Icons.arrow_back_ios_new_rounded, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

class _GlassSection extends StatelessWidget {
  final Widget child;
  const _GlassSection({required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.06),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white24),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.15), blurRadius: 18, offset: const Offset(0, 10))
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: child,
    );
  }
}

class _GlassTile extends StatelessWidget {
  final String title; // key i18n
  final Widget child;
  const _GlassTile({required this.title, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white24),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title.tr, style: const TextStyle(fontWeight: FontWeight.w700, color: Colors.white)),
          const SizedBox(height: 10),
          child,
        ],
      ),
    );
  }
}

class _SectionTitle extends StatelessWidget {
  final String title;
  const _SectionTitle({required this.title});

  @override
  Widget build(BuildContext context) {
    return Text(title.tr, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w700, color: Colors.white));
  }
}

class _SectionSub extends StatelessWidget {
  final String textKey;
  const _SectionSub({required this.textKey});

  @override
  Widget build(BuildContext context) {
    return Text(textKey.tr, style: const TextStyle(color: Colors.white70));
  }
}

class _Badge extends StatelessWidget {
  final String text;
  const _Badge({required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.10),
        borderRadius: BorderRadius.circular(999),
        border: Border.all(color: Colors.green.withOpacity(0.35)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.local_offer, size: 16, color: Colors.greenAccent),
          const SizedBox(width: 6),
          Text(text, style: const TextStyle(color: Colors.greenAccent, fontWeight: FontWeight.w700)),
        ],
      ),
    );
  }
}

class _InfoPill extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final VoidCallback? onCopy;

  const _InfoPill({
    required this.icon,
    required this.label,
    required this.value,
    this.onCopy,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Colors.white.withOpacity(0.08),
        border: Border.all(color: Colors.white24),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.white),
          const SizedBox(width: 8),
          Text('$label: ', style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w700)),
          Text(value, style: const TextStyle(color: Colors.white)),
          if (onCopy != null) ...[
            const SizedBox(width: 8),
            InkWell(
              onTap: onCopy,
              borderRadius: BorderRadius.circular(6),
              child: const Padding(
                padding: EdgeInsets.all(4.0),
                child: Icon(Icons.copy, size: 16, color: Colors.white70),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class _PrimaryButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;

  const _PrimaryButton({required this.onPressed, required this.child});

  factory _PrimaryButton.icon({required VoidCallback? onPressed, required Widget icon, required Widget label}) {
    return _PrimaryButton(
      onPressed: onPressed,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [icon, const SizedBox(width: 8), label],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withOpacity(0.18),
        foregroundColor: Colors.white,
        side: const BorderSide(color: Colors.white30),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(vertical: 12),
        elevation: 0,
      ),
      child: child,
    );
  }
}

class _GlassTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  const _GlassTextField({required this.controller, required this.hintText});

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      style: const TextStyle(color: Colors.white),
      cursorColor: Colors.white,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: const TextStyle(color: Colors.white60),
        isDense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        filled: true,
        fillColor: Colors.white.withOpacity(0.08),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.white30),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.white),
        ),
      ),
    );
  }
}

class BankilyCheckoutPage extends StatefulWidget {
  final double amount;
  final int itemId;

  const BankilyCheckoutPage({
    Key? key,
    required this.amount,
    required this.itemId,
  }) : super(key: key);

  @override
  State<BankilyCheckoutPage> createState() => _BankilyCheckoutPageState();
}

class _BankilyCheckoutPageState extends State<BankilyCheckoutPage> {
  final TextEditingController _phoneController = TextEditingController();

  bool _isLoading = false;

  String _codeBpay = "";

  Future<void> _processPayment() async {
    if (_phoneController.text.isEmpty || _codeBpay.length < 4) {
      Get.snackbar(
        'Error',
        'Please enter both phone number and code',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await PaymentService.processBPayPayment(
        phone: _phoneController.text,
        passcode: _codeBpay,
        amount: widget.amount,
        itemId: widget.itemId.toString(),
      );

      setState(() => _isLoading = false);

      if (result['success']) {
        final transactionData = result['data'];

        _showPaymentSuccessDialog(transactionData);
      } else {
        Get.snackbar(
          'Error',
          result['message'],
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      Get.snackbar(
        'Error',
        'Failed to process payment',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _showPaymentSuccessDialog(Map<String, dynamic> transactionData) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Dialog(
          child: Container(
            width: 350,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 16,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Color(0xFF00B8D4),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 18),
                  child: Center(
                    child: Column(
                      children: [
                        Text(
                          'Transfert réussi!',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),
                    Text('Annonce sur Boutigak duree de 3 mois',
                        style: TextStyle(fontSize: 16)),
                    const SizedBox(height: 12),
                    Icon(Icons.check_circle,
                        color: Color(0xFF00B8D4), size: 58),
                    const SizedBox(height: 16),
                    Text('Receveur: ${_phoneController.text}',
                        style: TextStyle(fontSize: 16)),
                    const SizedBox(height: 12),
                    Text('Montant envoyé: ${transactionData['amount']} MRU',
                        style: TextStyle(fontSize: 16)),
                    const SizedBox(height: 12),
                    Text('Trs ID : ${transactionData['transaction_id']}',
                        style:
                            TextStyle(fontSize: 16, color: Colors.grey[700])),
                    const SizedBox(height: 12),
                    Text(
                        'Date et heure: ${DateTime.now().toString().substring(0, 16)}',
                        style:
                            TextStyle(fontSize: 16, color: Colors.grey[700])),
                    const SizedBox(height: 18),
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          icon: Icon(Icons.picture_as_pdf),
                          label: Text(
                            'Télécharger le reçu PDF',
                            style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[800],
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          onPressed: () async {
                            await _generateAndSharePdf(transactionData);
                          },
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 20.0),
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFF00B8D4),
                            shape: RoundedRectangleBorder(),
                            padding: const EdgeInsets.symmetric(vertical: 14),
                          ),
                          onPressed: () {
                            Navigator.of(context).pop();
                            Get.offAll(() => ZoomDrawerWrapper());
                            Future.delayed(Duration(milliseconds: 300), () {
                              Get.to(() => MyItemsPage());
                            });
                          },
                          child: const Text(
                            'Effectué',
                            style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _generateAndSharePdf(
      Map<String, dynamic> transactionData) async {
    try {
      final pdf = pw.Document();

      // Charger le logo (assure-toi d'avoir une image dans assets/images/logo.png)
      final imageLogo =
          await imageFromAssetBundle('assets/images/biglogo_boutigak.png');

      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Padding(
              padding: pw.EdgeInsets.all(24),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  // Logo et titre
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.start,
                    children: [
                      pw.Image(imageLogo, width: 160),
                      pw.SizedBox(width: 700),
                    ],
                  ),
                  pw.SizedBox(height: 30),
                  pw.Text(
                    'Reçu de Paiement',
                    style: pw.TextStyle(
                      fontSize: 22,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),

                  pw.SizedBox(height: 30),
                  pw.Text('Annonce sur Boutigak - 3 mois',
                      style: pw.TextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                      )),
                  pw.SizedBox(height: 30),
                  // Détails transaction
                  pw.Text('Détails de la transaction',
                      style: pw.TextStyle(
                          fontSize: 16, fontWeight: pw.FontWeight.bold)),
                  pw.Divider(),
                  pw.SizedBox(height: 10),
                  pw.Text('Receveur : ${_phoneController.text}',
                      style: const pw.TextStyle(fontSize: 14)),
                  pw.Text('Montant : ${transactionData['amount']} MRU',
                      style: const pw.TextStyle(fontSize: 14)),

                  pw.Text(
                      'Transaction ID : ${transactionData['transaction_id']}',
                      style: const pw.TextStyle(fontSize: 14)),
                  pw.Text('Payment ID : ${transactionData['payment_id']}',
                      style: const pw.TextStyle(fontSize: 14)),
                  pw.SizedBox(height: 10),
                  pw.Text(
                      'Date : ${DateTime.now().toString().substring(0, 16)}',
                      style: const pw.TextStyle(fontSize: 12)),

                  pw.Spacer(),

                  // Bas de page
                  pw.Divider(),
                  pw.Text(
                    'Merci pour votre paiement via B-PAY',
                    style: pw.TextStyle(
                        fontSize: 12, fontStyle: pw.FontStyle.italic),
                  ),
                ],
              ),
            );
          },
        ),
      );

      final pdfBytes = await pdf.save();

      await Printing.sharePdf(
        bytes: pdfBytes,
        filename: 'receipt_bpay_${transactionData['transaction_id']}.pdf',
      );
    } catch (e) {
      debugPrint('Erreur lors de la génération du PDF : $e');
      Get.snackbar(
        'Erreur PDF',
        'Impossible de générer le PDF',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 350,
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 16,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Color(0xFF00B8D4),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Center(
                child: Text(
                  'Confirmer le transfert',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Annonce sur boutigak de 3 mois ',
                      style: TextStyle(
                        fontWeight: FontWeight.w800,
                        color: Colors.grey[700],
                        fontSize: 18,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 32),
                _buildDetailRow('Total à payer: ',
                    '${widget.amount.toStringAsFixed(2)} MRU'),
                const SizedBox(height: 32),
                _buildDetailRow('Code B-PAY: ', '019930'),
                const SizedBox(height: 24),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 40.0),
                  child: TextField(
                    controller: _phoneController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(8),
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    decoration: InputDecoration(
                      hintText: 'Téléphone',
                      suffixIcon: const Icon(Icons.phone),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                CodeBpayForm(
                  onCodeEntered: (code) {
                    setState(() {
                      _codeBpay = code;
                    });
                  },
                ),
                const SizedBox(height: 24),
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFF00B8D4),
                            shape: RoundedRectangleBorder(),
                            padding: const EdgeInsets.symmetric(vertical: 14),
                          ),
                          onPressed: () async {
                            await _processPayment();
                          },
                          child: const Text(
                            'Continuer',
                            style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white),
                          ),
                        ),
                      ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

Widget _buildDetailRow(String label, String value) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Text(label, style: TextStyle(color: Colors.grey[700], fontSize: 16)),
      Text(value, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
    ],
  );
}

class CodeBpayForm extends StatefulWidget {
  final Function(String) onCodeEntered; // Callback pour récupérer le code B-PAY

  const CodeBpayForm({Key? key, required this.onCodeEntered}) : super(key: key);

  @override
  _CodeBpayFormState createState() => _CodeBpayFormState();
}

class _CodeBpayFormState extends State<CodeBpayForm> {
  final List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());
  final List<TextEditingController> _controllers =
      List.generate(4, (index) => TextEditingController());

  @override
  void initState() {
    super.initState();

    // Écoute des changements pour envoyer le code au parent
    for (int i = 0; i < 4; i++) {
      _controllers[i].addListener(() {
        String enteredCode = _controllers.map((c) => c.text).join();
        widget.onCodeEntered(enteredCode);
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  /// Gère le passage automatique d'un champ à l'autre
  void _onChanged(String value, int index) {
    if (value.length > 1) {
      final characters = value.split('');
      for (int i = 0; i < characters.length; i++) {
        final pos = index + i;
        if (pos < 4) {
          _controllers[pos].text = characters[i];
        }
      }
      final nextPos = index + value.length;
      if (nextPos < 4) {
        FocusScope.of(context).requestFocus(_focusNodes[nextPos]);
      } else {
        FocusScope.of(context).unfocus();
      }
    } else {
      if (value.isNotEmpty && index < 3) {
        FocusScope.of(context).requestFocus(_focusNodes[index + 1]);
      } else if (value.isEmpty && index > 0) {
        FocusScope.of(context).requestFocus(_focusNodes[index - 1]);
      }
    }
    widget.onCodeEntered(_controllers.map((c) => c.text).join());
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Container(
          width: 300,
          height: 60,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(4, (index) {
              return SizedBox(
                width: 50,
                height: 60,
                child: TextFormField(
                  controller: _controllers[index],
                  focusNode: _focusNodes[index],
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  keyboardType: TextInputType.number,
                  textInputAction:
                      index < 3 ? TextInputAction.next : TextInputAction.done,
                  autofillHints: const [AutofillHints.oneTimeCode],
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(1),
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  onChanged: (value) => _onChanged(value, index),
                  decoration: InputDecoration(
                    counterText: "",
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
        // Icon(Icons.lock),
      ],
    );
  }
}
