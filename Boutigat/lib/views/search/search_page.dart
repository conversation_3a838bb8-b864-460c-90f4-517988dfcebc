import 'dart:developer';
import 'dart:ui';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/item_widget.dart';
import 'package:boutigak/views/widgets/safety_notice.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/search_controller.dart';
import 'package:get/get.dart';
import 'package:flutter/cupertino.dart';
import 'package:shimmer/shimmer.dart';

import '../../data/services/store_service.dart';





class SearchPage extends StatefulWidget {
  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final MYSearchController controller = Get.put(MYSearchController());
  final TextEditingController textController = TextEditingController();
  final AuthController authController = Get.put(AuthController());

  final FocusNode focusNode = FocusNode();


  @override
  void initState() {
    super.initState();
    controller.searchText.listen((value) {
      if (textController.text != value) {
        textController.text = value;
      }
    });



    print('here in search widget');
    

    if (authController.isAuthenticated.value ) {
       controller.fetchSearchHistory();
    }
   
    controller.getPopularCategories();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Column(
          children: [
            AppBar(
              automaticallyImplyLeading: false,
              title: CupertinoTextField(
                controller: textController,
                focusNode: focusNode,
                placeholder: "search".tr,
                prefix: IconButton(
                  icon: Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
                  onPressed: () {
                    controller.submitSearch();
                  },
                ),
                suffix: IconButton(
                  icon: Icon(Icons.clear, color: Theme.of(context).colorScheme.onSurface),
                  onPressed: () {
                    textController.clear();
                    controller.onSearchChanged('');
                    focusNode.unfocus();
                    controller.showHistory.value = true;
                  },
                ),
                onChanged: controller.onSearchChanged,
                onSubmitted: (value) {
                  controller.submitSearch();
                  focusNode.unfocus();
                },
              ),
              backgroundColor: Theme.of(context).colorScheme.surface,
            ),
          Expanded(
  child: Obx(() {
    final showHistory = controller.showHistory.value;

    return ListView(
      padding: EdgeInsets.zero,
      children: [
        // ===== Historique + Populaires =====
        if (showHistory) ...[
          // Historique
          ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.searchHistory.length,
            itemBuilder: (context, index) {
              final item = controller.searchHistory[index];
              return Column(
                children: [
                  ListTile(
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * 0.0407,
                    ),
                    title: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Icon(
                          FontAwesomeIcons.clockRotateLeft,
                          color: Theme.of(context).primaryColor,
                          size: 20,
                        ),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: MediaQuery.of(context).size.width * 0.0407,
                            ),
                            child: Text(
                              item.query,
                              style: TextStyle(
                                fontSize: 16,
                                color: Theme.of(context).colorScheme.onSurface,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                          onPressed: () => controller.removeSearchHistory(item.id),
                        ),
                      ],
                    ),
                    onTap: () => controller.submitHistorySearch(item.query),
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 12),
          PopularWordsWidget(
            words: controller.popularCategories,
            onWordSelected: controller.submitHistorySearch,
            searchController: controller,
          ),
        ]

        // ===== Résultats =====
        else ...[
          Padding(
  padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
  child: Row(
    children: [
      if (controller.isLoadingItems.value)
        Text(
          " ... ", // ou "–"
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: AppTextSizes.bodyText,
            fontWeight: AppFontWeights.bold,
          ),
        )
      else
        Text(
          "${controller.items.length} ",
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: AppTextSizes.bodyText,
            fontWeight: AppFontWeights.bold,
          ),
        ),
      Text("results_for".tr),
      Text(
        "\"${controller.searchText.value}\"",
        style: TextStyle(
          color: Theme.of(context).colorScheme.onSurface,
          fontSize: AppTextSizes.bodyText,
          fontWeight: AppFontWeights.bold,
        ),
      ),
    ],
  ),
),


          // --- ICI: AnimatedSwitcher entre Shimmer et Résultats ---
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 250),
            switchInCurve: Curves.easeOut,
            switchOutCurve: Curves.easeIn,
            child: controller.isLoadingItems.value
                ? const _SearchItemsShimmerColumn(key: ValueKey('loading'))
                : Padding(
                    key: const ValueKey('results'),
                    padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
                    child: ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: controller.items.length,
                      itemBuilder: (context, index) {
                        final item = controller.items[index];
                        return SearchItemWidget(item: item);
                      },
                    ),
                  ),
          ),
        ],
      ],
    );
  }),
)


          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    textController.dispose();
    super.dispose();
  }
}



class PopularWordsWidget extends StatelessWidget {
  final List<String> words;
  final void Function(String) onWordSelected;
  final MYSearchController searchController;

  const PopularWordsWidget({
    super.key,
    required this.words,
    required this.onWordSelected,
    required this.searchController,
  });

  @override
  Widget build(BuildContext context) {
    // Check for language changes and refresh popular categories if needed
    searchController.refreshPopularCategoriesOnLanguageChange();

    final theme = Theme.of(context);
    final primary = AppColors.primary; // couleur de marque
    final screenWidth = MediaQuery.of(context).size.width;
    final sidePadding = screenWidth * 0.0407;

    // Sur fond blanc, un “frosted” subtil: blur léger + voile translucide + bord coloré
    Widget _glassChip(String word) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => onWordSelected(word),
              borderRadius: BorderRadius.circular(10),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: sidePadding * 0.625,
                  vertical: sidePadding / 2.2,
                ),
                decoration: BoxDecoration(
                  color: primary.withOpacity(0.07),           // voile léger teinté
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: primary.withOpacity(0.25), width: 1),
                  boxShadow: [
                    BoxShadow(
                      color: primary.withOpacity(0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  word,
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    color: primary,                           // texte en primary
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: sidePadding),
          child: Text(
            "popular".tr,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: AppColors.onSurface, // titre en primary
            ),
          ),
        ),
        SizedBox(height: sidePadding),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: sidePadding),
          child: Wrap(
            spacing: sidePadding / 2,
            runSpacing: sidePadding / 2,
            children: words.map(_glassChip).toList(),
          ),
        ),
      ],
    );
  }
}


// Un item shimmer qui copie le layout de SearchItemWidget
class SearchItemShimmer extends StatelessWidget {
  const SearchItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    // Couleurs lisibles sur fond blanc
    final base = Colors.grey.shade300;    // #E0E0E0
    final highlight = Colors.grey.shade100; // #F5F5F5

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Shimmer.fromColors(
        baseColor: base,
        highlightColor: highlight,
        period: const Duration(milliseconds: 1200),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // image
            Container(
              width: 50.0,
              height: 60.0,
              decoration: BoxDecoration(
                color: base,
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            const SizedBox(width: 16),

            // titre + marque
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(height: 14, width: 120, color: base),
                  const SizedBox(height: 8),
                  Container(height: 12, width: 120, color: base),
                ],
              ),
            ),

            // prix
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(height: 12, width: 70, color: base),
                const SizedBox(height: 6),
                Container(height: 14, width: 60, color: base),
              ],
            ),
          ],
        ),
      ),
    );
  }
}


// Une liste de shimmers
class _SearchItemsShimmerColumn extends StatelessWidget {
  final int count;
  const _SearchItemsShimmerColumn({super.key, this.count = 8});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
      child: Column(
        children: List.generate(count, (_) => const SearchItemShimmer()),
      ),
    );
  }
}


class SearchItemWidget extends StatelessWidget {
  final Item item;

  const SearchItemWidget({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        if (item.storeId != null) {
          try {
            final store = await StoreService.getStoreById(item.storeId!);
            if (store != null) {
              Get.to(() => StoreDetailsPage(
                    store: store,
                    selectedItemId: item.id,
                  ));
              return;
            }
          } catch (e) {
            debugPrint('Error fetching store: $e');
          }
        }

        // 👉 si pas de store, on utilise le bottom sheet réutilisable
        showItemDetailBottomSheet(context, item);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        child: Row(
          children: [
            // ✅ Image produit
            Container(
              width: 50.0,
              height: 60.0,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.0),
                image: DecorationImage(
                  fit: BoxFit.cover,
                  image: item.images.isNotEmpty
                      ? NetworkImage('${item.images.first}')
                      : const AssetImage('assets/default_image.png')
                          as ImageProvider,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 2,
                    blurRadius: 4,
                    offset: const Offset(0.5, 0.5),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),

            // ✅ Titre + marque
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.getTitle(),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.brandName ?? "Unknown",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).disabledColor,
                    ),
                  ),
                ],
              ),
            ),

            const Spacer(),

            // ✅ Prix
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "price".tr + " (" + "mru".tr + ")",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).disabledColor,
                  ),
                ),
                Text(
                  "${item.price.toStringAsFixed(0)} ",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
