import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/location.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/utils/dashline.dart';
import 'package:boutigak/views/boutigat_store/checkout_page.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/profil/address_management_page.dart';
import 'package:boutigak/views/home/<USER>';
import 'package:boutigak/views/profil/my_order_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:async';

import 'package:shimmer/shimmer.dart';










double _asDouble(dynamic v) {
  if (v == null) return 0;
  if (v is num) return v.toDouble();
  if (v is String) {
    // enlève % et tout ce qui n’est pas chiffre/point/signe
    final cleaned = v.replaceAll(RegExp(r'[^0-9\.\-]'), '');
    return double.tryParse(cleaned) ?? 0;
  }
  return 0;
}

String _fmtPct(double p) => (p % 1 == 0) ? p.toStringAsFixed(0) : p.toStringAsFixed(2);




class CartPage extends StatelessWidget {
  final OrderController orderController;
  final Store store;
  final Color dominentColor; // couleur dominante du logo
  final Color surfaceColor;  // couleur du texte / icônes sur la couleur dominante

  CartPage({
    Key? key,
    required this.orderController,
    required this.store,
    required this.dominentColor,
    required this.surfaceColor,
  }) : super(key: key) {
    // calcule initial du total
    orderController.calculateTotal();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: dominentColor,
        elevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 12, right: 12),
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: () => Get.back(),
            child: Container(
              padding: const EdgeInsets.all(12),
              child: Icon(Icons.arrow_back_ios_new, size: 18, color: surfaceColor),
            ),
          ),
        ),
        titleSpacing: 0,
        title: Text('your_cart'.tr, style: TextStyle(color: surfaceColor, fontWeight: FontWeight.w800)),
      ),

      body: Obx(() {
        return Column(
          children: [
            // Bande “résumé” en haut
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: (store.images.isNotEmpty && store.images.first.isNotEmpty)
                        ? Image.network(store.images.first, width: 60, height: 60, fit: BoxFit.cover)
                        : Container(
                            width: 60,
                            height: 60,
                            color: Colors.grey.shade300,
                            child: Icon(Icons.storefront, color: Colors.grey.shade700),
                          ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      store.name ?? 'store'.tr,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w800),
                    ),
                  ),
                ],
              ),
            ),

            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Row(
                children: [
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                        style: TextStyle(fontSize: 16, color: Colors.grey.shade700),
                        children: [
                          TextSpan(
                            text: '${orderController.items.length}',
                            style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                          ),
                          TextSpan(text: ' ${'items_from'.tr} '),
                          TextSpan(
                            text: store.name ?? 'store'.tr,
                            style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),

            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.fromLTRB(16, 6, 16, 160),
                itemCount: orderController.items.length,
                itemBuilder: (context, index) {
                  final orderItemController = orderController.items[index];
                  final itemTag = orderItemController.itemId.value.toString();

                  final itemController = Get.put(ItemController(), tag: itemTag);
                  if (itemController.selectedItem.value == null) {
                    itemController.fetchItemById(orderItemController.itemId.value);
                  }

                  return Obx(() {
                    final item = itemController.selectedItem.value;

                    if (item == null) {
                      return _CartItemShimmer(dominentColor: dominentColor);
                    }

                    final double promo = _asDouble(item.promotionPercentage);
                    final int qty = orderItemController.quantity.value;

                    final double lineTotalVal = item.price * qty;
                    final String lineTotal = lineTotalVal.toStringAsFixed(2);

                    return Container(
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(14),
                        border: Border.all(color: Colors.black12.withOpacity(0.06)),
                        boxShadow: [
                          BoxShadow(color: Colors.black.withOpacity(0.04), blurRadius: 10, offset: const Offset(0, 4)),
                        ],
                      ),
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [

                          ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: (item.images.isNotEmpty && item.images.first.isNotEmpty)
                                ? Image.network(item.images.first, width: 70, height: 86, fit: BoxFit.cover)
                                : Container(
                                    width: 70,
                                    height: 86,
                                    color: Colors.grey.shade200,
                                    child: Icon(Icons.image_outlined, color: Colors.grey.shade600),
                                  ),
                          ),


                          const SizedBox(width: 12),

                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        item.title,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ),

                                    if (promo > 0)
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: Colors.red.withOpacity(0.08),
                                          borderRadius: BorderRadius.circular(999),
                                          border: Border.all(color: Colors.red.withOpacity(0.28)),
                                        ),
                                        child: Text(
                                          "${_fmtPct(promo)}% ${'discount'.tr}",
                                          style: const TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.w700,
                                            color: Colors.red,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),

                                const SizedBox(height: 8),

                                Wrap(
                                  spacing: 8,
                                  runSpacing: 6,
                                  children: [
                                    _MiniPillCart(text: '${'price'.tr}: ${item.price.toStringAsFixed(2)} ${'mru'.tr}'),
                                    _MiniPillCart(text: '${'quantity'.tr}: $qty'),
                                  ],
                                ),

                                const SizedBox(height: 10),

                                // Bas de ligne : actions qty à gauche, total à droite
                                Row(
                                  children: [
                                    // Bouton - (ou suppr si qty=1)
                                    _QtyBtn(
                                      icon: Icons.remove_rounded,
                                      bg: Colors.grey.shade200,
                                      fg: Colors.black87,
                                      onTap: () {
                                        if (qty > 1) {
                                          orderItemController.decrementQuantity();
                                          orderController.calculateTotal();
                                        } else {
                                          _removeItemDialog(context, orderItemController.itemId.value);
                                        }
                                      },
                                    ),
                                    const SizedBox(width: 10),
                                    // Bouton +
                                    _QtyBtn(
                                      icon: Icons.add_rounded,
                                      bg: theme.colorScheme.primary.withOpacity(0.12),
                                      fg: theme.colorScheme.primary,
                                      onTap: () {
                                        orderItemController.incrementQuantity();
                                        orderController.calculateTotal();
                                      },
                                    ),

                                    const Spacer(),

                                    // Total de ligne
                                    Text(
                                      '$lineTotal ${'mru'.tr}',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w800,
                                        color: theme.colorScheme.primary,
                                        fontSize: 15,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  });
                },
              ),
            ),

            // BARRE TOTAL + CHECKOUT collante
            _BottomCheckoutBar(
  dominentColor: dominentColor,
  surfaceColor: surfaceColor,
  totalRx: orderController.total,
  onCheckout: () async {
   
    final authController = Get.isRegistered<AuthController>()
        ? Get.find<AuthController>()
        : Get.put(AuthController());

 
    if (!authController.isAuthenticated.value) {
      await Get.to(() => LoginPage());

     
      if (!authController.isAuthenticated.value) {
        return; 
      }
    }


    Get.to(() => CheckoutPage(
          orderController: orderController,
          store: store,
          dominentColor: dominentColor,
          surfaceColor: surfaceColor,
        ));
  },
)

          ],
        );
      }),
    );
  }

  // ========= Helpers visu =========

  void _removeItemDialog(BuildContext context, int itemId) {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Center(
          child: Image.asset('assets/images/effacer.png', width: 100, height: 100),
        ),
        content: Text('remove_item_confirm'.tr),
        actions: [
          TextButton(
            child: Text('cancel'.tr, style: TextStyle(color: theme.colorScheme.onSurface)),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            child: Text('remove'.tr, style: TextStyle(color: theme.colorScheme.error)),
            onPressed: () {
              orderController.removeItem(itemId);
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }
}
// ===================== Petits widgets UI =====================

class _MiniPillCart extends StatelessWidget {
  final String text;
  const _MiniPillCart({required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(999),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Text(
        text,
        style: TextStyle(fontSize: 11, color: Colors.grey.shade800),
      ),
    );
  }
}

class _QtyBtn extends StatelessWidget {
  final IconData icon;
  final Color bg;
  final Color fg;
  final VoidCallback onTap;
  const _QtyBtn({required this.icon, required this.bg, required this.fg, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: bg,
      shape: const CircleBorder(),
      child: InkWell(
        customBorder: const CircleBorder(),
        onTap: onTap,
        child: SizedBox(
          width: 30,
          height: 30,
          child: Icon(icon, color: fg, size: 18),
        ),
      ),
    );
  }
}




class _QtyIcon extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final Color bg;
  final Color fg;
  const _QtyIcon({required this.icon, required this.onPressed, required this.bg, required this.fg});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: bg,
      shape: const CircleBorder(),
      child: InkWell(
        customBorder: const CircleBorder(),
        onTap: onPressed,
        child: const SizedBox(width: 32, height: 32, child: Icon(Icons.add, size: 18)), // placeholder
      ),
    );
  }
}

// (corrige l’icône dynamique)
extension on _QtyIcon {
  Widget build(BuildContext context) {
    return Material(
      color: bg,
      shape: const CircleBorder(),
      child: InkWell(
        customBorder: const CircleBorder(),
        onTap: onPressed,
        child: SizedBox(width: 32, height: 32, child: Icon(icon, color: fg, size: 18)),
      ),
    );
  }
}

class _BottomCheckoutBar extends StatelessWidget {
  final Color dominentColor;
  final Color surfaceColor;
  final RxDouble totalRx;
  final VoidCallback onCheckout;

  const _BottomCheckoutBar({
    required this.dominentColor,
    required this.surfaceColor,
    required this.totalRx,
    required this.onCheckout,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkDominant = dominentColor.computeLuminance() < 0.3;

    return Material(
      elevation: 16,
    
      child: SafeArea(
        top: false,
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 10, 16, 12),
          
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // ligne pointillée si tu utilises déjà ta méthode
              dashedLine(),
              const SizedBox(height: 10),

              Row(
                children: [
                  Expanded(
                    child: Text(
                      'total'.tr,
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                  ),
                  Obx(() => Text(
                        '${totalRx.value.toStringAsFixed(2)} ${'mru'.tr}',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w900, color: AppColors.primary),
                      )),
                ],
              ),
              const SizedBox(height: 12),

              // Bouton Checkout (couleur dominante)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: onCheckout,
                
                  label: Text('checkout'.tr),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: dominentColor,
                    foregroundColor: isDarkDominant ? Colors.white : Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ===================== SHIMMER ITEM =====================

class _CartItemShimmer extends StatelessWidget {
  final Color dominentColor;
  const _CartItemShimmer({required this.dominentColor});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(14),
          border: Border.all(color: Colors.black12.withOpacity(0.06)),
        ),
        child: Row(
          children: [
            Container(width: 64, height: 84, decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10))),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(width: 28, height: 14, color: Colors.white),
                      const SizedBox(width: 8),
                      Expanded(child: Container(height: 14, color: Colors.white)),
                      const SizedBox(width: 8),
                      Container(width: 60, height: 14, color: Colors.white),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(width: 28, height: 28, decoration: const BoxDecoration(color: Colors.white, shape: BoxShape.circle)),
                      const SizedBox(width: 10),
                      Container(width: 28, height: 28, decoration: const BoxDecoration(color: Colors.white, shape: BoxShape.circle)),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
class LocationPickerWidget extends StatefulWidget {
  final OrderController orderController;

  const LocationPickerWidget({Key? key, required this.orderController}) : super(key: key);

  @override
  State<LocationPickerWidget> createState() => _LocationPickerWidgetState();
}

class _LocationPickerWidgetState extends State<LocationPickerWidget> {
  @override
  void initState() {
    super.initState();
    widget.orderController.fetchUserLocations();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(() {
          // remove duplicates
          final uniqueLocations = <Location>[];
          final seenIds = <int>{};

          for (final location in widget.orderController.userLocations) {
            if (location.id != null && !seenIds.contains(location.id)) {
              seenIds.add(location.id!);
              uniqueLocations.add(location);
            } else if (location.id == null) {
              final exists = uniqueLocations.any((l) =>
                  l.name == location.name &&
                  l.address == location.address &&
                  l.latitude == location.latitude &&
                  l.longitude == location.longitude);
              if (!exists) uniqueLocations.add(location);
            }
          }

          // ensure selected exists
          Location? validSelected = widget.orderController.selectedLocation.value;
          if (validSelected != null) {
            final exists = uniqueLocations.any((l) =>
                l.id == validSelected!.id &&
                l.name == validSelected.name &&
                l.address == validSelected.address);
            if (!exists) {
              validSelected = null;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                widget.orderController.selectedLocation.value = null;
                widget.orderController.isLocationSelected.value = false;
              });
            }
          }

          return DropdownButtonFormField<Location>(
            value: validSelected,
            hint: Text('select_delivery_address'.tr),
            items: uniqueLocations.map((Location location) {
              return DropdownMenuItem<Location>(
                value: location,
                child: Text('${location.name} - ${location.address}'),
              );
            }).toList(),
            onChanged: (Location? location) {
              if (location != null) {
                widget.orderController.selectedLocation.value = location;
                widget.orderController.isLocationSelected.value = true;
              }
            },
            decoration: InputDecoration(
              labelText: 'delivery_address'.tr,
              prefixIcon: const Icon(Icons.place_outlined),
              filled: true,
              fillColor: Colors.grey.shade50,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(14)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(14),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(14),
                borderSide: BorderSide(color: Colors.blueGrey.shade400, width: 1.5),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
            ),
          );
        }),

        const SizedBox(height: 12),

        TextButton.icon(
          onPressed: () => Get.to(() => AddressManagementPage()),
          icon: const Icon(Icons.add_location_alt_outlined),
          label: Text('manage_addresses'.tr),
        ),
      ],
    );
  }
}
