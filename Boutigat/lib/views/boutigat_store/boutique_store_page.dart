import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:shimmer/shimmer.dart';
import 'boutiques_store_widgets.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';




import 'dart:developer';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/physics.dart'; // SpringDescription
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';


class StorePage extends StatefulWidget {
  const StorePage({Key? key}) : super(key: key);

  @override
  State<StorePage> createState() => _StorePageState();
}

class _StorePageState extends State<StorePage> {
  final TextEditingController searchController = TextEditingController();
  final StoreController storeController = Get.put(StoreController());
  final AuthController authController = Get.find<AuthController>();

  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  final FocusNode focusNode = FocusNode();

  // --- Lottie sync comme HomePage ---
  Duration? _lottieCycle;
  DateTime? _refreshStart;

  // Header Lottie (identique au comportement HomePage)
  final CustomHeader _header = CustomHeader(
    height: 100,
    builder: (context, mode) {
      final state = context.findAncestorStateOfType<_StorePageState>();
      final bool isRefreshing = mode == RefreshStatus.refreshing;
      return SizedBox(
        height: 100.h,
        child: Center(
          child: Padding(
  padding: const EdgeInsets.only(top: 12), 
  child: Lottie.asset(
    'assets/lottie/loader.json',
    width: 75,
    height: 75,
    animate: isRefreshing,
    repeat: isRefreshing,
    onLoaded: (composition) {
      state?._lottieCycle = composition.duration;
    },
  ),
),

        ),
      );
    },
  );

  @override
  void initState() {
    super.initState();

    // Chargement initial
    if (storeController.recommendedStores.isEmpty) {
      storeController.fetchRecomandedStores(refresh: true);
    }

    // Infinite scroll (si tu utilises storeScrollController dans le contrôleur)
    storeController.storeScrollController.addListener(_onScrollStores);
  }

  @override
  void dispose() {
    storeController.storeScrollController.removeListener(_onScrollStores);
    searchController.dispose();
    focusNode.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _onScrollStores() {
    final c = storeController.storeScrollController;
    if (!c.hasClients || storeController.isLoadingMoreStores.value) return;

    final position = c.position.pixels;
    final maxScroll = c.position.maxScrollExtent;

    // Seuil avant le bas
    if (position > maxScroll - 600) {
      log('StorePage: loading more stores...');
      storeController.fetchRecomandedStores(); // pagination
    }
  }

  void _onSearchChanged(String value) {
    if (value.isEmpty) {
      storeController.clearSearch();
    } else {
      storeController.searchStores(value);
    }
  }

  // --- OnRefresh aligné HomePage : attend tout + 1 cycle Lottie min ---
  Future<void> _onRefresh() async {
    _refreshStart = DateTime.now();
    try {
      // attends la reco (et ajoute d'autres futures si tu en as)
      await storeController.fetchRecomandedStores(refresh: true);

      final minCycle = _lottieCycle ?? const Duration(milliseconds: 1200);
      final elapsed = DateTime.now().difference(_refreshStart!);
      final remaining = minCycle - elapsed;
      if (remaining > Duration.zero) {
        await Future.delayed(remaining);
      }

      // petit tampon visuel
      await Future.delayed(const Duration(milliseconds: 180));

      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,

      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight + 16),
        child: SafeArea(
          top: true, bottom: false, left: false, right: false,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            color: Theme.of(context).colorScheme.surface,
            child: Row(
              children: [
                Text(
                  'stores'.tr,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: AppTextSizes.heading,
                    fontWeight: AppFontWeights.bold,
                  ),
                ),
                const SizedBox(width: 12),

                Expanded(
                  child: CupertinoTextField(
                    controller: searchController,
                    focusNode: focusNode,
                    placeholder: "search".tr,
                    prefix: IconButton(
                      icon: Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
                      onPressed: () => _onSearchChanged(searchController.text),
                    ),
                    suffix: IconButton(
                      icon: Icon(Icons.clear, color: Theme.of(context).colorScheme.onSurface),
                      onPressed: () {
                        searchController.clear();
                        _onSearchChanged('');
                        focusNode.unfocus();
                      },
                    ),
                    onChanged: _onSearchChanged,
                    onSubmitted: (value) {
                      _onSearchChanged(value);
                      focusNode.unfocus();
                    },
                  ),
                ),

                const SizedBox(width: 12),

                GestureDetector(
                  onTap: () => ZoomDrawer.of(context)!.toggle(),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 6),
                    child: authController.isAuthenticated.value
                        ? CircleAvatar(
                            radius: 22,
                            backgroundColor: Colors.grey[300],
                            child: Text(
                              '${authController.user?.firstName[0] ?? ''}'
                              '${authController.user?.lastName[0] ?? ''}'.toUpperCase(),
                              style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                            ),
                          )
                        : Container(
                            padding: const EdgeInsets.all(5),
                            decoration: BoxDecoration(
                              shape: BoxShape.rectangle,
                              border: Border.all(
                                color: Theme.of(context).colorScheme.onSurface,
                                width: 2,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Icon(
                              FontAwesomeIcons.solidUser,
                              size: 15,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),

     body: Obx(() {
  // on construit TOUJOURS le même SmartRefresher
  final bool showShimmer = storeController.isLoading.value && storeController.recommendedStores.isEmpty;
  final bool showEmpty = storeController.recommendedStores.isEmpty && !storeController.isLoading.value;

  return RefreshConfiguration(
    headerTriggerDistance: 70,
    maxOverScrollExtent: 60,
    enableScrollWhenRefreshCompleted: true,
    springDescription: const SpringDescription(mass: 1.9, stiffness: 170, damping: 16),
    child: SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: false,
      header: _header,                 // <-- même header Lottie que HomePage
      onRefresh: _onRefresh,           // <-- attend ≥ 1 cycle + fetch terminé

      child: showShimmer
          ? ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              physics: const ClampingScrollPhysics(),
              itemCount: 5,
              itemBuilder: (context, index) => const BoutiqueShimmerWidget(),
            )
          : showEmpty
              ? const Center(child: Text("Aucune boutique disponible."))
              : ListView.builder(
                  controller: storeController.storeScrollController,
                  physics: const ClampingScrollPhysics(),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: storeController.recommendedStores.length +
                      (storeController.isLoadingMoreStores.value ? 1 : 0),
                  itemBuilder: (context, index) {
                    // Loader Cupertino en bas (comme HomePage)
                    if (index == storeController.recommendedStores.length) {
                      return const Padding(
                        padding: EdgeInsets.all(16),
                        child: Center(child: CupertinoActivityIndicator(radius: 15)),
                      );
                    }

                    final boutique = storeController.recommendedStores[index];
                    return BoutiqueWidget(
                      boutique: boutique,
                      fontSize: 18,
                      iconSize: 12,
                    );
                  },
                ),
    ),
  );
}),

    );
  }
}

class BoutiqueShimmerWidget extends StatelessWidget {
  const BoutiqueShimmerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Image shimmer
            Container(
              height: 160,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(15),
              ),
            ),
            const SizedBox(height: 10),

            // Titre + tag
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(height: 14, width: 120, color: Colors.white),
                Container(
                  height: 12, width: 60,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(5)),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
