import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:get/get.dart';

import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/text_row_widget.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'dart:ui';

class ClosedStatusWidget extends StatelessWidget {
  final String openingTime;
  final double? iconSize;
  ClosedStatusWidget(
      {Key? key, required this.openingTime, required this.iconSize})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 5),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onSurface,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(FontAwesomeIcons.clock,
              color: Theme.of(context).colorScheme.surface, size: iconSize),
          SizedBox(width: 8),
          Text(
            'Schedule for $openingTime',
            style: TextStyle(
                color: Theme.of(context).colorScheme.surface,
                fontSize: 10,
                fontWeight: AppFontWeights.bold),
          ),
        ],
      ),
    );
  }
}

class BoutiquesListWidget extends StatelessWidget {
  BoutiquesListWidget({Key? key}) : super(key: key);
  
  final TextEditingController searchController = TextEditingController();
  final RxString searchQuery = ''.obs;

  @override
  Widget build(BuildContext context) {
    Get.put(StoreController());
    final StoreController storeController = Get.find<StoreController>();

    // Appeler fetchPromotedStores pour charger les boutiques si la liste est vide
    if (storeController.recommendedStores.isEmpty) {
      storeController.fetchRecomandedStores(refresh: true);
    }

    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: TextField(
            controller: searchController,
            decoration: InputDecoration(
              hintText: 'Search stores...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              contentPadding: EdgeInsets.symmetric(vertical: 0),
            ),
            onChanged: (value) => searchQuery.value = value,
          ),
        ),
        
        Obx(() {
          // Si les boutiques sont en train d'être chargées, afficher un loader
          if (storeController.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }
        
          // Si la liste des boutiques est vide, afficher un message
          if (storeController.recommendedStores.isEmpty) {
            return const Center(child: Text("Aucune boutique disponible."));
          }
        
          // Filter stores based on search query
          var filteredStores = storeController.recommendedStores.where((store) {
            return searchQuery.value.isEmpty || 
                   store.name.toLowerCase().contains(searchQuery.value.toLowerCase());
          }).toList();
        
          // Si aucun résultat de recherche
          if (filteredStores.isEmpty) {
            return const Center(child: Text("Aucun résultat trouvé."));
          }
        
          // Créer la liste de widgets pour chaque boutique
          List<Widget> boutiqueWidgets = filteredStores.map((boutique) {
            return BoutiqueWidget(
              boutique: boutique,
              fontSize: 18,
              iconSize: 12,
            );
          }).toList();
        
          // Retourner une colonne contenant tous les widgets des boutiques
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              children: boutiqueWidgets,
            ),
          );
        }),
      ],
    );
  }
}class BoutiqueWidget extends StatelessWidget {
  final Store boutique;
  final double? fontSize;
  final double? iconSize;

  BoutiqueWidget({
    Key? key,
    required this.boutique,
    required this.fontSize,
    required this.iconSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isOpen = boutique.isOpenNow();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            GestureDetector(
              onTap: () {
                Get.to(() => StoreDetailsPage(store: boutique));
                print('Navigate to ${boutique.name}');
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(15)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: Offset(0.5, 0.5),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    child: CachedImageWidget(
                      imageUrl: '${boutique.images.first}',
                      height: 160,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                boutique.name,
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  boutique.typeName ?? boutique.typeId.toString(),
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
        // Padding(
        //   padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.start,
        //     children: [
        //       if (!isOpen)
        //         ClosedStatusWidget(
        //           openingTime: boutique.openingTime,
        //           iconSize: iconSize,
        //         ),
        //     ],
        //   ),
        // ),
      ],
    );
  }
}
