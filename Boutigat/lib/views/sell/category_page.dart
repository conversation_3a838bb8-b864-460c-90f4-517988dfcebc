import 'package:boutigak/views/widgets/boutigak_loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/services/categories_service.dart';
import 'package:boutigak/data/services/session_service.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// ===============================
/// CATEGORY PICKER — Single Sheet
/// ===============================
class CategoryPage extends StatefulWidget {
  final Category? initialParent;
  const CategoryPage({super.key, this.initialParent});

  /// Helper pour ouvrir avec coins arrondis (UN SEUL SHEET)
static Future<void> show(BuildContext context, {Category? parent}) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    useSafeArea: true,
    backgroundColor: Colors.transparent, // pour voir les coins arrondis
    builder: (_) => ClipRRect(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
      child: FractionallySizedBox(
        heightFactor: 0.98, // <-- plus grand
        child: Material(
          color: Theme.of(context).colorScheme.surface,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // --- petite barre (grabber) pour indiquer le geste de fermeture ---
              const _Grabber(),
              // le contenu de la page
              Expanded(child: CategoryPage(initialParent: parent)),
            ],
          ),
        ),
      ),
    ),
  );
}



  @override
  State<CategoryPage> createState() => _CategoryPageState();
}

class _CategoryPageState extends State<CategoryPage> {
  final ItemController itemController = Get.find<ItemController>();

  Category? _currentParent;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocus = FocusNode();
  String _query = '';

  @override
  void initState() {
    super.initState();
    _currentParent = widget.initialParent;
    if (_currentParent == null) {
      itemController.fetchCategories();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocus.dispose();
    super.dispose();
  }

  void _onSearchChanged(String value) {
    setState(() => _query = value.trim().toLowerCase());
  }

  @override
  Widget build(BuildContext context) {
    final title = _currentParent == null
        ? 'select_category'.tr
        : 'select_subcategory_of'.trParams({'name': _currentParent!.getTitle()});

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Theme.of(context).colorScheme.surface,
        leadingWidth: 48,
        leading: _currentParent == null
            ? IconButton(
                icon: const Icon(Icons.close),
                tooltip: 'close'.tr,
                onPressed: () => Navigator.of(context).maybePop(),
              )
            : IconButton(
                icon: const Icon(Icons.arrow_back_ios_new_rounded),
                tooltip: 'back'.tr,
                onPressed: () {
                  setState(() {
                    // remonte d'un niveau
                    _currentParent = _findParentOf(_currentParent);
                  });
                },
              ),
        title: Text(title),
      ),
      body: Column(
        children: [
          // --- Barre de recherche style iOS (CupertinoTextField) ---
 

Padding(
  padding: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 10.h),
  child: CupertinoTextField(
   controller: _searchController,
              focusNode: _searchFocus,
    placeholder: 'search'.tr,
    prefix: IconButton(
      icon: Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
        onPressed: () => _onSearchChanged(_searchController.text),
    ),
    suffix: IconButton(
      icon: Icon(Icons.clear, color: Theme.of(context).colorScheme.onSurface),
      onPressed: () {
                  _searchController.clear();
                  _onSearchChanged('');
                  _searchFocus.unfocus();
                },
    ),
    onChanged: _onSearchChanged,
    onSubmitted: (value) {
      _onSearchChanged(value);
        _searchFocus.unfocus();
    },
  ),
),
          const _ModernDivider(),


          Expanded(child: _buildBody()),
        ],
      ),
    );
  }

  Widget _buildBody() {
    // Racine: observe le chargement / liste
    if (_currentParent == null) {
      return Obx(() {
        if (itemController.categories.isEmpty && itemController.isLoading.value) {
          return const _CategorySkeleton();
        }
        final root = itemController.categories.where((c) => c.parentId == null).toList();
        return _CategoryListView(
          categories: _filter(root),
          onTapParent: (cat) => setState(() => _currentParent = cat),
          onSelectLeaf: _selectLeaf,
          groupValueRx: itemController.categoryID,
        );
      });
    }

    // Sous-catégories déjà disponibles dans l'objet
    final children = _currentParent!.children;
    final list = _filter(children);

    if (list.isEmpty) {
      return _EmptyState(
        title: 'no_category_found'.tr,
        subtitle: _query.isEmpty ? 'choose_category_to_continue'.tr : 'try_another_keyword'.tr,
        icon: Icons.inventory_2_outlined,
      );
    }

    return _CategoryListView(
      categories: list,
      onTapParent: (cat) => setState(() => _currentParent = cat),
      onSelectLeaf: _selectLeaf,
      groupValueRx: itemController.categoryID,
    );
  }

  List<Category> _filter(List<Category> src) {
    if (_query.isEmpty) return src;
    return src.where((c) => c.getTitle().toLowerCase().contains(_query)).toList();
  }

  void _selectLeaf(Category cat) {
    itemController.categoryID.value = cat.id.toString();
    itemController.updateSelectedCategory(cat.id.toString());
    Navigator.of(context).maybePop(); // ferme le SEUL sheet
  }

  /// Retrouve le parent d'une catégorie (depuis la liste globale en mémoire)
  Category? _findParentOf(Category? child) {
    if (child == null) return null;
    // si on est déjà à la racine
    if (child.parentId == null) return null;

    // cherche dans la liste complète
    final all = itemController.categories;
    for (final c in all) {
      if (c.id == child.parentId) {
        return c;
      }
    }
    // fallback: revenir à la racine
    return null;
  }
}

/// ===============================
/// LIST VIEW minimaliste & moderne
/// ===============================
class _CategoryListView extends StatelessWidget {
  final List<Category> categories;
  final void Function(Category parent) onTapParent;
  final void Function(Category leaf) onSelectLeaf;
  final RxString groupValueRx;

  const _CategoryListView({
    required this.categories,
    required this.onTapParent,
    required this.onSelectLeaf,
    required this.groupValueRx,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: categories.length,
     separatorBuilder: (_, __) => const _ModernDivider(),



      
      itemBuilder: (_, i) {
        final cat = categories[i];
        final hasChildren = cat.children.isNotEmpty;

        return InkWell(
  onTap: () => hasChildren ? onTapParent(cat) : onSelectLeaf(cat),
  child: SizedBox(
    height: 52, // <— hauteur fixe pour toutes les lignes
    child: Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          // Texte
          Expanded(
            child: Text(
              cat.getTitle(),
              style: Theme.of(context).textTheme.bodyMedium,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          SizedBox(width: 8.w),

          // Trailing normalisé à 40×40 pour que la ligne garde la même hauteur
          hasChildren
              ? SizedBox(
                  width: 40,
                  height: 40,
                  child: Center(
                    child: Icon(
                      Icons.chevron_right_rounded,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                )
              : SizedBox(
                  width: 40,
                  height: 40,
                  child: Center(
                    child: Obx(() => Radio<String>(
                          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                          value: cat.id.toString(),
                          groupValue: groupValueRx.value,
                          onChanged: (_) => onSelectLeaf(cat),
                        )),
                  ),
                ),
        ],
      ),
    ),
  ),
);

      },
    );
  }
}

/// ===============================
/// EMPTY + SKELETON
/// ===============================
class _EmptyState extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  const _EmptyState({
    required this.title,
    required this.subtitle,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final onVar = Theme.of(context).colorScheme.onSurfaceVariant;
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 64.sp, color: onVar),
            SizedBox(height: 12.h),
            Text(title, style: Theme.of(context).textTheme.titleSmall),
            SizedBox(height: 4.h),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: onVar),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _CategorySkeleton extends StatelessWidget {
  const _CategorySkeleton();

  @override
  Widget build(BuildContext context) {
    final base = Theme.of(context).colorScheme.surfaceVariant.withOpacity(.35);
    return ListView.builder(
      itemCount: 10,
      itemBuilder: (_, i) => Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        height: 48.h,
        decoration: BoxDecoration(
          color: base,
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }
}

class _Grabber extends StatelessWidget {
  const _Grabber();

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 6),
      child: Center(
        child: Container(
          width: 42,
          height: 4,
          decoration: BoxDecoration(
            color: c.outlineVariant, // discret et moderne
            borderRadius: BorderRadius.circular(999),
          ),
        ),
      ),
    );
  }
}
class _ModernDivider extends StatelessWidget {
  const _ModernDivider({this.indent = 0, this.endIndent = 0});
  final double indent;
  final double endIndent;

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Container(
      height: 0.5,
      margin: EdgeInsetsDirectional.only(start: indent, end: endIndent),
      color: c.outlineVariant.withOpacity(0.3), // fin et net
    );
  }
}
