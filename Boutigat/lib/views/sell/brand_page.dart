import 'dart:math';

import 'package:boutigak/views/sell/category_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/data/models/brands.dart';
import 'package:boutigak/data/services/brands_service.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

/// ===============================
/// BRAND PICKER — Single Sheet robuste
/// ===============================
class BrandPage extends StatefulWidget {
  const BrandPage({super.key});

  /// Ouvre le sélecteur dans un **seul** bottom sheet (coins arrondis + grabber)
  static Future<void> show(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent, // nécessaire pour voir les coins arrondis
      builder: (_) => ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        child: FractionallySizedBox(
          heightFactor: 0.98, // plus grand
          child: Material(
            color: Theme.of(context).colorScheme.surface,
            child: const _BrandSheetBody(),
          ),
        ),
      ),
    );
  }

  @override
  State<BrandPage> createState() => _BrandPageState();
}

class _BrandPageState extends State<BrandPage> {
  @override
  Widget build(BuildContext context) {
    // Utiliser BrandPage.show(context) pour ouvrir le sheet
    return const SizedBox.shrink();
  }
}

/// ===============================
/// Corps du sheet (robustifié)
/// ===============================
class _BrandSheetBody extends StatefulWidget {
  const _BrandSheetBody();

  @override
  State<_BrandSheetBody> createState() => _BrandSheetBodyState();
}

class _BrandSheetBodyState extends State<_BrandSheetBody> {
  ItemController? _itemController; // peut être null si Get.find échoue
  String? _initError;

  final TextEditingController _searchCtrl = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  String _query = '';

  @override
  void initState() {
    super.initState();

    // Récupération sûre du controller
    try {
      _itemController = Get.find<ItemController>();
    } catch (e) {
      _initError = 'Controller Item introuvable: $e';
    }

    // Si on a bien un controller, on peut précharger prudemment
    if (_itemController != null) {
      final c = _itemController!;
      // Si une catégorie est choisie mais pas encore de marques -> fetch
      if (c.categoryID.value.isNotEmpty && (c.brands).isEmpty) {
        // Protéger par try/catch au cas où fetchBrands lancerait une exception
        Future.microtask(() async {
          try {
            await c.fetchBrands();
          } catch (_) {}
          if (mounted) setState(() {});
        });
      }
    }
  }

  @override
  void dispose() {
    _searchCtrl.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged(String v) {
    setState(() => _query = v.trim().toLowerCase());
  }

  @override
  Widget build(BuildContext context) {
    // Erreur d’init => état explicite (sinon écran noir en release)
    if (_initError != null) {
      return _FatalState(
        title: 'error'.tr,
        subtitle: _initError!,
        onClose: () => Navigator.of(context).maybePop(),
      );
    }

    final itemController = _itemController!; // non-null ici
    final hasCategory = itemController.categoryID.value.isNotEmpty;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const _Grabber(),

        AppBar(
          elevation: 0,
          surfaceTintColor: Colors.transparent,
          backgroundColor: Theme.of(context).colorScheme.surface,
          leading: IconButton(
            icon: const Icon(Icons.close),
            tooltip: 'close'.tr,
            onPressed: () => Navigator.of(context).maybePop(),
          ),
          title: Text('select_brand'.tr),
        ),

        // Barre de recherche iOS (exactement le snippet demandé)
        Padding(
          padding: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 10.h),
          child: CupertinoTextField(
            controller: _searchCtrl,
            focusNode: _focusNode,
            placeholder: "search".tr,
            prefix: IconButton(
              icon: Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
              onPressed: () => _onSearchChanged(_searchCtrl.text),
            ),
            suffix: IconButton(
              icon: Icon(Icons.clear, color: Theme.of(context).colorScheme.onSurface),
              onPressed: () {
                _searchCtrl.clear();
                _onSearchChanged('');
                _focusNode.unfocus();
              },
            ),
            onChanged: _onSearchChanged,
            onSubmitted: (value) {
              _onSearchChanged(value);
              _focusNode.unfocus();
            },
          ),
        ),
        const _ModernDivider(),

        Expanded(
          child: Obx(() {
            // Obx entoure toute la zone dynamique : si une erreur runtime arrive ici,
            // Flutter peut juste “blank screen” en release — donc on protège le max ci-dessous.
            try {
              final loading = itemController.isLoading.value;
              final brands = itemController.brands; // supposé RxList<Brand> initialisé

              if (!hasCategory) {
                return _NoCategorySelectedSimple(
                  onClose: () => Navigator.of(context).maybePop(),
                );
              }

              if (loading && brands.isEmpty) {
                return const _BrandSkeleton();
              }

              final List<Brands> source = brands; // déjà non-null
              final filtered = _query.isEmpty
                  ? source
                  : source.where((b) => (b.name).toLowerCase().contains(_query)).toList();

              if (filtered.isEmpty) {
                return _EmptyState(
                  title: 'no_brand_found'.tr,
                  subtitle: _query.isEmpty ? 'choose_brand_to_continue'.tr : 'try_another_keyword'.tr,
                  icon: Icons.inventory_2_outlined,
                );
              }

              return ListView.separated(
                itemCount: filtered.length,
                separatorBuilder: (_, __) => const _ModernDivider(),
                itemBuilder: (_, i) {
                  final brand = filtered[i];
                  final name = brand.name;

                  return _BrandRow(
                    name: name,
                    selectedRx: itemController.brand,
                    onSelect: () {
                      // protège la mise à jour
                      try {
                        itemController.brand.value = name;
                      } catch (_) {}
                      Navigator.of(context).maybePop(); // ferme le sheet
                    },
                  );
                },
              );
            } catch (e) {
              // Si quoi que ce soit jette une exception ici en release, on verrait un écran noir.
              // On catch et on affiche un état explicite.
              return _FatalState(
                title: 'error'.tr,
                subtitle: e.toString(),
                onClose: () => Navigator.of(context).maybePop(),
              );
            }
          }),
        ),
      ],
    );
  }
}

/// ====== Row uniforme (52px), trailing radio 40×40 ======
class _BrandRow extends StatelessWidget {
  final String name;
  final RxString selectedRx;
  final VoidCallback onSelect;

  const _BrandRow({
    required this.name,
    required this.selectedRx,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onSelect,
      child: SizedBox(
        height: 52,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  name,
                  style: Theme.of(context).textTheme.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 8.w),
              SizedBox(
                width: 40,
                height: 40,
                child: Center(
                  child: Obx(() => Radio<String>(
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                        value: name,
                        groupValue: selectedRx.value,
                        onChanged: (_) => onSelect(),
                      )),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// ====== Etat simple : pas de catégorie sélectionnée (pas d’ouverture auto) ======
class _NoCategorySelectedSimple extends StatelessWidget {
  final VoidCallback onClose;
  const _NoCategorySelectedSimple({required this.onClose});

  @override
  Widget build(BuildContext context) {
    final onVar = Theme.of(context).colorScheme.onSurfaceVariant;
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.warning_amber_rounded, size: 64.sp, color: Colors.orange),
            SizedBox(height: 12.h),
            Text(
              'select_category_first'.tr,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'select_category_first_sub'.tr,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: onVar),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            SizedBox(
              height: 44.h,
              child: FilledButton(
                onPressed: onClose,
                child: Text('close'.tr),
                style: FilledButton.styleFrom(
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// ====== Grabber (petite barre en haut) ======
class _Grabber extends StatelessWidget {
  const _Grabber();

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 6),
      child: Center(
        child: Container(
          width: 42,
          height: 4,
          decoration: BoxDecoration(
            color: c.outlineVariant,
            borderRadius: BorderRadius.circular(999),
          ),
        ),
      ),
    );
  }
}

/// ====== Divider moderne ======
class _ModernDivider extends StatelessWidget {
  const _ModernDivider({this.indent = 0, this.endIndent = 0});
  final double indent;
  final double endIndent;

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Container(
      height: 0.5,
      margin: EdgeInsetsDirectional.only(start: indent, end: endIndent),
      color: c.outlineVariant.withOpacity(0.3), // fin et net
    );
  }
}

/// ====== Skeleton simple ======
class _BrandSkeleton extends StatelessWidget {
  const _BrandSkeleton();

  @override
  Widget build(BuildContext context) {
    final base = Theme.of(context).colorScheme.surfaceVariant.withOpacity(.35);
    return ListView.builder(
      itemCount: 10,
      itemBuilder: (_, i) => Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        height: 48.h,
        decoration: BoxDecoration(
          color: base,
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }
}

/// ====== Empty / Fatal state ======
class _EmptyState extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  const _EmptyState({
    required this.title,
    required this.subtitle,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final onVar = Theme.of(context).colorScheme.onSurfaceVariant;
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 64.sp, color: onVar),
            SizedBox(height: 12.h),
            Text(title, style: Theme.of(context).textTheme.titleSmall),
            SizedBox(height: 4.h),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: onVar),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _FatalState extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback onClose;
  const _FatalState({required this.title, required this.subtitle, required this.onClose});

  @override
  Widget build(BuildContext context) {
    final onVar = Theme.of(context).colorScheme.onSurfaceVariant;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const _Grabber(),
        AppBar(
          elevation: 0,
          surfaceTintColor: Colors.transparent,
          backgroundColor: Theme.of(context).colorScheme.surface,
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: onClose,
          ),
          title: Text('select_brand'.tr),
        ),
        Expanded(
          child: Center(
            child: Padding(
              padding: EdgeInsets.all(24.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64.sp, color: onVar),
                  SizedBox(height: 12.h),
                  Text(title, style: Theme.of(context).textTheme.titleSmall),
                  SizedBox(height: 4.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: onVar),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  SizedBox(
                    height: 44.h,
                    child: FilledButton(
                      onPressed: onClose,
                      child: Text('close'.tr),
                      style: FilledButton.styleFrom(
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
