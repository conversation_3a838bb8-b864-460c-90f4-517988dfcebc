import 'dart:developer';

import 'package:boutigak/controllers/payment_controller.dart';
import 'package:boutigak/controllers/status_controller.dart';
import 'package:boutigak/data/models/payment_provider.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/widgets/upload_progress_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'sell_widgets.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/controllers/sell_controller.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/views/profil/my_items.dart';

class SellPage extends StatefulWidget {
  final int? itemId;
  
  const SellPage({Key? key, this.itemId}) : super(key: key);
  
  @override
  _SellPageState createState() => _SellPageState();
}

class _SellPageState extends State<SellPage> {
  final ItemController itemController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>(); // Add this
  
  @override
  void initState() {
    super.initState();
    _loadItemData();
  }

  @override
  void dispose() {
    itemController.clearItemData();
    photoController.clearPhotoActionData();
    itemController.clearFields();
    showValidationErrors.value = false;

    super.dispose();
  }
  
  Future<void> _loadItemData() async {
    if (widget.itemId != null) {
      await itemController.loadItemForEdit(widget.itemId!);
    }
  }
  final RxString uploadStatus = "Preparing upload...".obs;
  final int totalSteps = 4;
  final PhotoActionsController photoController = Get.put(
      PhotoActionsController(Get.find<ItemController>()),
      permanent: true);
  final RxBool isItemUploaded = false.obs;
  final RxDouble uploadProgress = 0.0.obs;
  final RxBool showValidationErrors = false.obs;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'sell_an_item'.tr,
          style: TextStyle(
              color: Theme.of(context).colorScheme.surface,
              fontSize: AppTextSizes.heading),
        ),
        elevation: 0,
        backgroundColor: AppColors.primary,
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey, 
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            children: <Widget>[
              Column(
                children: <Widget>[
                  SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        PhotoActionsWidget(),
                        Obx(() {
                          // Show error if no image and validation errors should be shown
                          if (photoController.photos.isEmpty && showValidationErrors.value) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Text(
                                "Veuillez ajouter au moins une photo.",
                                style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                              ),
                            );
                          }
                          return SizedBox.shrink();
                        }),
                        SizedBox(height: 20),
                        ItemDetailsEntryMainLanguage(formKey: _formKey, showValidationErrors: showValidationErrors),
                        SizedBox(height: 20),
                        ItemDetailsEntryArabic(formKey: _formKey, showValidationErrors: showValidationErrors),
                        SizedBox(height: 20),
                        ItemDetailsFormWidget(formKey: _formKey, showValidationErrors: showValidationErrors),
                          Obx(() {
                            if (itemController.selectedCategoryDetails.isEmpty) {
                              return const Center(child: Text(""));
                            }
                            return CategoryDetailsWidget(showValidationErrors: showValidationErrors);
                          }),
                          Align(
                            alignment: Alignment.bottomCenter,
                            child: Padding(
                              padding: EdgeInsets.only(bottom: 30),
                              child: Obx(() {
                                return Column(
                                  children: [
                                    if (uploadProgress.value > 0)
                                      Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                                        child: Container()
                                      ),
                                    SizedBox(height: 10),
                                    CustomButton(
                                      text: widget.itemId != null ? "update_item".tr : "upload_item".tr,
                                      onPressed: () async {
                                        // Enable validation error display
                                        showValidationErrors.value = true;

                                        // Force rebuild of all form fields to show validation
                                        setState(() {});

                                        // Wait a frame for the UI to update
                                        await Future.delayed(Duration(milliseconds: 100));

                                        // Check form validation
                                        bool isFormValid = _formKey.currentState!.validate();

                                        // Check additional validations
                                        bool hasPhotos = photoController.photos.isNotEmpty;
                                        bool hasCategory = itemController.categoryID.value.isNotEmpty;
                                        bool hasBrand = itemController.brand.value.isNotEmpty;

                                        if (!isFormValid || !hasPhotos || !hasCategory || !hasBrand) {
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(content: Text("Veuillez remplir tous les champs obligatoires.")),
                                          );
                                          return;
                                        }

                                        // Clear validation errors since form is valid
                                        showValidationErrors.value = false;

                                        log('in update');
                                        uploadProgress.value = 0.0;
                                        uploadStatus.value = "Préparation de l'envoi...";

                                        showUploadProgressDialog(context, uploadStatus, uploadProgress);

                                        if (widget.itemId != null) {
                                          await itemController.updateItem(widget.itemId!);
                                          itemController.clearItemData();
                                          photoController.clearPhotoActionData();
                                          itemController.clearFields();

                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(content: Text("item_updated_successfully".tr)),
                                          );

                                          uploadProgress.value = 0.0;
                                          uploadStatus.value = "";

                                           Navigator.of(context).pop(); // Dismiss dialog


                                Get.to(() => MyItemsPage());


                                         
                                        } else {
                                          try {
                                            // Start with realistic progress updates
                                            uploadProgress.value = 0.1;
                                            uploadStatus.value = "Validation des données...";
                                            await Future.delayed(Duration(milliseconds: 200));

                                            uploadProgress.value = 0.2;
                                            uploadStatus.value = "Préparation des images...";
                                            await Future.delayed(Duration(milliseconds: 200));

                                            uploadStatus.value = "Téléchargement en cours...";

                                            int? uploadedItemId = await itemController.postItem((progress) {
                                              // Map the actual upload progress to 20% - 95% range
                                              uploadProgress.value = 0.2 + (progress * 0.75);
                                              if (progress > 0.5) {
                                                uploadStatus.value = "Finalisation...";
                                              }
                                            });

                                            uploadProgress.value = 1.0;
                                            uploadStatus.value = "Envoi terminé !";

                                            if (itemController.isItemUploaded.value) {
                                              // Brief success display then immediate navigation
                                              await Future.delayed(Duration(milliseconds: 500));

                                              if (mounted) {
                                                Navigator.of(context).pop(); // Dismiss dialog
                                              }

                                              // Clear form data before navigation
                                              itemController.clearItemData();
                                              photoController.clearPhotoActionData();
                                              itemController.clearFields();

                                              // Reset progress
                                              uploadProgress.value = 0.0;
                                              uploadStatus.value = "";

                                              Get.to(
                                                PaymentWidget(
                                                  amount: itemController.categoryPri.value,
                                                  initialAmount: itemController.categoryPri.value,
                                                  itemId: uploadedItemId!,
                                                )
                                              );

                                              if (mounted) {
                                                ScaffoldMessenger.of(context).showSnackBar(
                                                  SnackBar(content: Text("item_uploaded_successfully".tr)),
                                                );
                                              }
                                            }
                                          } catch (e) {
                                            uploadStatus.value = "Échec de l'envoi. Veuillez réessayer.";
                                            if (mounted) {
                                              Navigator.of(context).pop(); // Dismiss dialog
                                              ScaffoldMessenger.of(context).showSnackBar(
                                                SnackBar(content: Text("Erreur lors de l'envoi : ${e.toString()}")),
                                              );
                                            }
                                          }
                                        }
                                      },
                                    ),
                                  ],
                                );
                              }),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

 



  void showUploadProgressDialog(BuildContext context, RxString status, RxDouble progress) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              UploadProgressWidget(
                progress: progress.value,
                status: status.value,
                isComplete: progress.value >= 1.0,
              ),
            ],
          )),
        ),
      ),
    );
  }
}

