import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ConditionPage extends StatelessWidget {

  const ConditionPage({super.key});

  static Future<void> show(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      builder: (_) => ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        child: FractionallySizedBox(
          heightFactor: 0.98,
          child: Material(
            color: Theme.of(context).colorScheme.surface,
            child: const _ConditionSheetBody(),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();
}

class _ConditionSheetBody extends StatelessWidget {
  const _ConditionSheetBody();

  @override
  Widget build(BuildContext context) {
    final itemController = Get.find<ItemController>();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const _Grabber(),
        AppBar(
          elevation: 0,
          surfaceTintColor: Colors.transparent,
          backgroundColor: Theme.of(context).colorScheme.surface,
          leading: IconButton(
            icon: const Icon(Icons.close),
            tooltip: 'close'.tr,
            onPressed: () => Navigator.of(context).maybePop(),
          ),
          title: Text('select_condition'.tr),
        ),
        Expanded(
          child: ListView.separated(
            itemCount: ItemCondition.values.length,
            separatorBuilder: (_, __) => const _ModernDivider(),
            itemBuilder: (context, index) {
              final condition = ItemCondition.values[index];
              final selected =
                  itemController.condition.value.toEnum() == condition;

              return InkWell(
                onTap: () {
                  itemController.condition.value = condition.labelKey.tr;
                  Navigator.of(context).maybePop();
                },
                child: SizedBox(
                  height: 80,
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                condition.labelKey.tr,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge
                                    ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                condition.descKey.tr,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurfaceVariant),
                              ),
                            ],
                          ),
                        ),
                        Radio<ItemCondition>(
                          value: condition,
                          groupValue: itemController.condition.value.toEnum(),
                          onChanged: (_) {
                            itemController.condition.value =
                                condition.labelKey.tr;
                            Navigator.of(context).maybePop();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// ===== Grabber =====
class _Grabber extends StatelessWidget {
  const _Grabber();

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 6),
      child: Center(
        child: Container(
          width: 42,
          height: 4,
          decoration: BoxDecoration(
            color: c.outlineVariant,
            borderRadius: BorderRadius.circular(999),
          ),
        ),
      ),
    );
  }
}

/// ===== Enum & Extensions =====
enum ItemCondition {
  newWithTags,
  newWithoutTags,
  veryGood,
  good,
  satisfactory,
}

extension ItemConditionX on ItemCondition {
  String get labelKey {
    switch (this) {
      case ItemCondition.newWithTags:
        return 'condition_new_with_tags';
      case ItemCondition.newWithoutTags:
        return 'condition_new_without_tags';
      case ItemCondition.veryGood:
        return 'condition_very_good';
      case ItemCondition.good:
        return 'condition_good';
      case ItemCondition.satisfactory:
        return 'condition_satisfactory';
    }
  }

  String get descKey {
    switch (this) {
      case ItemCondition.newWithTags:
        return 'condition_new_with_tags_desc';
      case ItemCondition.newWithoutTags:
        return 'condition_new_without_tags_desc';
      case ItemCondition.veryGood:
        return 'condition_very_good_desc';
      case ItemCondition.good:
        return 'condition_good_desc';
      case ItemCondition.satisfactory:
        return 'condition_satisfactory_desc';
    }
  }
}

extension StringToCondition on String {
  ItemCondition toEnum() {
    switch (this) {
      // English translations
      case 'New with tags':
      case 'New with packaging':
      // French translations
      case 'Neuf avec emballage':
      // Arabic translations
      case 'جديد مع التغليف':
      // Translation key
      case 'condition_new_with_tags':
        return ItemCondition.newWithTags;

      // English translations
      case 'New without tags':
      case 'New without packaging':
      // French translations
      case 'Neuf sans emballage':
      // Arabic translations
      case 'جديد بدون تغليف':
      // Translation key
      case 'condition_new_without_tags':
        return ItemCondition.newWithoutTags;

      // English translations
      case 'Very good':
      // French translations
      case 'Très bon état':
      // Arabic translations
      case 'حالة ممتازة':
      // Translation key
      case 'condition_very_good':
        return ItemCondition.veryGood;

      // English translations
      case 'Good':
      // French translations
      case 'Bon état':
      // Arabic translations
      case 'حالة جيدة':
      // Translation key
      case 'condition_good':
        return ItemCondition.good;

      // English translations
      case 'Satisfactory':
      // French translations
      case 'État satisfaisant':
      // Arabic translations
      case 'حالة مقبولة':
      // Translation key
      case 'condition_satisfactory':
        return ItemCondition.satisfactory;

      default:
        return ItemCondition.satisfactory;
    }
  }
}
class _ModernDivider extends StatelessWidget {
  const _ModernDivider({this.indent = 0, this.endIndent = 0});
  final double indent;
  final double endIndent;

  @override
  Widget build(BuildContext context) {
    final c = Theme.of(context).colorScheme;
    return Container(
      height: 0.5,
      margin: EdgeInsetsDirectional.only(start: indent, end: endIndent),
      color: c.outlineVariant.withOpacity(0.3), // fin et net
    );
  }
}
