import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/constants/app_colors.dart';

class CustomButtonToday extends StatelessWidget {
  final String text;
  final void Function()? onPressed;
  const CustomButtonToday({
    super.key,
    required this.text,
    required this.onPressed
  });

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width * 0.4389;
    double height = MediaQuery.of(context).size.height * 0.05;
    double borderRadius = 12.0;

    return Container(
  decoration: BoxDecoration(
    boxShadow: [
      BoxShadow(
        color: Colors.grey.withOpacity(0.3),
        spreadRadius: 1,
        blurRadius: 2,
        offset: Offset(0.2, 0.2), // Position de l'ombre
      ),
    ],
    borderRadius: BorderRadius.circular(borderRadius), // Appliquer le même rayon aux coins
  ),
  child: SizedBox(
    width: width,
    height: 44,
    child: ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        backgroundColor: Theme.of(context).colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        textStyle: const TextStyle(fontSize: AppTextSizes.subtitle),
        elevation: 0.0, 
      ),
      child: Text(
        text,
        style: TextStyle(
          color: Theme.of(context).colorScheme.onSurface,
          fontWeight: AppFontWeights.bold,
        ),
      ),
    ),
  ),
);

  }
}

class TextLink extends StatelessWidget {
  final String text;
  final String routeName;

  const TextLink({super.key, required this.text, required this.routeName});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.toNamed(routeName),
      child: Text(
        text,
        style:  TextStyle(
          color: Theme.of(context).colorScheme.onSurface,
          fontSize: AppTextSizes.heading,
          fontWeight: AppFontWeights.bold
        ),
      ),
    );
  }
}

class BoutiqueScrollWidget extends StatelessWidget {
  const BoutiqueScrollWidget({super.key});

  @override
  Widget build(BuildContext context) {
    double squareSize = MediaQuery.of(context).size.width * 0.20;
    final StoreController storeController = Get.find<StoreController>();

    // Appeler fetchPromotedStores pour charger les boutiques si la liste est vide
    if (storeController.recommendedStores.isEmpty) {
     // storeController.fetchPromotedStores();
     storeController.fetchRecomandedStores(refresh: true);
    }

    return Obx(() {
      // Si les boutiques sont en train d'être chargées, afficher un loader
      if (storeController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      // Si la liste des boutiques est vide, afficher un message
      if (storeController.recommendedStores.isEmpty) {
        return const Center(child: Text("Aucune boutique disponible."));
      }

      return Padding(
        padding: const EdgeInsets.only(right: 16.0),
        child: SizedBox(
          height: squareSize,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: storeController.recommendedStores.map((boutique) {
        return GestureDetector(
          onTap: () {
             Get.to(() => StoreDetailsPage(store: boutique));
            print('Navigate to ${boutique.name}');
          },
          child: Container(
            width: squareSize,
            margin: const EdgeInsets.only(left: 16.0),
            decoration: BoxDecoration(
              image: DecorationImage(
                
                // Use the first image of the boutique if it exists, otherwise display a default image
                image: boutique.images.isNotEmpty
                    ? NetworkImage('${boutique.images.first}')
                    : AssetImage('assets/images/boutique_exemple4.png') as ImageProvider,
                fit: BoxFit.cover,
              ),
              borderRadius: BorderRadius.circular(15.0),
        
              color: AppColors.surface
            ),
          ),
        );
            }).toList(), // Convert the Iterable to a List
          ),
        ),
      );
    });
  }
}
