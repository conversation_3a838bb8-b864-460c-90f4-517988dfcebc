import 'dart:async';
import 'dart:developer';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/utils/deepLinkHandler.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:boutigak/views/profil/%20language_selector_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/home/<USER>';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/connectivity_widget.dart';
import 'package:boutigak/views/widgets/item_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:lottie/lottie.dart';


import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';
import 'home_widgets.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'favorite_page.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:boutigak/controllers/ad_controller.dart';
import 'package:boutigak/data/models/ad.dart';


class HomePage extends StatefulWidget {
  final bool openDrawer;
  const HomePage({Key? key, this.openDrawer = false}) : super(key: key);

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final ItemController itemController = Get.find<ItemController>();
  final AuthController authController = Get.put(AuthController());
  final StoreController storeController = Get.put(StoreController());
  final AdController adController = Get.put(AdController());

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  // Carousel
  final CarouselSliderController carouselController = CarouselSliderController();

  // Lottie cycle tracking
  Duration? _lottieCycle;
  DateTime? _refreshStart;

  // Pour éviter d’ouvrir le popup de connectivité à chaque build
  bool _connectivityShown = false;

  // Header Lottie – capture la durée via onLoaded
  final CustomHeader header = CustomHeader(
    height: 100.0.h, // ScreenUtil sera appliqué au build; hauteur fixe OK ici
    builder: (context, mode) {
      final state = context.findAncestorStateOfType<_HomePageState>();
      final bool isRefreshing = mode == RefreshStatus.refreshing;
      return SizedBox(
        height: 100.h,
        child: Center(
          child: Padding(
  padding: const EdgeInsets.only(top: 12), // 👈 petit espace en haut
  child: Lottie.asset(
    'assets/lottie/loader.json',
    width: 75,
    height: 75,
    animate: isRefreshing,
    repeat: isRefreshing,
    onLoaded: (composition) {
      state?._lottieCycle = composition.duration;
    },
  ),
),

        ),
      );
    },
  );

  Future<void> _safe(Future<void> Function() op) async {
    try { await op(); } catch (_) {}
  }

  Future<void> _refreshData() async {
    _refreshStart = DateTime.now();
    try {
      // Lancer TOUS les fetchs et les attendre
      final f1 = itemController.fetchItems(refresh: true);
      final f2 = storeController.fetchPromotedStores();
      // Avant tu faisais un fetch Ads en arrière-plan; ici on l’attend pour synchroniser l’arrêt du Lottie
      final f3 = adController.fetchAds();

      await Future.wait([f1, f2, f3]);

      // Garantir au moins UN cycle complet de lottie (fallback 1200ms)
      final minCycle = _lottieCycle ?? const Duration(milliseconds: 2200);
      final elapsed = DateTime.now().difference(_refreshStart!);
      final remaining = minCycle - elapsed;

      if (remaining > Duration.zero) {
        await Future.delayed(remaining);
      }

      // Petit tampon visuel pour une fin plus douce
      await Future.delayed(const Duration(milliseconds: 500));

      _refreshController.refreshCompleted();
    } catch (_) {
      _refreshController.refreshFailed();
    }
  }

  @override
  void initState() {
    super.initState();

    // Listen scroll for infinite load
    itemController.scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Ouvre le drawer au 1er frame si demandé
      if (widget.openDrawer) {
        ZoomDrawer.of(context)?.open();
      }

      // Affiche le popup de connectivité UNE seule fois
      if (!_connectivityShown) {
        _connectivityShown = true;
        ConnectivityPopup.show(context);
      }
    });
  }

  void _onScroll() {
    final controller = itemController.scrollController;
    if (!controller.hasClients || itemController.isLoadingMore.value) return;

    final position = controller.position.pixels;
    final maxScroll = controller.position.maxScrollExtent;

    // Déclenche le load un peu avant le bas (évite spam et lags)
    if (position > maxScroll - 600) {
      log('infinite scroll: loading more items...');
      itemController.fetchItems();
    }
  }

  @override
  void dispose() {
    itemController.scrollController.removeListener(_onScroll);
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    return Center(
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: AppBar(
          elevation: 0,
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          backgroundColor: Theme.of(context).colorScheme.surface,
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: EdgeInsets.symmetric(
                  horizontal: MediaQuery.of(context).size.width * 0.05,
                ),
                child: ColorFiltered(
                  colorFilter: ColorFilter.mode(AppColors.primary, BlendMode.srcIn),
                  child: Image.asset(
                    'assets/images/biglogo_boutigak.png',
                    width: 100,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => ZoomDrawer.of(context)?.toggle(),
                child: Container(
                  margin: EdgeInsets.only(
                    right: MediaQuery.of(context).size.width * 0.04,
                    left:  MediaQuery.of(context).size.width * 0.04,
                  ),
                  child: authController.isAuthenticated.value
                      ? CircleAvatar(
                          radius: 22,
                          backgroundColor: Colors.grey[300],
                          child: Text(
                            '${authController.user?.firstName[0] ?? ''}${authController.user?.lastName[0] ?? ''}'.toUpperCase(),
                            style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                          ),
                        )
                      : Container(
                          padding: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            shape: BoxShape.rectangle,
                            border: Border.all(
                              color: Theme.of(context).colorScheme.onSurface,
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            FontAwesomeIcons.solidUser,
                            size: 15,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),

        // 🔧 Anti-saut: configuration du rafraîchissement + physique non rebondissante
        body: RefreshConfiguration(
          headerTriggerDistance: 70,
          maxOverScrollExtent: 60,
          enableScrollWhenRefreshCompleted: true,
          // Ressort plus "ferme" pour limiter l’oscillation visible
          springDescription: const SpringDescription(mass: 1.9, stiffness: 170, damping: 16),
          child: SmartRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: false,
            header: header,
            onRefresh: _refreshData,
            child: CustomScrollView(
              controller: itemController.scrollController,
              physics: const ClampingScrollPhysics(), // <- clé pour enlever le rebond
              slivers: [
                // Boutons haut
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h, top: 6.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomButtonToday(
                          text: 'following'.tr,
                          onPressed: () {
                            if (authController.isAuthenticated.value) {
                              Get.to(() => const FollowedStoresPage());
                            } else {
                              Get.to(() => const LoginPage());
                            }
                          },
                        ),
                        CustomButtonToday(
                          text: 'favorites'.tr,
                          onPressed: () {
                            if (authController.isAuthenticated.value) {
                              Get.to(() =>  FavoritePage());
                            } else {
                              Get.to(() =>  LoginPage());
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                // Carousel pubs
                const SliverToBoxAdapter(child: SizedBox(height: 4)),
                SliverToBoxAdapter(child: _buildAdCarousel()),

                const SliverToBoxAdapter(child: SizedBox(height: 8)),

                // Titre boutiques
                SliverToBoxAdapter(
                  child: Row(
                    children: [
                      SizedBox(width: 16.w),
                      Text(
                        "shop_we_think_you_love".tr,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontSize: AppTextSizes.subHeading,
                          fontWeight: AppFontWeights.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SliverToBoxAdapter(child: SizedBox(height: 8)),
                SliverToBoxAdapter(child: _buildBoutiqueScrollWidget()),

                const SliverToBoxAdapter(child: SizedBox(height: 8)),

                // Titre produits
                SliverToBoxAdapter(
                  child: Row(
                    children: [
                      SizedBox(width: 16.w),
                      Text(
                        "you_might_like".tr,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontSize: AppTextSizes.subHeading,
                          fontWeight: AppFontWeights.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                // Grid items
                Obx(() {
                  if (itemController.items.isEmpty) {
                    return SliverToBoxAdapter(child: buildShimmerGrid(context));
                  }
                  return SliverPadding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    sliver: SliverGrid(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: MediaQuery.of(context).size.width < 360 ? 0.65 : 344 / 537,
                        crossAxisSpacing: MediaQuery.of(context).size.width * 0.03,
                        mainAxisSpacing: 10.0,
                      ),
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          if (index >= itemController.items.length) return null;
                          return LayoutBuilder(
                            builder: (context, constraints) {
                              return ItemWidget(item: itemController.items[index]);
                            },
                          );
                        },
                        childCount: itemController.items.length,
                      ),
                    ),
                  );
                }),

                // Loader fin de liste
                Obx(() {
                  if (itemController.isLoadingMore.value && itemController.items.isNotEmpty) {
                    return SliverToBoxAdapter(
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 20.h),
                        child: const Center(child: CupertinoActivityIndicator(radius: 15)),
                      ),
                    );
                  }
                  return const SliverToBoxAdapter(child: SizedBox.shrink());
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // -------- Widgets utilitaires --------

  Widget buildBoutiqueShimmerEffect(BuildContext context) {
    double squareSize = MediaQuery.of(context).size.width * 0.20;
    return Padding(
      padding: const EdgeInsets.only(right: 16.0),
      child: SizedBox(
        height: squareSize,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: 5,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: squareSize,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15.0),
                    color: Colors.grey[300],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBoutiqueScrollWidget() {
    double squareSize = MediaQuery.of(context).size.width * 0.20;

    return Padding(
      padding: const EdgeInsets.only(right: 16.0),
      child: SizedBox(
        height: squareSize,
        child: Obx(() {
          if (storeController.isLoading.value || storeController.promotedStores.isEmpty) {
            return buildBoutiqueShimmerEffect(context);
          }

          return ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: storeController.promotedStores.length,
            itemBuilder: (context, index) {
              var boutique = storeController.promotedStores[index];

              return GestureDetector(
                onTap: () async {
                  final OrderController orderController = Get.put(OrderController(initialOrderId: 1));
                  orderController.clearOrder();
                  Get.to(() => StoreDetailsPage(store: boutique));
                },
                child: Container(
                  width: squareSize,
                  margin: const EdgeInsets.only(left: 16.0),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey, width: 0.5),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: FadeInImage(
                      placeholder: const AssetImage('assets/images/placeholder_logo.png'),
                      image: boutique.images.isNotEmpty
                          ? NetworkImage('${boutique.images.first}')
                          : const AssetImage('assets/images/placeholder_logo.png') as ImageProvider,
                      fit: BoxFit.cover,
                      placeholderFit: BoxFit.cover,
                      fadeInDuration: const Duration(milliseconds: 300),
                      imageErrorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey,
                          alignment: Alignment.center,
                          child: Image.asset('assets/images/placeholder_logo.png'),
                        );
                      },
                    ),
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }

  Widget buildShimmerGrid(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.all(sidePadding),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 344 / 550,
        crossAxisSpacing: sidePadding,
        mainAxisSpacing: 10,
      ),
      itemCount: 6,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: screenWidth * 0.4388,
                height: screenWidth * 0.5485,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[300],
                ),
              ),
              SizedBox(height: sidePadding),
              Container(
                width: screenWidth * 0.3,
                height: 14.0,
                color: Colors.grey[300],
              ),
              const SizedBox(height: 8.0),
              Align(
                alignment: Alignment.centerRight,
                child: Container(
                  width: screenWidth * 0.2,
                  height: 12.0,
                  color: Colors.grey[300],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAdCarousel() {
    return Obx(() {
      if (adController.isLoading.value || adController.ads.isEmpty) {
        return _buildAdCarouselShimmer();
      }

      final isRTL = Directionality.of(context) == TextDirection.rtl;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CarouselSlider.builder(
            carouselController: carouselController,
            itemCount: adController.ads.length,
            options: CarouselOptions(
              viewportFraction: 0.92,
              padEnds: false,
              enlargeCenterPage: false,
              enableInfiniteScroll: true,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 5),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              autoPlayCurve: Curves.easeInOut,
              onPageChanged: (index, reason) {
                adController.currentAdIndex.value = index;
              },
            ),
            itemBuilder: (context, index, realIndex) {
              final ad = adController.ads[index];
              return GestureDetector(
                onTap: () => _handleAdTap(ad),
                child: Container(
                  margin: EdgeInsets.only(
                    left: isRTL ? 2 : (index == 0 ? 16 : 8),
                    right: isRTL ? (index == 0 ? 16 : 8) : 2,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedImageWidget(
                      imageUrl: ad.imageUrl,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              );
            },
          ),
          SizedBox(height: 10.h),
          Center(
            child: Obx(() => Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(adController.ads.length, (index) {
                final isActive = index == adController.currentAdIndex.value;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  height: 6,
                  width: isActive ? 18 : 8,
                  decoration: BoxDecoration(
                    color: isActive
                        ? AppColors.primary
                        : AppColors.primary.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                );
              }),
            )),
          ),
        ],
      );
    });
  }

  void _handleAdTap(Ad ad) async {
    switch (ad.targetType) {
      case 'item':
        await itemController.fetchItemById(ad.targetId as int);
        if (itemController.selectedItem.value != null) {
          Get.to(() => ItemDetailsPage(item: itemController.selectedItem.value!));
        }
        break;

      case 'store':
        final Store? store = await StoreService.getStoreById(ad.targetId as int);
        if (store != null) {
          Get.to(() => StoreDetailsPage(store: store));
        }
        break;

      case 'item-store':
        final Store? store = await StoreService.getStoreById(ad.storeId as int);
        if (store != null) {
          Get.to(() => StoreDetailsPage(store: store, selectedItemId: ad.targetId));
        }
        break;
    }
  }

  Widget _buildAdCarouselShimmer() {
    final width = MediaQuery.of(context).size.width * 0.92;
    final height = 160.h;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: height,
            width: width,
            margin: const EdgeInsets.only(left: 16, right: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey[300],
            ),
          ),
        ),
        SizedBox(height: 10.h),
        Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(4, (index) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                height: 6,
                width: index == 0 ? 18 : 8,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }
}