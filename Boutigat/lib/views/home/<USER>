import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:lottie/lottie.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';



import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Assumes you have these:
/// import 'package:your_app/widgets/cached_image_widget.dart';
/// import 'package:your_app/pages/store_details_page.dart';
/// import 'package:your_app/controllers/store_controller.dart';
/// import 'package:your_app/models/store.dart';

class FollowedStoresPage extends StatefulWidget {
  const FollowedStoresPage({Key? key}) : super(key: key);

  @override
  State<FollowedStoresPage> createState() => _FollowedStoresPageState();
}

class _FollowedStoresPageState extends State<FollowedStoresPage> {
  final StoreController storeController =
      Get.isRegistered<StoreController>() ? Get.find<StoreController>() : Get.put(StoreController());

  @override
  void initState() {
    super.initState();
    // Aligner avec MyOrdersPage : fetch dans addPostFrameCallback
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFollowedStores();
    });
  }

  Future<void> _loadFollowedStores() async {
    await storeController.fetchFollowedStores();
  }

  Future<void> _onPullToRefresh() async {
    try {
      await storeController.fetchFollowedStores();
    } catch (e) {
      Get.snackbar(
        'error'.tr.isEmpty ? 'Error' : 'error'.tr,
        'refresh_followed_failed'.tr.isEmpty ? 'Failed to refresh followed stores: $e' : 'refresh_followed_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
bool isFollowLoading = false; // état local pour gérer le loader

Future<void> _toggleFollow(Store store) async {
  final storeId = store.id;
  if (storeId == null) {
    debugPrint('toggleFollow: storeId is null');
    return;
  }

  // Bloque si déjà en cours
  if (isFollowLoading) return;

  setState(() {
    isFollowLoading = true;
  });

  try {
    final result = await StoreService.followUnfollowStore(storeId, store.isFollowed);

    if (result != null && result['success'] == true) {
      setState(() {
        // MAJ avec la vérité serveur (si présent)
        store.isFollowed = result['is_followed'] ?? !store.isFollowed;
        store.followersCount = result['followers_count'] ?? store.followersCount;
      });
    } else {
      debugPrint('toggleFollow: API returned null or success=false');
    }
  } catch (e) {
    debugPrint('Error toggling follow: $e');
  } finally {
    if (mounted) {
      setState(() {
        isFollowLoading = false;
      });
    }
  }
}

  void _navigateToHome() {
    // même comportement que MyOrdersPage
    Get.offAll(() => ZoomDrawerWrapper(), transition: Transition.noTransition);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Dégradé vert fond haut
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.92),
                    theme.colorScheme.primary.withOpacity(0.90),
                  ],
                ),
              ),
            ),
          ),

          SafeArea(
            top: true,
            bottom: false,
            child: Obx(() {
              final isLoading = storeController.isLoadingFollowed.value; // ajoute ce bool dans ton controller si besoin
              final stores = storeController.FollowedStores;

              if (isLoading) {
                return Column(
                  children: const [
                    _GlassHeader(title: 'followed_stores'),
                    Expanded(child: _FSShimmerList()),
                  ],
                );
              }

              return CustomScrollView(
                physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                slivers: [
                  // Pull-to-refresh iOS (loader blanc)
                  CupertinoSliverRefreshControl(
                    onRefresh: _onPullToRefresh,
                    builder: (context, refreshState, pulledExtent, refreshTriggerPullDistance, refreshIndicatorExtent) {
                      return const Center(
                        child: CupertinoActivityIndicator(radius: 14, color: Colors.white),
                      );
                    },
                  ),

                  const SliverToBoxAdapter(child: _GlassHeader(title: 'followed_stores')),

                  if (stores.isEmpty)
                    SliverToBoxAdapter(
                      child: _FSEmptyState(
                        title: 'no_followed_title'.tr.isEmpty ? 'No followed stores yet' : 'no_followed_title'.tr,
                        subtitle: 'no_followed_subtitle'.tr.isEmpty
                            ? 'Follow stores you like and they will appear here.'
                            : 'no_followed_subtitle'.tr,
                        ctaText: 'back_home'.tr.isEmpty ? 'Back to Home' : 'back_home'.tr,
                        onCta: _navigateToHome,
                      ),
                    )
                  else
                    SliverToBoxAdapter(
                      child: LayoutBuilder(
                        builder: (context, _) {
                          final paddingTop = MediaQuery.of(context).padding.top;
                          final viewportH = MediaQuery.of(context).size.height - paddingTop;
                          return ConstrainedBox(
                            constraints: BoxConstraints(minHeight: viewportH),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  ListView.separated(
                                    padding: const EdgeInsets.fromLTRB(12, 12, 12, 16),
                                    physics: const NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemCount: stores.length,
                                    separatorBuilder: (_, __) => const SizedBox(height: 8),
                                    itemBuilder: (context, index) {
                                      final store = stores[index];
                                      return _FollowedStoreCard(
  store: store,
  onTap: () => Get.to(
    () => StoreDetailsPage(store: store),
    transition: Transition.rightToLeft,
  ),
  onToggleFollow: () => _toggleFollow(store), // 👈 ici
  isFollowLoading: isFollowLoading,           // 👈 ici
);


                                    },
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }
}

/* ===================== Cards / Shimmer / Empty ===================== */
class _FollowedStoreCard extends StatelessWidget {
  final Store store;
  final VoidCallback onTap;
  final VoidCallback? onToggleFollow;
  final bool isFollowLoading; // optionnel (par défaut false)

  const _FollowedStoreCard({
    Key? key,
    required this.store,
    required this.onTap,
    this.onToggleFollow,
    this.isFollowLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final t = Theme.of(context);
    final images = store.images ?? [];
    final storeName = (store.name ?? '').trim().isEmpty
        ? ('unknown_store'.tr.isEmpty ? 'Unknown Store' : 'unknown_store'.tr)
        : store.name!.trim();
    final type = (store.typeName ?? '').trim();
    final isFollowing = store.isFollowed;
    final followers = store.followersCount ?? 0;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: t.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.black12.withOpacity(0.1)),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Vignette
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: (images.isNotEmpty && images.first != null)
                  ? CachedImageWidget(
                      imageUrl: '${images.first}',
                      width: 64,
                      height: 64,
                      fit: BoxFit.cover,
                      borderRadius: BorderRadius.circular(12),
                    )
                  : Image.asset(
                      'assets/default_store_image.png',
                      width: 64,
                      height: 64,
                      fit: BoxFit.cover,
                    ),
            ),
            const SizedBox(width: 12),

            // Infos
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Nom
                  Text(
                    storeName,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w700),
                  ),
                  const SizedBox(height: 6),

                  // Type
                  if (type.isNotEmpty)
                    _FSChip(label: type, color: Colors.blueGrey),
                ],
              ),
            ),

            const SizedBox(width: 12),

            // Bouton Follow / Unfollow avec compteur
           TextButton(
  onPressed: (onToggleFollow == null || isFollowLoading) ? null : onToggleFollow,
  style: TextButton.styleFrom(
    backgroundColor: Colors.black, // vert si suivi
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    foregroundColor: Colors.white,
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
  ),
  child: isFollowLoading
      ? const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        )
      : Text(
          isFollowing
              ? '${'following'.tr.isEmpty ? 'Following' : 'following'.tr} $followers'
              : '${'follow'.tr.isEmpty ? 'Follow' : 'follow'.tr} $followers',
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w700),
        ),
)

          ],
        ),
      ),
    );
  }
}

class _FSChip extends StatelessWidget {
  final String label;
  final Color color;
  const _FSChip({required this.label, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.10),
        border: Border.all(color: color.withOpacity(0.45)),
        borderRadius: BorderRadius.circular(9),
      ),
      child: Text(
        label,
        style: TextStyle(fontSize: 11, fontWeight: FontWeight.w700, color: color),
      ),
    );
  }
}

class _FSShimmerList extends StatelessWidget {
  const _FSShimmerList({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 16),
      itemCount: 6,
      itemBuilder: (context, index) => const _FSShimmerCard(),
    );
  }
}

class _FSShimmerCard extends StatelessWidget {
  const _FSShimmerCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.white.withOpacity(0.30),
      highlightColor: Colors.white.withOpacity(0.10),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.18),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white24),
        ),
        child: Row(
          children: [
            Container(width: 64, height: 64, decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(12))),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(height: 14, width: 180, color: Colors.white),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      Container(height: 12, width: 60, color: Colors.white),
                      const SizedBox(width: 8),
                      Container(height: 12, width: 40, color: Colors.white),
                      const SizedBox(width: 8),
                      Container(height: 12, width: 50, color: Colors.white),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(height: 30, width: 80, color: Colors.white),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _FSEmptyState extends StatelessWidget {
  final String title;
  final String subtitle;
  final String ctaText;
  final VoidCallback onCta;

  const _FSEmptyState({
    required this.title,
    required this.subtitle,
    required this.ctaText,
    required this.onCta,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 56, 24, 24),
      child: Column(
        children: [
          // Illustration
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.18),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.35)),
            ),
            child: const Icon(Icons.store_mall_directory_rounded, color: Colors.white, size: 56),
          ),
          const SizedBox(height: 16),
          Text(title, style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.w800)),
          const SizedBox(height: 8),
          Text(subtitle, textAlign: TextAlign.center, style: const TextStyle(color: Colors.white70)),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onCta,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white.withOpacity(0.18),
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white30),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
              elevation: 0,
            ),
            child: Text(ctaText),
          ),
        ],
      ),
    );
  }
}

class _GlassHeader extends StatelessWidget {
  final String title;
  const _GlassHeader({required this.title});

    void _navigateToHome() async {
    Get.offAll(
      () => ZoomDrawerWrapper(),
      transition: Transition.noTransition,
    );
  }


  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.25),
              border: Border.all(color: Colors.white.withOpacity(0.35)),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const SizedBox(width: 6),
                _GlassIconButton(
                  icon: Icons.arrow_back_ios_new_rounded,
                  onTap: () => _navigateToHome(),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title.tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(width: 46), // équilibre visuel avec le bouton retour
              ],
            ),
          ),
        ),
      ),
    );
  }
}


class _GlassIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _GlassIconButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: InkWell(
          onTap: onTap,
          child: SizedBox(
            width: 40,
            height: 40,
            child: Icon(icon, color: Colors.white),
          ),
        ),
      ),
    );
  }
}