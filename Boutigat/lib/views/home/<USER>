import 'dart:ui';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/safety_notice.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/cupertino.dart';
import 'package:shimmer/shimmer.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '/constants/app_colors.dart';
import 'package:boutigak/views/widgets/item_widget.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// === PAGE FAVORIS – style MyOrdersPage ===
class FavoritePage extends StatefulWidget {
  @override
  _FavoritePageState createState() => _FavoritePageState();
}

class _FavoritePageState extends State<FavoritePage> {
  final ItemController itemController = Get.find<ItemController>();

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLikedItems();
    });
  }

  Future<void> _loadLikedItems() async {
    setState(() => _isLoading = true);
    try {
      await itemController.fetchLikedItems();
    } catch (e) {
      debugPrint("Erreur fetchLikedItems: $e");
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _onPullToRefresh() async {
    try {
      await itemController.fetchLikedItems();
    } catch (e) {
      Get.snackbar(
        'error'.tr.isEmpty ? 'Error' : 'error'.tr,
        'refresh_favorites_failed'.tr.isEmpty ? 'Failed to refresh favorites' : 'refresh_favorites_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _navigateToHome() {
    Get.offAll(() => ZoomDrawerWrapper(), transition: Transition.noTransition);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    ScreenUtil.init(context, designSize: const Size(375, 812));

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Dégradé vert
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.92),
                    theme.colorScheme.primary.withOpacity(0.90),
                  ],
                ),
              ),
            ),
          ),

          SafeArea(
            top: true,
            bottom: false,
            child: _isLoading
                ? Column(
                    children: const [
                      _GlassHeader(title: 'favorites'),
                      Expanded(child: _FavShimmerList()),
                    ],
                  )
                : Obx(() {
                    final likedItems = itemController.likedItems;

                    return CustomScrollView(
                      physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                      slivers: [
                        // Pull to refresh iOS – loader blanc
                        CupertinoSliverRefreshControl(
                          onRefresh: _onPullToRefresh,
                          builder: (context, refreshState, pulledExtent, refreshTriggerPullDistance, refreshIndicatorExtent) {
                            return const Center(
                              child: CupertinoActivityIndicator(radius: 14, color: Colors.white),
                            );
                          },
                        ),

                        const SliverToBoxAdapter(child: _GlassHeader(title: 'favorites')),

                        if (likedItems.isEmpty)
                          SliverToBoxAdapter(
                            child: _FavEmptyState(
                              title: 'no_favorites_title'.tr.isEmpty ? 'No favorite items' : 'no_favorites_title'.tr,
                              subtitle: 'no_favorites_subtitle'.tr.isEmpty
                                  ? 'Like items you love and they will appear here.'
                                  : 'no_favorites_subtitle'.tr,
                              ctaText: 'back_home'.tr.isEmpty ? 'Back to Home' : 'back_home'.tr,
                              onCta: _navigateToHome,
                            ),
                          )
                        else
                          SliverToBoxAdapter(
                            child: LayoutBuilder(
                              builder: (context, _) {
                                final paddingTop = MediaQuery.of(context).padding.top;
                                final viewportH = MediaQuery.of(context).size.height - paddingTop;
                                return ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: viewportH),
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                                    ),
                                    child: ListView.separated(
                                      padding: const EdgeInsets.fromLTRB(12, 12, 12, 16),
                                      physics: const NeverScrollableScrollPhysics(),
                                      shrinkWrap: true,
                                      itemCount: likedItems.length,
                                      separatorBuilder: (_, __) => const SizedBox(height: 8),
                                      itemBuilder: (context, index) {
                                        final item = likedItems[index];
                                        return LikedItemWidget(
                                          key: ValueKey(item.id),
                                          item: item,
                                          isFavorited: item.isLiked,
                                          toggleFavorite: () {}, // géré dans le widget
                                        );
                                      },
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                      ],
                    );
                  }),
          ),
        ],
      ),
    );
  }
}


class _FavShimmerList extends StatelessWidget {
  const _FavShimmerList({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 16),
      itemCount: 6,
      itemBuilder: (context, index) => const _FavShimmerCard(),
    );
  }
}

class _FavShimmerCard extends StatelessWidget {
  const _FavShimmerCard({super.key});

  @override
  Widget build(BuildContext context) {
    // shimmer doux, en blanc, pour matcher ton header/verre
    final base      = Colors.white.withOpacity(0.55);
    final highlight = Colors.white.withOpacity(0.32);

    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: highlight,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        // Le "glass" s'applique DANS le card (blur + fond translucide)
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 14, sigmaY: 14),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.0),
                // même look que tes autres sections glass
                color: Colors.white.withOpacity(0.38),
                border: Border.all(color: Colors.white.withOpacity(0.48)),
                // léger “sheen” pour le verre
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.42),
                    Colors.white.withOpacity(0.24),
                  ],
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(width: 4.0),

                  // Image 60x70 avec effet glass
                  _GlassBox(width: 60, height: 70, radius: 8),

                  const SizedBox(width: 8.0),

                  // Corps
                  Expanded(
                    child: Row(
                      children: [
                        // Colonne titre + chip marque
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Titre (145 x 14)
                            _GlassBox(width: 145, height: 14, radius: 4),

                            const SizedBox(height: 8),

                            // Chip marque (90 x 18)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.40),
                                borderRadius: BorderRadius.circular(5),
                                border: Border.all(color: Colors.white.withOpacity(0.48)),
                              ),
                              child: _GlassBox(width: 78, height: 12, radius: 3), // contenu suggéré
                            ),
                          ],
                        ),

                        const Spacer(),

                        // Colonne prix (label + valeur)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // "price (mru)" label simulé (90 x 12)
                            _GlassBox(width: 90, height: 12, radius: 4),
                            const SizedBox(height: 8),
                            // valeur prix (60 x 14)
                            _GlassBox(width: 60, height: 14, radius: 4),
                          ],
                        ),

                        const Spacer(),
                      ],
                    ),
                  ),

                  // Icône cœur (cercle verre 28, icône suggérée)
                  Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withOpacity(0.40),
                      border: Border.all(color: Colors.white.withOpacity(0.48)),
                    ),
                    child: Center(
                      child: Icon(Icons.favorite_border,
                          size: 16, color: Colors.white.withOpacity(0.7)),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Petit helper pour dessiner un rectangle "glass" réutilisable
class _GlassBox extends StatelessWidget {
  final double width;
  final double height;
  final double radius;

  const _GlassBox({
    required this.width,
    required this.height,
    this.radius = 8,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width, height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radius),
        color: Colors.white.withOpacity(0.22),
        border: Border.all(color: Colors.white.withOpacity(0.28)),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.26),
            Colors.white.withOpacity(0.16),
          ],
        ),
      ),
    );
  }
}

class _FavEmptyState extends StatelessWidget {
  final String title;
  final String subtitle;
  final String ctaText;
  final VoidCallback onCta;

  const _FavEmptyState({
    required this.title,
    required this.subtitle,
    required this.ctaText,
    required this.onCta,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 56, 24, 24),
      child: Column(
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.18),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withOpacity(0.35)),
            ),
            child: const Icon(Icons.favorite_border_rounded, color: Colors.white, size: 56),
          ),
          const SizedBox(height: 16),
          Text(title, style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.w800)),
          const SizedBox(height: 8),
          Text(subtitle, textAlign: TextAlign.center, style: const TextStyle(color: Colors.white70)),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onCta,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white.withOpacity(0.18),
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white30),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
              elevation: 0,
            ),
            child: Text(ctaText),
          ),
        ],
      ),
    );
  }
}

Widget _shBox({double? width, double? height}) {
  return Shimmer.fromColors(
    baseColor: Colors.grey[300]!,
    highlightColor: Colors.grey[100]!,
    child: Container(width: width, height: height, color: Colors.grey[300]),
  );
}

Widget _shCircle(double size) {
  return Shimmer.fromColors(
    baseColor: Colors.grey[300]!,
    highlightColor: Colors.grey[100]!,
    child: Container(
      width: size,
      height: size,
      decoration: const BoxDecoration(shape: BoxShape.circle, color: Colors.grey),
    ),
  );
}
class _GlassHeader extends StatelessWidget {
  final String title;
  const _GlassHeader({required this.title});

    void _navigateToHome() async {
    Get.offAll(
      () => ZoomDrawerWrapper(),
      transition: Transition.noTransition,
    );
  }


  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.25),
              border: Border.all(color: Colors.white.withOpacity(0.35)),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const SizedBox(width: 6),
                _GlassIconButton(
                  icon: Icons.arrow_back_ios_new_rounded,
                  onTap: () => _navigateToHome(),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title.tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(width: 46), // équilibre visuel avec le bouton retour
              ],
            ),
          ),
        ),
      ),
    );
  }
}


class _GlassIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _GlassIconButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: InkWell(
          onTap: onTap,
          child: SizedBox(
            width: 40,
            height: 40,
            child: Icon(icon, color: Colors.white),
          ),
        ),
      ),
    );
  }
}