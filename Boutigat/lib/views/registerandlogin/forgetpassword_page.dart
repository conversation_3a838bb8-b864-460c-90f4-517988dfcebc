import 'package:boutigak/controllers/otp_controller.dart';
import 'package:boutigak/controllers/reset_password_controlleur.dart';
import 'package:boutigak/views/registerandlogin/reset_password_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Réutilise tes classes existantes
// import 'password_strength_widgets.dart'; // si tu as externalisé PasswordStrengthBar / PasswordRequirements

class ForgotAndResetPasswordPage extends StatefulWidget {
  const ForgotAndResetPasswordPage({super.key});

  @override
  State<ForgotAndResetPasswordPage> createState() => _ForgotAndResetPasswordPageState();
}

class _ForgotAndResetPasswordPageState extends State<ForgotAndResetPasswordPage> {
  // Contrôleurs existants (comme dans tes pages actuelles)
  final OtpController otpC = Get.put(OtpController());
  final ResetPasswordController resetC = Get.put(ResetPasswordController());

  // Étape (1 = demander OTP, 2 = réinitialiser)
  final RxInt _step = 1.obs;

  // Champs
  final _phoneCtrl   = TextEditingController();
  final _otpCtrl     = TextEditingController();
  final _newCtrl     = TextEditingController();
  final _confirmCtrl = TextEditingController();

  // Visibilité mot de passe (reactive)
  final RxBool _newVisible     = false.obs;
  final RxBool _confirmVisible = false.obs;

  @override
  void dispose() {
    _phoneCtrl.dispose();
    _otpCtrl.dispose();
    _newCtrl.dispose();
    _confirmCtrl.dispose();
    super.dispose();
  }

  // --------- Widgets utilitaires (style aligné à PasswordPage) ---------
  OutlineInputBorder _outline(BuildContext context, Color color) => OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.r),
        borderSide: BorderSide(color: color, width: 1),
      );

  InputDecoration _decor(
    BuildContext context, {
    required String label,
    required String hint,
    required IconData prefix,
    Widget? suffix,
    Color? fill,
  }) {
    final theme = Theme.of(context);
    return InputDecoration(
      labelText: label,
      hintText: hint,
      isDense: true,
      filled: true,
      fillColor: fill ?? theme.colorScheme.surfaceVariant.withOpacity(0.4),
      contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 14.h),
      border: _outline(context, theme.dividerColor),
      enabledBorder: _outline(context, theme.dividerColor),
      focusedBorder: _outline(context, theme.colorScheme.primary),
      prefixIcon: Icon(prefix),
      suffixIcon: suffix,
    );
  }

  Widget _passwordField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required String hint,
    required RxBool visible,
    bool readOnly = false,
  }) {
    final theme = Theme.of(context);
    final fill = theme.colorScheme.surfaceVariant.withOpacity(0.4);

    return Obx(() => TextField(
          controller: controller,
          obscureText: !visible.value,
          readOnly: readOnly,
          textInputAction: TextInputAction.next,
          decoration: _decor(
            context,
            label: label,
            hint: hint,
            prefix: Icons.password_outlined,
            fill: fill,
            suffix: IconButton(
              tooltip: visible.value ? 'hide_password'.tr : 'show_password'.tr,
              icon: Icon(visible.value ? Icons.visibility : Icons.visibility_off),
              onPressed: () => visible.toggle(),
            ),
          ),
        ));
  }

  // --------- Actions ---------
  Future<void> _requestOtp() async {
    final phone = _phoneCtrl.text.trim();
    if (phone.isEmpty) {
      Get.snackbar('Error', 'Veuillez saisir votre téléphone', snackPosition: SnackPosition.BOTTOM);
      return;
    }
    // isLoading est sur otpC côté vue; on s'appuie sur son propre indicateur si dispo,
    // sinon on peut temporairement se baser sur resetC.isLoading pour désactiver
    await otpC.requestPasswordReset(phone);
    // Ici, si succès côté backend, on passe à l’étape 2 :
    _step.value = 2;
  }

Future<void> _submitReset() async {
  final ok = await resetC.resetPassword(
    _phoneCtrl.text.trim(),
    _otpCtrl.text.trim(),
    _newCtrl.text.trim(),
    _confirmCtrl.text.trim(),
  );
  if (ok) {
    _otpCtrl.clear();
    _newCtrl.clear();
    _confirmCtrl.clear();
    Get.snackbar('success'.tr, 'password_changed'.tr, snackPosition: SnackPosition.BOTTOM);
    // Exemple: retour à la page login
    // Get.offAllNamed('/login');
  }
}


  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final c1 = theme.colorScheme.primary;
    final c2 = theme.colorScheme.primary.withOpacity(0.95);

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      appBar: AppBar(
        title: Text('forgot_password'.tr), // "Mot de passe oublié"
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // ---- Fond dégradé
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [c1, c2],
                ),
              ),
            ),
          ),

          // ---- Contenu
          Obx(() {
            final loadingAny = otpC.isLoading.value || resetC.isLoading.value;
            return CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Header translucide (même style PasswordPage)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 12.h),
                    child: Material(
                      color: Colors.white.withOpacity(0.10),
                      borderRadius: BorderRadius.circular(16.r),
                      child: Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.r),
                          border: Border.all(color: Colors.white.withOpacity(0.25)),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              width: 56.w,
                              height: 56.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withOpacity(0.28),
                                    Colors.white.withOpacity(0.12),
                                  ],
                                ),
                                border: Border.all(color: Colors.white.withOpacity(0.35)),
                              ),
                              child: const Icon(Icons.sms, color: Colors.white, size: 26),
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _step.value == 1
                                        ? 'reset_step1_title'.tr // ex: "Obtenir un code OTP"
                                        : 'reset_step2_title'.tr, // ex: "Créer un nouveau mot de passe"
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w800,
                                      fontSize: 16,
                                    ),
                                  ),
                                  SizedBox(height: 4.h),
                                  Text(
                                    _step.value == 1
                                        ? 'reset_step1_sub'.tr // ex: "Saisissez votre téléphone pour recevoir un code"
                                        : 'reset_step2_sub'.tr, // ex: "Entrez le code OTP et votre nouveau mot de passe"
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.85),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // Surface + Formulaire unifié
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(24.r),
                        topRight: Radius.circular(24.r),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.06),
                          blurRadius: 20,
                          offset: const Offset(0, -6),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 24.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Material(
                            color: theme.colorScheme.surface,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16.r),
                              side: BorderSide(color: theme.dividerColor.withOpacity(0.25)),
                            ),
                            child: Padding(
                              padding: EdgeInsets.all(12.w),
                              child: Column(
                                children: [
                                  // ----- Étape 1 : Téléphone
                                  TextField(
                                    controller: _phoneCtrl,
                                    keyboardType: TextInputType.phone,
                                    textInputAction: TextInputAction.next,
                                    decoration: _decor(
                                      context,
                                      label: 'phone_number'.tr,
                                      hint: 'enter_phone'.tr,
                                      prefix: Icons.phone,
                                    ),
                                  ),

                                  if (_step.value == 2) ...[
                                    SizedBox(height: 12.h),

                                    // ----- Étape 2 : OTP
                                    TextField(
                                      controller: _otpCtrl,
                                      keyboardType: TextInputType.number,
                                      textInputAction: TextInputAction.next,
                                      decoration: _decor(
                                        context,
                                        label: 'otp_code'.tr,
                                        hint: 'enter_otp'.tr,
                                        prefix: Icons.sms_outlined,
                                      ),
                                    ),

                                    SizedBox(height: 12.h),

                                    // Nouveau mot de passe + indicateurs
                                    _passwordField(
                                      context: context,
                                      controller: _newCtrl,
                                      label: 'new_password'.tr,
                                      hint: 'new_password_hint'.tr,
                                      visible: _newVisible,
                                    ),
                                    SizedBox(height: 10.h),
                                    PasswordStrengthBar(passwordListenable: _newCtrl),
                                    SizedBox(height: 8.h),
                                    PasswordRequirements(passwordListenable: _newCtrl),

                                    SizedBox(height: 12.h),

                                    // Confirmation
                                    _passwordField(
                                      context: context,
                                      controller: _confirmCtrl,
                                      label: 'confirm_password'.tr,
                                      hint: 'confirm_password_hint'.tr,
                                      visible: _confirmVisible,
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 18.h),

                          // ----- Erreur globale backend (reset)
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 200),
                            child: resetC.showErrorMessage.value.isEmpty
                                ? const SizedBox.shrink()
                                : Container(
                                    key: const ValueKey('reset-error-box'),
                                    padding: EdgeInsets.all(12.w),
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.errorContainer,
                                      borderRadius: BorderRadius.circular(10.r),
                                      border: Border.all(color: theme.colorScheme.error.withOpacity(.25)),
                                    ),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Icon(Icons.error_outline, color: theme.colorScheme.onErrorContainer),
                                        SizedBox(width: 8.w),
                                        Expanded(
                                          child: Text(
                                            resetC.showErrorMessage.value,
                                            style: TextStyle(
                                              color: theme.colorScheme.onErrorContainer,
                                              fontSize: 13.sp,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                          ),

                          const Spacer(),

                          // ----- Boutons (un seul visible selon l’étape)
                          SizedBox(
                            width: double.infinity,
                            height: 48.h,
                            child: FilledButton.icon(
                              onPressed: loadingAny
                                  ? null
                                  : () async {
                                      FocusScope.of(context).unfocus();
                                      if (_step.value == 1) {
                                        await _requestOtp();
                                      } else {
                                        await _submitReset();
                                      }
                                    },
                              icon: loadingAny
                                  ? SizedBox(
                                      width: 18.w,
                                      height: 18.w,
                                      child: const CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  : Icon(_step.value == 1 ? Icons.send_to_mobile : Icons.lock_reset),
                              label: Text(
                                loadingAny
                                    ? 'loading'.tr
                                    : (_step.value == 1 ? 'send_code'.tr : 'reset_password'.tr),
                              ),
                              style: FilledButton.styleFrom(
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          }),

          // ---- Overlay barre de progression top (optionnel)
          Obx(() {
            final loadingAny = otpC.isLoading.value || resetC.isLoading.value;
            if (!loadingAny) return const SizedBox.shrink();
            return IgnorePointer(
              ignoring: true,
              child: AnimatedOpacity(
                opacity: 1,
                duration: const Duration(milliseconds: 150),
                child: Container(
                  color: Colors.black.withOpacity(0.04),
                  alignment: Alignment.topCenter,
                  padding: EdgeInsets.only(top: 8.h),
                  child: const LinearProgressIndicator(minHeight: 2),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
class PasswordStrengthBar extends StatelessWidget {
  const PasswordStrengthBar({super.key, required this.passwordListenable});
  final TextEditingController passwordListenable;

  int _score(String s) {
    var score = 0;
    if (s.length >= 8) score++;
    if (RegExp(r'[A-Z]').hasMatch(s)) score++;
    if (RegExp(r'[a-z]').hasMatch(s)) score++;
    if (RegExp(r'\d').hasMatch(s)) score++;
    if (RegExp(r'[!@#\$%^&*(),.?":{}|<>_\-\\/\[\]]').hasMatch(s)) score++;
    return score.clamp(0, 5);
  }

  String _label(int score) {
    switch (score) {
      case 0:
      case 1:
        return 'weak'.tr;
      case 2:
      case 3:
        return 'medium'.tr;
      default:
        return 'strong'.tr;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: passwordListenable,
      builder: (_, value, __) {
        final s = value.text;
        final score = _score(s);
        final percent = score / 5.0;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(6.r),
              child: LinearProgressIndicator(
                value: percent == 0 ? 0.02 : percent,
                minHeight: 8.h,
              ),
            ),
            SizedBox(height: 6.h),
            Row(
              children: [
                Icon(Icons.shield, size: 16.sp, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 6.w),
                Text(
                  '${'strength'.tr}: ${_label(score)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
class PasswordRequirements extends StatelessWidget {
  const PasswordRequirements({super.key, required this.passwordListenable});
  final TextEditingController passwordListenable;

  bool _hasUpper(String s) => RegExp(r'[A-Z]').hasMatch(s);
  bool _hasLower(String s) => RegExp(r'[a-z]').hasMatch(s);
  bool _hasDigit(String s) => RegExp(r'\d').hasMatch(s);
  bool _hasSymbol(String s) => RegExp(r'[!@#\$%^&*(),.?":{}|<>_\-\\/\[\]]').hasMatch(s);
  bool _hasLen(String s) => s.length >= 8;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: passwordListenable,
      builder: (_, value, __) {
        final s = value.text;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _RequirementRow(text: 'req_min_length'.tr, ok: _hasLen(s)),
            _RequirementRow(text: 'req_uppercase'.tr, ok: _hasUpper(s)),
            _RequirementRow(text: 'req_lowercase'.tr, ok: _hasLower(s)),
            _RequirementRow(text: 'req_digit'.tr, ok: _hasDigit(s)),
            _RequirementRow(text: 'req_symbol'.tr, ok: _hasSymbol(s)),
          ],
        );
      },
    );
  }
}
class _RequirementRow extends StatelessWidget {
  const _RequirementRow({required this.text, required this.ok});
  final String text;
  final bool ok;

  @override
  Widget build(BuildContext context) {
    final color = ok ? Colors.green : Theme.of(context).colorScheme.onSurfaceVariant;
    final icon = ok ? Icons.check_circle : Icons.radio_button_unchecked;
    return Padding(
      padding: EdgeInsets.only(bottom: 6.h),
      child: Row(
        children: [
          Icon(icon, size: 16.sp, color: color),
          SizedBox(width: 6.w),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: color),
            ),
          ),
        ],
      ),
    );
  }
}