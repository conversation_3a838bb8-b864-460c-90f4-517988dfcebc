import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '/constants/app_colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// ✅ Responsive CustomTextFormField, CustomGenderRadioField, and TextLink without fixed height

class TextLink extends StatelessWidget {
  final String text;
  final String routeName;

  const TextLink({super.key, required this.text, required this.routeName});

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);

    return GestureDetector(
      onTap: () => Get.offAndToNamed(routeName),
      child: Text(
        text,
        style: TextStyle(
          color: AppColors.primary,
          
          
          fontSize: 14.sp,
        ),
      ),
    );
  }
}


// ======================= TEXT FIELD =======================
class CustomTextFormField extends StatefulWidget {
  final String hintText;
  final RxString value;
  final double width;
  final RxnString? errorText;
  final bool obscureText;
  final IconData? prefixIcon;
  final List<TextInputFormatter>? inputFormatters;

  const CustomTextFormField({
    super.key,
    required this.hintText,
    required this.value,
    required this.width,
    required this.errorText,
    this.obscureText = false,
    this.prefixIcon,
    this.inputFormatters,
  });

  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  bool _obscure = false;
  final _focusNode = FocusNode();

  // ✅ No `late`: initialize immediately to avoid LateInitializationError
  final TextEditingController _controller = TextEditingController();

  Worker? _syncWorker;

  @override
  void initState() {
    super.initState();
    _obscure = widget.obscureText;

    // initial value
    _controller.text = widget.value.value;

    // keep controller synced with RxString
    _syncWorker = ever<String>(widget.value, (v) {
      if (_controller.text != v) {
        _controller.text = v;
        _controller.selection = TextSelection.collapsed(offset: _controller.text.length);
      }
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    _syncWorker?.dispose();
    super.dispose();
  }

  OutlineInputBorder _outline(Color color) => OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.r),
        borderSide: BorderSide(color: color, width: 1),
      );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final baseBorderColor = Colors.grey.shade400;
    final errorOn = (widget.errorText?.value?.isNotEmpty ?? false);
    final minH = 55.h;

    return SizedBox(
      width: widget.width.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ConstrainedBox(
            constraints: BoxConstraints(minHeight: minH),
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              onChanged: (v) => widget.value.value = v,
              obscureText: _obscure,
              inputFormatters: widget.inputFormatters,
              textAlignVertical: TextAlignVertical.center, // ✅ cursor & text centered
              style: TextStyle(
                color: theme.colorScheme.onSurface,
                fontSize: 14.sp,
                height: 1.2,
              ),
              cursorHeight: 18.sp,
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: TextStyle(color: AppColors.disabled, fontSize: 14.sp),
                isDense: true,
                filled: true,
                fillColor: theme.colorScheme.surface,
                contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 0),
                prefixIcon: (widget.prefixIcon != null)
                    ? Padding(
                        padding: EdgeInsetsDirectional.only(start: 10.w, end: 8.w),
                        child: Icon(widget.prefixIcon, size: 20.sp, color: AppColors.disabled),
                      )
                    : null,
                prefixIconConstraints: BoxConstraints(minWidth: 0, minHeight: minH),
                suffixIcon: widget.obscureText
                    ? IconButton(
                        onPressed: () => setState(() => _obscure = !_obscure),
                        icon: Icon(
                          _obscure ? Icons.visibility_off : Icons.visibility,
                          size: 20.sp,
                          color: AppColors.disabled,
                        ),
                      )
                    : null,
                enabledBorder: _outline(baseBorderColor),
                focusedBorder: _outline(theme.colorScheme.primary),
                errorBorder: _outline(AppColors.error),
                focusedErrorBorder: _outline(AppColors.error),
                border: _outline(baseBorderColor),
              ),
            ),
          ),
          if (errorOn) ...[
            SizedBox(height: 6.h),
            Text(
              widget.errorText!.value!,
              style: TextStyle(color: AppColors.error, fontSize: 12.sp, height: 1.1),
            ),
          ],
        ],
      ),
    );
  }
}

// ======================= GENDER FIELD (UNIFORM LOOK) =======================
class CustomGenderRadioField extends StatelessWidget {
  final String hintText;
  final Rxn<String> value;
  final double width;
  final RxnString? errorText;

  const CustomGenderRadioField({
    super.key,
    required this.hintText,
    required this.value,
    required this.width,
    required this.errorText,
  });

  OutlineInputBorder _outline(BuildContext context, Color color) => OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.r),
        borderSide: BorderSide(color: color, width: 1),
      );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final minH = 55.h;
    final baseBorderColor = Colors.grey.shade400;

    return SizedBox(
      width: width.w,
      child: Obx(() {
        final hasError = (errorText?.value?.isNotEmpty ?? false);
        final current = value.value;

        Widget _chip(String label, String v) {
          final selected = current == v;
          return InkWell(
            borderRadius: BorderRadius.circular(8.r),
            onTap: () => value.value = v,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 160),
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: selected ? theme.colorScheme.primary.withOpacity(0.10) : theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8.r),
             //   border: Border.all(color: selected ? theme.colorScheme.primary : Colors.grey.shade300, width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 160),
                    width: 16.w,
                    height: 16.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: selected ? theme.colorScheme.primary : Colors.grey,
                        width: 2,
                      ),
                      color: selected ? theme.colorScheme.primary : Colors.transparent,
                    ),
                    child: selected ? Icon(Icons.check, size: 12.sp, color: Colors.white) : null,
                  ),
                  SizedBox(width: 6.w),
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: theme.colorScheme.onSurface,
                      fontWeight: selected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InputDecorator(
              isFocused: false,
              isEmpty: current == null || current!.isEmpty,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyle(color: AppColors.disabled, fontSize: 14.sp),
                isDense: true,
                filled: true,
                fillColor: theme.colorScheme.surface,
                contentPadding: EdgeInsets.symmetric(horizontal: 12.w),
                prefixIcon: Padding(
                  padding: EdgeInsetsDirectional.only(start: 10.w, end: 8.w),
                  child: Icon(Icons.wc, size: 20.sp, color: AppColors.disabled),
                ),
                prefixIconConstraints: BoxConstraints(minWidth: 0, minHeight: minH),
                enabledBorder: _outline(context, baseBorderColor),
                focusedBorder: _outline(context, theme.colorScheme.primary),
                errorBorder: _outline(context, AppColors.error),
                focusedErrorBorder: _outline(context, AppColors.error),
                border: _outline(context, baseBorderColor),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: minH),
                child: Row(
                  children: [
                    Expanded(
                      child: Wrap(
                        alignment: WrapAlignment.spaceBetween,
                        spacing: 8.w,
                        runSpacing: 8.h,
                        children: [
                          _chip('male'.tr, 'Male'),
                          _chip('female'.tr, 'Female'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (hasError) ...[
              SizedBox(height: 6.h),
              Text(
                errorText!.value!,
                style: TextStyle(color: AppColors.error, fontSize: 12.sp, height: 1.1),
              ),
            ],
          ],
        );
      }),
    );
  }
}

class LoadingButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  final Color? bgColor;
  final double? width;
  final double height;
  final double borderRadius;

  const LoadingButton({
    super.key,
    required this.text,
    required this.onPressed,
    required this.isLoading,
    this.bgColor,
    this.width,
    this.height = 44,
    this.borderRadius = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    final double computedWidth =
        width ?? MediaQuery.of(context).size.width * 0.9184;

    final Color baseColor = bgColor ?? AppColors.primary;

    return SizedBox(
      width: computedWidth,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: baseColor,
          disabledBackgroundColor: baseColor.withOpacity(0.7),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 14),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isLoading) ...[
              SizedBox(
                width: 18,
                height: 18,
                child: const CircularProgressIndicator(
                  strokeWidth: 2.2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 8), // 👈 espace entre loader et texte
            ],
            Text(
              text,
              style: TextStyle(
                fontWeight: AppFontWeights.medium,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
