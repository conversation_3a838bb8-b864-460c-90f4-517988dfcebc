import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/views/widgets/otp_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/register_controller.dart';
import 'registerandlogin_widgets.dart';
import 'package:boutigak/views/widgets/customlogo_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';



class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});





  void _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw 'Could not launch $url';
    }
  }





  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    final RegisterController controller = Get.find<RegisterController>();

   final screenWidth = MediaQuery.of(context).size.width;
final sidePadding = screenWidth * 0.0407; // tu peux garder ton ratio
final verticalPadding = screenWidth * 0.02;

// 👇 espace horizontal entre les 2 champs
final double gap = 12.w;

// largeur réellement utilisable à l’intérieur des paddings
final usable = screenWidth - (sidePadding * 2);

// chaque champ prend la moitié de l’espace restant une fois le gap soustrait
final double fieldWidth = (usable - gap) / 2;


    return Scaffold(
      extendBodyBehindAppBar: true,
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            SizedBox(height: MediaQuery.of(context).size.height * 0.12),
             Padding(
               padding:  EdgeInsets.symmetric(horizontal: sidePadding),
               child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                 children: [
                   Text(
                              'create_account'.tr,
                              style: TextStyle(fontSize: 24.sp, fontWeight: AppFontWeights.bold),
                            ),
                 ],
               ),
               
             ),
              SizedBox(height: verticalPadding),
                    Padding(
                      padding:  EdgeInsets.symmetric(horizontal: sidePadding),
                      child: Row(
                        children: [
                          Text(
                              "login_here".tr,
                              style: TextStyle(
                                color: AppColors.onSurface,
                                
                                
                                fontSize:14.sp,
                              )),
                              SizedBox(width: 4.w),
                          TextLink(
                            text: "login".tr,
                            routeName: '/login',
                          ),
                        ],
                      ),
                    ),
            SizedBox(height:verticalPadding),
            const CustomLogoWidget(),
            SizedBox(height: verticalPadding),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: sidePadding),
              child: Form(
                child: Column(
                  children: [
                   Wrap(
  spacing: gap,                 // 👈 espace horizontal constant
  runSpacing: verticalPadding,  // 👈 espace si ça passe à la ligne
  children: [
    SizedBox(
      width: fieldWidth,
      child: CustomTextFormField(
        hintText: 'first_name'.tr,
        value: controller.firstName,
        errorText: controller.firstNameError,
        width: fieldWidth,           // garde pour ton widget interne
        prefixIcon: Icons.person,
      ),
    ),
    SizedBox(
      width: fieldWidth,
      child: CustomTextFormField(
        hintText: 'last_name'.tr,
        value: controller.lastName,
        errorText: controller.lastNameError,
        width: fieldWidth,
        prefixIcon: Icons.person_outline,
      ),
    ),
  ],
),

                    SizedBox(height: verticalPadding),
                   CustomTextFormField(
  hintText: 'phone_number'.tr,
  value: controller.phoneNumber,
  errorText: controller.phoneNumberError,
  width: screenWidth * 0.9184,
  prefixIcon: Icons.phone,
  inputFormatters: [
    FilteringTextInputFormatter.digitsOnly,
    LengthLimitingTextInputFormatter(8),
  ],
),

                    SizedBox(height: verticalPadding),
                  CustomGenderRadioField(
  key: const ValueKey('gender_field'), // Empêche la re-render d’agrandissement
  hintText: 'gender'.tr,
  width: screenWidth * 0.9184,
  value: controller.gender,
  errorText: controller.genderError,
),

                    SizedBox(height: verticalPadding),
                    CustomTextFormField(
                      hintText: 'password'.tr,
                      value: controller.password,
                      errorText: controller.passwordError,
                      obscureText: true,
                      width: screenWidth * 0.9184,
                      prefixIcon: Icons.lock,
                    ),
                    SizedBox(height: verticalPadding),

                  

                    // ✅ Terms & Privacy Notice
                  Padding(
  padding: EdgeInsets.only(bottom: verticalPadding
  ),
  child: Wrap(
    alignment: WrapAlignment.center,
    children: [
      Text(
        "by_registering_you_accept".tr,
        style: TextStyle(fontSize: 10.sp),
      ),
      GestureDetector(
        onTap: () => _launchURL('https://boutigak.com/privacy-policy'),
        child: Text(
          "terms_of_use".tr,
          style: TextStyle(
            fontSize: 10.sp,
            color: AppColors.primary,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
      Text(
        "and_our".tr,
        style: TextStyle(fontSize: 10.sp),
      ),
      GestureDetector(
        onTap: () => _launchURL('https://boutigak.com/privacy-policy'),
        child: Text(
          "privacy_policy".tr,
          style: TextStyle(
            fontSize: 10.sp,
            color: AppColors.primary,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    ],
  ),
),


                   Obx(() {
  final loading = controller.isLoading.value;
  return LoadingButton(
    text: "register".tr,
    isLoading: loading,
    onPressed: () {
      if (loading) return;
      if (controller.validateForm()) {
        controller.register();
      }
    },
  );
}),

                   
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
