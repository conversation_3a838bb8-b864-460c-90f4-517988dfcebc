import 'dart:convert';
import 'dart:developer';
import 'package:boutigak/data/models/badge.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:http/http.dart' as http;

class BadgeService {
  static Future<BadgeCounts> getBadgeCounts() async {
    try {


      log('route in url  ${Uri.parse('${hostURLs[PossiblesHosts.prode]}api/badges')}');



     

      var token = await WebService.getToken();


      log('token ${token}');


      final response = await http.get(
        Uri.parse('${hostURLs[PossiblesHosts.prode]}api/badges'),
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json; charset=UTF-8',
          'Authorization': token,
        },
      );



      print('get badges count ${response.statusCode} ${response.body}');
      if (response.statusCode == 200) {
        return BadgeCounts.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to load badge counts');
      }
    } catch (e) {
      throw Exception('Error getting badge counts: $e');
    }
  }

  static Future<BadgeCounts> resetBadge(String module) async {
    try {


          var token = await WebService.getToken();

      final response = await http.post(
        Uri.parse('${hostURLs[PossiblesHosts.prode]}api/badges/reset'),


      headers: {
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json; charset=UTF-8',
          'Authorization': token,
        },
        body: json.encode({'module': module}),
      );

      if (response.statusCode == 200) {
        return BadgeCounts.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to reset badge');
      }
    } catch (e) {
      throw Exception('Error resetting badge: $e');
    }
  }
}
