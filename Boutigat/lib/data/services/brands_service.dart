import 'dart:convert';

import 'package:boutigak/data/models/brands.dart';
import 'package:get/get.dart';  
import 'package:boutigak/data/services/webservices.dart';


class BrandService {
  
  //  method to fetch brands
  static Future<List<Brands>?> fetchBrands(String categoryID) async {
    var response =  await WebService.get("${AvailableServices.brands}?category_id=${categoryID}");

    if (response.statusCode == 200) {
      List<dynamic> body = jsonDecode(response.body);
      List<Brands> brands = body.map((dynamic item) => Brands.fromJson(item)).toList();
      
      return brands;
    } else {
      return null;
    }
  }

  //  method to create a brand
  static Future<bool> createBrand(String name, int categoryId) async {
    var response = await WebService.post(
      AvailableServices.brands,
      body: jsonEncode({
        "name": name,
        "category_id": categoryId,
      }),
    );

    if (response.statusCode == 201) {
    //  print('Brand created successfully');
      return true;
    } else {
    //  // Get.snakbar("Error", "Failed to create brand");
      return false;
    }
  }
}
