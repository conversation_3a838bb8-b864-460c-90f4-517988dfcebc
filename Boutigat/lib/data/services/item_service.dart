import 'dart:convert';
import 'dart:developer';

import 'dart:io';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/item_image.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';
import 'package:get/get.dart';


import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:get/get.dart';


class ItemService {
  static Future<List<Item>?> fetchItems({int page = 1, int perPage = 10}) async {
    try {
      var response = await WebService.get(
        '${AvailableServices.recommended}?page=$page&per_page=$perPage'
      );




      if (response.statusCode == 200) {
        Map<String, dynamic> body = jsonDecode(response.body);
      print('response items  ${body['items'][0]['category']}');

        List<dynamic> items = body['items'];
        return items.map((dynamic item) => Item.fromJson(item)).toList();
      }
      return null;
    } catch (e) {
      return null;
    }
  }



   static Future<bool> addPayment({
    required int itemId,
    required int providerId,
    required double amount,
    required String paymentType,
    required File paymentImage,
    String? promoCode,
  }) async {
    try {
      String token = await WebService.getToken();
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${getCurrentBackendURL()}api/items/add-payment'),
      );

      request.headers['Authorization'] = token;
      request.headers['Accept'] = 'application/json';

      request.fields['item_id'] = itemId.toString();
      request.fields['provider_id'] = providerId.toString();
      request.fields['amount'] = amount.toString();
      request.fields['payment_type'] = paymentType;
      if (promoCode != null) {
        request.fields['promo_code'] = promoCode;
      }


      print('request ${request.fields}');



      var imageStream = http.ByteStream(paymentImage.openRead());
      var imageLength = await paymentImage.length();
      var multipartFile = http.MultipartFile(
        'payment_image',
        imageStream,
        imageLength,
        filename: basename(paymentImage.path),
      );
      request.files.add(multipartFile);

      var response = await request.send();
      return response.statusCode == 201;
    } catch (e) {
      return false;
    }
  }
 
  static Future<List<Item>?> fetchgeneralItems({int page = 1, int perPage = 10}) async {
    try {
      var response = await WebService.get(
        '${AvailableServices.generalrecommended}?page=$page&per_page=$perPage'
      );

      if (response.statusCode == 200) {


        Map<String, dynamic> body = jsonDecode(response.body);

        log('response items  ${body}');
        List<dynamic> items = body['items'];
        return items.map((dynamic item) => Item.fromJson(item)).toList();
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  static Future<List<Item>?> fetchLikedItems() async {
    try {
      var response = await WebService.get(AvailableServices.likeditems);
   //    print('Response status code: ${response.statusCode}');
   //  print('response ${response.body}');
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        List<Item> items = body.map((dynamic item) => Item.fromJson(item)).toList();
        return items;
      } else {
        // Get.snakbar("Error", "Failed to load liked items");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching liked items ${e.toString()}");
      return null;
    }
  }


static Future<dynamic> postItem(Item item, Function(double) onUploadProgress) async {
  try {
    Map<String, String> fields = {
      'id': item.id.toString(),
      'title': item.title,
      'description': item.description,
      'price': item.price.toString(),
      'condition': item.condition,
      'quantity': item.quantity.toString(),
      'brand_id': item.brandId.toString(),
      'category_id': item.categoryId.toString(),
      'category_detail_id': item.categoryDetailId == null ? '' : item.categoryDetailId.toString(),
        'title_ar': item.titleAr ?? '',
        'description_ar': item.descriptionAr ?? '',
    };






    // print category_detail_id
    print('category_detail_id: ${item.categoryDetailId}');

    if (item.categoryItemDetails.isNotEmpty) {
      for (var i = 0; i < item.categoryItemDetails.length; i++) {
        fields['category_item_details[$i][id]'] = item.categoryItemDetails[i].id.toString();
        fields['category_item_details[$i][value]'] = item.categoryItemDetails[i].value;
        fields['category_item_details[$i][label_en]'] = item.categoryItemDetails[i].labelEn;
        fields['category_item_details[$i][label_ar]'] = item.categoryItemDetails[i].labelAr;
        fields['category_item_details[$i][label_fr]'] = item.categoryItemDetails[i].labelFr;
      }
    }

    // Convert images to File list
    List<File> files = item.images.map((imagePath) => File(imagePath)).toList();

    // Call the postMultipart with progress tracking
    var response = await WebService.postMultipart(
      AvailableServices.items,
      fields: fields,
      files: files,
      onSendProgress: (int sent, int total) {
        double progress = sent / total;
        onUploadProgress(progress);
      },
    );
 
    var responseString = await response.stream.bytesToString();
    var responseJson = jsonDecode(responseString);
 print('Response status code: ${response.statusCode}');
   print('Response body: $responseJson');
var a = responseJson['categoryPrice'];
    print('Response status code: ${response.statusCode}');
    print('Response body categoryPrice: ${responseJson['categoryPrice']}');

    print('Response status code: ${a.runtimeType}');



    if (response.statusCode == 201) {
      // Get.snakbar("Success", "Item successfully posted");
      return  
      {
        'categoryPrice' : double.parse(responseJson['categoryPrice']),
        'item' :  responseJson['item']
      };
    } else {
      // Get.snakbar("Error", "Failed to post item");
      return null;
    }
  } catch (e) {
    // Get.snakbar("Error", "An error occurred while posting the item");
    return null;
  }
}

  
 static Future<bool> putItem(Item item, {List<int> imageIdsToDelete = const []}) async {
  try {
    Map<String, String> fields = {
      'id': item.id.toString(),
      'title': item.title,
      'description': item.description,
      'price': item.price.toString(),
      'condition': item.condition,
      'quantity': item.quantity.toString(),
      'brand_id': item.brandId.toString(),
      'category_id': item.categoryId.toString(),
              'title_ar': item.titleAr ?? '',
        'description_ar': item.descriptionAr ?? '',
    };

    // Add category item details
    if (item.categoryItemDetails.isNotEmpty) {
      for (var i = 0; i < item.categoryItemDetails.length; i++) {
        fields['category_item_details[$i][id]'] = item.categoryItemDetails[i].id.toString();
        fields['category_item_details[$i][value]'] = item.categoryItemDetails[i].value;
        fields['category_item_details[$i][label_en]'] = item.categoryItemDetails[i].labelEn;
        fields['category_item_details[$i][label_ar]'] = item.categoryItemDetails[i].labelAr;
        fields['category_item_details[$i][label_fr]'] = item.categoryItemDetails[i].labelFr;
      }
    }

    // Add image IDs to delete
    if (imageIdsToDelete.isNotEmpty) {
      fields['delete_image_ids'] = imageIdsToDelete.join(',');
    }

    // Get ItemController to access existing images
    final itemController = Get.find<ItemController>();
    
    // Debug: Print existing images from controller
    log('Existing images in controller: ${itemController.existingImages.map((img) => '${img.id}:${img.url}').join(', ')}');
    log('Current image order in item: ${item.images.join(', ')}');
    
    // Create an array for image orders instead of individual fields
    Map<String, dynamic> imageOrders = {};

    // Process all images in their current order
    for (int i = 0; i < item.images.length; i++) {
      String imagePath = item.images[i];
      
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        // Find the matching image using filename comparison
        var existingImage = itemController.existingImages.firstWhere(
          (img) => img.url.split('/').last == imagePath.split('/').last,
          orElse: () => ItemImage(id: -1, url: '', order: 0),
        );
        
        log('Found image: ID=${existingImage.id}, URL=${existingImage.url}, Order=${existingImage.order}');
        
        if (existingImage.id != -1) {
          // Add to imageOrders map
          imageOrders[existingImage.id.toString()] = i.toString();
          log('Setting order for image ${existingImage.id} to $i');
        } else {
          log('Could not find image ID for URL: $imagePath');
        }
      }
    }

    // Convert the map to JSON and add to fields
    fields['image_orders'] = jsonEncode(imageOrders);
    log('Image orders JSON: ${fields['image_orders']}');
    
    // Only include new local file paths for upload
    List<File> files = [];
    int newImageIndex = 0;
    
    for (String imagePath in item.images) {
      // Only add local files (not URLs) as new images
      if (!imagePath.startsWith('http://') && !imagePath.startsWith('https://')) {
        files.add(File(imagePath));
        // Add order information for new images
        fields['new_image_orders[$newImageIndex]'] = newImageIndex.toString();
        newImageIndex++;
      }
    }

    log('New files to upload: ${files.length}');
    log('Image IDs to delete: $imageIdsToDelete');
    log('Image orders: ${fields.entries.where((e) => e.key.contains("image_orders")).toList()}');
    
    // Always send image_order_updated flag to ensure order is processed
    fields['image_order_updated'] = 'true';




    log('Fields to send: $fields');
    // Make a PUT request
    var response = await WebService.postMultipart(
      '${AvailableServices.itemsUpdate}',
      fields: fields,
      files: files,
    );

    var responseString = await response.stream.bytesToString();
    var responseJson = jsonDecode(responseString);

    log('Response status code update item: ${response.statusCode}');
    log('Response body update item: $responseJson');
    
    if (response.statusCode == 200) {
      return true;
    } else {
      return false;
    }
  } catch (e) {
    log("Error updating item: $e");
    return false;
  }
}


static Future<bool> deleteItem(int itemId) async {
    try {
      // Construire l'URL de suppression avec l'ID de l'élément
      String url = AvailableServices.deleteItem.replaceAll("{id}", itemId.toString());

      // Appeler le service Web pour effectuer la requête DELETE
      var response = await WebService.delete(url);

      if (response.statusCode == 200 || response.statusCode == 204) {
        // Get.snakbar("Success", "Item successfully deleted");
        return true;
      } else {
        // Get.snakbar("Error", "Failed to delete item");
        return false;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while deleting the item: ${e.toString()}");
      return false;
    }
  }
 

  static Future<bool> likeUnlikeItem(int itemId, bool isLiked) async {
    try {
      String action = isLiked ? 'unlike' : 'like';
      var response = await WebService.post('${AvailableServices.items}/like-unlike/${itemId}');



    print('Response status code: ${response.statusCode}');
    print('Response body: ${response.body}');
      if (response.statusCode == 200) {
        var responseBody = jsonDecode(response.body);
        if (isLiked && responseBody['message'] == 'Item unliked') {
          // Get.snakbar("Success", "Item successfully unliked");
          return true;
        } else if (!isLiked && responseBody['message'] == 'Item liked') {
          // Get.snakbar("Success", "Item successfully liked");
          return true;
        } else {
          // Get.snakbar("Error", "Failed to ${action} item");
          return false;
        }
      } else {
        // Get.snakbar("Error", "Failed to ${action} item");
        return false;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while trying  to the item");
      return false;
    }
  }


  static Future<Item?> getItemById(int id) async {
    try {
      var response = await WebService.get('${AvailableServices.items}/$id');
      



      if (response.statusCode == 200) {
        var data = jsonDecode(response.body);



      print('response get item ${data['matterport_link']}');


        log('data get item ${data['store_id']}');
        
        Item item = Item(
          storeId: data['store_id'] is String ? int.parse(data['store_id']) : data['store_id'],
          id: data['id'] is String ? int.parse(data['id']) : data['id'],
          title: data['title']?.toString() ?? '',
          titleAr: data['title_ar']?.toString(),
          descriptionAr: data['description_ar']?.toString(),
          description: data['description']?.toString() ?? '',
          price: data['price'] is String ? double.parse(data['price']) : (data['price'] ?? 0.0).toDouble(),
          condition: data['condition']?.toString() ?? '',
          quantity: data['quantity'] is String ? int.parse(data['quantity']) : (data['quantity'] ?? 1),
          brandId: data['brand_id'] is String ? int.parse(data['brand_id']) : data['brand_id'],
          brandName: data['brand']?['name']?.toString(),
          categoryId: data['category_id'] is String ? int.parse(data['category_id']) : data['category_id'],
          categoryName: data['category']?['title_en']?.toString() ?? data['category']?['title_ar']?.toString() ?? data['category']?['title_fr']?.toString(),
          userName: "${data['user']?['firstname']?.toString() ?? ''} ${data['user']?['lastname']?.toString() ?? ''}".trim(),
          isLiked: data['is_liked'] ?? false,
          createdAt: data['created_at'] != null ? DateTime.parse(data['created_at']) : null,
          isPromoted: data['is_promoted'] ?? false,
          promotionPercentage: data['promotion_percentage'],
          hasPromotion: data['has_promotion'] ?? false,
          images: [],
          categoryItemDetails: data['category_details'] != null
              ? (data['category_details'] as List<dynamic>)
                  .map((detail) => CategoryItemDetail.fromJson(detail))
                  .toList()
              : [],

            matterportLink: data['matterport_link'] ?? '',
        );
    


        // log item  categoryItemDetails 
        log(' details ${item.categoryItemDetails.length}');

   
        // Parse transformed_images if available
        if (data['transformed_images'] != null) {
          List<dynamic> imageData = data['transformed_images'];
          
          // Log raw image data
          log('Raw image data from API: ${imageData}');
          
          if (imageData.isNotEmpty && imageData[0]['order'] != null) {
            log('Before sorting: ${imageData.map((img) => 'ID:${img['id']} Order:${img['order']}').join(', ')}');
            imageData.sort((a, b) => (a['order'] ?? 0).compareTo(b['order'] ?? 0));
            log('After sorting: ${imageData.map((img) => 'ID:${img['id']} Order:${img['order']}').join(', ')}');
          } else {
            log('No order field found in image data or empty image list');
          }
          
          // Log the final image URLs in order
          item.images = imageData.map((img) => img['url'].toString()).toList();
          log('Final image URLs in order: ${item.images}');
          
          // Store the full image objects in the controller
          final itemController = Get.find<ItemController>();



          log('images_data  ${imageData}');
          List<ItemImage> images = imageData.map((img) => ItemImage(
            id: img['id'],
            url: img['url'],
            order: img['order'] ?? 0,
          )).toList();
        

          // log images 
          log('images  ${images.map((img) => 'id ${img.id} url :${img.url} order:${img.order}')}');



          // Use the update method to ensure proper logging
          itemController.updateExistingImages(images);
          
          // Log the image data for debugging
          log('Image data from API: ${imageData.map((img) => '${img['id']}:${img['url']}:${img['order']}').join(', ')}');
        }
        
        return item;
      }
      return null;
    } catch (e) {
      log('Error getting item by ID: $e');
      return null;
    }
  }

  
}








class PaymentProvider {
  final int id;
  final String name;
  final String providerCode;
  final String? logoUrl;
  final bool isActive;

  PaymentProvider({
    required this.id,
    required this.name,
    required this.providerCode,
    this.logoUrl,
    required this.isActive,
  });

  factory PaymentProvider.fromJson(Map<String, dynamic> json) {
    return PaymentProvider(
      id: json['id'],
      name: json['name'],
      providerCode: json['provider_code'],
      logoUrl: json['logo'],
      isActive: json['is_active'],
    );
  }
}

class PaymentProviderService {
  static Future<List<PaymentProvider>> fetchProviders() async {
    try {
      final response = await WebService.get(AvailableServices.activePaymentProviders);
      


      print('response   ${response.statusCode}');
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);



      print('responseData   ${responseData}');

        if (responseData['success'] == true && responseData['data'] != null) {
          return (responseData['data'] as List)
              .map((json) => PaymentProvider.fromJson(json))
              .toList();
        }
      }
      throw Exception('Failed to load payment providers');
    } catch (e) {
      throw Exception('Error: $e');
    }
  }

}
