import 'dart:convert';
import 'dart:developer';

import 'package:boutigak/data/models/social_media_link.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:get/get.dart';



class SocialMediaService {
  /// Récupérer tous les liens sociaux d’un store
  static Future<List<SocialMediaLink>?> fetchLinks(int storeId) async {
    try {
      var response = await WebService.get(
        AvailableServices.getLinks.replaceFirst("{storeId}", "$storeId"),
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> responseBody = jsonDecode(response.body);
        List<dynamic> data = responseBody['data'];

        log("Fetched social links: $data");

        List<SocialMediaLink> links =
            data.map((e) => SocialMediaLink.fromJson(e)).toList();
        return links;
      } else {
        log("Failed to load social links: ${response.body}");
        return null;
      }
    } catch (e) {
      log("Error fetching social links: $e");
      return null;
    }
  }

  /// Créer un nouveau lien social pour un store
  static Future<bool> addLink(int storeId, Map<String, dynamic> body) async {
    try {
      var response = await WebService.post(
        AvailableServices.postLinks.replaceFirst("{storeId}", "$storeId"),
        body: body,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        log("Failed to add social link: ${response.body}");
        return false;
      }
    } catch (e) {
      log("Error adding social link: $e");
      return false;
    }
  }

  /// Modifier un lien social existant
  static Future<bool> updateLink(
      int storeId, int linkId, Map<String, dynamic> body) async {
    try {
      var response = await WebService.put(
        AvailableServices.putLink
            .replaceFirst("{storeId}", "$storeId")
            .replaceFirst("{linkId}", "$linkId"),
        body: body,
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        log("Failed to update social link: ${response.body}");
        return false;
      }
    } catch (e) {
      log("Error updating social link: $e");
      return false;
    }
  }

  /// Supprimer un lien social
  static Future<bool> deleteLink(int storeId, int linkId) async {
    try {
      var response = await WebService.delete(
        AvailableServices.deleteLink
            .replaceFirst("{storeId}", "$storeId")
            .replaceFirst("{linkId}", "$linkId"),
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        log("Failed to delete social link: ${response.body}");
        return false;
      }
    } catch (e) {
      log("Error deleting social link: $e");
      return false;
    }
  }
}
