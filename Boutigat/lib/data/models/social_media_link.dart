class SocialMediaLink {
  final int id;
  final int storeId;
  final String platform;
  final String link;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  SocialMediaLink({
    required this.id,
    required this.storeId,
    required this.platform,
    required this.link,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SocialMediaLink.fromJson(Map<String, dynamic> json) {
    return SocialMediaLink(
      id: json['id'],
      storeId: json['store_id'],
      platform: json['platform'],
      link: json['link'],
      isActive: json['is_active'] is bool
          ? json['is_active']
          : (json['is_active'] == 1 || json['is_active'] == 'true'),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'store_id': storeId,
      'platform': platform,
      'link': link,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
