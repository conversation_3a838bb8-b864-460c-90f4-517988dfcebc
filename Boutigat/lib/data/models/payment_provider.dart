class PaymentProvider {
  final int id;
  final String name;
  final String providerCode;
  final bool hasApi;
  final bool isActive;
  final String? logoUrl;
 

  PaymentProvider({
    required this.id,
    required this.name,
    required this.providerCode,
    required this.hasApi,
    required this.isActive,

    this.logoUrl,
   
  });

  factory PaymentProvider.fromJson(Map<String, dynamic> json) {
    return PaymentProvider(
      id: json['id'],
      name: json['name'],
      providerCode: json['provider_code'],
      hasApi: json['has_api'],
      isActive: json['is_active'],
      logoUrl: 'https://www.boutigak.com/${json['logo']}'

    );
  }

}
class StorePaymentProvider {
  final int? id;
 final int? providerId;
 final String providerName;
 final String? providerLogo;
 final String? paymentCode;
  final String? phoneNumber;

  StorePaymentProvider({
    this.id,
    this.providerId,
   required this.providerName,
    this.providerLogo,
    this.paymentCode,
     this.phoneNumber,
  });

factory StorePaymentProvider.fromJson(Map<String, dynamic> json) {
  return StorePaymentProvider(
   id: json['id'] != null
       ? (json['id'] is String ? int.parse(json['id']) : json['id'] as int)
      : 0,
    providerId: json['provider_id'] != null
        ? (json['provider_id'] is String ? int.parse(json['provider_id']) : json['provider_id'] as int)
        : 0, 
    providerName: json['providerName'] ?? '', 
    providerLogo:  'https://www.boutigak.com/${json['logo']}', 
    paymentCode: json['payment_code'] ,
    phoneNumber: json['phone_number'] ,
  );  
}


}
