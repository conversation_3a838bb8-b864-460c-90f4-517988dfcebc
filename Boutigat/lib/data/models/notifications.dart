class NotificationItem {
  final int id;
  final int userId;
  final String title;
  final String message;
  final String type;
  bool isRead;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int? storeId;
  final String? storeName;
  final List<String> storeImageUrls;
  final String? image;

  NotificationItem({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.type,
    required this.isRead,
    required this.createdAt,
    required this.updatedAt,
    this.storeId,
    this.storeName,
    this.image,
    required this.storeImageUrls,
  });

  
  factory NotificationItem.fromJson(Map<String, dynamic> json) {
    
   final store = json['store_data'];
    final List<String> images = store != null && store['image_url'] != null
        ? [store['image_url'].toString()]
        : [];
    return NotificationItem(
      id: json['id'],
      userId: json['user_id'],
      title: json['title'],
      message: json['message'],
      type: json['type'],
      isRead: json['is_read'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      storeId: store != null ? store['id'] : null,
      storeName: store != null ? store['name'] : null,
      storeImageUrls: images,
      image : json['image'] != null ? json['image']['media']['url'] : null ,
    );
  }
}

class NotificationResponse {
  final int currentPage;
  final List<NotificationItem> notifications;
  final String? nextPageUrl;
  final String? prevPageUrl;
  final int total;

  NotificationResponse({
    required this.currentPage,
    required this.notifications,
    this.nextPageUrl,
    this.prevPageUrl,
    required this.total,
  });


  factory NotificationResponse.fromJson(Map<String, dynamic> json) {
    return NotificationResponse(
      currentPage: json['current_page'],
      notifications: (json['data'] as List<dynamic>)
          .map((item) => NotificationItem.fromJson(item))
          .toList(),
      nextPageUrl: json['next_page_url'],
      prevPageUrl: json['prev_page_url'],
      total: json['total'],
    );
  }
}