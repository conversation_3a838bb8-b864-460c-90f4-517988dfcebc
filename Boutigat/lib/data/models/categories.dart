import 'package:get/get.dart';

class Category {
  final int? id;
  final String? titleEn;
  final String? titleAr;
  final String? titleFr;
  final int? parentId;
  final List<Category> children;
  final List<CategoryDetails> details;

  Category({
    this.id,
    this.titleEn,
    this.titleAr,
    this.titleFr,
    this.parentId,
    this.children = const [],
    this.details = const [],
  });

  factory Category.fromJson(dynamic json) {
    if (json == null) {
      return Category();
    }

    // If backend only sends an int (category_id)
    if (json is int) {
      return Category(id: json);
    }

    // If backend only sends a string (category name)
    if (json is String) {
      return Category(titleEn: json, titleFr: json, titleAr: json);
    }

    // If backend sends a full/partial object
    if (json is Map<String, dynamic>) {
      return Category(
        id: json['id'],
        titleEn: json['title_en'] ?? '',
        titleAr: json['title_ar'] ?? '',
        titleFr: json['title_fr'] ?? '',
        parentId: json['parent_id'],
        children: (json['children'] as List<dynamic>?)
                ?.map((i) => Category.fromJson(i))
                .toList() ??
            [],
        details: (json['details'] as List<dynamic>?)
                ?.map((i) => CategoryDetails.fromJson(i))
                .toList() ??
            [],
      );
    }

    // Fallback in case of unknown type
    return Category();
  }

  String getTitle() {
    String languageCode = Get.locale?.languageCode ?? 'en';

    switch (languageCode) {
      case 'ar':
        return titleAr ?? titleEn ?? '';
      case 'fr':
        return titleFr ?? titleEn ?? '';
      default:
        return titleEn ?? '';
    }
  }
}


class CategoryDetails {
  final int id;
  final String labelEn;
  final String labelAr;
  final String labelFr;
  final int categoryId;
  final DateTime createdDate;
  final DateTime createdAt;
  final DateTime updatedAt;
   String? value;

  CategoryDetails({
    required this.id,
    required this.labelEn,
    required this.labelAr,
    required this.labelFr,
    required this.categoryId,
    required this.createdDate,
    required this.createdAt,
    required this.updatedAt,
    this.value,
  });

  factory CategoryDetails.fromJson(Map<String, dynamic> json) {
    return CategoryDetails(
      id: json['id'],
      labelEn: json['label_en'],
      labelAr: json['label_ar'],
      labelFr: json['label_fr'],
      categoryId: json['category_id'],
      createdDate: json['created_date'] != null ? DateTime.parse(json['created_date']) : DateTime.now(),
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : DateTime.now(),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : DateTime.now(),
    );
  }

   String getlabel() {
    String languageCode = Get.locale?.languageCode ?? 'en'; 


    print('languageCode $languageCode');
    print('labelAr $labelAr');
    print('labelFr $labelFr');
    print('labelEn $labelEn');
    switch (languageCode) {
      case 'ar':
        return labelAr;
      case 'fr':
        return labelFr;
      default:
        return labelEn;
    }
  }
}