import 'dart:developer';

import 'package:boutigak/data/models/social_media_link.dart';
import 'package:intl/intl.dart';
class Store {
  int? id;
  final String name;
  final String description;
  final int? typeId;
  final String openingTime;
  final String closingTime;
  final List<String> images; 
  final String? typeName;
  int followersCount; 
  int itemsCount;
  bool isFollowed; // Ajout de l'attribut isFollowed
  bool? isOpen;
  final double? latitude;
  final double? longitude;
  final List<SocialMediaLink> socialMediaLinks;
  bool isNotificationsEnabled;

  Store({
    this.id,
    required this.name,
    required this.description,
    required this.typeId,
     this.typeName,
    required this.openingTime,
    required this.closingTime,
    required this.images,
    this.followersCount = 0, 
    this.itemsCount = 0,
    this.isFollowed = false, // Valeur par défaut false
    this.isOpen,
    this.latitude,
    this.longitude,
    this.socialMediaLinks = const [],
    this.isNotificationsEnabled = false,
  });

  // Factory pour créer un Store à partir d'un JSON
  factory Store.fromJson(Map<String, dynamic> json) {
    return Store(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      typeId: json['type_id'] ?? null,
      typeName: json['type_name'],
      openingTime: json['opening_time'],
      closingTime: json['closing_time'],
      images: (json['images'] as List<dynamic>?)
          ?.map((image) {
            // Handle both direct URL and media object structure
            if (image['url'] != null) {
              return image['url'] as String;
            } else if (image['media'] != null && image['media']['url'] != null) {
              return image['media']['url'] as String;
            }
            return ''; // Return empty string for null URLs
          })
          .where((url) => url.isNotEmpty) // Filter out empty URLs
          .toList() ?? [],
      followersCount: json['followers_count'] ?? 0, 
      itemsCount: json['items_count'] ?? 0, 
      isFollowed: json['is_followed'] ?? false, 
      isOpen: json['is_open'] ?? false,
      latitude: json['latitude'] != null ? (json['latitude'] as num).toDouble() : null,
      longitude: json['longitude'] != null ? (json['longitude'] as num).toDouble() : null,
       socialMediaLinks: (json['social_media_links'] as List<dynamic>?)
              ?.map((e) => SocialMediaLink.fromJson(e))
              .toList() ??
          [],
      isNotificationsEnabled: json['is_notifications_enabled']  ?? false,
      // type_name  
    );
  }


  // Méthode pour convertir un Store en JSON
Map<String, dynamic> toJson() {
  return {
    'id': id?.toString(),
    'name': name,
    'description': description,
    'type_id':   typeId?.toString(),
    'opening_time': openingTime,
    'closing_time': closingTime,
    'images': images.map((image) => {'image': image}).toList(),
    'is_open': isOpen,
    'latitude': latitude,
    'longitude': longitude,
  };
}

bool isOpenNow() {
  try {

    log('is open ${isOpen} , ${id} , name : ${name}');
    if (isOpen != null) {
      return isOpen!;
    }

    final now = DateTime.now();
    final format = DateFormat.Hm(); // HH:mm



    log('here .');
    final opening = format.parse(openingTime);
    final closing = format.parse(closingTime);

    final todayOpening = DateTime(now.year, now.month, now.day, opening.hour, opening.minute);

   log('openiing ${opening}');
   log('closing ${closing}');


    DateTime todayClosing = DateTime(now.year, now.month, now.day, closing.hour, closing.minute);

    // Si le magasin ferme après minuit
    if (closing.hour < opening.hour) {
      todayClosing = todayClosing.add(const Duration(days: 1));
    }

    return now.isAfter(todayOpening) && now.isBefore(todayClosing);
  } catch (e) {
    return false;
  }
}



}

class StoreType {
  final int id;
  final String name;

  StoreType({
    required this.id,
    required this.name,
  });

  // Factory pour créer un StoreType à partir d'un JSON
  factory StoreType.fromJson(Map<String, dynamic> json) {
    return StoreType(
      id: json['id'],
      name: json['name'],
    );
  }
}
