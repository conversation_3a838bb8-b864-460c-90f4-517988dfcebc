import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:boutigak/constants/app_colors.dart';

/// Widget shown when the current user has blocked another user
class UserBlockedMessage extends StatelessWidget {
  final String interlocutorName;
  
  const UserBlockedMessage({
    Key? key,
    required this.interlocutorName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ConversationController conversationController = Get.find();
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        border: Border.all(color: Colors.orange.shade200),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.block,
            size: 48,
            color: Colors.orange.shade600,
          ),
          const SizedBox(height: 12),
          Text(
            'You blocked $interlocutorName',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.orange.shade800,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'You cannot send or receive messages from this user.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.orange.shade700,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => conversationController.toggleBlockUser(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Unblock User',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget shown when the current user has been blocked by another user
class BlockedByUserMessage extends StatelessWidget {
  final String interlocutorName;
  
  const BlockedByUserMessage({
    Key? key,
    required this.interlocutorName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        border: Border.all(color: Colors.red.shade200),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.block,
            size: 48,
            color: Colors.red.shade600,
          ),
          const SizedBox(height: 12),
          Text(
            'You have been blocked',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red.shade800,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            '$interlocutorName has blocked you. You cannot send messages to this user.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.red.shade700,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Widget that replaces the message input when users are blocked
class BlockedMessageInput extends StatelessWidget {
  final bool isUserBlocked;
  final bool isBlockedByUser;
  final String interlocutorName;
  
  const BlockedMessageInput({
    Key? key,
    required this.isUserBlocked,
    required this.isBlockedByUser,
    required this.interlocutorName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String message;
    Color backgroundColor;
    Color textColor;
    
    if (isUserBlocked) {
      message = 'You have blocked $interlocutorName';
      backgroundColor = Colors.orange.shade100;
      textColor = Colors.orange.shade800;
    } else {
      message = 'You have been blocked by $interlocutorName';
      backgroundColor = Colors.red.shade100;
      textColor = Colors.red.shade800;
    }
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border(
          top: BorderSide(
            color: Colors.grey[300]!,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.block,
            color: textColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (isUserBlocked) ...[
            const SizedBox(width: 12),
            TextButton(
              onPressed: () => Get.find<ConversationController>().toggleBlockUser(),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              ),
              child: const Text(
                'Unblock',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Widget that shows blocking status in the conversation area
class ConversationBlockingStatus extends StatelessWidget {
  final bool isUserBlocked;
  final bool isBlockedByUser;
  final String interlocutorName;
  
  const ConversationBlockingStatus({
    Key? key,
    required this.isUserBlocked,
    required this.isBlockedByUser,
    required this.interlocutorName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isUserBlocked) {
      return UserBlockedMessage(interlocutorName: interlocutorName);
    } else if (isBlockedByUser) {
      return BlockedByUserMessage(interlocutorName: interlocutorName);
    }
    
    return const SizedBox.shrink();
  }
}
