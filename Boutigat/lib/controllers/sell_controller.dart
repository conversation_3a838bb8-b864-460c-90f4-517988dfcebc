import 'dart:developer';

import 'package:get/get.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'dart:io';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:dio/dio.dart' as dio;

class PhotoActionsController extends GetxController {
  final Rx<Color> photoButtonColor = Rx<Color>(AppColors.onBackground);
  final Rx<Color> uploadButtonColor = Rx<Color>(AppColors.onBackground);
  final RxList<String> photos = <String>[].obs;
  final RxBool isActionInProgress = false.obs;
  final ImagePicker picker = ImagePicker();
  final ItemController item;
  final RxBool lastActionWasUpload = false.obs;
  RxInt currentPage = 0.obs;
  PageController pageController = PageController();
  late Worker _photosWorker;

  PhotoActionsController(this.item);

  void takePhoto() async {
    if (isActionInProgress.value) return;
    isActionInProgress.value = true;
    photoButtonColor.value = AppColors.primary;
    lastActionWasUpload.value = false;

    try {
      final pickedFile = await picker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        addPhoto(pickedFile.path);
   
      } else {
    
      }
    } catch (e) {
      print('Error taking photo: $e');
  
    } finally {
      isActionInProgress.value = false;
      photoButtonColor.value = AppColors.onBackground;
    }
  }



 

void uploadPhoto() async {
  if (isActionInProgress.value) return;
  isActionInProgress.value = true;
  uploadButtonColor.value = AppColors.primary;
  lastActionWasUpload.value = true;

  try {
    final List<XFile>? pickedFiles = await picker.pickMultiImage();
    
    if (pickedFiles != null && pickedFiles.isNotEmpty) {
      for (final file in pickedFiles) {
        if (photos.length >= 10) {
         
          break;
        }
        addPhoto(file.path);
       
      }
    } else {
     
    }
  } catch (e) {
 
  } finally {
    isActionInProgress.value = false;
    uploadButtonColor.value = AppColors.onBackground;
  }
}

 void addPhoto(String path) {
    final bool isNetwork = path.startsWith('http://') || path.startsWith('https://');
    final bool existsLocally = File(path).existsSync();
    if (isNetwork || existsLocally) {
      if (!photos.contains(path)) {
        photos.add(path);
      }
      if (!item.photos.contains(path)) {
        item.addPhoto(path);
      }
      log('[Controller] addPhoto: added path=$path (network=$isNetwork exists=$existsLocally)');
    } else {
      log('[Controller] addPhoto: path not added (not network and file missing) -> $path');
    }
  }



  void removePhoto(String path) {
    photos.remove(path);
    
    log('path: $path');
    if (path.startsWith('http://') || path.startsWith('https://')) {
      // Extract the image ID from the URL or find the corresponding image
      final itemController = item;
      
      log('existingImages: ${itemController.existingImages.map((img) => '${img.id}: ${img.url}').join(', ')}');
      
      final imageId = itemController.existingImages
          .where((img) => path.contains(img.url))
          .map((img) => img.id)
          .firstOrNull;

      log('imageId: $imageId');
      
      if (imageId != null) {
        // Mark this image for deletion
        itemController.markImageForDeletion(imageId);
        print("Marked image ID $imageId for deletion");
      }
    }
    
    item.removePhoto(path);
  }



 
  @override
  void onInit() {
    super.onInit();
    // Initialize from ItemController if available (useful in edit mode with network images)
    if (photos.isEmpty && item.photos.isNotEmpty) {
      photos.assignAll(item.photos);
      log('[Controller] onInit: synced initial photos from ItemController (${photos.length})');
    }
    // Log any change to the photos list
    _photosWorker = ever<List<String>>(photos, (list) {
      log('[Controller] photos changed -> len=${list.length} list=$list');
    });
    pageController.addListener(() {
      int page = pageController.page!.round();
      if (currentPage.value != page) {
        currentPage.value = page;
      }
    });
  }

  @override
  void onClose() {
    pageController.dispose();
    _photosWorker.dispose();
    super.onClose();
  }



  void setCurrentPage(int index) {
    currentPage.value = index;
    pageController.jumpToPage(index);
  }





  void reorderPhotos(int oldIndex, int newIndex) {
    final int len = photos.length;
    log('[Controller] reorderPhotos called -> oldIndex=$oldIndex, newIndex=$newIndex, len=$len');

    if (len == 0) {
      log('[Controller] reorderPhotos aborted: empty list');
      return;
    }

    if (oldIndex < 0 || oldIndex >= len) {
      log('[Controller] reorderPhotos aborted: oldIndex out of range');
      return;
    }

    // Clamp newIndex into [0, len]
    if (newIndex < 0) newIndex = 0;
    if (newIndex > len) newIndex = len;

    // Flutter ReorderableListView semantics: when moving down,
    // the removal shifts indices, so decrement newIndex by 1
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }

    log('[Controller] adjusted indices -> oldIndex=$oldIndex, newIndex=$newIndex');
    final before = List<String>.from(photos);

    try {
      // Immutable-style reorder to avoid side effects while observers rebuild
      final List<String> next = List<String>.from(photos);
      final String movedPhoto = next.removeAt(oldIndex);
      next.insert(newIndex, movedPhoto);
      photos.assignAll(next);
      log('[Controller] before=${before}');
      log('[Controller] after =${photos}');

      if (photos.isEmpty) {
        // Unexpected; restore previous state
        photos.assignAll(before);
        log('[Controller][WARN] photos unexpectedly empty after reorder. Restored previous state.');
      }

      // Update only this controller; avoid touching ItemController here
      update();
      log('[Controller] reorderPhotos completed. controller.photos.len=${photos.length}');
    } catch (e, st) {
      // Restore state on error
      photos.assignAll(before);
      log('[Controller][ERROR] reorder failed: $e');
      log('$st');
      update();
    }
  }


  // When uploading to the server, ensure we send the images in the correct order
  Future<List<dio.MultipartFile>> prepareImageFiles() async {
    List<dio.MultipartFile> imageFiles = [];
    
    // Process images in the order they appear in the photos list
    for (int i = 0; i < photos.length; i++) {
      final String photoPath = photos[i];
      if (photoPath.startsWith('http')) {
        // Skip network images as they're already on the server
        continue;
      }
      
      File file = File(photoPath);
      if (await file.exists()) {
        String fileName = photoPath.split('/').last;
        imageFiles.add(await dio.MultipartFile.fromFile(
          photoPath,
          filename: fileName,
        ));
      }
    }
    
    return imageFiles;
  }

 void clearPhotoActionData() {
 

  photos.clear();


  update(); 
}



}
