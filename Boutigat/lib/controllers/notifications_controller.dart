import 'package:boutigak/data/models/notifications.dart';
import 'package:boutigak/data/services/notifications_service.dart';
import 'package:get/get.dart';

class NotificationController extends GetxController {
  // Observable list to hold the notifications
  RxList<NotificationItem> notifications = <NotificationItem>[].obs;
  RxBool isLoading = false.obs;
  RxBool isError = false.obs;
  RxInt currentPage = 1.obs;
  RxInt totalNotifications = 0.obs;

  // Method to fetch notifications
  Future<void> fetchNotifications({int page = 1}) async {
    try {
      if (page == 1) {
        isLoading.value = true; // Start loading for first page
      }
      
      NotificationResponse? response = await NotificationService.fetchNotifications(page: page);



      
      if (response != null) {
        currentPage.value = response.currentPage;
        totalNotifications.value = response.total;

        // Update the list with fetched notifications
        if (page == 1) {
          notifications.assignAll(response.notifications);
        } else {
          notifications.addAll(response.notifications);
        }
        isError.value = false;
      }
    } catch (e) {
      print('Error fetching notifications: ${e}');
      isError.value = true;
    } finally {
      isLoading.value = false; // Stop loading
    }
  }

  // Method to load the next page
  Future<void> loadMoreNotifications() async {
    if (!isLoading.value && currentPage.value * 15 < totalNotifications.value) {
      isLoading.value = true;
      await fetchNotifications(page: currentPage.value + 1);
    }
  }

  // Method to mark a notification as read
  Future<void> markAsRead(int notificationId) async {
    try {
      await NotificationService.markAsRead(notificationId);
      
      // Update the local notification to show as read
      int index = notifications.indexWhere((notification) => notification.id == notificationId);
      if (index != -1) {
        NotificationItem notification = notifications[index];
        notification.isRead = true;
        notifications[index] = notification;
        notifications.refresh();
      }
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }
}
