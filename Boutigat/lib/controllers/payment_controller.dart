




import 'package:boutigak/data/models/payment_provider.dart';
import 'package:boutigak/data/services/payment_provider_service.dart';
import 'package:get/get.dart';

class PaymentController extends GetxController {
  // Observable list to hold the payment providers
  RxList<PaymentProvider> paymentProviders = <PaymentProvider>[].obs;
  RxList<StorePaymentProvider> storePaymentProviders = <StorePaymentProvider>[].obs;
  var selectedProvider = ''.obs;

  // Loading state to indicate if data is being fetched
  RxBool isLoading = false.obs;
  RxBool isError = false.obs;

  // Method to fetch payment providers
  Future<void> fetchPaymentProviders() async {
    try {
      isLoading.value = true; // Start loading
      List<PaymentProvider>? providers = await PaymentService.fetchPaymentProviders();

      if (providers != null) {
        
        paymentProviders.assignAll(providers); // Update the list with fetched data
      } else {
      //  // Get.snakbar("Error", "Failed to load payment providers");
      }
    } catch (e) {
    //  // Get.snakbar("Error", "An error occurred while fetching payment providers: ${e}");
      print('Response body: ${e}');
    } finally {
      isLoading.value = false; // Stop loading
    }
  }
   Future<void> fetchStorePaymentProviders() async {
    try {
      isLoading.value = true; // Start loading
      List<StorePaymentProvider>? providers = await PaymentService.fetchStorePaymentProviders();

      if (providers != null) {
        storePaymentProviders.assignAll(providers); // Update the list with fetched data
      } else {
     //   // Get.snakbar("Error", "Failed to load store payment providers");
      }
    } catch (e) {
    //  // Get.snakbar("Error", "An error occurred while fetching store payment providers: ${e}");
      print('Response body: ${e}');
    } finally {
      isLoading.value = false; // Stop loading
    }
  }
  // Method to add a new payment provider
  Future<void> addPaymentProvider(int providerId, String paymentCode, String phoneNumber) async {
    try {


      print('in add payment rpovider ');

      isLoading.value = true; // Start loading

      // Create the body of the request
      Map<String, dynamic> requestBody = {
        "provider_id": providerId,
        "payment_code": paymentCode,
        "phone_number": phoneNumber,
      };

      // Send the request to add a new payment provider
      bool isAdded = await PaymentService.addPaymentProvider(requestBody);

      if (isAdded) {
        // If the addition was successful, refetch the providers
        await fetchPaymentProviders();
     //   // Get.snakbar("Success", "Payment provider added successfully");
      } else {
     //   // Get.snakbar("Error", "Failed to add payment provider");
      }
    } catch (e) {
   //   // Get.snakbar("Error", "An error occurred while adding payment provider: ${e}");
  //    print('Response body: ${e}');
    } finally {
      isLoading.value = false; // Stop loading
    }
  }
  Future<void> fetchStoreProvidersById(int storeId) async {
    try {
      isLoading.value = true;
      List<StorePaymentProvider>? providers = await PaymentService.fetchStoreProvidersById(storeId);


      if (providers != null) {
        storePaymentProviders.assignAll(providers);
      }
    } catch (e) {
      print('Error fetching store providers: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Method to update a payment provider
  Future<bool> updatePaymentProvider(int id, String paymentCode, String phoneNumber) async {
    try {
      isLoading.value = true;

      Map<String, dynamic> requestBody = {
        "payment_code": paymentCode,
        "phone_number": phoneNumber,
      };

      bool isUpdated = await PaymentService.updatePaymentProvider(id, requestBody);

      if (isUpdated) {
        await fetchStorePaymentProviders();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('Error updating payment provider: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Method to delete a payment provider
  Future<bool> deletePaymentProvider(int id) async {
    try {
      isLoading.value = true;

      bool isDeleted = await PaymentService.deletePaymentProvider(id);

      if (isDeleted) {
        await fetchStorePaymentProviders();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('Error deleting payment provider: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}
