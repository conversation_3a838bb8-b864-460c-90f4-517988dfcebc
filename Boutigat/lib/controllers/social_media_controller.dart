import 'dart:developer';
import 'package:boutigak/data/models/social_media_link.dart';
import 'package:boutigak/data/services/social_media_link_service.dart';
import 'package:get/get.dart';


class SocialMediaController extends GetxController {
  final int storeId;
  SocialMediaController(this.storeId);

  RxList<SocialMediaLink> links = <SocialMediaLink>[].obs;
  RxBool isLoading = false.obs;
  RxBool isError = false.obs;

  // Inputs utilisateur (numéro/pseudos)
  RxString whatsappPhone = ''.obs;     // ex: 22244556677 (sans + ni espaces)
  RxString tiktokUser = ''.obs;        // ex: tonpseudo
  RxString facebookUser = ''.obs;      // ex: TonNomDePage
  RxString snapchatUser = ''.obs;      // ex: tonpseudo

  @override
  void onInit() {
    super.onInit();
    fetchLinks();
  }

  Future<void> fetchLinks() async {
    try {
      isLoading.value = true;
      final res = await SocialMediaService.fetchLinks(storeId);
      if (res != null) {
        links.assignAll(res);
        _prefillInputsFromLinks();
        isError.value = false;
      } else {
        isError.value = true;
      }
    } catch (e) {
      log('Error fetching social links: $e');
      isError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  // -------- Helpers parsing/compose --------
  SocialMediaLink? _linkFor(String platform) {
    return links.firstWhereOrNull((e) => e.platform.toLowerCase() == platform);
  }

  void _prefillInputsFromLinks() {
    final w = _linkFor('whatsapp')?.link;
    final t = _linkFor('tiktok')?.link;
    final f = _linkFor('facebook')?.link;
    final s = _linkFor('snapchat')?.link;

    if (w != null && w.isNotEmpty) {
      // https://wa.me/22244556677 -> 22244556677
      final digits = w.replaceAll(RegExp(r'[^0-9]'), '');
      whatsappPhone.value = digits;
    }
    if (t != null && t.isNotEmpty) {
      // https://www.tiktok.com/@tonpseudo -> tonpseudo
      final m = RegExp(r'tiktok\.com/@([^/?#]+)').firstMatch(t);
      tiktokUser.value = m != null ? m.group(1)! : '';
    }
    if (f != null && f.isNotEmpty) {
      // https://www.facebook.com/TonNom -> TonNom
      final m = RegExp(r'facebook\.com/([^/?#]+)').firstMatch(f);
      facebookUser.value = m != null ? m.group(1)! : '';
    }
    if (s != null && s.isNotEmpty) {
      // https://www.snapchat.com/add/tonpseudo -> tonpseudo
      final m = RegExp(r'snapchat\.com/add/([^/?#]+)').firstMatch(s);
      snapchatUser.value = m != null ? m.group(1)! : '';
    }
  }

  String composeWhatsAppUrl(String phoneDigits) =>
      'https://wa.me/${_onlyDigits(phoneDigits)}';
  String composeTikTokUrl(String user) =>
      'https://www.tiktok.com/@${user.trim()}';
  String composeFacebookUrl(String user) =>
      'https://www.facebook.com/${user.trim()}';
  String composeSnapchatUrl(String user) =>
      'https://www.snapchat.com/add/${user.trim()}';

  String _onlyDigits(String s) => s.replaceAll(RegExp(r'[^0-9]'), '');

  // -------- Create/Update par plateforme (is_active toujours true) --------
  Future<bool> _upsertPlatform({
    required String platform,
    required String url,
  }) async {
    final existing = _linkFor(platform);
    if (existing == null) {
      return await SocialMediaService.addLink(storeId, {
        "platform": platform,
        "link": url,
        "is_active": true, // toujours actif côté API
      });
    } else {
      return await SocialMediaService.updateLink(storeId, existing.id, {
        "platform": platform,
        "link": url,
        "is_active": true,
      });
    }
  }

  /// Sauvegarder toutes les modifs en une fois (upsert 4 plateformes)
  Future<bool> saveAll() async {
    try {
      isLoading.value = true;

      final ops = <Future<bool>>[];

      // WhatsApp (si numéro non vide)
      final w = whatsappPhone.value.trim();
      if (w.isNotEmpty) {
        ops.add(_upsertPlatform(platform: 'whatsapp', url: composeWhatsAppUrl(w)));
      }

      // TikTok
      final t = tiktokUser.value.trim();
      if (t.isNotEmpty) {
        ops.add(_upsertPlatform(platform: 'tiktok', url: composeTikTokUrl(t)));
      }

      // Facebook
      final f = facebookUser.value.trim();
      if (f.isNotEmpty) {
        ops.add(_upsertPlatform(platform: 'facebook', url: composeFacebookUrl(f)));
      }

      // Snapchat
      final s = snapchatUser.value.trim();
      if (s.isNotEmpty) {
        ops.add(_upsertPlatform(platform: 'snapchat', url: composeSnapchatUrl(s)));
      }

      if (ops.isEmpty) {
        return true; // rien à enregistrer mais pas d'erreur
      }

      final results = await Future.wait(ops);
      final ok = results.every((v) => v);
      if (ok) await fetchLinks();
      return ok;
    } catch (e) {
      log('Error saving social links: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Supprimer rapidement une plateforme (par exemple via bouton dans la liste)
  Future<bool> deletePlatform(String platform) async {
    final existing = _linkFor(platform);
    if (existing == null) return true;
    try {
      isLoading.value = true;
      final ok = await SocialMediaService.deleteLink(storeId, existing.id);
      if (ok) await fetchLinks();
      return ok;
    } catch (e) {
      log('Error delete $platform: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }
    // True si la plateforme existe dans la liste actuelle
  bool existsPlatform(String platform) {
    return links.any((e) => e.platform.toLowerCase() == platform.toLowerCase());
  }

  // Optionnel: renvoyer le SocialMediaLink existant
  SocialMediaLink? getPlatformLink(String platform) {
    return links.firstWhereOrNull((e) => e.platform.toLowerCase() == platform.toLowerCase());
  }

}
