


import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/data/services/auth_service.dart';

class ResetPasswordController extends GetxController {
  RxBool phone = false.obs;
  RxBool password = false.obs;
  RxBool passwordConfirm = false.obs;

  final isLoading = false.obs;
  final showErrorMessage = "".obs;

  bool validatePasswords(String password, String passwordConfirm) {
    if (password != passwordConfirm) {
      showErrorMessage.value = "Les mots de passe ne correspondent pas.";
      return false;
    }
    if (password.length < 8 || passwordConfirm.length < 8) {
      showErrorMessage.value = "Le mot de passe doit contenir au moins 8 caractères.";
      return false;
    }
    return true;
  }

  Future<bool> resetPassword(
    String phone,
    String otp,
    String password,
    String passwordConfirm,
  ) async {
    isLoading.value = true;
    showErrorMessage.value = "";

    // Validation locale
    if (!validatePasswords(password, passwordConfirm)) {
      isLoading.value = false;
      return false;
    }

    try {
      final response = await AuthService.resetPassword(
        phone: phone,
        otp: otp,
        password: password,
        passwordConfirmation: passwordConfirm,
      );

      // 🔎 Adapte le test suivant à ta réponse backend
      final success = response != null &&
          (response["success"] == true ||
           response["message"] == "Password reset successfully");

      if (success) {
        // Laisse l'UI gérer la navigation et le snackbar
        return true;
      } else {
        showErrorMessage.value = "Erreur lors de la réinitialisation du mot de passe.";
        return false;
      }
    } catch (e) {
      showErrorMessage.value = "Erreur : ${e.toString()}";
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}


