
import 'package:boutigak/data/models/user.dart';
import 'package:boutigak/data/services/auth_service.dart';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';


class RegisterController extends GetxController {
  var firstName = ''.obs;
  var lastName = ''.obs;
  var nni = ''.obs;
  var phoneNumber = ''.obs;
  var email = ''.obs;
  var password = ''.obs;
  
  var gender = Rxn<String>(); 
  var firstNameError = RxnString();
  var lastNameError = RxnString();
  var nniError = RxnString();
  var phoneNumberError = RxnString();
  var emailError = RxnString();
  var passwordError = RxnString();
  var confirmPasswordError = RxnString();
  var genderError = RxnString(); 
  var isLoading = false.obs;
  var isOtpSent = false.obs;
  var isOtpVerified = false.obs;
  var verificationId = ''.obs;
 
  // Function to validate the form fields
  bool validateForm() {
    // Reset errors
    _resetErrors();

    bool isValid = true;

    // Validations with immediate error updates
    if (firstName.trim().isEmpty) {
      firstNameError.value = "First name is required";
      isValid = false;
    }

    if (lastName.trim().isEmpty) {
      lastNameError.value = "Last name is required";
      isValid = false;
    }
 
    if (phoneNumber.trim().isEmpty) {
      phoneNumberError.value = "Phone number is required";
      isValid = false;
    } else if (!isValidPhoneNumber(phoneNumber.value)) {
      phoneNumberError.value = "Invalid phone number format";
      isValid = false;
    }

   













    if (password.trim().isEmpty) {
      passwordError.value = "Password is required";
      isValid = false;
    }

  
    
    if (gender.value == null || (gender.value != 'Male' && gender.value != 'Female')) {
      genderError.value = "required Gender";
      isValid = false;
    }
  
    
    update();
    return isValid;
  }

  // Helper functions
  void _resetErrors() {
    firstNameError.value = null;
    lastNameError.value = null;
    nniError.value = null;
    phoneNumberError.value = null;
    emailError.value = null;
    genderError.value = null;
    passwordError.value = null;
    
  }

  bool isEmail(String email) {
    final emailRegex = RegExp(r'^[a-zA-Z0-9._]+@[a-zA-Z0-9]+\.[a-zA-Z]+');
    return emailRegex.hasMatch(email);
  }

  bool _isNumeric(String str) {
    return RegExp(r'^-?[0-9]+$').hasMatch(str);
  }

  bool isValidPhoneNumber(String number) {
    final phoneRegex = RegExp(r'^(\+222)?[2-4]\d{7}$');
    return phoneRegex.hasMatch(number);
  }

  void register() async {
    isLoading.value = true;
    await Future.delayed(const Duration(seconds: 2));
    if (validateForm()) {
      // print("Registration successful");
      // await Future.delayed(const Duration(seconds: 3));
      User user = User(id: null, 
      firstName: firstName.value, 
      lastName: lastName.value, 
      invitationcode: null, 
      phoneNumber: phoneNumber.value, 
      gender: gender.value ??'Male', 
      password: password.value);


      bool registered =await AuthService.register(user);
      if(registered)Get.to(() => const LoginPage());
   
      isLoading.value = false;
    } else {
      print("Please correct the errors in the form");
      isLoading.value = false;
    
    
    }



  }



  void _getFCMToken() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    try {
      var  fcmToken = await messaging.getToken();
      print("FCM Token in login : $fcmToken");
        _sendTokenToAPI(fcmToken!);
    } catch (e) {
      print('Error getting FCM token: $e');
    }

  
  }

  // Fonction pour envoyer le token FCM à l'API via AuthService
  void _sendTokenToAPI(String tokenFCM) async {
    var success = await AuthService.sendTokenToAPI(tokenFCM);
    if (success) {
      print("FCM Token envoyé avec succès à l'API register");
    } else {
      print("Erreur lors de l'envoi du FCM Token à l'API register");
    }
  }


}


