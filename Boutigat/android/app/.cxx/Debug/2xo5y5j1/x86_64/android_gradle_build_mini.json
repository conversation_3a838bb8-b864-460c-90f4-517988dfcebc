{"buildFiles": ["/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/development/Boutigat/android/app/.cxx/Debug/2xo5y5j1/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/development/Boutigat/android/app/.cxx/Debug/2xo5y5j1/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}