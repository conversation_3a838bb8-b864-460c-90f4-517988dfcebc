name: boutigak
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  font_awesome_flutter: ^10.7.0
  google_nav_bar: ^5.0.6
  cached_network_image: ^3.3.1
  flutter_image_compress: ^2.4.0
  url_launcher: ^6.1.7
  transparent_image: ^2.0.0
  google_fonts: ^6.1.0
  connectivity_plus: ^6.1.4
  dio: ^5.0.3
  image_picker: ^1.1.2
  path_provider: ^2.1.4
  get: ^4.6.5 
  intl: ^0.20.2
  shimmer: ^3.0.0
  timeago: ^3.0.0
  image: ^4.0.0
  pdf: ^3.10.4
  http: ^1.3.0
  latlong2: ^0.9.1
  provider: ^6.0.0
  google_maps_flutter: ^2.1.0
  webview_flutter: ^4.0.0
  webview_flutter_android: ^4.0.0  
  webview_flutter_wkwebview: ^3.16.0  
  webview_flutter_platform_interface: ^2.10.0 
  #mapbox_gl: ^0.12.0
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  jwt_decoder: ^2.0.1
  shared_preferences: ^2.2.3
  liquid_pull_to_refresh: ^3.0.1
  share_plus: ^11.0.0
  pull_to_refresh: ^2.0.0
  firebase_auth: ^5.5.4  
  firebase_messaging: ^15.2.6 
  lottie: ^3.3.1
  firebase_core: ^3.13.1
  flutter_local_notifications: ^19.2.1
  app_links: ^6.3.2
  flutter_app_badger: ^1.3.0
  carousel_slider: ^5.0.0
  flutter_zoom_drawer: ^3.2.0
  badges: ^3.1.2
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_screenutil: ^5.9.0
  palette_generator: ^0.3.0
  geolocator: ^13.0.4
  permission_handler: ^12.0.0+1
  flutter_switch: ^0.3.2
  country_flags: ^3.2.0  # Downgraded from ^3.3.0
  printing: ^5.6.1
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/images/biglogo_boutigak.png
    - assets/images/placeholder_logo.png
    - assets/images/add.png
    - assets/images/box.png
    - assets/images/add_solid.png
    - assets/images/envelope.png
    - assets/images/envelope_solid.png
    - assets/images/home.png
    - assets/images/home_solid.png
    - assets/images/search.png
    - assets/images/search_solid.png
    - assets/images/shop.png
    - assets/images/shop_solid.png
    - assets/images/icon.png
    - assets/images/logo.png
    - assets/images/snapchat.png
    - assets/images/argent.png
    - assets/images/effacer.png
    - assets/images/home.png
    - assets/images/Becod.png
    - assets/lottie/loader.json
    - assets/lottie/Done.json
    - assets/lottie/oldshop.json
    - assets/lottie/Boutigaklogo.json 
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
