<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>

  <!-- Identité app -->
  <key>CFBundleDevelopmentRegion</key>
  <string>$(DEVELOPMENT_LANGUAGE)</string>
  <key>CFBundleDisplayName</key>
  <string>Boutigak</string>
  <key>CFBundleExecutable</key>
  <string>$(EXECUTABLE_NAME)</string>
  <key>CFBundleIdentifier</key>
  <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
  <key>CFBundleInfoDictionaryVersion</key>
  <string>6.0</string>
  <key>CFBundleName</key>
  <string>Boutigak</string>
  <key>CFBundlePackageType</key>
  <string>APPL</string>
  <key>CFBundleShortVersionString</key>
  <string>$(FLUTTER_BUILD_NAME)</string>
  <key>CFBundleVersion</key>
  <string>$(FLUTTER_BUILD_NUMBER)</string>

  <!-- Flutter / iOS -->
  <key>FlutterDeepLinkingEnabled</key>
  <true/>
  <key>CADisableMinimumFrameDurationOnPhone</key>
  <true/>
  <key>LSRequiresIPhoneOS</key>
  <true/>
  <key>UILaunchStoryboardName</key>
  <string>LaunchScreen</string>
  <!-- Si tu n’utilises pas de storyboard principal natif, tu peux supprimer la clé suivante. -->
  <key>UIMainStoryboardFile</key>
  <string>Main</string>
  <key>UIApplicationSupportsIndirectInputEvents</key>
  <true/>


  <key>UIDeviceFamily</key>
  <array>
    <integer>1</integer> 
  </array>

 
  <key>UISupportedInterfaceOrientations</key>
  <array>
    <string>UIInterfaceOrientationPortrait</string>
  </array>
 
 <key>NSCameraUsageDescription</key>
  <string>We use the camera to take photos of products and listings in Boutigak.</string>

  <key>NSPhotoLibraryUsageDescription</key>
  <string>We access your photo library to select images for your products or listings.</string>

 

  <key>NSLocationWhenInUseUsageDescription</key>
  <string>Boutigak uses your location so shop owners can add a store location and customers can set an order location. Access is used only while you use the app.</string>


  <key>UIBackgroundModes</key>
  <array>
    <string>remote-notification</string>
  </array>

 
  <key>LSApplicationQueriesSchemes</key>
  <array>
    <string>snapchat</string>
  </array>


  <key>SCSDKClientId</key>
  <string>8aea929b-bbde-4d84-92d7-77736ee78a61</string>

  
</dict>
</plist>
