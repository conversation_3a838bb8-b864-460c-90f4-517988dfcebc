#!/usr/bin/env python3
import sys
import os

def analyze_ab_results():
    """Analyze Apache Bench results"""
    try:
        with open('full_results.txt', 'r') as f:
            content = f.read()
            
        # Extract key metrics
        lines = content.split('\n')
        metrics = {}
        
        for line in lines:
            if 'Complete requests:' in line:
                metrics['complete_requests'] = line.split(':')[1].strip()
            elif 'Failed requests:' in line:
                metrics['failed_requests'] = line.split(':')[1].strip()
            elif 'Requests per second:' in line:
                metrics['rps'] = line.split(':')[1].split('[')[0].strip()
            elif 'Time per request:' in line and 'mean' in line:
                metrics['mean_response_time'] = line.split(':')[1].split('[')[0].strip()
        
        print("📊 Quick Analysis:")
        print(f"✅ Completed: {metrics.get('complete_requests', 'N/A')} requests")
        print(f"❌ Failed: {metrics.get('failed_requests', 'N/A')} requests")
        print(f"🚀 RPS: {metrics.get('rps', 'N/A')}")
        print(f"⏱️  Avg Response: {metrics.get('mean_response_time', 'N/A')}")
        
        # Calculate success rate
        try:
            complete = int(metrics.get('complete_requests', '0'))
            failed = int(metrics.get('failed_requests', '0'))
            if complete > 0:
                success_rate = ((complete - failed) / complete) * 100
                print(f"📈 Success Rate: {success_rate:.2f}%")
        except:
            pass
            
    except FileNotFoundError:
        print("❌ Results file not found. Make sure you're in the results directory.")
    except Exception as e:
        print(f"❌ Error analyzing results: {e}")

if __name__ == "__main__":
    analyze_ab_results()
