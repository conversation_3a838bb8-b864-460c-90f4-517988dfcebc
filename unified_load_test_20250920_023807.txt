================================================================================
                    UNIFIED PROGRESSIVE LOAD TEST RESULTS
================================================================================
Test Date: Sat Sep 20 02:38:07 +01 2025
URL: https://boutigak.com/api/items/general-recommended  
Requests per Stage: 5000
Timeout: 30s
Test Stages: 100 250 500 750 1000 1500 2000 2500 3000

System Configuration:
- Max Open Files: 10000
- Max Processes: 2666
- macOS Version: 14.1
- Apache Bench Version: This is ApacheBench, Version 2.3 <$Revision: 1923142 $>

================================================================================
                               PRE-FLIGHT CHECK
================================================================================
Running pre-flight check...
✓ PRE-FLIGHT SUCCESSFUL
  HTTP Status: 200
  Total Time: 1.061440s
  Connect Time: 0.217151s
  Time to First Byte: 1.059747s


PROGRESSIVE TEST STARTED: Sat Sep 20 02:38:08 +01 2025
Expected Duration: Approximately 1620 seconds

================================================================================
                       STAGE 1: 100 CONCURRENT USERS
================================================================================
Started: Sat Sep 20 02:38:11 +01 2025
Command: ab -n 5000 -c 100 -s 30 -k -r -H 'Accept: application/json' -H 'User-Agent: UnifiedTest-Stage1' -H 'Cache-Control: no-cache' 'https://boutigak.com/api/items/general-recommended'

APACHE BENCH OUTPUT:
--------------------
This is ApacheBench, Version 2.3 <$Revision: 1923142 $>
Copyright 1996 Adam Twiss, Zeus Technology Ltd, http://www.zeustech.net/
Licensed to The Apache Software Foundation, http://www.apache.org/

Benchmarking boutigak.com (be patient)
Completed 500 requests
Completed 1000 requests
Completed 1500 requests
Completed 2000 requests
SSL read failed (5) - closing connection

Test aborted after 10 failures

apr_socket_connect(): Operation already in progress (37)
Total of 2055 requests completed

STAGE 1 ANALYSIS:
-------------------------
✗ Status: FAILED (Exit Code: 37)
  Duration: 94s
  Error Details: UNKNOWN_ERROR:37
  Completed: Sat Sep 20 02:39:45 +01 2025

ERROR SUMMARY:
Primary Error: UNKNOWN_ERROR:37

LAST 10 LINES OF OUTPUT:
Completed 500 requests
Completed 1000 requests
Completed 1500 requests
Completed 2000 requests
SSL read failed (5) - closing connection

Test aborted after 10 failures

apr_socket_connect(): Operation already in progress (37)
Total of 2055 requests completed


================================================================================
                             FINAL TEST SUMMARY  
================================================================================
Test Completed: Sat Sep 20 02:39:45 +01 2025
Total Test Duration: Unknown seconds

OVERALL RESULTS:
- Successful Stages: 0/9
- Maximum Successful Concurrent Users: 0
- Failed Stage: 1

STAGE SUMMARY:
  Stage 1 (100 users): ✗ FAILED
  Stage 2 (250 users): - SKIPPED
  Stage 3 (500 users): - SKIPPED
  Stage 4 (750 users): - SKIPPED
  Stage 5 (1000 users): - SKIPPED
  Stage 6 (1500 users): - SKIPPED
  Stage 7 (2000 users): - SKIPPED
  Stage 8 (2500 users): - SKIPPED
  Stage 9 (3000 users): - SKIPPED

RECOMMENDATIONS:
❌ All stages failed - server configuration issues detected
❌ Check SSL certificate, server capacity, and network connectivity
❌ Start with very low concurrency (10-50 users) for troubleshooting

NEXT STEPS:
1. Review detailed results above for each stage
2. Monitor server logs during peak load (0 users)
3. Check SSL/TLS configuration if failures occurred
4. Use distributed testing for loads higher than 0 users
5. Consider HTTP testing to isolate SSL vs application performance

FILES GENERATED:
- unified_load_test_20250920_023807.txt (This complete report)
- load_test_metrics_20250920_023807.csv (CSV format for analysis/graphing)

================================================================================
                               END OF REPORT
================================================================================
