{"openapi": "3.0.0", "info": {"title": "Boutigak API", "version": "1.0"}, "paths": {"/api/auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Register a new user", "description": "Register a new user with the provided information.", "operationId": "d764dd091cc4494ae0baf360b03319f3", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"firstname": {"type": "string", "example": "<PERSON>"}, "lastname": {"type": "string", "example": "<PERSON><PERSON>"}, "phone": {"type": "string", "example": "**********"}, "password": {"type": "string", "example": "12345678"}, "invitationcode": {"type": "string", "example": "ABC123"}, "gender": {"type": "string", "example": "male"}}, "type": "object"}}}}, "responses": {"200": {"description": "Successful registration"}, "400": {"description": "Validation error"}}}}, "/api/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "User login", "description": "Authenticate the user and return an access token.", "operationId": "8dcb70df1020986038d098cc08d05dae", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"phone": {"type": "string", "example": "**********"}, "password": {"type": "string", "example": "12345678"}}, "type": "object"}}}}, "responses": {"200": {"description": "Successful login"}, "401": {"description": "Unauthorized"}, "400": {"description": "Validation error"}}}}, "/api/auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "User logout", "description": "Invalidate the user's token and log them out.", "operationId": "69281b12abb272c76871f19cb17ca563", "responses": {"200": {"description": "Successful logout"}}}}, "/api/change-password": {"post": {"tags": ["User"], "summary": "Change the authenticated user's password", "description": "Allows the authenticated user to change their password by providing the current password and a new one.", "operationId": "changePassword", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["current_password", "new_password", "new_password_confirmation"], "properties": {"current_password": {"type": "string", "example": "oldPassword123"}, "new_password": {"type": "string", "format": "password", "minLength": 8, "example": "newPassword123"}, "new_password_confirmation": {"type": "string", "format": "password", "example": "newPassword123"}}, "type": "object"}}}}, "responses": {"200": {"description": "Password changed successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Password changed successfully"}}, "type": "object"}}}}, "401": {"description": "Unauthorized - Current password is incorrect", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Current password is incorrect"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"errors": {"properties": {"current_password": {"type": "array", "items": {"type": "string"}}, "new_password": {"type": "array", "items": {"type": "string"}}}, "type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/password/recover": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Recover password by verifying phone number", "description": "Verify the user's phone number and allow them to set a new password.", "operationId": "recoverPassword", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"phone": {"type": "string", "example": "**********"}, "password": {"type": "string", "example": "new_password123"}, "password_confirm": {"type": "string", "example": "new_password123"}}, "type": "object"}}}}, "responses": {"200": {"description": "Password reset successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Password reset successfully"}}, "type": "object"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"error": {"type": "object"}}, "type": "object"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "User not found"}}, "type": "object"}}}}}}}, "/api/auth/delete-account": {"delete": {"tags": ["<PERSON><PERSON>"], "summary": "Delete user account", "description": "Delete user account and add delai of 30 days to recover it.", "operationId": "45879cad670569f9c3632891ddabf556", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"delete_reason": {"type": "string", "example": "No longer needed"}}, "type": "object"}}}}, "responses": {"200": {"description": "User account deleted successfully"}, "401": {"description": "Unauthenticated"}, "404": {"description": "User not found"}, "500": {"description": "Server error"}}}}, "/api/auth/recover-account": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Recover user account", "description": "Recover user account before 30 days.", "operationId": "2f0451d1bc2e2cebd62b4d436ad13cc8", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"phone": {"type": "string", "example": "**********"}, "password": {"type": "string", "example": "new_password123"}, "password_confirm": {"type": "string", "example": "new_password123"}}, "type": "object"}}}}, "responses": {"200": {"description": "User account recovered successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "User account recovered successfully"}}, "type": "object"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"error": {"type": "object"}}, "type": "object"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "User not found"}}, "type": "object"}}}}}}}, "/api/user/update-language": {"put": {"tags": ["<PERSON><PERSON>"], "summary": "Update user language preference", "description": "Update the language preference for the authenticated user.", "operationId": "9348ac0ddc2dfdc0aa5f697f39ab61c8", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"lang": {"description": "Language code (e.g., 'en', 'fr', 'ar')", "type": "string", "example": "fr"}}, "type": "object"}}}}, "responses": {"200": {"description": "Language updated successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Language updated successfully"}, "user": {"properties": {"id": {"type": "integer", "example": 1}, "firstname": {"type": "string", "example": "<PERSON>"}, "lastname": {"type": "string", "example": "<PERSON><PERSON>"}, "lang": {"type": "string", "example": "fr"}}, "type": "object"}}, "type": "object"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"error": {"type": "object"}}, "type": "object"}}}}, "401": {"description": "Unauthenticated"}}, "security": [{"bearerAuth": []}]}}, "/api/payments/bpay/initialize": {"post": {"tags": ["Payments"], "summary": "Initialize B-Pay payment session", "description": "Initialize payment session", "operationId": "8bac1615a103d0cab8417cf5f8a881ac", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["amount"], "properties": {"amount": {"type": "number", "example": "100.00"}}, "type": "object"}}}}, "responses": {"200": {"description": "Payment session initialized"}, "422": {"description": "Validation error"}}}}, "/api/payments/bpay/process": {"post": {"tags": ["Payments"], "summary": "Process B-Pay payment", "description": "Process payment", "operationId": "ffe39e3b53aaba97a8b3412eb0b9627c", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["transaction_id", "phone", "passcode"], "properties": {"transaction_id": {"type": "string"}, "phone": {"type": "string"}, "passcode": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Payment processed successfully"}, "422": {"description": "Validation error"}}}}, "/api/payments/bpay/{transactionId}/status": {"get": {"tags": ["Payments"], "summary": "Check B-Pay payment status", "description": "Verify payment status", "operationId": "b6a293f625cddcf00839853f16021053", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Payment status retrieved"}, "404": {"description": "Transaction not found"}}}}, "/api/brands": {"get": {"tags": ["Brand"], "summary": "Get all brands", "description": "Retrieve a list of all brands.", "operationId": "3c41aa2e65314941e37a634e810c5f73", "parameters": [{"name": "category_id", "in": "query", "description": "The ID of the category to filter brands by.", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Brand Name"}, "created_at": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00Z"}}, "type": "object"}}}}}}}, "post": {"tags": ["Brand"], "summary": "Create a new brand", "description": "Create a new brand with a name.", "operationId": "1dae5363a453b159e035bc04b3fbc0be", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name", "category_id"], "properties": {"name": {"type": "string", "example": "New Brand"}, "category_id": {"type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"201": {"description": "Brand created successfully", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "New Brand"}, "created_at": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00Z"}}, "type": "object"}}}}, "400": {"description": "Bad request"}, "409": {"description": "Conflict - Brand already exists"}}}}, "/api/categories": {"get": {"tags": ["Category"], "summary": "Get all categories with their children and details", "description": "Retrieve all categories along with their children and associated details.", "operationId": "94afbe1837f5d70c8798d113b4a8fb8e", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Category Name"}, "children": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 2}, "name": {"type": "string", "example": "Subcategory Name"}, "details": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "label": {"type": "string", "example": "Detail Label"}}, "type": "object"}}}, "type": "object"}}, "details": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "label": {"type": "string", "example": "Detail Label"}}, "type": "object"}}}, "type": "object"}}}}}}}}, "/api/delivery-charges": {"get": {"tags": ["DeliveryCharge"], "summary": "Get all delivery charges", "description": "Retrieve a list of all delivery charges.", "operationId": "2bdd74f4fd8d47e0e275a5d218afc49d", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryCharge"}}}}}, "401": {"description": "Unauthenticated"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["DeliveryCharge"], "summary": "Create a new delivery charge", "description": "Create a new delivery charge.", "operationId": "aecc1948aef3c06875741e570e06e7c5", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"amount": {"type": "number", "format": "float", "example": 50}, "type": {"type": "string", "example": "delivery normal"}}, "type": "object"}}}}, "responses": {"201": {"description": "Delivery charge created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryCharge"}}}}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthenticated"}}, "security": [{"bearerAuth": []}]}}, "/api/delivery-charges/{id}": {"get": {"tags": ["DeliveryCharge"], "summary": "Get a delivery charge by ID", "description": "Retrieve a delivery charge by its ID.", "operationId": "e850f54f4b05b0524fc81e471d219aba", "parameters": [{"name": "id", "in": "path", "description": "Delivery charge ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryCharge"}}}}, "404": {"description": "Delivery charge not found"}, "401": {"description": "Unauthenticated"}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["DeliveryCharge"], "summary": "Update a delivery charge", "description": "Update the details of a delivery charge.", "operationId": "0d30c6740c28d6d7fd2af6e57c9b40d0", "parameters": [{"name": "id", "in": "path", "description": "Delivery charge ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"amount": {"type": "number", "format": "float", "example": 50}, "type": {"type": "string", "example": "delivery normal"}}, "type": "object"}}}}, "responses": {"200": {"description": "Delivery charge updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryCharge"}}}}, "400": {"description": "Invalid input"}, "404": {"description": "Delivery charge not found"}, "401": {"description": "Unauthenticated"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["DeliveryCharge"], "summary": "Delete a delivery charge", "description": "Delete a delivery charge by its ID.", "operationId": "06277579244ae744237922da1625bad6", "parameters": [{"name": "id", "in": "path", "description": "Delivery charge ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Delivery charge deleted successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Delivery charge deleted successfully"}}, "type": "object"}}}}, "404": {"description": "Delivery charge not found"}, "401": {"description": "Unauthenticated"}}, "security": [{"bearerAuth": []}]}}, "/api/device-tokens": {"post": {"tags": ["DeviceToken"], "summary": "Store a new device token", "description": "Stores a new device token for the authenticated user.", "operationId": "storeDeviceToken", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"token": {"type": "string", "example": "your_device_token"}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> saved successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "<PERSON><PERSON> saved successfully"}}, "type": "object"}}}}, "422": {"description": "Validation error"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["DeviceToken"], "summary": "Delete the device token for the authenticated user", "description": "Deletes the device token for the authenticated user.", "operationId": "destroy", "responses": {"200": {"description": "Token deleted successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Token deleted successfully"}}, "type": "object"}}}}, "404": {"description": "Token not found"}}, "security": [{"bearerAuth": []}]}}, "/api/device-tokens/current": {"get": {"tags": ["DeviceToken"], "summary": "Get the device token for the authenticated user", "description": "Returns the device token for the authenticated user.", "operationId": "getTokenByCurrentUser", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"token": {"type": "string", "example": "your_device_token"}}, "type": "object"}}}}, "404": {"description": "Token not found"}}, "security": [{"bearerAuth": []}]}}, "/api/payment-providers": {"get": {"tags": ["Payment Providers"], "summary": "Get all payment providers", "description": "Returns list of all payment providers", "operationId": "b2c317e528debd44f9bbd5113ba550b1", "parameters": [{"name": "has_api", "in": "query", "description": "Filter by API capability", "required": false, "schema": {"type": "boolean"}}, {"name": "active", "in": "query", "description": "Filter by active status", "required": false, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "BANKILY"}, "provider_code": {"type": "string", "example": "BPAY"}, "has_api": {"type": "boolean", "example": true}, "is_active": {"type": "boolean", "example": true}, "description": {"type": "string", "example": "Bankily Payment Gateway"}, "logo_url": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}, "type": "object"}}}}, "500": {"description": "Server error"}}}}, "/api/payment-providers/{id}": {"get": {"tags": ["Payment Providers"], "summary": "Get payment provider details", "description": "Returns details of a specific payment provider", "operationId": "e4277d7016bebc4200737813eb1639a1", "parameters": [{"name": "id", "in": "path", "description": "Payment provider ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "BANKILY"}, "provider_code": {"type": "string", "example": "BPAY"}, "has_api": {"type": "boolean", "example": true}, "is_active": {"type": "boolean", "example": true}, "description": {"type": "string", "example": "Bankily Payment Gateway"}, "logo_url": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "Provider not found"}}}}, "/api/payment-providers/api-enabled": {"get": {"tags": ["Payment Providers"], "summary": "Get active API-enabled payment providers", "description": "Returns list of active payment providers that support API integration", "operationId": "0893c4c3059442b3f36f5ed7692ac51a", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "BANKILY"}, "provider_code": {"type": "string", "example": "BPAY"}, "description": {"type": "string", "example": "Bankily Payment Gateway"}, "logo_url": {"type": "string", "nullable": true}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/items": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Create a new item", "description": "Create a new item with the provided details.", "operationId": "af37857663e6805dd367a55f374c9ee1", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"title": {"type": "string", "example": "Sample Item"}, "description": {"type": "string", "example": "This is a sample item."}, "price": {"type": "number", "format": "float", "example": 99.99}, "condition": {"type": "string", "example": "new"}, "brand_id": {"type": "integer", "example": 1}, "category_id": {"type": "integer", "example": 1}, "category_item_details": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "value": {"type": "string", "example": "Sample value"}}, "type": "object"}}, "images": {"type": "array", "items": {"type": "string", "format": "binary"}}}, "type": "object"}}}}, "responses": {"201": {"description": "Successful creation"}, "400": {"description": "Validation error"}, "500": {"description": "Failed to create item"}}}}, "/api/liked-items": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get liked items", "description": "Retrieve a list of items liked by the authenticated user.", "operationId": "getLikedItems", "responses": {"200": {"description": "Successful operation"}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/items/like-unlike/{item}": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Like or unlike an item", "description": "Like or unlike an item by the authenticated user.", "operationId": "601e5e396105b1b9d5cc3ec4a7c9f333", "parameters": [{"name": "item", "in": "path", "description": "The item's ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "<PERSON><PERSON> liked"}}, "type": "object"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/items/recommended": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get recommended items", "description": "Retrieve items that the user already liked and have the same category as the stores they follow.", "operationId": "df348b677b72331b58a85e9c18ef2e6e", "responses": {"200": {"description": "Successful operation"}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/followed-stores": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get followed stores", "description": "Retrieve all stores followed by the authenticated user.", "operationId": "getFollowedStores", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "description": {"type": "string"}}, "type": "object"}}}}}, "404": {"description": "No stores found"}}, "security": [{"bearerAuth": []}]}}, "/api/items/search": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Search for items", "description": "Search for items based on a query string. If the user is authenticated, the search history will be saved.", "operationId": "9ba8d40af8740cbb5242579ac0e72bc9", "parameters": [{"name": "query", "in": "query", "description": "The search query string", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Sample Item"}, "description": {"type": "string", "example": "This is a sample item."}, "price": {"type": "number", "format": "float", "example": 99.99}, "is_promoted": {"type": "boolean", "example": true}, "status": {"type": "string", "example": "APPROVED"}, "brand": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Brand Name"}}, "type": "object"}, "category": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Category Title"}}, "type": "object"}, "user": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "User Name"}}, "type": "object"}}, "type": "object"}}}}}, "400": {"description": "Bad Request"}}}}, "/api/items-status/{item}": {"put": {"tags": ["<PERSON><PERSON>"], "summary": "Update item status", "description": "Change the status of an item. If the status is 'REJECTED', a reason must be provided.", "operationId": "4615ec30bdd1d7785c8f12b940ae16d8", "parameters": [{"name": "item", "in": "path", "description": "The item's ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"status": {"type": "string", "example": "APPROVED"}, "rejection_reason": {"type": "string", "example": "Reason for rejection"}}, "type": "object"}}}}, "responses": {"200": {"description": "Successful operation"}, "400": {"description": "Validation error"}, "404": {"description": "Item not found"}, "500": {"description": "Server error"}}, "security": [{"bearerAuth": []}]}}, "/api/search-history": {"get": {"tags": ["Search"], "summary": "Get search history", "description": "Retrieve the search history of the authenticated user.", "operationId": "e66653225d536824de33112bce00eb9e", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "query": {"type": "string", "example": "Sample query"}, "searched_at": {"type": "string", "format": "date-time", "example": "2024-07-25T15:32:07Z"}}, "type": "object"}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/items/general-recommended": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get general recommended items", "description": "Retrieve a list of general recommended items.", "operationId": "f40fee5cf984648f2c6865f23cec0d5a", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Sample Item"}, "description": {"type": "string", "example": "This is a sample item."}, "price": {"type": "number", "format": "float", "example": 99.99}, "is_promoted": {"type": "boolean", "example": true}, "status": {"type": "string", "example": "APPROVED"}, "brand": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Brand Name"}}, "type": "object"}, "category": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Category Title"}}, "type": "object"}, "user": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "User Name"}}, "type": "object"}}, "type": "object"}}}}}, "500": {"description": "Server error"}}}}, "/api/user-items": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get user items", "description": "Retrieve a list of items created by the authenticated user.", "operationId": "26cf7d106a37a2da8b7cb2add8679d50", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Sample Item"}, "description": {"type": "string", "example": "This is a sample item."}, "price": {"type": "number", "format": "float", "example": 99.99}, "is_promoted": {"type": "boolean", "example": true}, "status": {"type": "string", "example": "APPROVED"}}, "type": "object"}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/items/{item}": {"put": {"tags": ["<PERSON><PERSON>"], "summary": "Update an item", "description": "Update an item with the provided details.", "operationId": "cb7bb88b9f9f00e6abc52a60a7549e2d", "parameters": [{"name": "item", "in": "path", "description": "The item's ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"title": {"type": "string", "example": "Sample Item"}, "description": {"type": "string", "example": "This is a sample item."}, "price": {"type": "number", "format": "float", "example": 99.99}, "condition": {"type": "string", "example": "new"}, "brand_id": {"type": "integer", "example": 1}, "category_id": {"type": "integer", "example": 1}, "category_item_details": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "value": {"type": "string", "example": "Sample value"}}, "type": "object"}}, "images": {"type": "array", "items": {"type": "string", "format": "binary"}}}, "type": "object"}}}}, "responses": {"200": {"description": "Successful operation"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "500": {"description": "Failed to update item"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["<PERSON><PERSON>"], "summary": "Delete an item", "description": "Delete an item created by the authenticated user.", "operationId": "73ae3be3abe0405a34ff78aeedcb43b3", "parameters": [{"name": "item", "in": "path", "description": "The item's ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation"}, "401": {"description": "Unauthorized"}, "404": {"description": "Item not found"}}, "security": [{"bearerAuth": []}]}}, "/api/user-items-status": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get user items statuses", "description": "Retrieve a list of items created by the authenticated user with their status, first image, updated status date, and whether the owner has updated the item.", "operationId": "7fcc6da1135fd94453ee480d5b268f5e", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Sample Item"}, "status": {"type": "string", "example": "approved"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-07-25T15:32:07Z"}, "is_promoted": {"type": "boolean", "example": true}, "created_at": {"type": "string", "format": "date-time", "example": "2024-07-25T15:32:07Z"}, "images": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "url": {"type": "string", "example": "http://example.com/image.jpg"}}, "type": "object"}}, "status_message": {"type": "string", "example": "Approved at 2024-07-25 15:32:07"}, "owner_updated": {"type": "boolean", "example": true}}, "type": "object"}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/items/{id}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get item", "description": "Retrieve an item by ID.", "operationId": "12ecaa4da052d255e65762b7dafb41e5", "parameters": [{"name": "id", "in": "path", "description": "The item's ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Sample Item"}, "description": {"type": "string", "example": "This is a sample item."}, "price": {"type": "number", "format": "float", "example": 99.99}, "is_promoted": {"type": "boolean", "example": true}, "status": {"type": "string", "example": "APPROVED"}, "brand": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Brand Name"}}, "type": "object"}, "category": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Category Title"}}, "type": "object"}, "user": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "User Name"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "Item not found"}}}}, "/api/stores/my-store/items/{itemId}": {"put": {"tags": ["Store"], "summary": "Update an item in the authenticated user's store", "description": "Update an item in the authenticated user's store by item ID.", "operationId": "updateItemInMyStore", "parameters": [{"name": "itemId", "in": "path", "description": "Item ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"title": {"type": "string"}, "price": {"type": "number", "format": "float"}, "description": {"type": "string"}, "condition": {"type": "string"}, "quantity": {"type": "integer"}, "brand_id": {"type": "integer"}, "category_id": {"type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "Item updated successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Item updated successfully"}}, "type": "object"}}}}, "404": {"description": "Item not found"}, "500": {"description": "Server error"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Store"], "summary": "Delete an item from the authenticated user's store", "description": "Delete an item from the authenticated user's store by item ID.", "operationId": "deleteItemFromMyStore", "parameters": [{"name": "itemId", "in": "path", "description": "Item ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Item deleted successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Item deleted successfully"}}, "type": "object"}}}}, "404": {"description": "Item not found"}, "500": {"description": "Server error"}}, "security": [{"bearerAuth": []}]}}, "/api/search-history/{id}": {"delete": {"tags": ["Search"], "summary": "Delete search history", "description": "Delete a search history record by ID.", "operationId": "36442c559852bc1343116149d1d547f8", "parameters": [{"name": "id", "in": "path", "description": "The search history ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation"}, "404": {"description": "Search history not found"}, "500": {"description": "Server error"}}, "security": [{"bearerAuth": []}]}}, "/api/notifications/send": {"post": {"tags": ["Notifications"], "summary": "Send a push notification", "description": "Send a push notification to a specific device token", "operationId": "7764ff34f990c495c4ac5d69ae57d94a", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["token", "title", "body"], "properties": {"token": {"type": "string", "example": "device_token_here"}, "title": {"type": "string", "example": "Notification Title"}, "body": {"type": "string", "example": "Notification Body"}}, "type": "object"}}}}, "responses": {"200": {"description": "Notification sent successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Notification sent successfully"}}, "type": "object"}}}}, "500": {"description": "Error sending notification", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "Failed to send notification"}}, "type": "object"}}}}}}}, "/api/notifications": {"get": {"tags": ["Notifications"], "summary": "Get user notifications in their preferred language", "description": "Retrieve all notifications for the authenticated user with localized content", "operationId": "aaf4ce18cea6a979369dcecb3a097928", "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "default": 15}}], "responses": {"200": {"description": "List of notifications in user's preferred language", "content": {"application/json": {"schema": {"properties": {"current_page": {"type": "integer"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "user_id": {"type": "integer"}, "title": {"type": "string"}, "message": {"type": "string"}, "type": {"type": "string"}, "is_read": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}, "store_data": {"type": "object", "nullable": true}}, "type": "object"}}, "pagination_metadata": {"type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/discussions": {"get": {"tags": ["Discussions"], "summary": "Get all discussions for the authenticated user", "operationId": "e35f3f8e429e57e9b363b8b84ca437df", "responses": {"200": {"description": "List of discussions", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "item_id": {"type": "integer"}, "buyer_id": {"type": "integer"}, "seller_id": {"type": "integer"}, "store_id": {"type": "integer", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string"}}, "type": "object"}}}}}}, "post": {"tags": ["Discussions"], "summary": "Create a new discussion", "operationId": "8a384bc40701f0be1619e7ad6276fd9c", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["item_id"], "properties": {"item_id": {"type": "integer", "example": 1}, "store_id": {"type": "integer", "example": 1, "nullable": true}}, "type": "object"}}}}, "responses": {"201": {"description": "Discussion created successfully", "content": {"application/json": {"schema": {"properties": {"discussion": {"properties": {"id": {"type": "integer"}, "item_id": {"type": "integer"}, "buyer_id": {"type": "integer"}, "seller_id": {"type": "integer"}, "store_id": {"type": "integer", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"errors": {"type": "object"}}, "type": "object"}}}}}}}, "/discussions/{discussionId}/messages": {"post": {"tags": ["Messages"], "summary": "Send a message in a discussion", "operationId": "31c86be9ee25a6a1c8f9befb10e047d8", "parameters": [{"name": "discussionId", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"content": {"type": "string", "example": "Hello!"}, "is_an_offer": {"type": "boolean", "example": false}, "price": {"type": "number", "format": "float", "example": 100}, "reply_discution_id": {"type": "integer", "nullable": true}}, "type": "object"}}}}, "responses": {"201": {"description": "Message sent successfully", "content": {"application/json": {"schema": {"properties": {"message": {"properties": {"id": {"type": "integer"}, "discution_id": {"type": "integer"}, "sender_id": {"type": "integer"}, "content": {"type": "string"}, "is_an_offer": {"type": "boolean"}, "price": {"type": "number", "format": "float"}, "reply_discution_id": {"type": "integer", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "sender": {"properties": {"id": {"type": "integer"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "Discussion not found", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"errors": {"type": "object"}}, "type": "object"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string"}}, "type": "object"}}}}}}}, "/messages/{itemId}": {"get": {"tags": ["Messages"], "summary": "Get all messages for a specific item grouped by date", "operationId": "6bdd5a3cb61c1051e5fc2d2a1c7170dc", "parameters": [{"name": "itemId", "in": "path", "description": "ID of the item to get messages for", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "List of discussions with messages grouped by date", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "item_id": {"type": "integer"}, "buyer_id": {"type": "integer"}, "seller_id": {"type": "integer"}, "store_id": {"type": "integer", "nullable": true}, "is_store_discussion": {"type": "boolean"}, "store_details": {"type": "object", "nullable": true}, "messages_by_date": {"type": "object", "additionalProperties": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "content": {"type": "string"}, "sender_id": {"type": "integer"}, "is_an_offer": {"type": "boolean"}, "price": {"type": "number"}, "sent_by_me": {"type": "boolean"}, "sender_details": {"type": "object"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "type": "object"}}}}, "type": "object"}}}}}, "404": {"description": "No discussions found"}}}}, "/api/orders": {"post": {"tags": ["Order"], "summary": "Create a new order", "description": "Creates a new order for the authenticated user.", "operationId": "createOrder", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"store_id": {"type": "integer"}, "items": {"type": "array", "items": {"properties": {"item_id": {"type": "integer"}, "quantity": {"type": "integer"}}, "type": "object"}}, "delivery_charge_id": {"type": "integer"}, "location_id": {"type": "integer", "nullable": true}}, "type": "object"}}}}, "responses": {"201": {"description": "Order created successfully", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "user_id": {"type": "integer"}, "store_id": {"type": "integer"}, "status": {"type": "string"}, "delivery_charge": {"type": "number", "format": "float"}, "location_id": {"type": "integer", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "400": {"description": "Invalid input"}, "500": {"description": "Internal server error"}}, "security": [{"bearerAuth": []}]}}, "/api/order-store": {"get": {"tags": ["Store"], "summary": "Get orders for stores owned by the authenticated user", "description": "Retrieve all orders for the stores owned by the authenticated user.", "operationId": "78df6f184cfd3babef147cdb1c52a818", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "store_id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 1}, "status": {"type": "string", "example": "pending"}, "delivery_charge": {"type": "number", "format": "float", "example": 5}, "location_id": {"type": "integer", "example": 1}, "created_at": {"type": "string", "format": "date-time", "example": "2024-10-13T02:20:22.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-10-13T02:20:22.000000Z"}}, "type": "object"}}}}}, "401": {"description": "Unauthenticated"}, "500": {"description": "Server error"}}, "security": [{"bearerAuth": []}]}}, "/api/orders-update-status": {"put": {"tags": ["Order"], "summary": "Update order status", "description": "Update the status of an order. Status can be PENDING, ACCEPTED, CANCELLED, WAITING_PAYMENT, DELIVERED.", "operationId": "ae570baa559f594c29c6e7213ff716e0", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer", "example": 1}, "status": {"type": "string", "example": "ACCEPTED"}}, "type": "object"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Order status updated successfully"}}, "type": "object"}}}}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthenticated"}, "403": {"description": "Forbidden"}, "404": {"description": "Order not found"}, "500": {"description": "Internal server error"}}, "security": [{"bearerAuth": []}]}}, "/api/orders/{id}": {"put": {"tags": ["Order"], "summary": "Update order details", "description": "Update the details of an order. Only the owner of the store can update the order.", "operationId": "07318789cbd29f9886cefd631f4ff1ea", "parameters": [{"name": "id", "in": "path", "description": "Order ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"status": {"type": "string", "example": "ACCEPTED"}, "delivery_charge_id": {"type": "integer", "example": 1}, "location_id": {"type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Order updated successfully"}}, "type": "object"}}}}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthenticated"}, "403": {"description": "Forbidden"}, "404": {"description": "Order not found"}, "500": {"description": "Internal server error"}}, "security": [{"bearerAuth": []}]}}, "/api/orders/mine": {"get": {"tags": ["Order"], "summary": "Get orders for the authenticated user", "description": "Returns a list of orders for the authenticated user.", "operationId": "getOrders", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "total_price": {"type": "number", "format": "float"}, "status": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "items": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "price": {"type": "number", "format": "float"}, "quantity": {"type": "integer"}}, "type": "object"}}}, "type": "object"}}}}}, "401": {"description": "Unauthenticated"}}, "security": [{"bearerAuth": []}]}}, "/api/orders/{id}/edit": {"put": {"tags": ["Order"], "summary": "Edit order items", "description": "Edit the items of an order if a product is not available or the quantity ordered is not complete.", "operationId": "b86a96693c5ff74f9b897ce3e49a3965", "parameters": [{"name": "id", "in": "path", "description": "Order ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"items": {"type": "array", "items": {"properties": {"item_id": {"type": "integer"}, "quantity": {"type": "integer"}}, "type": "object"}}}, "type": "object"}}}}, "responses": {"200": {"description": "Order items updated successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Order items updated successfully"}, "cloned_order": {"properties": {"id": {"type": "integer"}, "user_id": {"type": "integer"}, "store_id": {"type": "integer"}, "status": {"type": "string"}, "delivery_charge": {"type": "number", "format": "float"}, "location_id": {"type": "integer", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthenticated"}, "403": {"description": "Forbidden"}, "404": {"description": "Order not found"}, "500": {"description": "Internal server error"}}, "security": [{"bearerAuth": []}]}}, "/api/orders/{id}/clone": {"post": {"tags": ["Order"], "summary": "Clone an order", "description": "Creates a clone of the specified order.", "operationId": "fcef802445dbe8c818d2ef699962b18b", "parameters": [{"name": "id", "in": "path", "description": "Order ID", "required": true, "schema": {"type": "integer"}}], "responses": {"201": {"description": "Order cloned successfully", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "user_id": {"type": "integer"}, "store_id": {"type": "integer"}, "status": {"type": "string"}, "delivery_charge": {"type": "number", "format": "float"}, "location_id": {"type": "integer", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "404": {"description": "Order not found"}, "500": {"description": "Internal server error"}}, "security": [{"bearerAuth": []}]}}, "/api/payment-proof": {"post": {"tags": ["Payment Proof"], "summary": "Submit a new payment proof", "description": "Submit payment proof for either an item or order payment. For non-store payments, promo codes can be applied for discounts.", "operationId": "storePaymentProof", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["to_store", "provider_id", "screenshot"], "properties": {"to_store": {"description": "Whether the payment is to a store", "type": "boolean"}, "item_id": {"description": "ID of the item being paid for (required if order_id not provided)", "type": "integer"}, "order_id": {"description": "ID of the order being paid for (required if item_id not provided)", "type": "integer"}, "provider_id": {"description": "ID of the payment provider", "type": "integer"}, "screenshot": {"description": "Payment screenshot (max 5MB)", "type": "string", "format": "binary"}, "promo_code": {"description": "Optional promotional code for discount (only applicable when to_store is false)", "type": "string", "nullable": true}}, "type": "object"}}}}, "responses": {"201": {"description": "Payment proof submitted successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Payment proof submitted successfully"}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 1}, "provider_id": {"type": "integer", "example": 1}, "to_store": {"type": "boolean", "example": true}, "amount": {"type": "integer", "example": 101}, "original_amount": {"type": "integer", "example": 120}, "discount_amount": {"type": "integer", "example": 20}, "promo_code": {"type": "string", "example": "SUMMER20", "nullable": true}, "screenshot": {"type": "string", "example": "payment_proofs/abc123.jpg"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"errors": {"properties": {"promo_code": {"type": "array", "items": {"type": "string", "example": "The promo code is invalid or expired."}}}, "type": "object"}}, "type": "object"}}}}, "403": {"description": "Unauthorized access", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthorized"}}, "type": "object"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Failed to submit payment proof"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/payment-proof/{id}": {"get": {"tags": ["Payment Proof"], "summary": "Get payment proof details", "description": "Retrieve details of a specific payment proof", "operationId": "showPaymentProof", "parameters": [{"name": "id", "in": "path", "description": "Payment proof ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Payment proof details retrieved successfully", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 1}, "provider_id": {"type": "integer", "example": 1}, "to_store": {"type": "boolean", "example": true}, "amount": {"type": "number", "format": "float", "example": 100.5}, "screenshot": {"type": "string", "example": "payment_proofs/abc123.jpg"}, "reference_number": {"type": "string", "example": "REF123"}, "provider": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "PayPal"}}, "type": "object"}, "item": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Item Name"}}, "type": "object", "nullable": true}, "order": {"properties": {"id": {"type": "integer", "example": 1}, "status": {"type": "string", "example": "pending"}}, "type": "object", "nullable": true}, "store": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Store Name"}}, "type": "object", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "Payment proof not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Payment proof not found"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/category-prices": {"get": {"tags": ["Category Prices"], "summary": "Get all category prices", "description": "Retrieve a list of all categories with their prices (rounded to nearest integer)", "operationId": "getCategoryPrices", "responses": {"200": {"description": "List of category prices retrieved successfully", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "category_id": {"type": "integer", "example": 1}, "category_name": {"type": "string", "example": "Electronics"}, "price": {"type": "integer", "example": 101}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}, "type": "object"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Failed to retrieve category prices"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/calculate-price": {"post": {"tags": ["Category Prices"], "summary": "Calculate price with promo code", "description": "Calculate the discounted price for a category using a promo code (all prices rounded to nearest integer)", "operationId": "calculatePrice", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["category_id", "promo_code"], "properties": {"category_id": {"type": "integer", "example": 1}, "promo_code": {"type": "string", "example": "SUMMER20"}}, "type": "object"}}}}, "responses": {"200": {"description": "Price calculation successful", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"original_price": {"type": "integer", "example": 100}, "discounted_price": {"type": "integer", "example": 80}, "discount_amount": {"type": "integer", "example": 20}, "discount_percentage": {"type": "integer", "example": 20}, "is_valid": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Promo code applied successfully"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"errors": {"properties": {"promo_code": {"type": "array", "items": {"type": "string", "example": "The promo code is invalid or expired."}}}, "type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/payment-verifications": {"get": {"tags": ["Admin"], "summary": "Get all app payment proofs", "description": "Get all app payment proofs", "operationId": "7b286d5558b8d928ed07fc5c35866915", "parameters": [{"name": "status", "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED"]}}, {"name": "per_page", "in": "query", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "List of payment proofs"}, "401": {"description": "Unauthenticated"}, "403": {"description": "Unauthorized"}}}}, "/api/admin/payment-verifications/{id}": {"get": {"tags": ["Admin"], "summary": "Get specific payment proof details", "description": "Get specific payment proof details", "operationId": "bcf53e3a21cd21b53b475c7de1c5d88c", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Payment proof details"}, "404": {"description": "Payment proof not found"}}}}, "/api/admin/payment-verifications/stats": {"get": {"tags": ["Admin"], "summary": "Get payment verification statistics", "description": "Get verification statistics", "operationId": "d0234abac23de32088c794749b0e5cb3", "responses": {"200": {"description": "Statistics retrieved successfully"}}}}, "/api/profile/update": {"put": {"tags": ["Profile"], "summary": "Update user profile", "description": "Update the authenticated user's profile information.", "operationId": "be2b16764aaa39173d9cc6fe1e5f771f", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"firstname": {"type": "string", "example": "<PERSON>"}, "lastname": {"type": "string", "example": "<PERSON><PERSON>"}}, "type": "object"}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Profile updated successfully"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"errors": {"type": "object"}}, "type": "object"}}}}, "401": {"description": "Unauthenticated"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/recommended": {"get": {"tags": ["Store"], "summary": "Get recommended stores", "description": "Get stores based on user's liked item categories, followed stores' favorite categories, and promoted stores.", "operationId": "bb4a08f80a3512df235f3d218aaf50fa", "responses": {"200": {"description": "Successful operation"}, "404": {"description": "No stores found"}}, "security": [{"bearerAuth": []}]}}, "/api/stores": {"put": {"tags": ["Store"], "summary": "Update an existing store", "description": "Update an existing store with associated images.", "operationId": "updateStore", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["name", "description", "type_id", "images"], "properties": {"name": {"type": "string", "example": "Updated Store"}, "description": {"type": "string", "example": "An updated description"}, "type_id": {"type": "integer", "example": 2}, "images": {"type": "array", "items": {"properties": {"image": {"type": "string", "format": "binary"}, "dimmension": {"type": "string", "example": "1920x1080"}}, "type": "object"}}}, "type": "object"}}}}, "responses": {"200": {"description": "Store updated successfully", "content": {"application/json": {"schema": {"properties": {"store_id": {"type": "integer"}}, "type": "object"}}}}, "404": {"description": "Store not found"}, "422": {"description": "Validation error"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Store"], "summary": "Create a new store", "description": "Create a new store with associated images.", "operationId": "createStore", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["name", "description", "type_id", "images", "opening_time", "closing_time"], "properties": {"name": {"type": "string", "example": "My Store"}, "description": {"type": "string", "example": "A great store"}, "type_id": {"type": "integer", "example": 1}, "opening_time": {"type": "string", "example": "08:00"}, "closing_time": {"type": "string", "example": "20:00"}, "images": {"type": "array", "items": {"properties": {"image": {"type": "string", "format": "binary"}, "dimmension": {"type": "string", "example": "1024x768"}}, "type": "object"}}}, "type": "object"}}}}, "responses": {"201": {"description": "Store created successfully", "content": {"application/json": {"schema": {"properties": {"store_id": {"type": "integer"}}, "type": "object"}}}}, "422": {"description": "Validation error"}, "403": {"description": "User already has a store"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/my-store/location": {"get": {"tags": ["Store"], "summary": "Get store location", "description": "Returns the location of the authenticated user's store.", "operationId": "getStoreLocation", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "address": {"type": "string"}, "latitude": {"type": "number", "format": "float"}, "longitude": {"type": "number", "format": "float"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "404": {"description": "Store not found"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Store"], "summary": "Create or update a store's location", "description": "Create or update a store's location.", "operationId": "updateStoreLocation", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["address", "latitude", "longitude", "name"], "properties": {"address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "string", "example": "40.7128"}, "longitude": {"type": "string", "example": "-74.0060"}, "name": {"type": "string", "example": "Store Name"}}, "type": "object"}}}}, "responses": {"200": {"description": "Location updated successfully", "content": {"application/json": {"schema": {"properties": {"location_id": {"type": "integer"}}, "type": "object"}}}}, "404": {"description": "Store not found"}, "422": {"description": "Validation error"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/favorites-categories": {"get": {"tags": ["Store"], "summary": "Get favorite categories of a store", "description": "Returns a list of favorite categories for the authenticated store.", "operationId": "getFavoriteCategories", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "type": "object"}}}}}, "404": {"description": "Store not found"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Store"], "summary": "Add a category to store's favorite categories", "description": "Adds a category to the list of the store's favorite categories.", "operationId": "addCategoryToFavorite", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["category_id"], "properties": {"category_id": {"type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"201": {"description": "Category added to favorites successfully", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}}, "type": "object"}}}}, "404": {"description": "Store or category not found"}, "422": {"description": "Validation error"}}, "security": [{"bearerAuth": []}]}}, "/api/store-item": {"post": {"tags": ["Store"], "summary": "Create a new item for a store", "description": "Create a new item with the provided details for a specific store.", "operationId": "089233b5f963de92b7ce9a4d26b2fa86", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"title": {"type": "string", "example": "Sample Item"}, "description": {"type": "string", "example": "This is a sample item."}, "price": {"type": "number", "format": "float", "example": 99.99}, "condition": {"type": "string", "example": "new"}, "quantity": {"type": "integer", "example": 10}, "brand_id": {"type": "integer", "example": 1}, "category_id": {"type": "integer", "example": 1}, "category_item_details": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "value": {"type": "string", "example": "Sample value"}}, "type": "object"}}, "images": {"type": "array", "items": {"type": "string", "format": "binary"}}}, "type": "object"}}}}, "responses": {"201": {"description": "Successful creation"}, "400": {"description": "Validation error"}, "500": {"description": "Failed to create item"}}}}, "/api/store-types": {"get": {"tags": ["Store"], "summary": "Get store types", "description": "Returns a list of store types.", "operationId": "getStoreTypes", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "type": "object"}}}}}}}}, "/api/stores/{storeId}/liked-items": {"get": {"tags": ["Store"], "summary": "Get liked items for a specific store", "description": "Returns a list of liked items for a specific store.", "operationId": "getLikedItemsForStore", "parameters": [{"name": "storeId", "in": "path", "description": "Store ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "price": {"type": "number", "format": "float"}}, "type": "object"}}}}}, "404": {"description": "Store not found"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/follow/{storeId}": {"put": {"tags": ["Store"], "summary": "Follow or unfollow a store", "description": "Toggle follow status for a store by ID.", "operationId": "followStore", "parameters": [{"name": "storeId", "in": "path", "description": "Store ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Store follow status toggled successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Store not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/stores/promoted-recommended": {"get": {"tags": ["Store"], "summary": "Get recommended stores", "description": "Get recommended stores (promoted) in a 4x3 display format. If fewer than 12 promoted stores, fill with stores having the most followers.", "operationId": "708a5a5ec5ac0c00afc12226f7582eb2", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Store Name"}, "description": {"type": "string", "example": "Store Description"}, "followers_count": {"type": "integer", "example": 100}, "is_promoted": {"type": "boolean", "example": true}, "images": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "url": {"type": "string", "example": "http://example.com/image.jpg"}}, "type": "object"}}}, "type": "object"}}}}}, "401": {"description": "Unauthenticated"}, "500": {"description": "Server error"}}}}, "/api/stores/general-recommended": {"get": {"tags": ["Store"], "summary": "Get general recommended stores", "description": "Get promoted stores and top stores by number of followers.", "operationId": "67c3a54bfcccbb82d34d081aa8522f67", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Store Name"}, "description": {"type": "string", "example": "Store Description"}, "followers_count": {"type": "integer", "example": 100}, "is_promoted": {"type": "boolean", "example": true}, "is_followed": {"type": "boolean", "example": false}}, "type": "object"}}}}}, "500": {"description": "Server error"}}}}, "/api/stores/add-favorites-category": {"post": {"tags": ["Store"], "summary": "Add favorite category", "description": "Add a category to the authenticated user's store's favorite categories.", "operationId": "27b04fd2b0650fa16db9b05491f4e599", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"category_id": {"type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"201": {"description": "Category added to favorites successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Category added to favorites successfully"}}, "type": "object"}}}}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "User does not have a store"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/info": {"get": {"tags": ["Store"], "summary": "Get store information", "description": "Returns store information including images, number of followers, number of items, and items.", "operationId": "getStoreInfo", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "description": {"type": "string"}, "images": {"type": "array", "items": {"type": "string"}}, "followers_count": {"type": "integer"}, "items_count": {"type": "integer"}, "items": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "price": {"type": "number", "format": "float"}}, "type": "object"}}}, "type": "object"}}}}, "404": {"description": "Store not found"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/{storeId}/favorites-categories": {"get": {"tags": ["Store"], "summary": "Get favorite categories of a store by store ID", "description": "Returns a list of favorite categories for the specified store.", "operationId": "getFavoriteCategoriesByStoreId", "parameters": [{"name": "storeId", "in": "path", "description": "Store ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "title_en": {"type": "string"}, "title_ar": {"type": "string"}, "title_fr": {"type": "string"}, "parent": {"properties": {"id": {"type": "integer"}, "title_en": {"type": "string"}, "title_ar": {"type": "string"}, "title_fr": {"type": "string"}}, "type": "object"}}, "type": "object"}}}}}, "404": {"description": "Store not found"}}, "security": [{"bearerAuth": []}]}}, "/api/items/favorite-category/{categoryId}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get items by favorite category ID", "description": "Returns a list of items for the authenticated user by favorite category ID.", "operationId": "getItemsByFavoriteCategoryId", "parameters": [{"name": "categoryId", "in": "path", "description": "Favorite Category ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "price": {"type": "number", "format": "float"}, "description": {"type": "string"}, "condition": {"type": "string"}, "quantity": {"type": "integer"}, "brand_id": {"type": "integer"}, "category_id": {"type": "integer"}, "images": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "url": {"type": "string"}}, "type": "object"}}}, "type": "object"}}}}}, "404": {"description": "Items not found"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/{storeId}/items/category/{categoryId}": {"get": {"tags": ["Store"], "summary": "Get items by category ID for a specific store", "description": "Returns a list of items for a specific store by category ID.", "operationId": "getItemsByCategoryId", "parameters": [{"name": "storeId", "in": "path", "description": "Store ID", "required": true, "schema": {"type": "integer"}}, {"name": "categoryId", "in": "path", "description": "Category ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "price": {"type": "number", "format": "float"}, "description": {"type": "string"}, "condition": {"type": "string"}, "quantity": {"type": "integer"}, "brand_id": {"type": "integer"}, "category_id": {"type": "integer"}, "images": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "url": {"type": "string"}}, "type": "object"}}}, "type": "object"}}}}}, "404": {"description": "Items not found"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/{storeId}/items": {"get": {"tags": ["Store"], "summary": "Get items by store ID", "description": "Returns a list of items for a specific store by store ID.", "operationId": "getItemsByStoreId", "parameters": [{"name": "storeId", "in": "path", "description": "Store ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "price": {"type": "number", "format": "float"}, "description": {"type": "string"}, "condition": {"type": "string"}, "quantity": {"type": "integer"}, "brand_id": {"type": "integer"}, "category_id": {"type": "integer"}, "images": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "url": {"type": "string"}}, "type": "object"}}}, "type": "object"}}}}}, "404": {"description": "Items not found"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/my-store/items": {"get": {"tags": ["Store"], "summary": "Get items for the authenticated user's store", "description": "Returns a list of items for the authenticated user's store.", "operationId": "getMyStoreItems", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "price": {"type": "number", "format": "float"}, "description": {"type": "string"}, "condition": {"type": "string"}, "quantity": {"type": "integer"}, "brand_id": {"type": "integer"}, "category_id": {"type": "integer"}, "images": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "url": {"type": "string"}}, "type": "object"}}}, "type": "object"}}}}}, "404": {"description": "Items not found"}}, "security": [{"bearerAuth": []}]}}, "/followed-stores": {"get": {"tags": ["Stores"], "summary": "Get stores followed by the authenticated user", "operationId": "cd4939631da3785f05fe3eca4a53136d", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Store Name"}, "description": {"type": "string", "example": "Store Description"}, "followers_count": {"type": "integer", "example": 100}}, "type": "object"}}}}}, "401": {"description": "Unauthenticated"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/favorite-category/{categoryId}": {"delete": {"tags": ["Store"], "summary": "Delete favorite category", "description": "Delete a favorite category from the authenticated user's store.", "operationId": "c75ebb835d45c40ec95cc94a29bac6b9", "parameters": [{"name": "categoryId", "in": "path", "description": "Category ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Category removed from favorites successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Category removed from favorites successfully"}}, "type": "object"}}}}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Store or category not found"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/{id}": {"get": {"tags": ["Store"], "summary": "Get store details", "description": "Get detailed information about a store, including whether the authenticated user follows it.", "operationId": "c389f6ce0433e87ed10727f45e846c33", "parameters": [{"name": "id", "in": "path", "description": "Store ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Store Name"}, "description": {"type": "string", "example": "Store Description"}, "followers_count": {"type": "integer", "example": 100}, "is_promoted": {"type": "boolean", "example": true}, "is_followed": {"type": "boolean", "example": false}, "images": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "url": {"type": "string", "example": "http://example.com/image.jpg"}}, "type": "object"}}}, "type": "object"}}}}, "401": {"description": "Unauthenticated"}, "404": {"description": "Store not found"}, "500": {"description": "Server error"}}}}, "/api/store/orders": {"get": {"tags": ["Orders"], "summary": "Get all orders for the authenticated user's store", "description": "Returns a list of orders with their items and images for the store associated with the authenticated user.", "operationId": "52dc3aea93a6c4e738e70c5ad64ef4db", "responses": {"200": {"description": "List of store orders", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"description": "Order ID", "type": "integer"}, "store_id": {"description": "Store ID", "type": "integer"}, "status": {"description": "Order status", "type": "string"}, "delivery_charge": {"description": "Delivery charge", "type": "number", "format": "float"}, "delivery_charge_id": {"description": "Delivery charge ID", "type": "integer"}, "location_id": {"description": "Location ID", "type": "integer"}, "user_id": {"description": "User ID", "type": "integer"}, "created_at": {"description": "Creation timestamp", "type": "string", "format": "date-time"}, "updated_at": {"description": "Update timestamp", "type": "string", "format": "date-time"}, "items": {"type": "array", "items": {"properties": {"id": {"description": "Item ID", "type": "integer"}, "title": {"description": "Item title", "type": "string"}, "description": {"description": "Item description", "type": "string"}, "price": {"description": "Item price", "type": "number", "format": "float"}, "sold_out": {"description": "Is the item sold out", "type": "boolean"}, "condition": {"description": "Item condition", "type": "string"}, "status": {"description": "Item status", "type": "string"}, "rejection_reason": {"description": "Rejection reason if any", "type": "string", "nullable": true}, "quantity": {"description": "Item quantity in the order", "type": "integer"}, "brand_id": {"description": "Brand ID", "type": "integer"}, "user_id": {"description": "User ID", "type": "integer"}, "store_id": {"description": "Store ID", "type": "integer"}, "category_id": {"description": "Category ID", "type": "integer"}, "created_at": {"description": "Creation timestamp", "type": "string", "format": "date-time"}, "updated_at": {"description": "Update timestamp", "type": "string", "format": "date-time"}, "is_promoted": {"description": "Is the item promoted", "type": "boolean"}, "images": {"type": "array", "items": {"properties": {"id": {"description": "Image ID", "type": "integer"}, "type": {"description": "Image type", "type": "string"}, "url": {"description": "Image URL", "type": "string"}, "aspect_ratio": {"description": "Image aspect ratio", "type": "string"}, "created_at": {"description": "Creation timestamp", "type": "string", "format": "date-time"}, "updated_at": {"description": "Update timestamp", "type": "string", "format": "date-time"}}, "type": "object"}}}, "type": "object"}}}, "type": "object"}}}}}, "404": {"description": "Store or orders not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Store not found"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/store/item/{itemId}/promotion": {"put": {"tags": ["Store"], "summary": "Set a promotion for a specific item in the store", "description": "Sets a promotion percentage for a specific item in the user's store.", "operationId": "setPromotionForItemInMyStore", "parameters": [{"name": "itemId", "in": "path", "description": "ID of the item", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["promotion_percentage"], "properties": {"promotion_percentage": {"description": "The promotion percentage to be set", "type": "number", "format": "float", "example": 15.5}}, "type": "object"}}}}, "responses": {"200": {"description": "Promotion set successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Promotion set successfully"}}, "type": "object"}}}}, "404": {"description": "Store or item not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Store not found or Item not found"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Store"], "summary": "Remove a promotion from a specific item in the store", "description": "Removes the promotion for a specific item in the user's store.", "operationId": "removePromotionForItemInMyStore", "parameters": [{"name": "itemId", "in": "path", "description": "ID of the item", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Promotion removed successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Promotion removed successfully"}}, "type": "object"}}}}, "404": {"description": "Store or item not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Store not found or Item not found"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/stores/my-store/promotions": {"post": {"tags": ["Store"], "summary": "Add a promotion for the authenticated user's store", "description": "Add a promotion for the authenticated user's store.", "operationId": "addPromotionForMyStore", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["promotion_percentage"], "properties": {"promotion_percentage": {"description": "Promotion percentage", "type": "number", "format": "float", "example": 15.5}}, "type": "object"}}}}, "responses": {"200": {"description": "Promotion added successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Promotion added successfully"}}, "type": "object"}}}}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Store not found"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Store"], "summary": "Remove a promotion from the authenticated user's store", "description": "Remove a promotion from the authenticated user's store.", "operationId": "removePromotionForMyStore", "responses": {"200": {"description": "Promotion removed successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Promotion removed successfully"}}, "type": "object"}}}}, "404": {"description": "Store not found"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/payment-providers/add": {"post": {"tags": ["Store Payment Providers"], "summary": "Add a new payment provider to store", "description": "Add a new payment provider with payment details to the authenticated user's store", "operationId": "5a5de5bf1178aaf2cf013adf4810263f", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["provider_id"], "properties": {"provider_id": {"description": "ID of the payment provider", "type": "integer"}, "payment_code": {"description": "Payment code for the provider", "type": "string", "nullable": true}, "phone_number": {"description": "Phone number for the provider", "type": "string", "nullable": true}}, "type": "object"}}}}, "responses": {"201": {"description": "Provider added successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Payment provider added successfully"}, "data": {"properties": {"id": {"type": "integer"}, "provider_id": {"type": "integer"}, "provider_name": {"type": "string"}, "provider_logo": {"type": "string"}, "payment_code": {"type": "string"}, "phone_number": {"type": "string"}, "is_active": {"type": "boolean"}}, "type": "object"}}, "type": "object"}}}}, "400": {"description": "Provider already exists for this store", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "This payment provider is already added to your store"}}, "type": "object"}}}}, "404": {"description": "Store not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Store not found"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"errors": {"type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/stores/payment-providers": {"get": {"tags": ["Store Payment Providers"], "summary": "Get store payment providers", "description": "Get all payment providers for the authenticated user's store with their payment codes and phone numbers", "operationId": "6e6bed2e029e01646a55a2ba2a280667", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "provider_id": {"type": "integer"}, "provider_name": {"type": "string"}, "provider_logo": {"type": "string"}, "payment_code": {"type": "string"}, "phone_number": {"type": "string"}}, "type": "object"}}}}}, "404": {"description": "Store not found"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Store Payment Providers"], "summary": "Update store payment providers", "description": "Update payment providers information for the store in bulk", "operationId": "f4579d802db376d0c91d52ae4b4e9388", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"provider_id": {"type": "integer"}, "payment_code": {"type": "string", "nullable": true}, "phone_number": {"type": "string", "nullable": true}}, "type": "object"}}}}}, "responses": {"200": {"description": "Providers updated successfully"}, "404": {"description": "Store not found"}, "422": {"description": "Validation error"}}, "security": [{"bearerAuth": []}]}}, "/api/stores/{store_id}/payment-providers/{provider_id}": {"get": {"tags": ["Store Payment Providers"], "summary": "Get specific provider details", "description": "Get payment code and phone number for a specific provider of a store", "operationId": "e8ad0b3d4c3c3d46e050250862b109e3", "parameters": [{"name": "store_id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "provider_id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"payment_code": {"type": "string"}, "phone_number": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Provider not found for this store"}}}}, "/api/store/payments": {"get": {"tags": ["Store Payment Verification"], "summary": "Get store's payment proofs", "description": "Retrieve all payment proofs for the authenticated user's store with pagination", "operationId": "getStorePaymentProofs", "responses": {"200": {"description": "Payment proofs retrieved successfully", "content": {"application/json": {"schema": {"properties": {"data": {"properties": {"current_page": {"type": "integer", "example": 1}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "store_id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 1}, "provider_id": {"type": "integer", "example": 1}, "order_id": {"type": "integer", "example": 1}, "amount": {"type": "number", "format": "float", "example": 100.5}, "screenshot": {"type": "string", "example": "path/to/screenshot.jpg"}, "status": {"type": "string", "example": "PENDING"}, "reference_number": {"type": "string", "example": "REF123"}, "to_store": {"type": "boolean", "example": true}, "user": {"properties": {"id": {"type": "integer", "example": 1}, "firstname": {"type": "string", "example": "<PERSON>"}, "lastname": {"type": "string", "example": "<PERSON><PERSON>"}}, "type": "object"}, "order": {"properties": {"id": {"type": "integer", "example": 1}, "status": {"type": "string", "example": "pending"}}, "type": "object"}, "provider": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "PayPal"}}, "type": "object"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "first_page_url": {"type": "string"}, "from": {"type": "integer"}, "last_page": {"type": "integer"}, "last_page_url": {"type": "string"}, "per_page": {"type": "integer"}, "to": {"type": "integer"}, "total": {"type": "integer"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "Store not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Store not found"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/store/payments/{id}/verify": {"post": {"tags": ["Store Payment Verification"], "summary": "Verify a payment proof", "description": "Approve or reject a payment proof for the authenticated user's store", "operationId": "verifyStorePayment", "parameters": [{"name": "id", "in": "path", "description": "Payment proof ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["status"], "properties": {"status": {"description": "Payment verification status", "type": "string", "enum": ["APPROVED", "REJECTED"]}, "rejection_reason": {"description": "Required when status is REJECTED", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Payment verification completed successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Payment verification completed"}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "status": {"type": "string", "example": "APPROVED"}, "rejection_reason": {"type": "string", "nullable": true}, "verified_by": {"type": "integer", "example": 1}, "verified_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"errors": {"type": "object"}}, "type": "object"}}}}, "404": {"description": "Store or payment proof not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Store not found"}}, "type": "object"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Failed to verify payment"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/users/location": {"get": {"tags": ["User"], "summary": "Get authenticated user location", "description": "Retrieve the location of the authenticated user.", "operationId": "getAuthenticatedUserLocation", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer"}, "address": {"type": "string"}, "latitude": {"type": "number", "format": "float"}, "longitude": {"type": "number", "format": "float"}, "name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}}, "404": {"description": "Location not found"}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["User"], "summary": "Update authenticated user location", "description": "Update the location of the authenticated user.", "operationId": "updateAuthenticatedUserLocation", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["address", "latitude", "longitude", "name"], "properties": {"address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}, "name": {"type": "string", "example": "Home"}}, "type": "object"}}}}, "responses": {"200": {"description": "Location updated successfully", "content": {"application/json": {"schema": {"properties": {"location_id": {"type": "integer"}}, "type": "object"}}}}, "422": {"description": "Validation error"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["User"], "summary": "Store authenticated user location", "description": "Store a new location for the authenticated user.", "operationId": "storeAuthenticatedUserLocation", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["address", "latitude", "longitude", "name"], "properties": {"address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}, "name": {"type": "string", "example": "Home"}}, "type": "object"}}}}, "responses": {"201": {"description": "Location stored successfully", "content": {"application/json": {"schema": {"properties": {"location_id": {"type": "integer"}}, "type": "object"}}}}, "422": {"description": "Validation error"}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"DeliveryCharge": {"title": "Delivery Charge", "description": "Delivery Charge model", "properties": {"id": {"description": "ID of the delivery charge", "type": "integer"}, "amount": {"description": "Amount of the delivery charge", "type": "number", "format": "float"}, "created_at": {"description": "Creation timestamp", "type": "string", "format": "date-time"}, "updated_at": {"description": "Update timestamp", "type": "string", "format": "date-time"}}, "type": "object"}}, "securitySchemes": {"bearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "description": "Enter JWT token with Bearer prefix (Bearer <token>)"}}}, "tags": [{"name": "Payment Proof", "description": "API Endpoints for payment proof management"}, {"name": "Store Payment Verification", "description": "API Endpoints for store payment verification management"}, {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON>"}, {"name": "User", "description": "User"}, {"name": "Payments", "description": "Payments"}, {"name": "Brand", "description": "Brand"}, {"name": "Category", "description": "Category"}, {"name": "DeliveryCharge", "description": "DeliveryCharge"}, {"name": "DeviceToken", "description": "DeviceToken"}, {"name": "Payment Providers", "description": "Payment Providers"}, {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON>"}, {"name": "Search", "description": "Search"}, {"name": "Store", "description": "Store"}, {"name": "Notifications", "description": "Notifications"}, {"name": "Discussions", "description": "Discussions"}, {"name": "Messages", "description": "Messages"}, {"name": "Order", "description": "Order"}, {"name": "Category Prices", "description": "Category Prices"}, {"name": "Admin", "description": "Admin"}, {"name": "Profile", "description": "Profile"}, {"name": "Stores", "description": "Stores"}, {"name": "Orders", "description": "Orders"}, {"name": "Store Payment Providers", "description": "Store Payment Providers"}], "security": [{"bearerAuth": []}]}