<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Boutigak — Politique de Confidentialité & CGU</title>
  <meta name="theme-color" content="#005b96" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.12.2/lottie.min.js"></script>
  <style>
    :root{
      --brand:#005b96;
      --text-900:#0b1220;
      --text-700:#2b3445;
      --bg:#f7f9fc;
      --card:#ffffff;
      --line:#e7edf4;
      --radius:16px;
      --container:min(1120px,92vw);
      --shadow:0 18px 40px rgba(0,0,0,.08);
      --title:#027782;
    }
    *{box-sizing:border-box}
    html,body{margin:0;background:var(--bg);color:var(--text-900);font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial}
    #splash{position:fixed; inset:0; z-index:9999; display:grid; place-items:center; background:#fff}
    #splash .inner{width:min(420px,80vw); aspect-ratio:1/1; display:grid; place-items:center}
    .hidden{ opacity:0; pointer-events:none; transition:opacity .35s ease }
    header{ position:sticky; top:0; backdrop-filter:saturate(1.5) blur(8px); background:rgba(255,255,255,.65); border-bottom:1px solid var(--line); z-index:10 }
    .nav{ width:var(--container); margin-inline:auto; display:flex; align-items:center; justify-content:space-between; padding:14px 4px; gap:16px }
    .logo-overlay{ position:relative; display:inline-block; width:160px; height:40px }
    .logo-overlay img{ width:100%; height:100%; object-fit:contain }
    .lang{ display:flex; align-items:center; gap:10px; background:var(--card); border:1px solid var(--line); padding:8px 12px; border-radius:999px }
    .flag-emoji{font-size:18px; line-height:1; display:inline-flex; align-items:center}
    .lang select{ appearance:none; border:none; background:transparent; outline:none; font-weight:700; cursor:pointer }
    main{ width:var(--container); margin:28px auto; padding:0 2px }
    .page-title{ font-size:clamp(22px,3.2vw,36px); font-weight:900; line-height:1.25; margin:8px 0 4px; text-align:center }
    .page-sub{ color:var(--text-700); text-align:center; margin:0 0 22px }
    .meta{ text-align:center; font-size:14px; color:var(--text-700); margin-bottom:22px }
    .card{ background:var(--card); border:1px solid var(--line); border-radius:var(--radius); box-shadow:var(--shadow); padding:clamp(16px,2vw,24px); margin-bottom:18px }
    .card h2{ margin:6px 0 10px; font-size:clamp(18px,2.4vw,26px) }
    .card h3{ margin:14px 0 6px; font-size:clamp(16px,2.1vw,22px) }
    .page-title,.card h2,.card h3{ color:var(--title) }
    .card p{ margin:8px 0; line-height:1.65 }
    .card ul{ margin:8px 0 8px 1.1em }
    .card li{ margin:6px 0; line-height:1.6 }
    .toc{ display:grid; grid-template-columns:repeat(auto-fit,minmax(260px,1fr)); gap:12px }
    .toc a{ text-decoration:none; color:inherit; border:1px solid var(--line); background:rgba(11,18,32,.02); padding:12px 14px; border-radius:12px; display:block }
    .toc a:hover{ background:rgba(11,18,32,.045) }
    footer{ text-align:center; color:var(--text-700); border-top:1px solid var(--line); padding:22px 0 40px }
    [dir="rtl"] .card ul{ margin:8px 1.1em 8px 0 }
    [hidden]{ display:none !important }
  </style>
</head>
<body>
  <!-- Splash Lottie -->
  <div id="splash" aria-label="Loading animation">
    <div class="inner"><div id="lottie" style="width:100%;height:100%"></div></div>
  </div>
  <!-- App -->
  <div id="app" style="visibility:hidden">
    <header>
      <nav class="nav">
        <div class="logo-overlay">
          <img src="{{ asset('assets/boutigak-logo.png') }}" alt="Boutigak" />
        </div>
        <div class="lang">
          <span id="flag" class="flag-emoji" aria-hidden="true">🇫🇷</span>
          <select id="langSelect" aria-label="Change language">
            <option value="ar">العربية</option>
            <option value="fr" selected>Français</option>
            <option value="en">English</option>
          </select>
        </div>
      </nav>
    </header>
    <main>
      <h1 id="title" class="page-title">Politique de Confidentialité & Conditions d'Utilisation</h1>
      <p id="subtitle" class="page-sub">Lisez comment nous protégeons vos données et les règles d'utilisation de Boutigak.</p>
      <div class="meta"><span id="lastUpdated"></span></div>
      <div class="card toc" id="toc">
        <a href="#privacy">🔐 <span data-i18n="toc_privacy">Politique de Confidentialité</span></a>
        <a href="#terms">📜 <span data-i18n="toc_terms">Conditions Générales d'Utilisation</span></a>
      </div>
       <!-- ===================== FR ===================== -->
      <article class="card" data-lang="fr">
        <h2 id="privacy">Politique de Confidentialité - Boutigak</h2>
        <h3>1. Informations collectées</h3>
        <p>Boutigak collecte les informations suivantes :</p>
        <ul>
          <li>Nom et prénom</li>
          <li>Genre</li>
          <li>Numéro de téléphone</li>
          <li>Localisation géographique (uniquement pour permettre aux boutiques de livrer les commandes)</li>
          <li>Code commerçant pour les wallets (afin de permettre le paiement direct aux commerçants)</li>
          <li>Informations techniques (système d'exploitation, pages visitées, statistiques d'utilisation comme la durée des sessions et le comportement de navigation dans l'application)</li>
        </ul>
        <h3>2. Utilisation des données</h3>
        <p>Les informations collectées servent uniquement à :</p>
        <ul>
          <li>La gestion des comptes utilisateurs</li>
          <li>L'amélioration continue de l'application et des services proposés</li>
          <li>Faciliter les transactions entre acheteurs et commerçants</li>
          <li>Comprendre les habitudes d'utilisation pour améliorer l'expérience utilisateur</li>
        </ul>
        <h3>3. Partage avec des tiers</h3>
        <p>Les données personnelles ne sont partagées avec aucun tiers externe. Toutefois, nous utilisons les services suivants pour améliorer l'expérience utilisateur :</p>
        <ul>
          <li>Firebase pour les notifications push</li>
          <li>Matterport via webview (pour la visualisation)</li>
          <li>SnapKit pour partager des annonces sur Snapchat</li>
          <li>Google Maps pour l'affichage et la gestion de la localisation</li>
        </ul>
        <p>Ces services ont leurs propres politiques de confidentialité.</p>
        <h3>4. Stockage et sécurité des données</h3>
        <p>Vos données sont stockées sur des serveurs privés sécurisés. Nous appliquons un cryptage avancé et un accès strictement limité pour garantir la sécurité de vos informations.</p>
        <h3>5. Droits des utilisateurs</h3>
        <p>Vous pouvez à tout moment consulter, modifier ou supprimer vos données directement depuis votre compte dans l'application. Vous pouvez également demander la suppression définitive de votre compte et de toutes les données associées.</p>
        <p>Une fois votre demande validée, toutes les données personnelles associées à votre compte seront supprimées de nos serveurs dans un délai maximal de 10 jours, sauf si une obligation légale impose leur conservation pour une durée plus longue.</p>
      </article>

      <article class="card" data-lang="fr">
        <h2 id="terms">Conditions Générales d'Utilisation - Boutigak</h2>
        <h3>1. Acceptation des conditions</h3>
        <p>En utilisant l’application Boutigak, vous acceptez pleinement ces conditions générales d'utilisation.</p>
        <h3>2. Fonctionnement de l'application</h3>
        <p>Boutigak est une marketplace permettant aux utilisateurs :</p>
        <ul>
          <li>D'ouvrir une boutique en ligne (réservée uniquement aux commerçants établis en Mauritanie)</li>
          <li>De publier des annonces pour vendre des produits</li>
        </ul>
        <h3>3. Responsabilité</h3>
        <p>Boutigak agit uniquement comme plateforme de mise en relation. Boutigak décline toute responsabilité :</p>
        <ul>
          <li>Sur la qualité, la conformité ou la légalité des produits ou services vendus</li>
          <li>En cas de litige entre acheteur et vendeur</li>
          <li>Concernant les paiements effectués directement vers les boutiques par le biais des wallets ou autres moyens</li>
          <li>Concernant la gestion, la rapidité ou la qualité du traitement des commandes</li>
        </ul>
        <p>Chaque boutique est entièrement responsable de la gestion de ses commandes et du service client associé. Boutigak se réserve le droit de fermer immédiatement toute boutique ne répondant pas rapidement aux commandes ou présentant un traitement de mauvaise qualité.</p>
        <h3>4. Contenu interdit et fermeture de compte</h3>
        <p>Il est strictement interdit aux utilisateurs ou boutiques de publier ou diffuser sur la plateforme :</p>
        <ul>
          <li>Des contenus à caractère politique, haineux, violent, discriminatoire, sexuel ou tout contenu contraire à la morale et aux bonnes mœurs</li>
          <li>Des produits appartenant ou relevant d'une autre boutique sans autorisation</li>
        </ul>
        <p>Boutigak se réserve le droit de fermer immédiatement, sans préavis ni remboursement, tout compte utilisateur ou boutique violant ces règles, ou suspecté d'être utilisé par plusieurs personnes sans acquitter les frais d'abonnement requis pour chaque utilisateur.</p>
        <h3>5. Abonnement pour boutiques et tarifs des annonces</h3>
        <p>L’ouverture d’une boutique nécessite la souscription d’un abonnement payant. Les tarifs sont déterminés après prise de contact et en fonction de la taille et des besoins spécifiques de la boutique. La publication d'annonces individuelles est soumise à une tarification variable selon la catégorie du produit vendu (maison, voiture, smartphone, etc.). Les tarifs exacts sont disponibles dans l'application ou sur demande directe.</p>
        <h3>6. Politique de remboursement</h3>
        <p>Aucun remboursement n’est possible après paiement des frais d’abonnement ou toute autre transaction effectuée sur la plateforme.</p>
        <h3>7. Litiges et réclamations</h3>
        <p>En cas de litige entre une boutique et un acheteur, Boutigak pourra intervenir en tant qu'intermédiaire afin de faciliter une résolution amiable. Toute réclamation doit être soumise via notre service client dans un délai raisonnable après la transaction concernée. Cette clause ne s'applique pas aux annonces individuelles.</p>
        <h3>8. Propriété intellectuelle</h3>
        <p>Tous les contenus présents sur Boutigak sont protégés par les lois sur la propriété intellectuelle. Toute utilisation non autorisée, reproduction ou distribution est strictement interdite.</p>
        <h3>9. Modification des conditions</h3>
        <p>Boutigak se réserve le droit de modifier ces conditions à tout moment. Les utilisateurs seront informés de tout changement important affectant leurs droits ou obligations.</p>
        <h3>10. Restrictions d'utilisation</h3>
        <p>L'ouverture d'une boutique est exclusivement réservée aux commerçants opérant en Mauritanie.</p>
        <h3>11. Loi applicable et juridiction compétente</h3>
        <p>Ces conditions générales d’utilisation sont régies par le droit mauritanien. En cas de litige, seules les juridictions de Mauritanie seront compétentes.</p>
      </article>

      <!-- ===================== EN ===================== -->
      <article class="card" data-lang="en" hidden>
        <h2 id="privacy-en">Privacy Policy – Boutigak</h2>
        <h3>1. Information We Collect</h3>
        <p>Boutigak collects the following information:</p>
        <ul>
          <li>First and last name</li>
          <li>Gender</li>
          <li>Phone number</li>
          <li>Geographic location (only to enable stores to deliver orders)</li>
          <li>Merchant code for wallets (to enable direct payments to merchants)</li>
          <li>Technical information such as operating system, pages visited, and usage statistics like session length and in‑app navigation behavior</li>
        </ul>
        <h3>2. How We Use Data</h3>
        <p>The information collected is used solely to:</p>
        <ul>
          <li>Manage user accounts</li>
          <li>Continuously improve the app and services we offer</li>
          <li>Facilitate transactions between buyers and merchants</li>
          <li>Understand usage patterns to enhance user experience</li>
        </ul>
        <h3>3. Sharing with Third Parties</h3>
        <p>Personal data is not shared with any external third party. However, we use the following services to improve the user experience:</p>
        <ul>
          <li>Firebase for push notifications</li>
          <li>Matterport via webview (for viewing)</li>
          <li>SnapKit to share listings on Snapchat</li>
          <li>Google Maps for displaying and managing location</li>
        </ul>
        <p>These services have their own privacy policies.</p>
        <h3>4. Data Storage & Security</h3>
        <p>Your data is stored on secure private servers. We apply advanced encryption and strictly controlled access to protect your information.</p>
        <h3>5. Your Rights</h3>
        <p>You can view, modify, or delete your data at any time directly from your account in the app. You may also request permanent deletion of your account and all associated data.</p>
        <p>Once your request is validated, all personal data associated with your account will be deleted from our servers within a maximum of 10 days, unless a legal obligation requires longer retention.</p>
      </article>

      <article class="card" data-lang="en" hidden>
        <h2 id="terms-en">Terms of Service – Boutigak</h2>
        <h3>1. Acceptance of the Terms</h3>
        <p>By using the Boutigak app, you fully accept these Terms of Service.</p>
        <h3>2. How the App Works</h3>
        <p>Boutigak is a marketplace that allows users to:</p>
        <ul>
          <li>Open an online store (reserved for merchants established in Mauritania)</li>
          <li>Publish listings to sell products</li>
        </ul>
        <h3>3. Liability</h3>
        <p>Boutigak acts solely as a matchmaking platform and disclaims all liability:</p>
        <ul>
          <li>For the quality, conformity, or legality of products or services sold</li>
          <li>In the event of a dispute between buyer and seller</li>
          <li>For payments made directly to stores via wallets or other means</li>
          <li>For the management, speed, or quality of order processing</li>
        </ul>
        <p>Each store is fully responsible for managing its orders and customer service. Boutigak reserves the right to immediately close any store that does not respond promptly to orders or provides poor‑quality service.</p>
        <h3>4. Prohibited Content & Account Closure</h3>
        <p>Users or stores are strictly prohibited from posting or distributing on the platform:</p>
        <ul>
          <li>Content that is political, hateful, violent, discriminatory, sexual, or otherwise contrary to morality and public decency</li>
          <li>Products belonging to or listed by another store without authorization</li>
        </ul>
        <p>Boutigak reserves the right to close, without notice or refund, any user account or store that violates these rules, or is suspected of being used by multiple people without paying the required subscription fees for each user.</p>
        <h3>5. Store Subscriptions & Listing Fees</h3>
        <p>Opening a store requires a paid subscription. Pricing is determined after contact and depends on the size and specific needs of the store. Individual listings are subject to variable fees depending on the product category (home, car, smartphone, etc.). Exact pricing is available in the app or on request.</p>
        <h3>6. Refund Policy</h3>
        <p>No refunds are possible after payment of subscription fees or any other transaction on the platform.</p>
        <h3>7. Disputes & Claims</h3>
        <p>In the event of a dispute between a store and a buyer, Boutigak may act as an intermediary to facilitate an amicable resolution. Any claim must be submitted via our customer service within a reasonable time after the relevant transaction. This clause does not apply to individual listings.</p>
        <h3>8. Intellectual Property</h3>
        <p>All content on Boutigak is protected by intellectual property laws. Any unauthorized use, reproduction, or distribution is strictly prohibited.</p>
        <h3>9. Changes to the Terms</h3>
        <p>Boutigak reserves the right to modify these terms at any time. Users will be informed of any material changes affecting their rights or obligations.</p>
        <h3>10. Usage Restrictions</h3>
        <p>Opening a store is strictly reserved for merchants operating in Mauritania.</p>
        <h3>11. Governing Law & Jurisdiction</h3>
        <p>These Terms of Service are governed by Mauritanian law. In case of dispute, the courts of Mauritania have exclusive jurisdiction.</p>
      </article>

      <!-- ===================== AR ===================== -->
      <article class="card" data-lang="ar" dir="rtl" hidden>
        <h2 id="privacy-ar">سياسة الخصوصية – Boutigak</h2>
        <h3>1. البيانات التي نجمعها</h3>
        <p>يجمع Boutigak المعلومات التالية:</p>
        <ul>
          <li>الاسم واللقب</li>
          <li>الجنس</li>
          <li>رقم الهاتف</li>
          <li>الموقع الجغرافي (فقط لتمكين المتاجر من توصيل الطلبات)</li>
          <li>رمز التاجر لمحافظ الدفع (لتمكين الدفع المباشر للتجار)</li>
          <li>معلومات تقنية مثل نظام التشغيل والصفحات التي تمت زيارتها وإحصاءات الاستخدام مثل مدة الجلسات وسلوك التصفح داخل التطبيق</li>
        </ul>
        <h3>2. كيفية استخدام البيانات</h3>
        <p>تُستخدم المعلومات المجمَّعة حصريًا من أجل:</p>
        <ul>
          <li>إدارة حسابات المستخدمين</li>
          <li>التحسين المستمر للتطبيق والخدمات المقدمة</li>
          <li>تسهيل المعاملات بين المشترين والتجار</li>
          <li>فهم عادات الاستخدام لتحسين تجربة المستخدم</li>
        </ul>
        <h3>3. المشاركة مع أطراف ثالثة</h3>
        <p>لا تتم مشاركة البيانات الشخصية مع أي طرف خارجي. ومع ذلك نستخدم الخدمات التالية لتحسين تجربة المستخدم:</p>
        <ul>
          <li>Firebase للإشعارات الفورية</li>
          <li>Matterport عبر WebView (لعرض المحتوى)</li>
          <li>SnapKit لمشاركة الإعلانات على سناب شات</li>
          <li>خرائط Google للعرض وإدارة الموقع</li>
        </ul>
        <p>لهذه الخدمات سياسات خصوصية خاصة بها.</p>
        <h3>4. تخزين البيانات وأمانها</h3>
        <p>تُخزن بياناتك على خوادم خاصة آمنة. نطبق تشفيرًا متقدمًا وضوابط وصول صارمة لحماية معلوماتك.</p>
        <h3>5. حقوق المستخدم</h3>
        <p>يمكنك في أي وقت عرض بياناتك أو تعديلها أو حذفها مباشرة من حسابك داخل التطبيق. كما يمكنك طلب الحذف النهائي لحسابك وجميع البيانات المرتبطة به.</p>
        <p>بعد اعتماد طلبك، سيتم حذف جميع بياناتك الشخصية من خوادمنا خلال مدة لا تتجاوز 10 أيام، ما لم تُلزمنا القوانين بالاحتفاظ بها لمدة أطول.</p>
      </article>

      <article class="card" data-lang="ar" dir="rtl" hidden>
        <h2 id="terms-ar">الشروط العامة للاستخدام – Boutigak</h2>
        <h3>1. قبول الشروط</h3>
        <p>باستخدامك تطبيق Boutigak فإنك تقبل هذه الشروط كاملة.</p>
        <h3>2. طريقة عمل التطبيق</h3>
        <p>Boutigak عبارة عن سوق إلكتروني يتيح للمستخدمين:</p>
        <ul>
          <li>فتح متجر إلكتروني (مخصص للتجار العاملين في موريتانيا فقط)</li>
          <li>نشر إعلانات لبيع المنتجات</li>
        </ul>
        <h3>3. المسؤولية</h3>
        <p>يعمل Boutigak منصة وساطة فقط، ولا يتحمل أي مسؤولية:</p>
        <ul>
          <li>عن جودة أو مطابقة أو قانونية المنتجات أو الخدمات المباعة</li>
          <li>في حال وقوع نزاع بين المشتري والبائع</li>
          <li>عن المدفوعات التي تُدفع مباشرة إلى المتاجر عبر المحافظ أو غيرها</li>
          <li>عن إدارة الطلبات أو سرعتها أو جودة معالجتها</li>
        </ul>
        <p>المتجر مسؤول بالكامل عن إدارة طلباته وخدمة عملائه. ويحتفظ Boutigak بالحق في إغلاق أي متجر فورًا إذا لم يستجب للطلبات بسرعة أو قدَّم خدمة ضعيفة الجودة.</p>
        <h3>4. المحتوى المحظور وإغلاق الحساب</h3>
        <p>يُحظر على المستخدمين أو المتاجر نشر أو بث ما يلي على المنصة:</p>
        <ul>
          <li>المحتوى السياسي أو المحرض على الكراهية أو العنف أو التمييز أو المحتوى الجنسي أو أي محتوى مخالف للأخلاق العامة</li>
          <li>منتجات تعود لمتجر آخر دون ترخيص</li>
        </ul>
        <p>يحتفظ Boutigak بالحق في إغلاق أي حساب أو متجر فورًا ودون إشعار أو استرداد، عند مخالفة هذه القواعد أو الاشتباه باستخدامه من قِبل عدة أشخاص دون سداد رسوم الاشتراك المطلوبة لكل مستخدم.</p>
        <h3>5. اشتراك المتاجر ورسوم الإعلانات</h3>
        <p>يتطلب فتح متجر اشتراكًا مدفوعًا، وتُحدد الأسعار بعد التواصل وبحسب حجم واحتياجات المتجر. تخضع الإعلانات الفردية لرسوم متغيرة حسب فئة المنتج (منزل، سيارة، هاتف ذكي، إلخ). تتوفر الأسعار الدقيقة في التطبيق أو عند الطلب.</p>
        <h3>6. سياسة الاسترداد</h3>
        <p>لا يُتاح أي استرداد بعد دفع رسوم الاشتراك أو أي معاملة أخرى على المنصة.</p>
        <h3>7. النزاعات والشكاوى</h3>
        <p>في حال وقوع نزاع بين متجر ومشتري، قد يتدخل Boutigak كوسيط لتسهيل حل ودي. يجب تقديم أي شكوى عبر خدمة العملاء خلال مدة معقولة بعد المعاملة المعنية. لا تسري هذه الفقرة على الإعلانات الفردية.</p>
        <h3>8. الملكية الفكرية</h3>
        <p>جميع المحتويات على Boutigak محمية بقوانين الملكية الفكرية. يُمنع أي استخدام أو نسخ أو توزيع غير مصرح به.</p>
        <h3>9. تعديل الشروط</h3>
        <p>يحتفظ Boutigak بالحق في تعديل هذه الشروط في أي وقت، وسيتم إخطار المستخدمين بأي تغييرات جوهرية تؤثر على حقوقهم أو التزاماتهم.</p>
        <h3>10. قيود الاستخدام</h3>
        <p>فتح المتجر مخصص للتجار الذين يمارسون نشاطهم في موريتانيا فقط.</p>
        <h3>11. القانون والاختصاص القضائي</h3>
        <p>تخضع هذه الشروط لقوانين موريتانيا، وتختص محاكم موريتانيا حصريًا بأي نزاع.</p>
      </article>

    </main>
    <footer>
      © <span id="year"></span> Boutigak — All rights reserved
    </footer>
  </div>
  <script>
    (function(){
      try{
        const ok = CSS.supports('-webkit-mask-image','url("")') || CSS.supports('mask-image','url("")');
        if (ok) document.documentElement.classList.add('supports-mask');
      }catch(e){}
    })();
    document.getElementById('year').textContent = new Date().getFullYear();
    (function(){
      const d = new Date();
      const fmt = d.toLocaleDateString(undefined, { year:'numeric', month:'long', day:'numeric' });
      document.getElementById('lastUpdated').textContent = 'Dernière mise à jour / Last updated: ' + fmt;
    })();
    const reduceMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    function revealApp(){
      const s = document.getElementById('splash');
      const a = document.getElementById('app');
      s.classList.add('hidden');
      setTimeout(()=>{ s.style.display='none'; }, 400);
      a.style.visibility='visible';
    }
    const fallback = setTimeout(revealApp, 4000);
    if(reduceMotion){
      clearTimeout(fallback); revealApp();
    }else{
      const anim = lottie.loadAnimation({
        container: document.getElementById('lottie'),
        renderer: 'svg',
        loop: false,
        autoplay: true,
        path: '{{ asset('assets/Boutigaklogo.json') }}'
      });
      anim.addEventListener('complete', ()=>{ clearTimeout(fallback); revealApp(); });
      anim.addEventListener('data_failed', ()=>{ clearTimeout(fallback); revealApp(); });
    }
    const flags = { ar: '🇲🇷', fr: '🇫🇷', en: '🇬🇧' };
    const titles = {
      ar: 'سياسة الخصوصية والشروط – Boutigak',
      fr: "Politique de Confidentialité & Conditions d'Utilisation",
      en: 'Privacy Policy & Terms – Boutigak'
    };
    const subtitles = {
      ar: 'اقرأ كيف نحمي بياناتك والقواعد المنظمة لاستخدام Boutigak.',
      fr: "Lisez comment nous protégeons vos données et les règles d'utilisation de Boutigak.",
      en: 'Read how we protect your data and the rules for using Boutigak.'
    };
    const flagEl = document.getElementById('flag');
    const langSelect = document.getElementById('langSelect');
    const titleEl = document.getElementById('title');
    const subEl = document.getElementById('subtitle');
    function applyLang(l){
      try{ localStorage.setItem('lang', l); }catch(e){}
      document.querySelectorAll('[data-lang]')
        .forEach(el => el.hidden = (el.getAttribute('data-lang') !== l));
      flagEl.textContent = flags[l] || flags.en;
      titleEl.textContent = titles[l] || titles.en;
      subEl.textContent = subtitles[l] || subtitles.en;
      document.documentElement.lang = l;
      document.documentElement.dir = (l === 'ar') ? 'rtl' : 'ltr';
      document.title = (l === 'fr') ? 'Boutigak — Politique de Confidentialité & CGU'
                    : (l === 'ar') ? 'Boutigak — سياسة الخصوصية والشروط'
                    : 'Boutigak — Privacy Policy & Terms';
      const tocMap = {
        ar: { privacy:'سياسة الخصوصية', terms:'الشروط العامة للاستخدام' },
        fr: { privacy:'Politique de Confidentialité', terms:"Conditions Générales d'Utilisation" },
        en: { privacy:'Privacy Policy', terms:'Terms of Service' }
      };
      const t = tocMap[l] || tocMap.en;
      const toc = document.getElementById('toc');
      toc.querySelector('[data-i18n="toc_privacy"]').textContent = t.privacy;
      toc.querySelector('[data-i18n="toc_terms"]').textContent = t.terms;
    }
    (function initLang(){
      let saved = null;
      try{ saved = localStorage.getItem('lang'); }catch(e){}
      const initial = saved || langSelect.value || 'fr';
      langSelect.value = initial;
      applyLang(initial);
    })();
    langSelect.addEventListener('change', e => applyLang(e.target.value));
  </script>
</body>
</html>
