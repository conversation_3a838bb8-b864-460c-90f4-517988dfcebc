@extends('layouts.app')

@section('title', 'Forgot Password | ' . config('app.name'))

@section('content')
    <div class="card">
        <div class="card-body">
            <div class="text-center mt-4">
                <div class="mb-3">
                    <a href="{{ url('/') }}" class="auth-logo">
                        <img src="{{ asset('backoffice/assets/images/logo-dark.png') }}" height="30" class="logo-dark mx-auto" alt="">
                        <img src="{{ asset('backoffice/assets/images/logo-light.png') }}" height="30" class="logo-light mx-auto" alt="">
                    </a>
                </div>
            </div>

            <h4 class="text-muted text-center font-size-18"><b>Forgot Password</b></h4>

            <div class="p-3">
                <div class="mb-4 text-sm text-gray-600">
                    {{ __('Forgot your password? No problem. Just let us know your phone number and we will send you a password reset link that will allow you to choose a new one.') }}
                </div>

                <!-- Session Status -->
                <x-auth-session-status class="mb-4" :status="session('status')" />

                <form class="form-horizontal mt-3" method="POST" action="{{ route('password.phone') }}">
                    @csrf
                    <div class="form-group mb-3 row">
                        <div class="col-12">
                            <input class="form-control @error('phone') is-invalid @enderror" type="text" name="phone" required="" placeholder="Phone number" value="{{ old('phone') }}">
                            @error('phone')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>

                    <div class="form-group mb-3 text-center row mt-3 pt-1">
                        <div class="col-12">
                            <button class="btn btn-info w-100 waves-effect waves-light" type="submit">{{ __('Send Password Reset Link') }}</button>
                        </div>
                    </div>
                </form>

                <div class="form-group mb-0 row mt-2">
                    <div class="col-12 text-center">
                        <a href="{{ route('login') }}" class="text-muted"><i class="mdi mdi-arrow-left"></i> Back to Login</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
