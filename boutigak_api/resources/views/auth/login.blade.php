@extends('layouts.app')

@section('title', 'Login | ' . config('app.name'))

@section('content')
    <div class="card">
        <div class="card-body">
            <div class="text-center mt-4">
                <div class="mb-3">
                    <a href="#" class="auth-logo">
                        <img src="{{ asset('backoffice/assets/images/biglogo_boutigak.png') }}" height="60" class="logo-dark mx-auto" alt="">
                        <img src="{{ asset('backoffice/assets/images/biglogo_boutigak.png') }}" height="60" class="logo-light mx-auto" alt="">
                    </a>
                </div>
            </div>

            <div class="alert alert-info mb-4">
                Server IP Test: {{ $_SERVER['SERVER_ADDR'] ?? 'Unknown' }}<br>
                Session ID: {{ session()->getId() }}<br>
                CSRF Token: {{ csrf_token() }}<br>
                Session Driver: {{ config('session.driver') }}
            </div>

            <h4 class="text-muted text-center font-size-18"><b>Admin Login</b></h4>

            <div class="p-3">
                <form class="form-horizontal mt-3" method="POST" action="{{ route(name: 'login') }}">
                    @csrf 
                    
                    <div class="form-group mb-3 row">
                        <div class="col-12">
                            <input class="form-control @error('phone') is-invalid @enderror" type="text" name="phone" required="" placeholder="Phone number" value="{{ old('phone') }}">
                            @error('phone')
                            <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>
                    </div>

                    <div class="form-group mb-3 row">
                        <div class="col-12">
                            <input class="form-control @error('password') is-invalid @enderror" type="password" name="password" required="" placeholder="Password">
                            @error('password')
                            <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>
                    </div>


                    <div class="form-group mb-3 text-center row mt-3 pt-1">
                        <div class="col-12">
                            <button class="btn btn-default w-100 waves-effect waves-light text-white fw-bold fs-4" type="submit" style="background-color: #027782;">Log In</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection