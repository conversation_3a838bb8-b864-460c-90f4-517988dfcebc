@extends('backoffice.layouts.layout')

@section('title', 'Update Password | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Update Password</h4>
                        </div>
                        <div class="card-body">
                            @if ($errors->any())
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                            <form method="post" action="{{ route('password.update') }}" class="mt-6 space-y-6">
                                @csrf
                                @method('put')

                                <div class="mb-3">
                                    <label for="update_password_current_password" class="form-label">{{ __('Current Password') }}</label>
                                    <input type="password" class="form-control" id="update_password_current_password" name="current_password" autocomplete="current-password" required>
                                </div>

                                <div class="mb-3">
                                    <label for="update_password_password" class="form-label">{{ __('New Password') }}</label>
                                    <input type="password" class="form-control" id="update_password_password" name="password" autocomplete="new-password" required>
                                </div>

                                <div class="mb-3">
                                    <label for="update_password_password_confirmation" class="form-label">{{ __('Confirm Password') }}</label>
                                    <input type="password" class="form-control" id="update_password_password_confirmation" name="password_confirmation" autocomplete="new-password" required>
                                </div>

                                <div class="flex items-center gap-4">
                                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
