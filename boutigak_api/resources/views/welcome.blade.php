<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Boutigak — App</title>
  <meta name="theme-color" content="#005b96" />

  <!-- <PERSON><PERSON>-web -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.12.2/lottie.min.js"></script>

  <style>
    :root{
      --brand:#005b96;
      --text-900:#0b1220;
      --text-700:#2b3445;
      --bg:#f7f9fc;
      --card:#ffffff;
      --line:#e7edf4;
      --radius:16px;
      --container:min(1120px,92vw);
      --shadow:0 18px 40px rgba(0,0,0,.08);
    }
    *{box-sizing:border-box}
    html,body{margin:0;background:var(--bg);color:var(--text-900);font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial}
    .support{
      width:var(--container);
      margin:10px auto 34px;
      background:var(--card);
      border:1px solid var(--line);
      border-radius:var(--radius);
      box-shadow:var(--shadow);
      padding:clamp(16px,2vw,24px);
      text-align:start;
    }
    .support h2{ margin:0 0 8px; font-size:clamp(18px,2.4vw,26px) }
    .support .support-grid{
      display:grid;
      grid-template-columns:repeat(auto-fit,minmax(260px,1fr));
      gap:12px; margin-top:12px;
    }
    .support .support-item{
      display:flex; align-items:center; gap:10px;
      background:rgba(11,18,32,.02);
      border:1px solid var(--line);
      border-radius:12px;
      padding:12px 14px;
      text-decoration:none; color:inherit;
    }
    .support .emoji{ font-size:20px }
    .support .label{ opacity:.8 }
    .support .value{ font-weight:700 }
    #splash{
      position:fixed; inset:0; z-index:9999;
      display:grid; place-items:center;
      background:#fff;
    }
    #splash .inner{
      width:min(420px,80vw); aspect-ratio:1/1;
      display:grid; place-items:center;
    }
    .hidden{ opacity:0; pointer-events:none; transition:opacity .35s ease }
    header{
      position:sticky; top:0;
      backdrop-filter:saturate(1.5) blur(8px);
      background:rgba(255,255,255,.65);
      border-bottom:1px solid var(--line);
      z-index:10;
    }
    .nav{
      width:var(--container); margin-inline:auto;
      display:flex; align-items:center; justify-content:space-between;
      padding:14px 4px;
      gap:16px;
    }
    .logo-overlay {
      position: relative;
      display: inline-block;
      width: 160px;
      height: 40px;
    }
    .logo-overlay img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .brand-text{ font-weight:800; letter-spacing:.2px }
    .lang{
      display:flex; align-items:center; gap:10px; background:var(--card);
      border:1px solid var(--line); padding:8px 12px; border-radius:999px
    }
    .flag-emoji{font-size:18px; line-height:1; display:inline-flex; align-items:center}
    .lang select{
      appearance:none; border:none; background:transparent; outline:none;
      font-weight:700; cursor:pointer
    }
    main{ width:var(--container); margin:34px auto; text-align:center }
    .title{ font-size:clamp(22px,3.6vw,40px); font-weight:900; line-height:1.25 }
    .subtitle{ color:var(--text-700) }
    .mock{
      width:min(820px,100%);
      border-radius:var(--radius);
      overflow:hidden;
      display:flex; align-items:center; justify-content:center;
      margin:20px auto;
    }
    .mock img{ width:100%; height:auto; display:block }
    .stores{
      display:flex; gap:14px; flex-wrap:wrap; justify-content:center;
      margin:4px 0 60px;
    }
    .store-btn{
      display:flex; align-items:center; gap:10px;
      padding:10px 14px; border-radius:12px;
      background:#0b1220; color:#fff;
      transition:transform .08s ease;
      border:1px solid rgba(255,255,255,.15);
      min-width:210px; justify-content:center;
      text-decoration:none;
    }
    .store-btn:hover{ transform:translateY(-2px) }
    .store-btn img{ height:22px; width:auto; display:block }
    footer{ text-align:center; color:var(--text-700); border-top:1px solid var(--line); padding:22px 0 40px }
    [dir="ltr"] .nav{ flex-direction:row }
  </style>
</head>
<body>

  <!-- Splash Lottie -->
  <div id="splash" aria-label="Loading animation">
    <div class="inner"><div id="lottie" style="width:100%;height:100%"></div></div>
  </div>

  <!-- Contenu -->
  <div id="app" style="visibility:hidden">
    <header>
      <nav class="nav">
        <div class="logo-overlay">
          <img src="{{ asset('assets/boutigak-logo.png') }}" alt="Boutigak" />
        </div>
        <div class="lang">
          <span id="flag" class="flag-emoji" aria-hidden="true">🇲🇷</span>
          <select id="langSelect" aria-label="Change language">
            <option value="ar" selected>العربية</option>
            <option value="fr">Français</option>
            <option value="en">English</option>
          </select>
        </div>
      </nav>
    </header>

    <main>
      <h1 id="title" class="title">حمّل تطبيق بوتيگك لاستكشاف المنتجات وأكثر!</h1>
      <p id="subtitle" class="subtitle">تسوّق بسهولة، اكتشف المتاجر القريبة، واطلب في ثوانٍ.</p>

      <div class="mock">
        <img src="{{ asset('assets/boutigak.png') }}" alt="Aperçu de l’application Boutigak">
      </div>

      <div class="stores">
        <a class="store-btn" href="#" id="playLink" rel="noopener">
          <img src="{{ asset('assets/google-play.png') }}" alt="Google Play">
          <span id="playText">Get it on Google Play</span>
        </a>
        <a class="store-btn" href="#" id="appLink" rel="noopener">
          <img src="{{ asset('assets/app-store.png') }}" alt="App Store">
          <span id="appText">Download on the App Store</span>
        </a>
      </div>
    </main>
    <section class="support" id="support">
      <h2 id="supportTitle">Support</h2>
      <p id="supportDesc">Contact us via:</p>
      <div class="support-grid">
        <a class="support-item" href="mailto:contact@bécod.com" aria-label="Email contact@bécod.com">
          <span class="emoji" aria-hidden="true">📧</span>
          <span class="label" id="emailLabel">Email</span>
          <span class="value">contact@bécod.com</span>
        </a>
        <a class="support-item" href="tel:+22238407840" aria-label="Phone +22238407840">
          <span class="emoji" aria-hidden="true">📞</span>
          <span class="label" id="phoneLabel">Phone</span>
          <span class="value">+22238407840</span>
        </a>
      </div>
    </section>
    <footer>
      © <span id="year"></span> Boutigak — All rights reserved
    </footer>
  </div>

  <script>
    (function(){
      try{
        const ok = CSS.supports('-webkit-mask-image','url("")') || CSS.supports('mask-image','url("")');
        if (ok) document.documentElement.classList.add('supports-mask');
      }catch(e){}
    })();
    document.getElementById('year').textContent = new Date().getFullYear();
    const reduceMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    function revealApp(){
      const s = document.getElementById('splash');
      const a = document.getElementById('app');
      s.classList.add('hidden');
      setTimeout(()=>{ s.style.display='none'; }, 400);
      a.style.visibility='visible';
    }
    const fallback = setTimeout(revealApp, 4000);
    if(reduceMotion){
      clearTimeout(fallback); revealApp();
    }else{
      const anim = lottie.loadAnimation({
        container: document.getElementById('lottie'),
        renderer: 'svg',
        loop: false,
        autoplay: true,
        path: '{{ asset('assets/Boutigaklogo.json') }}'
      });
      anim.addEventListener('complete', ()=>{ clearTimeout(fallback); revealApp(); });
      anim.addEventListener('data_failed', ()=>{ clearTimeout(fallback); revealApp(); });
    }
    const dict = {
      ar:{title:'حمّل تطبيق بوتيگك لاستكشاف المنتجات وأكثر!',
          subtitle:'تسوّق بسهولة، اكتشف المتاجر ، واطلب في ثوانٍ.',
          play:'حمّل التطبيق من Google Play',
          app:'حمّل التطبيق من App Store',
          supportTitle:'الدعم',
          supportDesc:'تواصل معنا عبر:',
          email:'البريد الإلكتروني',
          phone:'الهاتف'},
      fr:{title:'Télécharge l’app Boutigak pour découvrir des produits et plus !',
          subtitle:'Achète facilement, découvre les boutiques proches et commande en quelques secondes.',
          play:'Disponible sur Google Play',
          app:'Disponible sur l’App Store',
          supportTitle:'Support',
          supportDesc:'Contactez-nous via :',
          email:'E-mail',
          phone:'Téléphone'},
      en:{title:'Download Boutigak to discover stores & more!',
          subtitle:'Shop easily, find nearby stores, and order in seconds.',
          play:'Get it on Google Play',
          app:'Download on the App Store',
          supportTitle:'Support',
          supportDesc:'Contact us via:',
          email:'E-mail',
          phone:'Phone'}
    };
    const flags = { ar: '🇲🇷', fr: '🇫🇷', en: '🇬🇧' };
    const flagEl = document.getElementById('flag');
    const langSelect = document.getElementById('langSelect');
    const title = document.getElementById('title');
    const subtitle = document.getElementById('subtitle');
    const playText = document.getElementById('playText');
    const appText = document.getElementById('appText');
    const supportTitleEl = document.getElementById('supportTitle');
    const supportDescEl  = document.getElementById('supportDesc');
    const emailLabelEl   = document.getElementById('emailLabel');
    const phoneLabelEl   = document.getElementById('phoneLabel');
    function applyLang(l){
      const t = dict[l] || dict.en;
      title.textContent = t.title;
      subtitle.textContent = t.subtitle;
      playText.textContent = t.play;
      appText.textContent = t.app;
      supportTitleEl.textContent = t.supportTitle;
      supportDescEl.textContent  = t.supportDesc;
      emailLabelEl.textContent   = t.email;
      phoneLabelEl.textContent   = t.phone;
      flagEl.textContent = flags[l] || flags.en;
      document.documentElement.lang = l;
      document.documentElement.dir = (l === 'ar') ? 'rtl' : 'ltr';
      try{ localStorage.setItem('lang', l); }catch(e){}
    }
    (function initLang(){
      let saved = null;
      try{ saved = localStorage.getItem('lang'); }catch(e){}
      const initial = saved || langSelect.value || 'ar';
      langSelect.value = initial;
      applyLang(initial);
    })();
    langSelect.addEventListener('change', e => applyLang(e.target.value));
  </script>
</body>
</html>
