@extends('backoffice.layouts.layout')

@section('title', 'Edit Brand | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Edit Brand</h4>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('brands.update', $brand->id) }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="mb-3">
                                    <label for="name" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ $brand->name }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Category</label>
                                    <select class="form-control select2" multiple="multiple" id="category_id" name="category_id" required>
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ $brand->category_id == $category->id ? 'selected' : '' }}>{{ $category->title_en }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="gender" class="form-label">Gender</label>
                                    <select class="form-control" id="gender" name="gender" required>
                                        <option value="both" {{ $brand->gender == 'both' ? 'selected' : '' }}>Both</option>
                                        <option value="male" {{ $brand->gender == 'male' ? 'selected' : '' }}>Male</option>
                                        <option value="female" {{ $brand->gender == 'female' ? 'selected' : '' }}>Female</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">Save</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
