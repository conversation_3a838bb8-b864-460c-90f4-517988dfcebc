@extends('backoffice.layouts.layout')

@section('title', 'Stores | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')

            <!-- Promoted Stores Section -->
            <div class="row mb-4">
                @if($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">Promoted Stores</h4>
                            <button type="button" class="btn btn-default text-white" style="background-color: #027782;"
                                    data-bs-toggle="modal" data-bs-target="#storeSelectionModal">ADD
                            </button>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('stores.updatePromotionPositions') }}" method="POST" id="updatePositionsForm">
                                @csrf
                                <div class="d-flex overflow-auto pb-3" style="gap: 1rem;">
                                    @foreach ($stores->where('is_promoted', true)->sortBy('promotion_position') as $index => $promotedStore)
                                        <div class="card flex-shrink-0" style="width: 300px;">
                                            <div class="position-relative">
                        <span class="position-absolute top-0 start-0 badge bg-primary m-2">
                            {{ $index + 1 }}
                        </span>
                                                @if($promotedStore->images->first())
                                                    <img
                                                        src="{{ 
                                                        sftp_url($promotedStore->images->first()->media->url)
                                                         }}"
                                                        class="img-fluid"
                                                        alt="{{ $promotedStore->name }}"
                                                        style="height: 60px; width: 60px; object-fit: cover;">
                                                @endif
                                            </div>
                                            <div class="card-body">
                                                <h5 class="card-title text-truncate">{{ $promotedStore->name }}</h5>
                                                <p class="card-text" style="height: 48px; overflow: hidden;">{{ $promotedStore->description }}</p>
                                                <div class="mb-3">
                                                    <label for="position_{{ $promotedStore->id }}" class="form-label">Position</label>
                                                    <input type="number" class="form-control"
                                                           id="position_{{ $promotedStore->id }}"
                                                           name="positions[{{ $index }}][position]"
                                                           value="{{ $promotedStore->promotion_position }}">
                                                    <input type="hidden" name="positions[{{ $index }}][store_id]"
                                                           value="{{ $promotedStore->id }}">
                                                </div>
                                                <button type="button" class="btn btn-danger remove-promotion-btn"
                                                        data-store-id="{{ $promotedStore->id }}">Remove</button>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <button type="submit" class="btn btn-primary">Update Positions</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Stores Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">All Stores</h4>
                        </div>
                        <div class="card-body">
                            <table id="key-datatable" class="table dt-responsive nowrap w-100">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Name</th>
                                    <th>User</th>
                                    <th>Phone</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Store Selection Modal -->
    <div class="modal fade" id="storeSelectionModal" tabindex="-1" aria-labelledby="storeSelectionModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="storeSelectionModalLabel">Select Store to Promote</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('stores.promote') }}" method="POST" id="promoteStoreForm">
                        @csrf
                        <div class="mb-3">
                            <label for="store_id" class="form-label">Select Store</label>
                            <select class="form-select select2" id="store_id" name="store_id" data-placeholder="Choose a store..." required>
                                <option value="">Choose a store...</option>
                                @foreach($stores->where('is_promoted', false) as $store)
                                    <option value="{{ $store->id }}">{{ $store->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="position" class="form-label">Position in List</label>
                            <input type="number" class="form-control" id="position" name="position"
                                   min="1" max="{{ $stores->where('is_promoted', true)->count() + 1 }}"
                                   value="{{ $stores->where('is_promoted', true)->count() + 1 }}">
                            <div class="form-text text-muted">
                                If you select a position that is already taken, existing items will be shifted down to make room.
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Promote Store</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden form for removal actions -->
    <form id="removePromotionForm" action="{{ route('stores.removePromotion') }}" method="POST" style="display: none;">
        @csrf
        <input type="hidden" name="store_id" id="remove_store_id">
    </form>
@endsection

@push('script')
    <script type="text/javascript">
        // Initialize DataTable with AJAX
        $(document).ready(function () {
            $('#key-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('stores.index') }}",
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'name', name: 'name'},
                    {data: 'user.firstname', name: 'user.firstname'},
                    {data: 'user.phone', name: 'user.phone'},
                    {data: 'action', name: 'action', orderable: false, searchable: false},
                ],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                    '<"row"<"col-sm-12"tr>>' +
                    '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                lengthChange: true,
                searchPlaceholder: "Search stores..."
                // Initialize Select2 for store selection with modal as dropdown parent for proper z-index
            const $storeSelect = $('#store_id');
            function initStoreSelect2() {
                if ($storeSelect.data('select2')) { return; }
                $storeSelect.select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    dropdownParent: $('#storeSelectionModal'),
                    placeholder: $storeSelect.data('placeholder') || 'Choose a store...'
                });
            }

            // Initialize on modal show to ensure dropdownParent exists
            $('#storeSelectionModal').on('shown.bs.modal', function() {
                initStoreSelect2();
            });

            // If modal is already open or to preload
            if ($('#storeSelectionModal').is(':visible')) {
                initStoreSelect2();
            }
        });

            // Handle remove promotion buttons
            $('.remove-promotion-btn').on('click', function() {
                const storeId = $(this).data('store-id');

                Swal.fire({
                    title: 'Remove Promotion?',
                    text: "Are you sure you want to remove this store from promotions?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, remove it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $('#remove_store_id').val(storeId);
                        $('#removePromotionForm').submit();
                    }
                });
            });
        });

        // Delete confirmation
        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', function (e) {
                e.preventDefault();
                const form = this.closest('form');

                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, delete it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    }
                });
            });
        });

        $('#storeSelectionModal form').on('submit', function(e) {
            const position = parseInt($('#position').val());
            const maxPosition = parseInt($('#position').attr('max'));

            if (position > maxPosition) {
                e.preventDefault();
                Swal.fire({
                    title: 'Invalid Position',
                    text: `Position cannot be greater than ${maxPosition}`,
                    icon: 'error'
                });
                return false;
            }
        });

        // When position field changes in the promotion cards
        $('input[id^="position_"]').on('change', function() {
            const allPositions = $('input[id^="position_"]').map(function() {
                return $(this).val();
            }).get();

            // Check for duplicates
            const isDuplicate = allPositions.some(function(position, idx) {
                return allPositions.indexOf(position) !== idx;
            });

            if (isDuplicate) {
                $(this).addClass('is-invalid');
                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">This position is already taken</div>');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        });

        // Form validation before submit
        $('#updatePositionsForm').on('submit', function(e) {
            const invalidInputs = $(this).find('input.is-invalid');
            if (invalidInputs.length > 0) {
                e.preventDefault();
                Swal.fire({
                    title: 'Validation Error',
                    text: 'Please fix the errors in the form before submitting',
                    icon: 'error'
                });
                return false;
            }
        });
    </script>
@endpush
