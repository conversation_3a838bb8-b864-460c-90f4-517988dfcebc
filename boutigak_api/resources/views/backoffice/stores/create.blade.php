@extends('backoffice.layouts.layout')

@section('title', 'Create Store | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title">Create Store</h4>
                            <p class="card-title-desc">Fill in the details to create a new store.</p>

                            <form class="custom-validation" action="{{ route('stores.create-store') }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                <div class="mb-3">
                                    <label for="name">Store Name</label>
                                    <input type="text" class="form-control" name="name" required placeholder="Enter store name" />
                                </div>

                                <div class="mb-3">
                                    <label for="description">Description</label>
                                    <textarea class="form-control" name="description" rows="3" required placeholder="Enter store description"></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="type_id">Store Type</label>
                                    <select class="form-control" name="type_id" required>
                                        <option value="">Select Type</option>
                                        @foreach($storeTypes as $type) <!-- Assuming you have a $storeTypes variable -->
                                        <option value="{{ $type->id }}">{{ $type->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="opening_time">Opening Time</label>
                                    <input type="text" class="form-control" name="opening_time" required placeholder="e.g., 8H" />
                                </div>

                                <div class="mb-3">
                                    <label for="closing_time">Closing Time</label>
                                    <input type="text" class="form-control" name="closing_time" required placeholder="e.g., 22H" />
                                </div>

                                <div class="mb-3">
                                    <label for="images">Images</label>
                                    <div id="imageInputs">
                                        <div class="input-group mb-3">
                                            <input type="file" class="form-control" name="images[0][image]" accept="image/jpeg, image/png" required />
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-secondary" id="addImage">Add Another Image</button>
                                </div>

                                <div class="mb-0">
                                    <div>
                                        <button type="submit" class="btn btn-primary waves-effect waves-light me-1">Submit</button>
                                        <button type="reset" class="btn btn-secondary waves-effect">Cancel</button>
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </div> <!-- end col -->
            </div> <!-- end row -->
        </div> <!-- container-fluid -->
    </div> <!-- page-content -->

    <script>
        // Add functionality to add more image inputs
        let imageIndex = 1;
        document.getElementById('addImage').addEventListener('click', function() {
            const imageInputs = document.getElementById('imageInputs');
            const newInputGroup = document.createElement('div');
            newInputGroup.className = 'input-group mb-3';
            newInputGroup.innerHTML = `
                <input type="file" class="form-control" name="images[${imageIndex}][image]" accept="image/jpeg, image/png" required />
            `;
            imageInputs.appendChild(newInputGroup);
            imageIndex++;
        });
    </script>
@endsection
