@extends('backoffice.layouts.layout')

@section('title', 'Store Details | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <!-- Store Details -->
            <div class="row mb-4">
                <div class="col-4">
                    <div id="storeImagesCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            @foreach($store->images as $key => $image)
                                <div class="carousel-item {{ $key == 0 ? 'active' : '' }}">
                                    <img src="{{ sftp_url($image->media->url) }}" class="d-block w-25" alt="Store Image">
                                </div>
                            @endforeach
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#storeImagesCarousel"
                                data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#storeImagesCarousel"
                                data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                </div>
                <div class="col-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">{{ $store->name }}</h4>
                            <a href="{{ route('stores.index') }}" class="btn btn-primary">Back</a>
                        </div>
                        <div class="card-body">
                            <p><strong>Description:</strong> {{ $store->description }}</p>
                            <p><strong>Type:</strong> {{ $store->type->name_en }}</p>
                            <p><strong>Location:</strong> {{ $store->location?->address }}</p>
                            <p><strong>Opening Time:</strong> {{ $store->opening_time }}</p>
                            <p><strong>Closing Time:</strong> {{ $store->closing_time }}</p>
                            <p><strong>Promoted:</strong> {{ $store->is_promoted ? 'Yes' : 'No' }}</p>
                            <p><strong>User:</strong> {{ $store->user->firstname }} {{ $store->user->lastname }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Store Items -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Items</h4>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($store->items as $item)
                                    <tr>
                                        <td>{{ $item->id }}</td>
                                        <td>
                                            <img src="{{
                                            sftp_url($item->images->first()->url)
                                              }}"
                                                 class="img-fluid item-thumbnail"
                                                 alt="First image"
                                                 style="width: 60px; height: 60px; cursor: pointer;"
                                                 data-full="{{ $item->images->first()->url }}" />
                                        </td>
                                        <td>{{ $item->title }}</td>
                                        <td>{{ $item->price }}</td>
                                        <td>{{ $item->quantity }}</td>
                                        <td>
                                            <a href="{{ route('items.show', $item->id) }}"
                                               class="btn btn-primary btn-sm">Show</a>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Image Modal -->
            <div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content">
                        <div class="modal-body p-0">
                            <img id="modalImage" src="" class="img-fluid w-100 rounded" alt="Preview Image">
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function () {
            $('.item-thumbnail').on('click', function () {
                const fullImage = $(this).data('full');
                $('#modalImage').attr('src', fullImage);
                $('#imageModal').modal('show');
            });
        });
    </script>
@endpush
