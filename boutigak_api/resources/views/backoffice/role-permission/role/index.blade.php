@extends('backoffice.layouts.layout')

@section('title', 'Roles | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">Roles</h4>
                            @can('create role')
                                <a href="{{ url('roles/create') }}" class="btn btn-primary">Add Role</a>
                            @endcan
                        </div>
                        <div class="card-body">
                            <table id="key-datatable" class="table dt-responsive nowrap w-100">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Name</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div> <!-- end card body-->
                    </div> <!-- end card -->
                </div><!-- end col-->
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        $(document).ready(function () {
            $('#key-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('roles.index') }}",
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'name', name: 'name'},
                    {data: 'action', name: 'action', orderable: false, searchable: false},
                ]
            });

            $(document).on('click', '.btn-danger', function (e) {
                e.preventDefault();
                var canDelete = $(this).data('can-delete');
                if (canDelete) {
                    var url = $(this).attr('href');
                    swal({
                        title: "Are you sure?",
                        text: "Once deleted, you will not be able to recover this role!",
                        icon: "warning",
                        buttons: true,
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                window.location.href = url;
                            }
                        });
                } else {
                    swal({
                        title: "Permission Denied",
                        text: "You do not have permission to delete this role.",
                        icon: "error",
                        button: "OK",
                    });
                }
            });
        });
    </script>
@endpush
