@extends('backoffice.layouts.layout')

@section('title', 'Edit Role | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <div class="container mt-5">
                <div class="row">
                    <div class="col-md-12">
                        @if ($errors->any())
                            <ul class="alert alert-warning">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif

                        <div class="card">
                            <div class="card-header">
                                <h4>Edit Role
                                    <a href="{{ url('roles') }}" class="btn btn-danger float-end">Back</a>
                                </h4>
                            </div>
                            <div class="card-body">
                                <form action="{{ url('roles/' . $role->id) }}" method="POST">
                                    @csrf
                                    @method('PUT')
                                    <div class="mb-3">
                                        <label for="name">Role Name</label>
                                        <input type="text" name="name" value="{{ $role->name }}" class="form-control" />
                                    </div>
                                    <div class="mb-3">
                                        <button type="submit" class="btn btn-primary">Update</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
