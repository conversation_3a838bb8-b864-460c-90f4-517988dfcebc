@extends('backoffice.layouts.layout')

@section('title', 'Permissions | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">

                    @if (session('status'))
                        <div class="alert alert-success">{{ session('status') }}</div>
                    @endif

                    <div class="card mt-3">
                        <div class="card-header">
                            <h4>Permissions
                                @can('create permission')
                                    <a href="{{ url('permissions/create') }}" class="btn btn-primary float-end">Add Permission</a>
                                @endcan
                            </h4>
                        </div>
                        <div class="card-body">

                            <table id="key-datatable" class="table dt-responsive nowrap w-100">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Name</th>
                                    <th width="40%">Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        $(document).ready(function () {
            $('#key-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('permissions.index') }}",
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'name', name: 'name'},
                    {data: 'action', name: 'action', orderable: false, searchable: false},
                ]
            });

            $(document).on('click', '.btn-danger', function (e) {
                e.preventDefault();
                var canDelete = $(this).data('can-delete');
                if (canDelete) {
                    var url = $(this).attr('href');
                    swal({
                        title: "Are you sure?",
                        text: "Once deleted, you will not be able to recover this permission!",
                        icon: "warning",
                        buttons: true,
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                window.location.href = url;
                            }
                        });
                } else {
                    swal({
                        title: "Permission Denied",
                        text: "You do not have permission to delete this permission.",
                        icon: "error",
                        button: "OK",
                    });
                }
            });
        });
    </script>
@endpush
