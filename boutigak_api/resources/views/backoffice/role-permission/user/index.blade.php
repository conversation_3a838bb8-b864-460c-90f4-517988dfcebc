@extends('backoffice.layouts.layout')

@section('title', 'Users | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Users
                                @can('create user')
                                    <a href="{{ url('users/create') }}" class="btn btn-primary float-end">Add User</a>
                                @endcan
                            </h4>
                        </div>
                        <div class="card-body">
                            <h4 class="card-title">Users List</h4>
                            <p class="card-title-desc">
                                List of all users with actions to manage them.
                            </p>

                            <table id="key-datatable" class="table dt-responsive nowrap w-100">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>First Name</th>
                                    <th>Phone</th>
                                    <th>Last activity</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>

                        </div> <!-- end card body-->
                    </div> <!-- end card -->
                </div><!-- end col-->
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        $(document).ready(function () {
            $('#key-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('users.index') }}",
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'firstname', name: 'firstname'},
                    {data: 'phone', name: 'phone'},
                    {data: 'last_activity', name: 'last_activity'},
                    {data: 'action', name: 'action', orderable: false, searchable: false},
                ]
            });

            $(document).on('click', '.btn-danger', function (e) {
                e.preventDefault();
                var canDelete = $(this).data('can-delete');
                if (canDelete) {
                    var url = $(this).attr('href');
                    swal({
                        title: "Are you sure?",
                        text: "Once deleted, you will not be able to recover this user!",
                        icon: "warning",
                        buttons: true,
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                window.location.href = url;
                            }
                        });
                } else {
                    swal({
                        title: "Permission Denied",
                        text: "You do not have permission to delete this user.",
                        icon: "error",
                        button: "OK",
                    });
                }
            });
        });
    </script>
@endpush
