@extends('backoffice.layouts.layout')

@section('title', 'Create User | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <div class="container mt-5">
                <a href="{{ url('roles') }}" class="btn btn-primary mx-1">Roles</a>
                <a href="{{ url('permissions') }}" class="btn btn-info mx-1">Permissions</a>
                <a href="{{ url('users') }}" class="btn btn-warning mx-1">Users</a>
            </div>

            <div class="container mt-2">
                @include('backoffice.partials.messages')
                <div class="row">
                    @if ($errors->any())
                        <div class="alert alert-warning">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">Create User</h4>
                                <form action="{{ url('users') }}" method="POST">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="firstname" class="form-label">First Name</label>
                                        <input type="text" class="form-control" id="firstname" name="firstname"
                                               required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="lastname" class="form-label">Last Name</label>
                                        <input type="text" class="form-control" id="lastname" name="lastname" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone</label>
                                        <input type="text" class="form-control" id="phone" name="phone" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="invitationcode" class="form-label">Invitation Code</label>
                                        <input type="text" class="form-control" id="invitationcode"
                                               name="invitationcode" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Gender</label><br>
                                        <input type="radio" id="male" name="gender" value="male" required>
                                        <label for="male">Male</label><br>
                                        <input type="radio" id="female" name="gender" value="female" required>
                                        <label for="female">Female</label><br>
                                    </div>
                                    <div class="mb-3">
                                        <label for="roles" class="form-label">Roles</label>
                                        <select class="form-control select2" id="roles" name="roles[]"
                                                multiple="multiple" required>
                                            @foreach($roles as $role)
                                                <option
                                                    value="{{ $role->id }}" {{ in_array($role->id, old('roles', [])) ? 'selected' : '' }}>
                                                    {{ $role->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('roles')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="password" name="password"
                                               required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="password_confirmation" class="form-label">Confirm Password</label>
                                        <input type="password" class="form-control" id="password_confirmation"
                                               name="password_confirmation" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Create</button>
                                    <a href="{{ url('users') }}" class="btn btn-secondary">Back</a>
                                </form>
                            </div> <!-- end card body-->
                        </div> <!-- end card -->
                    </div><!-- end col-->
                </div>
            </div>
        </div>
    </div>
@endsection
@section('scripts')
    <script>
        $(document).ready(function () {
            $('.select2').select2({
                placeholder: "Select roles",
                allowClear: true
            });
        });
    </script>
@endsection
