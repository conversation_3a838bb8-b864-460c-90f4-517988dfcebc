@extends('backoffice.layouts.layout')

@section('title', 'Category Pricing | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">Category Pricing</h4>
                            <div>
                                <a href="{{ route('category_prices.create') }}" class="btn btn-primary mx-1">Add Category Price</a>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="key-datatable" class="table dt-responsive nowrap w-100 data-table">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Category Name</th>
                                    <th>Price</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        function confirmDelete(categoryPriceId) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('delete-form-' + categoryPriceId).submit();
                }
            })
        }
    </script>

    <script type="text/javascript">
        $(function () {
            var table = $('.data-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('category_prices.index') }}",
                    data: function (d) {
                        d.is_active = $('#is_active').val() || null;
                        d.price = $('#price').val() || null;
                    },
                    error: function(xhr, status, error) {
                        console.log('AJAX Error:', xhr.responseText);
                        alert('Error fetching data. Please check the console for more information.');
                    }
                },
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'category.title_en', name: 'category.title_en'},
                    {data: 'price', name: 'price'},
                    {data: 'action', name: 'action', orderable: false, searchable: false},
                ]
            });

            // Reload table when filters are changed
            $('#is_active, #price').change(function () {
                table.ajax.reload();
            });
        });
    </script>
@endpush
