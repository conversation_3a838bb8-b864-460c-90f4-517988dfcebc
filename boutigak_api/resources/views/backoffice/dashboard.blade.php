@extends('backoffice.layouts.layout')
@section('title', 'Tableau de bord')
@section('content')
    <div class="page-content">
        <div class="container-fluid">

            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">Dashboard</h4>

                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="javascript: void(0);">Login</a></li>
                                <li class="breadcrumb-item active">Dashboard</li>
                            </ol>
                        </div>

                    </div>
                </div>
            </div>
            <!-- end page title -->

            <!-- start analytics cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title"><i class="fas fa-users"></i> Active Users (24h)</h5>
                                    <h3 class="mb-0">{{ $analyticsData['currentActiveUsers'] }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title"><i class="fas fa-user-check"></i> Active Today</h5>
                                    <h3 class="mb-0">{{ $analyticsData['userActivitySummary']['active_today'] }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title"><i class="fas fa-calendar-week"></i> Active This Week</h5>
                                    <h3 class="mb-0">{{ $analyticsData['userActivitySummary']['active_this_week'] }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-week fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title"><i class="fas fa-percentage"></i> Activity Rate</h5>
                                    <h3 class="mb-0">{{ $analyticsData['userActivitySummary']['activity_rate_today'] }}%</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-pie fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end analytics cards -->

            <!-- start charts -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Daily Active Users (Last 30 Days)</h4>
                        </div>
                        <div class="card-body">
                            <div id="dailyActiveUsersChart" style="height: 350px;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end charts -->

            <!-- start cards -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-user-shield"></i> Roles</h5>
                            <p class="card-text">{{ $data['rolesCount'] }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-key"></i> Permissions</h5>
                            <p class="card-text">{{ $data['permissionsCount'] }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-users"></i> Users</h5>
                            <p class="card-text">{{ $data['usersCount'] }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-store"></i> Stores</h5>
                            <p class="card-text">{{ $data['storesCount'] }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-box"></i> Items</h5>
                            <p class="card-text">{{ $data['itemsCount'] }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title
                            "><i class="fas fa-money-bill-wave"></i> Store Pricing</h5>
                            <p class="card-text">{{ $data['storePricingsCount'] }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title
                            "><i class="fas fa-money-bill-wave"></i> Category Pricing</h5>
                            <p class="card-text">{{ $data['categoryPricingsCount'] }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="ri-advertisement-line"></i> Advertisements</h5>
                            <p class="card-text">{{ $data['adsCount'] }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end cards -->

        </div>
    </div>
@endsection

@push('script')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Daily Active Users Chart
    initializeDailyActiveUsersChart();

    // Auto-refresh data every 5 minutes
    setInterval(function() {
        refreshDashboardData();
    }, 300000); // 5 minutes
});

function initializeDailyActiveUsersChart() {
    fetch('/api/dashboard/daily-active-users?days=30')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const chartData = data.data;
                const dates = chartData.map(item => item.formatted_date);
                const activeUsers = chartData.map(item => item.active_users);

                const options = {
                    series: [{
                        name: 'Active Users',
                        data: activeUsers
                    }],
                    chart: {
                        type: 'area',
                        height: 350,
                        zoom: {
                            enabled: false
                        },
                        toolbar: {
                            show: true,
                            tools: {
                                download: true,
                                selection: false,
                                zoom: false,
                                zoomin: false,
                                zoomout: false,
                                pan: false,
                                reset: false
                            }
                        }
                    },
                    colors: ['#667eea'],
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth',
                        width: 2
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.7,
                            opacityTo: 0.3,
                            stops: [0, 90, 100]
                        }
                    },
                    xaxis: {
                        categories: dates,
                        title: {
                            text: 'Date'
                        }
                    },
                    yaxis: {
                        title: {
                            text: 'Active Users'
                        },
                        min: 0
                    },
                    grid: {
                        borderColor: '#e7e7e7',
                        row: {
                            colors: ['#f3f3f3', 'transparent'],
                            opacity: 0.5
                        }
                    },
                    tooltip: {
                        x: {
                            format: 'dd MMM'
                        },
                        y: {
                            formatter: function(val) {
                                return val + ' users'
                            }
                        }
                    }
                };

                const chart = new ApexCharts(document.querySelector("#dailyActiveUsersChart"), options);
                chart.render();

                // Store chart reference for updates
                window.dailyActiveUsersChart = chart;
            }
        })
        .catch(error => {
            console.error('Error loading daily active users chart:', error);
        });
}

function refreshDashboardData() {
    // Refresh current active users
    fetch('/api/dashboard/current-active-users')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the active users count in the card
                const activeUsersElement = document.querySelector('.bg-primary .card-body h3');
                if (activeUsersElement) {
                    activeUsersElement.textContent = data.active_users;
                }
            }
        })
        .catch(error => {
            console.error('Error refreshing active users:', error);
        });

    // Refresh user activity summary
    fetch('/api/dashboard/user-activity-summary')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const summary = data.summary;

                // Update cards
                const activeTodayElement = document.querySelector('.bg-success .card-body h3');
                if (activeTodayElement) {
                    activeTodayElement.textContent = summary.active_today;
                }

                const activeWeekElement = document.querySelector('.bg-info .card-body h3');
                if (activeWeekElement) {
                    activeWeekElement.textContent = summary.active_this_week;
                }

                const activityRateElement = document.querySelector('.bg-warning .card-body h3');
                if (activityRateElement) {
                    activityRateElement.textContent = summary.activity_rate_today + '%';
                }
            }
        })
        .catch(error => {
            console.error('Error refreshing activity summary:', error);
        });
}
</script>
@endpush
