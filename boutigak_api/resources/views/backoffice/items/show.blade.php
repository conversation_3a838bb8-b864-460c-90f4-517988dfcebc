@extends('backoffice.layouts.layout')
@section('title', 'Item Details')

@section('content')
<div class="page-content">
    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h2 class="mb-0 font-weight-bold text-primary">Item Details</h2>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item active">Post Details</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="row mb-4">
            <div class="col-12">
                <a href="{{ route('items.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Posts
                </a>
            </div>
        </div>

        <!-- Item Details -->
        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-4">
                <!-- Item Basic Info -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h4 class="card-title text-primary mb-4">{{ $item->title }}</h4>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span class="font-weight-bold">Price</span>
                                <span>{{ $item->price }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span class="font-weight-bold">Quantity</span>
                                <span>{{ $item->quantity }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span class="font-weight-bold">Brand</span>
                                <span>{{ $item->brand->name }}</span>
                            </li>
                            @if($item->user)
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">User</span>
                                    <span>{{ $item->user->firstname }} {{ $item->user->lastname }}</span>
                                </li>
                            @endif
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span class="font-weight-bold">Store</span>
                                <span>{{ $item->store->name ?? "" }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span class="font-weight-bold">Condition</span>
                                <span>{{ $item->condition }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span class="font-weight-bold">Category</span>
                                <span>{{ $item->category->title_en }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span class="font-weight-bold">Promoted</span>
                                <span>{{ $item->is_promoted ? 'Yes' : 'No' }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span class="font-weight-bold">Approved</span>
                                <span>{{ $item->status == \App\Enums\eItemStatus::APPROVED ? 'Yes' : 'No' }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span class="font-weight-bold">Matterport Link</span>
                                <span>
                                    @if($item->matterport_link)
                                        <a href="{{ $item->matterport_link }}" target="_blank">View Link</a>
                                    @else
                                        No Link
                                    @endif
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Category Post Details -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h4 class="card-title text-primary mb-4">Category Post Details</h4>
                        <ul class="list-group list-group-flush">
                            @foreach($item->categoryDetails as $detail)
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">{{ $detail->label_en }}</span>
                                    <span>{{ $detail->value }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="col-lg-8">
                <!-- Images Carousel -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h4 class="card-title text-primary mb-4">Post Images</h4>
                        <div id="itemImagesCarousel" class="carousel slide" data-bs-ride="carousel">
                            <div class="carousel-inner">
                                @foreach($item->images as $index => $image)
                                    <div class="carousel-item {{ $index == 0 ? 'active' : '' }}">
                                        <img src="{{ sftp_url($image->url) }}" class="d-block w-50 mx-auto rounded shadow-sm" alt="Item Image">
                                    </div>
                                @endforeach
                            </div>
                            <button class="carousel-control-prev" type="button" data-bs-target="#itemImagesCarousel" data-bs-slide="prev">
                                <span class="carousel-control-prev-icon"></span>
                                <span class="visually-hidden">Previous</span>
                            </button>
                            <button class="carousel-control-next" type="button" data-bs-target="#itemImagesCarousel" data-bs-slide="next">
                                <span class="carousel-control-next-icon"></span>
                                <span class="visually-hidden">Next</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h4 class="card-title text-primary mb-4">Actions</h4>
                        <button type="button" class="btn btn-primary" onclick="showMatterportModal()">Add Matterport Link</button>
                        <form id="status-form" action="{{ route('items.updateStatus', $item->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="status" id="status-input">
                            <button type="button"
                                class="btn {{ $item->status == \App\Enums\eItemStatus::APPROVED ? 'btn-warning' : 'btn-success' }}"
                                onclick="confirmStatusUpdate('{{ $item->status == \App\Enums\eItemStatus::APPROVED ? \App\Enums\eItemStatus::CREATED : \App\Enums\eItemStatus::APPROVED }}')">
                                {{ $item->status == \App\Enums\eItemStatus::APPROVED ? 'Unapprove' : 'Approve' }}
                            </button>
                        </form>
                        <form id="promote-form" action="{{ route('items.togglePromote', $item->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PUT')
                            <button type="button" class="btn {{ $item->is_promoted ? 'btn-secondary' : 'btn-warning' }} text-white" onclick="confirmPromoteToggle()">
                                {{ $item->is_promoted ? 'Unpromote' : 'Promote' }}
                            </button>
                        </form>
                        <button type="button" class="btn btn-danger" onclick="showRejectModal()">
                            {{ $item->status == \App\Enums\eItemStatus::REJECTED ? 'Update Rejection' : 'Reject' }}
                        </button>
                        <a href="{{ route('items.edit', $item->id) }}" class="btn btn-primary">Edit</a>
                        <form id="delete-form" action="{{ route('items.destroy', $item->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete</button>
                        </form>
                    </div>
                </div>

                <!-- Payment Details -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h4 class="card-title text-primary mb-4">Payment Details</h4>
                        @if($item->payments->isNotEmpty())
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Id</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Provider</th>
                                        <th>Promo code used</th>
                                        <th>Promo %</th>
                                        <th>Screenshot</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($item->payments as $payment)
                                        <tr>
                                            <td>{{ $payment->id }}</td>
                                            <td>{{ $payment->type }}</td>
                                            <td>{{ $payment->amount }}</td>
                                            <td>{{ $payment->provider->name ?? "BANKILY E-PAY" }}</td>
                                            <td>{{ $payment->promo_code ?? 'N/A' }}</td>
                                            <td>{{ $payment->item->promotion_percentage ?? 'N/A' }}</td>
                                            <td>
                                                @if($payment->photo_url)
                                                    <img src="{{ sftp_url($payment->photo_url) }}" alt="Payment Screenshot" class="rounded shadow-sm" style="height: 50px; width: 50px; object-fit: cover;">
                                                @else
                                                    No Screenshot
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        @else
                            <p>No payment details available.</p>
                        @endif
                    </div>
                </div>

                <!-- History Section -->
                @if($item->histories->isNotEmpty())
                    @php
                        $latestHistory = $item->histories->first();
                        $oldData = $latestHistory->old_data;
                    @endphp
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-body">
                            <h4 class="card-title text-primary mb-4">Latest Update History</h4>
                            <p class="text-muted">Updated by: {{ $latestHistory->user->firstname ?? 'System' }} on {{ $latestHistory->created_at->format('Y-m-d H:i') }}</p>

                            <div class="table-responsive mb-4">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Old Value</th>
                                            <th>Current Value</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($oldData as $field => $oldValue)
                                            @if(!is_array($oldValue) && isset($item->$field) && $item->$field != $oldValue)
                                                <tr>
                                                    <td>{{ ucfirst(str_replace('_', ' ', $field)) }}</td>
                                                    <td class="text-danger">{{ $oldValue }}</td>
                                                    <td class="text-success">{{ $item->$field }}</td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            @if(isset($oldData['images']))
                                <h5 class="mb-3">Images</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-muted">Old Images</h6>
                                        <div class="d-flex flex-wrap gap-2">
                                            @foreach($oldData['images'] as $img)
                                                <img src="{{ $img['url'] }}" class="img-thumbnail" style="max-width: 120px;">
                                            @endforeach
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-muted">Current Images</h6>
                                        <div class="d-flex flex-wrap gap-2">
                                            @foreach($item->images ?? [] as $img)
                                                <img src="{{ sftp_url($img->url) }}" class="img-thumbnail" style="max-width: 120px;">
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if(isset($oldData['category_details']))
                                <h5 class="mt-4">Category Details</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Label</th>
                                                <th>Old Value</th>
                                                <th>Current Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($oldData['category_details'] as $oldDetail)
                                                @php
                                                    $currentDetail = $item->categoryDetails->firstWhere('label_en', $oldDetail['label_en']);
                                                @endphp
                                                <tr>
                                                    <td>{{ $oldDetail['label_en'] }}</td>
                                                    <td class="text-danger">{{ $oldDetail['value'] }}</td>
                                                    <td class="text-success">{{ $currentDetail->value ?? '-' }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@include('backoffice.items.partials.modals')

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function showMatterportModal() {
            $('#matterportModal').modal('show');
        }

        function confirmStatusUpdate(newStatus) {
            const action = newStatus === '{{ \App\Enums\eItemStatus::APPROVED }}' ? 'approve' : 'unapprove';
            Swal.fire({
                title: 'Are you sure?',
                text: `Do you want to ${action} this item?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, ' + action + ' it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('status-input').value = newStatus;
                    document.getElementById('status-form').submit();
                }
            });
        }

        function confirmPromoteToggle() {
            const action = {{ $item->is_promoted ? 'true' : 'false' }} ? 'unpromote' : 'promote';
            Swal.fire({
                title: 'Are you sure?',
                text: `Do you want to ${action} this item?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, ' + action + ' it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('promote-form').submit();
                }
            });
        }

        function showRejectModal() {
            $('#rejectModal').modal('show');
        }

        function confirmReject() {
            Swal.fire({
                title: 'Are you sure?',
                text: 'Do you want to reject this item?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, reject it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('reject-form').submit();
                }
            });
        }

        function confirmDelete() {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('delete-form').submit();
                }
            });
        }
    </script>
@endpush
@endsection
