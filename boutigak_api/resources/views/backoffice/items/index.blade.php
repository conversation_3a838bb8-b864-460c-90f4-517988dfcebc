@extends('backoffice.layouts.layout')

@section('title', 'Items | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">Posts List</h4>
                        </div>
                        <div class="card-body">
                            <form id="filter-form" class="mb-4">
                                <div class="row">
                                    <div class="col-md">
                                        <label for="is_promoted">Promoted</label>
                                        <select id="is_promoted" class="form-control">
                                            <option value="">All</option>
                                            <option value="1">Promoted ({{ $promotedCount }})</option>
                                            <option value="0">Not Promoted ({{ $notPromotedCount }})</option>
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <label for="is_approved">Approval Status</label>
                                        <select id="is_approved" class="form-control">
                                            <option value="">All</option>
                                            <option value="approved">Approved ({{ $approvedCount }})</option>
                                            <option value="created">Not Approved ({{ $notApprovedCount }})</option>
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <label for="category_id">Category</label>
                                        <select id="category_id" class="form-control">
                                            <option value="">All</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}">{{ $category->title_en }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <label for="user_id">User</label>
                                        <select id="user_id" class="form-control">
                                            <option value="">All</option>
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}">{{ $user->firstname }} {{ $user->lastname }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md">
                                        <label for="sort_order">Sort Order</label>
                                        <select id="sort_order" class="form-control">
                                            <option value="recent">Most Recent</option>
                                            <option value="oldest">Oldest</option>
                                            <option value="sponsored">Sponsored</option>
                                        </select>
                                    </div>
                                </div>
                            </form>

                            <div class="table-responsive">
                                <table id="key-datatable" class="table dt-responsive nowrap w-100">
                                    <thead>
                                    <tr>
                                        <th>First Image</th>
                                        <th>Title</th>
                                        <th>User</th>
                                        <th>Phone</th>
                                        <th>Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Matterport Link Modal -->
    <div class="modal fade" id="matterportModal" tabindex="-1" aria-labelledby="matterportModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="matterportModalLabel">Add Matterport Link</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="matterportForm">
                        <div class="mb-3">
                            <label for="matterportLink" class="form-label">Matterport Link</label>
                            <input type="url" class="form-control" id="matterportLink" name="matterport_link" required>
                        </div>
                        <input type="hidden" id="itemId" name="item_id">
                        <button type="submit" class="btn btn-primary">Save</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        // Define the sftp_url function for JavaScript
        function sftp_url(path) {
            // Use the same base URL as defined in your PHP config
            
            return path
        }
        
        function openMatterportModal(itemId) {
            $('#itemId').val(itemId);
            $('#matterportModal').modal('show');
        }

        $(function () {
            var table = $('#key-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('items.index') }}",
                    data: function (d) {
                        d.is_promoted = $('#is_promoted').val();
                        d.is_approved = $('#is_approved').val();
                        d.category_id = $('#category_id').val();
                        d.user_id = $('#user_id').val();
                        d.sort_order = $('#sort_order').val();
                    },
                    error: function (xhr, status, error) {
                        console.log('AJAX Error:', xhr.responseText);
                        alert('Error fetching data. Please check the console for more information.');
                    }
                },
                columns: [
                    {
                        data: 'first_image', name: 'first_image', render: function (data, type, row) {
                            return data ? '<img src="' + sftp_url(data) + '" class="img-fluid img-thumbnail" alt="item-image" style="max-width: 50px;">' : 'No Image';
                        }
                    },
                    {data: 'title', name: 'title'},
                    {data: 'user.firstname', name: 'user.firstname'},
                    {data: 'user.phone', name: 'user.phone'},
                    {data: 'actions', name: 'actions', orderable: false, searchable: false},
                ]
            });

            $('#is_promoted, #is_approved, #category_id, #user_id, #sort_order').on('change keyup', function () {
                table.ajax.reload();
            });

            $('#matterportForm').on('submit', function (e) {
                e.preventDefault();
                var formData = $(this).serialize();

                $.ajax({
                    url: "{{ route('items.addMatterportLink') }}",
                    method: 'POST',
                    data: formData,
                    success: function (response) {
                        $('#matterportModal').modal('hide');
                        table.ajax.reload();
                        alert('Matterport link added successfully.');
                    },
                    error: function (xhr, status, error) {
                        console.log('AJAX Error:', xhr.responseText);
                        alert('Error adding Matterport link. Please check the console for more information.');
                    }
                });
            });
        });
    </script>
@endpush
