@extends('backoffice.layouts.layout')
@section('title', 'Edit Item')
@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h2 class="mb-0 font-weight-bold text-primary">Edit Item</h2>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="javascript: void(0);">Dashboard</a></li>
                                <li class="breadcrumb-item active">Edit Item</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <!-- Back Button -->
            <div class="row mb-4">
                <div class="col-12">
                    <a href="{{ route('items.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Items
                    </a>
                </div>
            </div>
            <!-- end back button -->

            <!-- Edit Item Form -->
            <div class="row">
                <div class="col-lg-8 offset-lg-2">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <form action="{{ route('items.update', $item->id) }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                @method('PUT')

                                <div class="form-group">
                                    <label for="title">Title</label>
                                    <input type="text" name="title" id="title" class="form-control" value="{{ old('title', $item->title) }}" required>
                                </div>

                                <div class="form-group">
                                    <label for="description">Description</label>
                                    <textarea name="description" id="description" class="form-control" required>{{ old('description', $item->description) }}</textarea>
                                </div>

                                <div class="form-group">
                                    <label for="price">Price</label>
                                    <input type="number" name="price" id="price" class="form-control" value="{{ old('price', $item->price) }}" required>
                                </div>

                                <div class="form-group">
                                    <label for="condition">Condition</label>
                                    <select name="condition" id="condition" class="form-control" required>
                                        <option value="New with packaging" {{ old('condition', $item->condition) == 'New with packaging' ? 'selected' : '' }}>New with packaging</option>
                                        <option value="New without packaging" {{ old('condition', $item->condition) == 'New without packaging' ? 'selected' : '' }}>New without packaging</option>
                                        <option value="Very good" {{ old('condition', $item->condition) == 'Very good' ? 'selected' : '' }}>Very good</option>
                                        <option value="Good" {{ old('condition', $item->condition) == 'Good' ? 'selected' : '' }}>Good</option>
                                        <option value="Satisfactory" {{ old('condition', $item->condition) == 'Satisfactory' ? 'selected' : '' }}>Satisfactory</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="brand_id">Brand</label>
                                    <select name="brand_id" id="brand_id" class="form-control" required>
                                        @foreach($brands as $brand)
                                            <option value="{{ $brand->id }}" {{ $item->brand_id == $brand->id ? 'selected' : '' }}>{{ $brand->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="category_id">Category</label>
                                    <select name="category_id" id="category_id" class="form-control" required>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ $item->category_id == $category->id ? 'selected' : '' }}>{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="category_item_details">Category Item Details</label>
                                    <div id="category_item_details">
                                        @foreach($item->categoryItemDetails as $detail)
                                            <div class="form-group">
                                                <input type="hidden" name="category_item_details[{{ $loop->index }}][id]" value="{{ $detail->id }}">
                                                <input type="text" name="category_item_details[{{ $loop->index }}][value]" class="form-control" value="{{ old('category_item_details.' . $loop->index . '.value', $detail->value) }}">
                                            </div>
                                        @endforeach
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="images">Images</label>
                                    <input type="file" name="images[]" id="images" class="form-control" multiple>
                                    <div class="mt-2">
                                        @foreach($item->images as $image)
                                            <img src="{{ sftp_url($image->url) }}" alt="Item Image" class="img-thumbnail" width="100">
                                        @endforeach
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">Update Item</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Edit Item Form -->
        </div>
    </div>
@endsection
