@extends('backoffice.layouts.layout')

@section('title', 'Promo Codes | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">Promo Codes</h4>
                            <a href="{{ route('promo-codes.create') }}" class="btn btn-primary">Create Promo Code</a>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="{{ route('promo-codes.index') }}">
                                <div class="row mb-3 align-items-center ">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="is_active">Status</label>
                                            <select name="is_active" id="is_active" class="form-control">
                                                <option value="">All</option>
                                                <option value="1">Active</option>
                                                <option value="0">Stopped</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="discount">Discount</label>
                                            <select name="discount" id="discount" class="form-control">
                                                <option value="">All</option>
                                                <option value="10">10%</option>
                                                <option value="20">20%</option>
                                                <option value="30">30%</option>
                                                <option value="40">40%</option>
                                                <option value="50">50%</option>
                                                <option value="60">60%</option>
                                                <option value="70">70%</option>
                                                <option value="80">80%</option>
                                                <option value="90">90%</option>
                                                <option value="100">100%</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="expires_at">Expires At</label>
                                            <input type="date" name="expires_at" id="expires_at" class="form-control">
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <table id="key-datatable" class="table dt-responsive nowrap w-100 data-table">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Code</th>
                                    <th>Discount</th>
                                    <th>Status</th>
                                    <th>Expires At</th>
                                    <th>Limit usages</th>
                                    <th>Usage count</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        function confirmDelete(promoCodeId) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('delete-form-' + promoCodeId).submit();
                }
            })
        }

        function confirmStop(promoCodeId, isActive) {
            Swal.fire({
                title: isActive ? 'Are you sure you want to stop this promo code?' : 'Are you sure you want to unstop this promo code?',
                text: "You can revert this action!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: isActive ? 'Yes, stop it!' : 'Yes, unstop it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.post({
                        url: '{{ url("promo-codes/stop") }}/' + promoCodeId,
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function (response) {
                            $('.data-table').DataTable().ajax.reload();
                            Swal.fire('Success', response.success, 'success');
                        },
                        error: function (xhr) {
                            Swal.fire('Error', 'An error occurred while updating the promo code status.', 'error');
                        }
                    });
                }
            })
        }
    </script>

    <script type="text/javascript">
        $(function () {
            const table = $('.data-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('promo-codes.index') }}",
                    data: function (d) {
                        d.is_active = $('#is_active').val() || null;
                        d.discount = $('#discount').val() || null;
                        d.expires_at = $('#expires_at').val() || null;
                    },
                    error: function (xhr, status, error) {
                        console.log('AJAX Error:', xhr.responseText);
                        alert('Error fetching data. Please check the console for more information.');
                    }
                },
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'code', name: 'code'},
                    {data: 'discount', name: 'discount'},
                    {data: 'is_active', name: 'is_active'},
                    {data: 'expires_at', name: 'expires_at'},
                    {data: 'limit_usages', name: 'limit_usages'},
                    {data: 'times_used', name: 'times_used'},
                    {data: 'action', name: 'action', orderable: false, searchable: false},
                ]
            });

            // Reload table when filters are changed
            $('#is_active, #discount, #expires_at').change(function () {
                table.ajax.reload();
            });
        });
    </script>
@endpush
