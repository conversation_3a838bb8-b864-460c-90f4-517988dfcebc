@extends('backoffice.layouts.layout')

@section('title', 'Users Subscriptions | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">Users Subscriptions</h4>
                            <div>
                                <a href="{{ route('users-subscription.create') }}" class="btn btn-primary mx-1">Add
                                    Subscription</a>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="key-datatable" class="table dt-responsive nowrap w-100 data-table">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>User Name</th>
                                    <th>Price</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        function confirmDelete(subscriptionId) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('delete-form-' + subscriptionId).submit();
                }
            })
        }
    </script>

    <script type="text/javascript">
        $(function () {
            var table = $('.data-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('users-subscription.index') }}",
                    error: function (xhr, status, error) {
                        console.log('AJAX Error:', xhr.responseText);
                        alert('Error fetching data. Please check the console for more information.');
                    }
                },
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'user.firstname', name: 'user.firstname'},
                    {data: 'price', name: 'price'},
                    {data: 'start_date', name: 'start_date'},
                    {data: 'end_date', name: 'end_date'},
                    {data: 'action', name: 'action', orderable: false, searchable: false},
                ]
            });
        });
    </script>
@endpush
