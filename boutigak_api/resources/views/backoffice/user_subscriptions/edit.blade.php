@extends('backoffice.layouts.layout')

@section('title', 'Edit Store Subscription | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-xl-6">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title">Edit User Subscription</h4>
                            <p class="card-title-desc">Update the details of the user subscription.</p>

                            <form class="custom-validation"
                                  action="{{ route('users-subscription.update', $subscription->id) }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="mb-3">
                                    <label>User</label>
                                    <select class="form-control select2"  multiple="multiple" name="user_id" required>
                                        @foreach($users as $user)
                                            <option
                                                value="{{ $user->id }}" {{ $user->id == $subscription->user_id ? 'selected' : '' }}>{{ $user->firstname." " .$user->lastname}}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label>Price</label>
                                    <input type="number" class="form-control" name="price"
                                           value="{{ $subscription->price }}" required placeholder="Enter price"/>
                                </div>

                                <div class="mb-3">
                                    <label>Start Date</label>
                                    <input type="date" class="form-control" name="start_date"
                                           value="{{ $subscription->start_date }}" required
                                           placeholder="Enter start date"/>
                                </div>

                                <div class="mb-3">
                                    <label>End Date</label>
                                    <input type="date" class="form-control" name="end_date"
                                           value="{{ $subscription->end_date }}" required placeholder="Enter end date"/>
                                </div>

                                <div class="mb-0">
                                    <div>
                                        <button type="submit" class="btn btn-primary waves-effect waves-light me-1">
                                            Submit
                                        </button>
                                        <button type="reset" class="btn btn-secondary waves-effect">
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </div> <!-- end col -->
            </div> <!-- end row -->
        </div> <!-- container-fluid -->
    </div> <!-- page-content -->
@endsection
