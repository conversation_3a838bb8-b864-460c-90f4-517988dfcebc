@extends('backoffice.layouts.layout')

@section('title', 'Create Store Subscription | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title">Create User Subscription</h4>
                            <p class="card-title-desc">Fill in the details to create a new user subscription.</p>
                            @if ($errors->any())
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    <ul>
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                            <form class="custom-validation" action="{{ route('users-subscription.store') }}" method="POST">
                                @csrf
                                <div class="mb-3">
                                    <label>Store</label>
                                    <select class="form-control select2"  multiple="multiple" name="user_id" required>
                                        <option value="">Select User</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}">{{ $user->firstname }} {{ $user->lastname }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label>Price</label>
                                    <input type="number" class="form-control" name="price" required placeholder="Enter price"/>
                                </div>

                                <div class="mb-3">
                                    <label>Start Date</label>
                                    <input type="date" class="form-control" name="start_date" required placeholder="Enter start date"/>
                                </div>

                                <div class="mb-3">
                                    <label>End Date</label>
                                    <input type="date" class="form-control" name="end_date" required placeholder="Enter end date"/>
                                </div>

                                <div class="mb-0">
                                    <div>
                                        <button type="submit" class="btn btn-primary waves-effect waves-light me-1">
                                            Submit
                                        </button>
                                        <button type="reset" class="btn btn-secondary waves-effect">
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </div> <!-- end col -->
            </div> <!-- end row -->
        </div> <!-- container-fluid -->
    </div> <!-- page-content -->
@endsection
