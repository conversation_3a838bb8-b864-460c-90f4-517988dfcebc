@extends('backoffice.layouts.layout')
@section('title', 'Order Details')
@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h2 class="mb-0 font-weight-bold text-primary">Order Details</h2>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="javascript: void(0);">Dashboard</a></li>
                                <li class="breadcrumb-item active">Order Details</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <!-- Back Button -->
            <div class="row mb-4">
                <div class="col-12">
                    <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Orders
                    </a>
                </div>
            </div>
            <!-- end back button -->

            <!-- Order Details -->
            <div class="row">
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h4 class="card-title text-primary mb-4">Order Information</h4>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">Order ID</span>
                                    <span>{{ $order->id }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">User</span>
                                    <span>{{ $order->user->firstname }} {{ $order->user->lastname }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">Phone number</span>
                                    <span>{{ $order->user->phone }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">Store</span>
                                    <span>{{ $order->store->name }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">Store Phone number</span>
                                    <span>{{ $order->store->user->phone }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">Status</span>
                                    <span>{{ $order->status }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">Total</span>
                                    <span>{{ $order->total }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">Paid</span>
                                    <span>{{ $order->is_paid ? 'Yes' : 'No' }}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-body">
                            <h4 class="card-title text-primary mb-4">Order Items</h4>
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>Item ID</th>
                                    <th>Item Image</th>
                                    <th>Item Name</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($formattedItems as $orderItem)
                                    <tr>
                                        <td>{{ $orderItem['item_id'] }}</td>
                                        <td><img src="{{ $orderItem['image_url'] ? sftp_url($orderItem['image_url']) : null }}"
                                                 alt="Item Image" class="img-fluid w-25">
                                        </td>
                                        <td>{{ $orderItem['title'] }}</td>
                                        <td>{{ $orderItem['quantity'] }}</td>
                                        <td>{{ $orderItem['price'] }}</td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Order Details -->
        </div>
    </div>
@endsection
