@extends('backoffice.layouts.layout')

@section('title', 'Orders | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">Orders</h4>
                            <div>
                                <a href="{{ route('orders.create') }}" class="btn btn-primary mx-1">Add Order</a>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="key-datatable" class="table dt-responsive nowrap w-100 data-table">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>User</th>
                                    <th>Store</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Is Paid</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Status Modal -->
    <div class="modal fade" id="changeStatusModal" tabindex="-1" role="dialog" aria-labelledby="changeStatusModalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="changeStatusModalLabel">Change Order Status</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="closeModal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="changeStatusForm" action="" method="POST">
                    @csrf
                    @method('PUT')
                    <input type="hidden" id="orderId" name="order_id" value="">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="orderStatus">Status</label>
                            <select class="form-control" id="orderStatus" name="status" required>
                                <option value="PENDING">Pending</option>
                                <option value="PROCESSING">Processing</option>
                                <option value="COMPLETED">Completed</option>
                                <option value="CANCELLED">Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" id="btnCloseModal">Close
                        </button>
                        <button type="submit" class="btn btn-primary">Save changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        $(function () {
            var table = $('.data-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('orders.index') }}",
                    data: function (d) {
                        d.status = $('#status').val() || null;
                        d.is_paid = $('#is_paid').val() || null;
                    },
                    error: function (xhr, status, error) {
                        console.log('AJAX Error:', xhr.responseText);
                        alert('Error fetching data. Please check the console for more information.');
                    }
                },
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'user.firstname', name: 'user.firstname'},
                    {data: 'store.name', name: 'store.name'},
                    {data: 'status', name: 'status'},
                    {data: 'total', name: 'total'},
                    {data: 'is_paid', name: 'is_paid'},
                    {data: 'actions', name: 'action', orderable: false, searchable: false},
                ]
            });

            $('#status, #is_paid').change(function () {
                table.ajax.reload();
            });

            $(document).on('click', '.change-status', function () {
                const orderId = $(this).data('id');
                const formAction = '{{ url("orders") }}/' + orderId + '/change-status';
                // open modal
                $('#changeStatusModal').modal('show');
                $('#changeStatusForm').attr('action', formAction);
                $('#orderId').val(orderId);
            });

            $('#closeModal, #btnCloseModal').on('click', function () {
                $('#changeStatusModal').modal('hide');
            });

        });
    </script>
@endpush
