<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Table Edits jQuery Plugin</title>
  <meta name="description" content="">
  <meta name="author" content="">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="//fonts.googleapis.com/css?family=Raleway:400,300,600" rel="stylesheet" type="text/css">

  <link rel="stylesheet" href="bower_components/skeleton/css/normalize.css">
  <link rel="stylesheet" href="bower_components/skeleton/css/skeleton.css">
  <link rel="stylesheet" href="bower_components/pikaday/css/pikaday.css">
  <link rel="stylesheet" href="bower_components/pikaday-skeleton/css/pikaday-skeleton.css">
  <link rel="stylesheet" href="bower_components/font-awesome/css/font-awesome.css">

  <style>
    body {
      margin-bottom: 100px;
    }

    .row {
      margin-bottom: 20px;
    }

    pre {
      margin-top: 0;
    }

    .button.button-small {
        height: 30px;
        line-height: 30px;
        padding: 0px 10px;
    }

    td input[type=text], td select {
      width: 100%;
      height: 30px;
      margin: 0;
      padding: 2px 8px;
    }

    th:last-child {
      text-align: right;
    }

    td:last-child {
        text-align: right;
    }

    td:last-child .button {
        width: 30px;
        height: 30px;
        text-align: center;
        padding: 0px;
        margin-bottom: 0px;
        margin-right: 5px;
        background-color: #FFF;
    }

    td:last-child .button .fa {
        line-height: 30px;
        width: 30px;
    }
  </style>
</head>
<body>
  <a href="https://github.com/nathancahill/table-edits"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://camo.githubusercontent.com/e7bbb0521b397edbd5fe43e7f760759336b5e05f/68747470733a2f2f73332e616d617a6f6e6177732e636f6d2f6769746875622f726962626f6e732f666f726b6d655f72696768745f677265656e5f3030373230302e706e67" alt="Fork me on GitHub" data-canonical-src="https://s3.amazonaws.com/github/ribbons/forkme_right_green_007200.png"></a>

  <div class="container">
    <div class="row">
      <div class="twelve columns" style="margin-top: 5%">
        <h4>$.Table Edits</h4>
        <p>
          Table Edits is a lightweight jQuery plugin for making table rows editable.
          Built as minimally as possible so it's easy to extend.
        </p>
        <table class="u-full-width demo">
          <thead>
            <tr>
              <th>Name</th>
              <th>Birthday</th>
              <th>Age</th>
              <th>Sex</th>
              <th>Edit</th>
            </tr>
          </thead>
          <tbody>
            <tr data-id="1">
              <td data-field="name">Dave Gamache</td>
              <td data-field="birthday">May 19, 2015</td>
              <td data-field="age">26</td>
              <td data-field="sex">Male</td>
              <td>
                <a class="button button-small edit" title="Edit">
                  <i class="fa fa-pencil"></i>
                </a>
              </td>
            </tr>
            <tr data-id="2">
              <td data-field="name">Dwayne Johnson</td>
              <td data-field="birthday">May 19, 2015</td>
              <td data-field="age">42</td>
              <td data-field="sex">Male</td>
              <td>
                <a class="button button-small edit" title="Edit">
                  <i class="fa fa-pencil"></i>
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="row">
      <div class="six columns">
        <p>
          <strong>Table Edits</strong> only does a couple things:
          <ul>
            <li>Replaces cell values with input fields on edit</li>
            <li>Handles row editing state</li>
            <li>Fires callbacks for edit, save and cancel</li>
          </ul>
          And <strong>optionally</strong>, a couple more:
          <br /><br />
          <ul>
            <li>Binds a button or double click to start editing</li>
            <li>Binds enter and escape keys to save and cancel</li>
            <li>Maintains column widths when switching to edit</li>
            <li>Create select fields instead of input fields</li>
          </ul>
        </p>
      </div>
      <div class="six columns">
<pre><code>$("table tr").editable({
    keyboard: true,
    dblclick: true,
    button: true,
    buttonSelector: ".edit",
    dropdowns: {},
    maintainWidth: true,
    edit: function(values) {},
    save: function(values) {},
    cancel: function(values) {}
});</code></pre>
      </div>
    </div>
    <div class="row">
      <div class="twelve columns">
        <p>
          The only additional markup <strong>Table Edits</strong> requires
          is a <code>data-field</code> attribute on each editable cell with it's column name.
        </p>
      </div>
    </div>
    <div class="row">
      <div class="six columns">
        <h5>Saving Table Data</h5>
        <p>
          Table Edits makes it easy to save edits. Callbacks are passed a <code>values</code>
          object with column names and values of the edited row.
          <br /><br />
          Posting the new data to an API endpoint is simple.
        </p>
      </div>
      <div class="six columns">
<pre><code>$("table tr").editable({
    save: function(values) {
      var id = $(this).data('id');
      $.post('/api/object/' + id, values);
    }
});</code></pre>
      </div>
    </div>
  </div>

  <script src="bower_components/jquery/dist/jquery.js"></script>
  <script src="bower_components/momentjs/moment.js"></script>
  <script src="bower_components/pikaday/pikaday.js"></script>
  <script src="build/table-edits.min.js"></script>
  <script>
    $(function() {
      var pickers = {};

      $('table tr').editable({
        dropdowns: {
          sex: ['Male', 'Female']
        },
        edit: function(values) {
          $(".edit i", this)
            .removeClass('fa-pencil')
            .addClass('fa-save')
            .attr('title', 'Save');

          pickers[this] = new Pikaday({
            field: $("td[data-field=birthday] input", this)[0],
            format: 'MMM D, YYYY'
          });
        },
        save: function(values) {
          $(".edit i", this)
            .removeClass('fa-save')
            .addClass('fa-pencil')
            .attr('title', 'Edit');

          if (this in pickers) {
            pickers[this].destroy();
            delete pickers[this];
          }
        },
        cancel: function(values) {
          $(".edit i", this)
            .removeClass('fa-save')
            .addClass('fa-pencil')
            .attr('title', 'Edit');

          if (this in pickers) {
            pickers[this].destroy();
            delete pickers[this];
          }
        }
      });
    });
  </script>
</body>
</html>
