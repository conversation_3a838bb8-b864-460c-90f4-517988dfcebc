<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Bootstrap rating</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap core CSS -->
    <link href="bower_components/bootstrap/dist/css/bootstrap.css" rel="stylesheet">
    <!-- Font Awesome CSS -->
    <link href="bower_components/fontawesome/css/font-awesome.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="bootstrap-rating.css" rel="stylesheet">
    <style>
      .custom-heart {
        font-size: 2.5em;
        color: red;
      }
    </style>
  </head>
  <body>
    <div class="survey-builder container">
      <h2>Bootstrap rating</h2>
      <hr/>
      <h3>Default rating</h3>
      <input type="hidden" class="rating"/>
      <h3>Half rating</h3>
      <input type="hidden" class="rating" data-fractions="2"/>
      <h3>Disabled rating</h3>
      <input type="hidden" class="rating" disabled="disabled"/>
      <h3>Readonly rating with a value</h3>
      <input type="hidden" class="rating" data-readonly value="3"/>
      <h3>Readonly rating with a fractional value</h3>
      <input type="hidden" class="rating" data-readonly value="2.5"/>
      <h3>Customized heart rating</h3>
      <input type="hidden" class="rating" data-filled="glyphicon glyphicon-heart custom-heart" data-empty="glyphicon glyphicon-heart-empty custom-heart"/>
      <h3>Only fill selected</h3>
      <input type="hidden" class="rating" data-filled="glyphicon glyphicon-heart-empty" data-filled-selected="glyphicon glyphicon-heart" data-empty="glyphicon glyphicon-heart-empty"/>
      <h3>Handle events</h3>
      <input type="hidden" class="rating check" data-filled="glyphicon glyphicon-check" data-empty="glyphicon glyphicon-unchecked"/>
      <h3>Programmatically set/get rate</h3>
      <input type="hidden" class="rating-tooltip" id="programmatically-rating">
      <input type="number" id="programmatically-value">
      <button type="button" id="programmatically-set">Set value</button>
      <button type="button" id="programmatically-get">Get value</button>
      <h3>Customize tooltips</h3>
      <input type="hidden" class="rating-tooltip"/>
      <h3>Set stop rate to 10 [1..10]</h3>
      <input type="hidden" class="rating-tooltip" data-stop="10"/>
      <h3>Set start rate to 5 [6..10]</h3>
      <input type="hidden" class="rating-tooltip" data-start="5"/>
      <h3>Set start and stop rate [2..10]</h3>
      <input type="hidden" class="rating-tooltip" data-start="1" data-stop="10"/>
      <h3>Set start and stop rate [2..10] with step 2</h3>
      <input type="hidden" class="rating-tooltip" data-stop="10" data-step="2"/>
      <h3>Set start and stop rate [-2..-10] with step -2</h3>
      <input type="hidden" class="rating-tooltip" data-stop="-10" data-step="-2"/>
      <h3>Font Awesome icons</h3>
      <input type="hidden" class="rating-tooltip" data-filled="fa fa-bell fa-3x" data-empty="fa fa-bell-o fa-3x"/>
      <h3>Fractional rating</h3>
      <input type="hidden" class="rating-tooltip-manual" data-filled="fa fa-star fa-3x" data-empty="fa fa-star-o fa-3x" data-fractions="3"/>
      <h3>Fractional right-to-left rating</h3>
      <div dir="rtl">
        <input type="hidden" class="rating-tooltip-manual" data-filled="fa fa-star fa-3x" data-empty="fa fa-star-o fa-3x" data-fractions="3"/>
      </div>
      <h3>Custom CSS icons</h3>
      <input type="hidden" class="rating" data-filled="symbol symbol-filled" data-empty="symbol symbol-empty" data-fractions="2"/>
    </div>
    <script type="text/javascript" src="bower_components/jquery/dist/jquery.js"></script>
    <script type="text/javascript" src="bower_components/bootstrap/js/tooltip.js"></script>
    <script type="text/javascript" src="bootstrap-rating.js"></script>
    <script>
      $(function () {
        $('input.check').on('change', function () {
          alert('Rating: ' + $(this).val());
        });
        $('#programmatically-set').click(function () {
          $('#programmatically-rating').rating('rate', $('#programmatically-value').val());
        });
        $('#programmatically-get').click(function () {
          alert($('#programmatically-rating').rating('rate'));
        });
        $('.rating-tooltip').rating({
          extendSymbol: function (rate) {
            $(this).tooltip({
              container: 'body',
              placement: 'bottom',
              title: 'Rate ' + rate
            });
          }
        });
        $('.rating-tooltip-manual').rating({
          extendSymbol: function () {
            var title;
            $(this).tooltip({
              container: 'body',
              placement: 'bottom',
              trigger: 'manual',
              title: function () {
                return title;
              }
            });
            $(this).on('rating.rateenter', function (e, rate) {
              title = rate;
              $(this).tooltip('show');
            })
            .on('rating.rateleave', function () {
              $(this).tooltip('hide');
            });
          }
        });
        $('.rating').each(function () {
          $('<span class="label label-default"></span>')
            .text($(this).val() || ' ')
            .insertAfter(this);
        });
        $('.rating').on('change', function () {
          $(this).next('.label').text($(this).val());
        });
      });
    </script>
  </body>
</html>
