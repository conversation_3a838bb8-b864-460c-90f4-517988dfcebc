/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.2 (2020-06-10)
 */
!function(c){"use strict";var e,t,n,r,o=tinymce.util.Tools.resolve("tinymce.PluginManager"),a=0,l=function(e,t,n){var r,o,i,u;o=(r=e.editorUpload.blobCache).create((i="mceu",u=(new Date).getTime(),i+"_"+Math.floor(1e9*Math.random())+ ++a+String(u)),n,t),r.add(o),e.insertContent(e.dom.createHTML("img",{src:o.blobUri()}))},f=tinymce.util.Tools.resolve("tinymce.util.Promise"),s=tinymce.util.Tools.resolve("tinymce.Env"),d=tinymce.util.Tools.resolve("tinymce.util.Delay"),i=function(u){u.ui.registry.addButton("quickimage",{icon:"image",tooltip:"Insert image",onAction:function(){var i;i=u,new f(function(n){var r=c.document.createElement("input");r.type="file",r.accept="image/*",r.style.position="fixed",r.style.left="0",r.style.top="0",r.style.opacity="0.001",c.document.body.appendChild(r),r.addEventListener("change",function(e){n(Array.prototype.slice.call(e.target.files))});var o=function(e){var t=function(){n([]),r.parentNode.removeChild(r)};s.os.isAndroid()&&"remove"!==e.type?d.setEditorTimeout(i,t,0):t(),i.off("focusin remove",o)};i.on("focusin remove",o),r.click()}).then(function(e){if(0<e.length){var t=e[0];n=t,new f(function(e){var t=new c.FileReader;t.onloadend=function(){e(t.result.split(",")[1])},t.readAsDataURL(n)}).then(function(e){l(u,e,t)})}var n})}}),u.ui.registry.addButton("quicktable",{icon:"table",tooltip:"Insert table",onAction:function(){var e,t,n,r,o,i;n=t=2,(e=u).plugins.table?e.plugins.table.insertTable(t,n):(o=t,i=n,(r=e).undoManager.transact(function(){var e,t;r.insertContent(function(e,t){var n,r,o;for(o='<table data-mce-id="mce" style="width: 100%">',o+="<tbody>",r=0;r<t;r++){for(o+="<tr>",n=0;n<e;n++)o+="<td><br></td>";o+="</tr>"}return o+="</tbody>",o+="</table>"}(o,i)),(e=r.dom.select("*[data-mce-id]")[0]).removeAttribute("data-mce-id"),t=r.dom.select("td,th",e),r.selection.setCursorLocation(t[0],0)}))}})},u=function(e){return function(){return e}},m=u(!1),g=u(!0),h=function(){return p},p=(e=function(e){return e.isNone()},{fold:function(e,t){return e()},is:m,isSome:m,isNone:g,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:u(null),getOrUndefined:u(undefined),or:n,orThunk:t,map:h,each:function(){},bind:h,exists:m,forall:g,filter:h,equals:e,equals_:e,toArray:function(){return[]},toString:u("none()")}),b=function(n){var e=u(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:g,isNone:m,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return b(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:p},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(m,function(e){return t(n,e)})}};return o},v={some:b,none:h,from:function(e){return null===e||e===undefined?p:b(e)}},y=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:u(e)}},w={fromHtml:function(e,t){var n=(t||c.document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw c.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return y(n.childNodes[0])},fromTag:function(e,t){var n=(t||c.document).createElement(e);return y(n)},fromText:function(e,t){var n=(t||c.document).createTextNode(e);return y(n)},fromDom:y,fromPoint:function(e,t,n){var r=e.dom();return v.from(r.elementFromPoint(t,n)).map(y)}},k=("undefined"!=typeof c.window?c.window:Function("return this;")(),function(r){return function(e){return n=typeof(t=e),(null===t?"null":"object"==n&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":n)===r;var t,n}}),T=function(t){return function(e){return typeof e===t}},N=k("string"),q=k("object"),E=k("array"),M=T("boolean"),S=(r=undefined,function(e){return r===e}),C=T("function");function O(e,t,n,r,o){return e(n,r)?v.some(n):C(o)&&o(n)?v.none():t(n,r,o)}var x,A=function(e,t){var n=e.dom();if(1!==n.nodeType)return!1;var r=n;if(r.matches!==undefined)return r.matches(t);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(t);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(t);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},D=function(e,t,n){for(var r=e.dom(),o=C(n)?n:u(!1);r.parentNode;){r=r.parentNode;var i=w.fromDom(r);if(t(i))return v.some(i);if(o(i))break}return v.none()},_=function(e,t,n){return D(e,function(e){return A(e,t)},n)},L=(x=N,function(e,t,n){return function(e,t){if(!t(e))throw new Error("Default value doesn't match requested type.")}(n,x),function(e,t){if(E(e)||q(e))throw new Error("expected a string but found: "+e);return S(e)?t:M(e)?!1===e?"":t:e}(e.getParam(t,n),n)}),P=function(o){var e=L(o,"quickbars_insert_toolbar","quickimage quicktable");0<e.trim().length&&o.ui.registry.addContextToolbar("quickblock",{predicate:function(e){var t=w.fromDom(e),n=o.schema.getTextBlockElements(),r=function(e){return e.dom()===o.getBody()};return O(function(e,t){return A(e,t)},_,t,"table",r).fold(function(){return O(function(e,t){return t(e)},D,t,function(e){return e.dom().nodeName.toLowerCase()in n&&o.dom.isEmpty(e.dom())},r).isSome()},function(){return!1})},items:e,position:"line",scope:"editor"})},U=function(n){var r=function(e){return"IMG"===e.nodeName||"FIGURE"===e.nodeName&&/image/i.test(e.className)},e=L(n,"quickbars_image_toolbar","alignleft aligncenter alignright");0<e.trim().length&&n.ui.registry.addContextToolbar("imageselection",{predicate:r,items:e,position:"node"});var t=L(n,"quickbars_selection_toolbar","bold italic | quicklink h2 h3 blockquote");0<t.trim().length&&n.ui.registry.addContextToolbar("textselection",{predicate:function(e){return!r(e)&&!n.selection.isCollapsed()&&(t=e,"false"!==n.dom.getContentEditableParent(t));var t},items:t,position:"selection",scope:"editor"})};!function B(){o.add("quickbars",function(e){i(e),P(e),U(e)})}()}(window);