/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.2 (2020-06-10)
 */
!function(p){"use strict";var S=function(e){var n=e;return{get:function(){return n},set:function(e){n=e}}},x=function(){},b=function(e){return function(){return e}},o=function(e){return e};function y(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=o.concat(e);return r.apply(null,t)}}var e,n,t,r,a=function(n){return function(e){return!n(e)}},f=b(!1),i=b(!0),u=function(){return c},c=(e=function(e){return e.isNone()},{fold:function(e,n){return e()},is:f,isSome:f,isNone:i,getOr:t=function(e){return e},getOrThunk:n=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:b(null),getOrUndefined:b(undefined),or:t,orThunk:n,map:u,each:x,bind:u,exists:f,forall:i,filter:u,equals:e,equals_:e,toArray:function(){return[]},toString:b("none()")}),l=function(t){var e=b(t),n=function(){return o},r=function(e){return e(t)},o={fold:function(e,n){return n(t)},is:function(e){return t===e},isSome:i,isNone:f,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:n,orThunk:n,map:function(e){return l(e(t))},each:function(e){e(t)},bind:r,exists:r,forall:r,filter:function(e){return e(t)?o:c},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(e){return e.is(t)},equals_:function(e,n){return e.fold(f,function(e){return n(t,e)})}};return o},C={some:l,none:u,from:function(e){return null===e||e===undefined?c:l(e)}},s=tinymce.util.Tools.resolve("tinymce.PluginManager"),d=function(r){return function(e){return t=typeof(n=e),(null===n?"null":"object"==t&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":t)===r;var n,t}},m=function(n){return function(e){return typeof e===n}},g=d("string"),h=d("array"),v=m("boolean"),w=m("function"),R=m("number"),T=Array.prototype.slice,O=Array.prototype.indexOf,D=Array.prototype.push,A=function(e,n){return t=e,r=n,-1<O.call(t,r);var t,r},E=function(e,n){for(var t=0,r=e.length;t<r;t++){if(n(e[t],t))return!0}return!1},k=function(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;o++){var i=e[o];r[o]=n(i,o)}return r},B=function(e,n){for(var t=0,r=e.length;t<r;t++){n(e[t],t)}},N=function(e,n){for(var t=[],r=0,o=e.length;r<o;r++){var i=e[r];n(i,r)&&t.push(i)}return t},I=function(e,n,t){return function(e,n){for(var t=e.length-1;0<=t;t--){n(e[t],t)}}(e,function(e){t=n(t,e)}),t},P=function(e,n,t){return B(e,function(e){t=n(t,e)}),t},M=function(e,n){return function(e,n,t){for(var r=0,o=e.length;r<o;r++){var i=e[r];if(n(i,r))return C.some(i);if(t(i,r))break}return C.none()}(e,n,f)},_=function(e,n){for(var t=0,r=e.length;t<r;t++){if(n(e[t],t))return C.some(t)}return C.none()},W=function(e){for(var n=[],t=0,r=e.length;t<r;++t){if(!h(e[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+e);D.apply(n,e[t])}return n},j=function(e,n){return W(k(e,n))},z=function(e,n){for(var t=0,r=e.length;t<r;++t){if(!0!==n(e[t],t))return!1}return!0},L=function(e,n){for(var t=0;t<e.length;t++){var r=n(e[t],t);if(r.isSome())return r}return C.none()},F=Object.keys,H=Object.hasOwnProperty,q=function(e,n){for(var t=F(e),r=0,o=t.length;r<o;r++){var i=t[r];n(e[i],i)}},U=function(e,t){return V(e,function(e,n){return{k:n,v:t(e,n)}})},V=function(e,r){var o={};return q(e,function(e,n){var t=r(e,n);o[t.k]=t.v}),o},K=function(e,n){return X(e,n)?C.from(e[n]):C.none()},X=function(e,n){return H.call(e,n)},$=("undefined"!=typeof p.window?p.window:Function("return this;")(),function(e){return e.dom().nodeName.toLowerCase()}),G=function(e){return e.dom().nodeType},Y=function(n){return function(e){return G(e)===n}},J=function(e){return 8===G(e)||"#comment"===$(e)},Q=Y(1),Z=Y(3),ee=function(e,n,t){if(!(g(t)||v(t)||R(t)))throw p.console.error("Invalid call to Attr.set. Key ",n,":: Value ",t,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(n,t+"")},ne=function(e,n,t){ee(e.dom(),n,t)},te=function(e,n){var t=e.dom();q(n,function(e,n){ee(t,n,e)})},re=function(e,n){var t=e.dom().getAttribute(n);return null===t?undefined:t},oe=function(e,n){return C.from(re(e,n))},ie=function(e,n){var t=e.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(n)},ue=function(e,n){e.dom().removeAttribute(n)},ce=function(e){return P(e.dom().attributes,function(e,n){return e[n.name]=n.value,e},{})},ae=function(e,n,t){return""===n||e.length>=n.length&&e.substr(t,t+n.length)===n},le=function(e,n){return-1!==e.indexOf(n)},fe=function(e,n){return ae(e,n,0)},se=(r=/^\s+|\s+$/g,function(e){return e.replace(r,"")}),de=function(e){return e.style!==undefined&&w(e.style.getPropertyValue)},me=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:b(e)}},ge={fromHtml:function(e,n){var t=(n||p.document).createElement("div");if(t.innerHTML=e,!t.hasChildNodes()||1<t.childNodes.length)throw p.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return me(t.childNodes[0])},fromTag:function(e,n){var t=(n||p.document).createElement(e);return me(t)},fromText:function(e,n){var t=(n||p.document).createTextNode(e);return me(t)},fromDom:me,fromPoint:function(e,n,t){var r=e.dom();return C.from(r.elementFromPoint(n,t)).map(me)}},pe=function(e){var n=Z(e)?e.dom().parentNode:e.dom();return n!==undefined&&null!==n&&n.ownerDocument.body.contains(n)},he=function(e){var n=e.dom().body;if(null===n||n===undefined)throw new Error("Body is not available yet");return ge.fromDom(n)},ve=function(e,n,t){if(!g(t))throw p.console.error("Invalid call to CSS.set. Property ",n,":: Value ",t,":: Element ",e),new Error("CSS value must be a string: "+t);de(e)&&e.style.setProperty(n,t)},be=function(e,n,t){var r=e.dom();ve(r,n,t)},we=function(e,n){var t=e.dom();q(n,function(e,n){ve(t,n,e)})},ye=function(e,n){var t=e.dom(),r=p.window.getComputedStyle(t).getPropertyValue(n);return""!==r||pe(e)?r:Se(t,n)},Se=function(e,n){return de(e)?e.style.getPropertyValue(n):""},xe=function(e,n){var t=e.dom(),r=Se(t,n);return C.from(r).filter(function(e){return 0<e.length})},Ce=function(e,n){var t,r,o=e.dom();r=n,de(t=o)&&t.style.removeProperty(r),oe(e,"style").map(se).is("")&&ue(e,"style")},Re=function(){return(Re=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e}).apply(this,arguments)},Te=function(t){var r,o=!1;return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return o||(o=!0,r=t.apply(null,e)),r}},Oe=function(e,n){var t=function(e,n){for(var t=0;t<e.length;t++){var r=e[t];if(r.test(n))return r}return undefined}(e,n);if(!t)return{major:0,minor:0};var r=function(e){return Number(n.replace(t,"$"+e))};return Ae(r(1),r(2))},De=function(){return Ae(0,0)},Ae=function(e,n){return{major:e,minor:n}},Ee={nu:Ae,detect:function(e,n){var t=String(n).toLowerCase();return 0===e.length?De():Oe(e,t)},unknown:De},ke="Firefox",Be=function(e){var n=e.current,t=e.version,r=function(e){return function(){return n===e}};return{current:n,version:t,isEdge:r("Edge"),isChrome:r("Chrome"),isIE:r("IE"),isOpera:r("Opera"),isFirefox:r(ke),isSafari:r("Safari")}},Ne={unknown:function(){return Be({current:undefined,version:Ee.unknown()})},nu:Be,edge:b("Edge"),chrome:b("Chrome"),ie:b("IE"),opera:b("Opera"),firefox:b(ke),safari:b("Safari")},Ie="Windows",Pe="Android",Me="Solaris",_e="FreeBSD",We="ChromeOS",je=function(e){var n=e.current,t=e.version,r=function(e){return function(){return n===e}};return{current:n,version:t,isWindows:r(Ie),isiOS:r("iOS"),isAndroid:r(Pe),isOSX:r("OSX"),isLinux:r("Linux"),isSolaris:r(Me),isFreeBSD:r(_e),isChromeOS:r(We)}},ze={unknown:function(){return je({current:undefined,version:Ee.unknown()})},nu:je,windows:b(Ie),ios:b("iOS"),android:b(Pe),linux:b("Linux"),osx:b("OSX"),solaris:b(Me),freebsd:b(_e),chromeos:b(We)},Le=function(e,n){var t=String(n).toLowerCase();return M(e,function(e){return e.search(t)})},Fe=function(e,t){return Le(e,t).map(function(e){var n=Ee.detect(e.versionRegexes,t);return{current:e.name,version:n}})},He=function(e,t){return Le(e,t).map(function(e){var n=Ee.detect(e.versionRegexes,t);return{current:e.name,version:n}})},qe=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Ue=function(n){return function(e){return le(e,n)}},Ve=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return le(e,"edge/")&&le(e,"chrome")&&le(e,"safari")&&le(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,qe],search:function(e){return le(e,"chrome")&&!le(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return le(e,"msie")||le(e,"trident")}},{name:"Opera",versionRegexes:[qe,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Ue("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Ue("firefox")},{name:"Safari",versionRegexes:[qe,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(le(e,"safari")||le(e,"mobile/"))&&le(e,"applewebkit")}}],Ke=[{name:"Windows",search:Ue("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return le(e,"iphone")||le(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Ue("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Ue("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Ue("linux"),versionRegexes:[]},{name:"Solaris",search:Ue("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Ue("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Ue("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Xe={browsers:b(Ve),oses:b(Ke)},$e=function(e,n){var t,r,o,i,u,c,a,l,f,s,d,m,g=Xe.browsers(),p=Xe.oses(),h=Fe(g,e).fold(Ne.unknown,Ne.nu),v=He(p,e).fold(ze.unknown,ze.nu);return{browser:h,os:v,deviceType:(r=h,o=e,i=n,u=(t=v).isiOS()&&!0===/ipad/i.test(o),c=t.isiOS()&&!u,a=t.isiOS()||t.isAndroid(),l=a||i("(pointer:coarse)"),f=u||!c&&a&&i("(min-device-width:768px)"),s=c||a&&!f,d=r.isSafari()&&t.isiOS()&&!1===/safari/i.test(o),m=!s&&!f&&!d,{isiPad:b(u),isiPhone:b(c),isTablet:b(f),isPhone:b(s),isTouch:b(l),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:b(d),isDesktop:b(m)})}},Ge=function(e){return p.window.matchMedia(e).matches},Ye=Te(function(){return $e(p.navigator.userAgent,Ge)}),Je=function(){return Ye()},Qe=function(e,n){var t=e.dom();if(1!==t.nodeType)return!1;var r=t;if(r.matches!==undefined)return r.matches(n);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(n);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(n);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},Ze=function(e){return 1!==e.nodeType&&9!==e.nodeType||0===e.childElementCount},en=function(e,n){return e.dom()===n.dom()},nn=function(e,n){return t=e.dom(),r=n.dom(),o=t,i=r,u=p.Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(o.compareDocumentPosition(i)&u);var t,r,o,i,u},tn=function(e,n){return Je().browser.isIE()?nn(e,n):(t=n,r=e.dom(),o=t.dom(),r!==o&&r.contains(o));var t,r,o},rn=Qe,on=function(e){return ge.fromDom(e.dom().ownerDocument)},un=function(e){return C.from(e.dom().parentNode).map(ge.fromDom)},cn=function(e,n){for(var t=w(n)?n:f,r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=ge.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o},an=function(e){return C.from(e.dom().previousSibling).map(ge.fromDom)},ln=function(e){return C.from(e.dom().nextSibling).map(ge.fromDom)},fn=function(e){return k(e.dom().childNodes,ge.fromDom)},sn=function(e,n){var t=e.dom().childNodes;return C.from(t[n]).map(ge.fromDom)},dn=function(n,t){un(n).each(function(e){e.dom().insertBefore(t.dom(),n.dom())})},mn=function(e,n){ln(e).fold(function(){un(e).each(function(e){pn(e,n)})},function(e){dn(e,n)})},gn=function(n,t){sn(n,0).fold(function(){pn(n,t)},function(e){n.dom().insertBefore(t.dom(),e.dom())})},pn=function(e,n){e.dom().appendChild(n.dom())},hn=function(e,n){dn(e,n),pn(n,e)},vn=function(r,o){B(o,function(e,n){var t=0===n?r:o[n-1];mn(t,e)})},bn=function(n,e){B(e,function(e){pn(n,e)})},wn=function(e){e.dom().textContent="",B(fn(e),function(e){yn(e)})},yn=function(e){var n=e.dom();null!==n.parentNode&&n.parentNode.removeChild(n)},Sn=function(e){var n,t=fn(e);0<t.length&&(n=e,B(t,function(e){dn(n,e)})),yn(e)},xn=function(e,n,t){return{element:b(e),rowspan:b(n),colspan:b(t)}},Cn=function(e,n,t){return{element:b(e),cells:b(n),section:b(t)}},Rn=function(e,n){return{element:b(e),isNew:b(n)}},Tn=function(e,n){return{cells:b(e),section:b(n)}},On=function(e,n){var t=[];return B(fn(e),function(e){n(e)&&(t=t.concat([e])),t=t.concat(On(e,n))}),t},Dn=function(e,n,t){return r=function(e){return Qe(e,n)},N(cn(e,t),r);var r},An=function(e,n){return t=function(e){return Qe(e,n)},N(fn(e),t);var t},En=function(e,n){return t=n,o=(r=e)===undefined?p.document:r.dom(),Ze(o)?[]:k(o.querySelectorAll(t),ge.fromDom);var t,r,o};function kn(e,n,t,r,o){return e(t,r)?C.some(t):w(o)&&o(t)?C.none():n(t,r,o)}var Bn=function(e,n,t){for(var r=e.dom(),o=w(t)?t:b(!1);r.parentNode;){r=r.parentNode;var i=ge.fromDom(r);if(n(i))return C.some(i);if(o(i))break}return C.none()},Nn=function(e,n,t){return Bn(e,function(e){return Qe(e,n)},t)},In=function(e,n){return t=function(e){return Qe(e,n)},M(e.dom().childNodes,function(e){return t(ge.fromDom(e))}).map(ge.fromDom);var t},Pn=function(e,n){return t=n,o=(r=e)===undefined?p.document:r.dom(),Ze(o)?C.none():C.from(o.querySelector(t)).map(ge.fromDom);var t,r,o},Mn=function(e,n,t){return kn(function(e,n){return Qe(e,n)},Nn,e,n,t)},_n=function(e,n,t){return void 0===t&&(t=0),oe(e,n).map(function(e){return parseInt(e,10)}).getOr(t)},Wn=function(e,n){return _n(e,n,1)},jn=function(e){return 1<Wn(e,"colspan")},zn=function(e){return 1<Wn(e,"rowspan")},Ln=function(e,n){return parseInt(ye(e,n),10)},Fn=b(10),Hn=b(10),qn=function(e,n){return Un(e,n,b(!0))},Un=function(e,n,t){return j(fn(e),function(e){return Qe(e,n)?t(e)?[e]:[]:Un(e,n,t)})},Vn=function(e,n){return function(e,n,t){if(void 0===t&&(t=f),t(n))return C.none();if(A(e,$(n)))return C.some(n);return Nn(n,e.join(","),function(e){return Qe(e,"table")||t(e)})}(["td","th"],e,n)},Kn=function(e){return qn(e,"th,td")},Xn=function(n,e){return un(e).map(function(e){return An(e,n)})},$n=(y(Xn,"th,td"),y(Xn,"tr"),function(e,n){return Mn(e,"table",n)}),Gn=function(e){var n=qn(e,"tr");return k(n,function(e){var n=e,t=un(n).map(function(e){var n=$(e);return"tfoot"===n||"thead"===n||"tbody"===n?n:"tbody"}).getOr("tbody"),r=k(Kn(e),function(e){var n=_n(e,"rowspan",1),t=_n(e,"colspan",1);return xn(e,n,t)});return Cn(n,r,t)})},Yn=function(e,n){return e+","+n},Jn=function(e,n){var t=j(e.all,function(e){return e.cells()});return N(t,n)},Qn={generate:function(e){var d={},n=[],t=e.length,m=0;return B(e,function(e,f){var s=[];B(e.cells(),function(e){for(var n=0;d[Yn(f,n)]!==undefined;)n++;for(var t,r,o,i=(t=e.element(),r=e.rowspan(),o=e.colspan(),{element:b(t),rowspan:b(r),colspan:b(o),row:b(f),column:b(n)}),u=0;u<e.colspan();u++)for(var c=0;c<e.rowspan();c++){var a=n+u,l=Yn(f+c,a);d[l]=i,m=Math.max(m,a+1)}s.push(i)}),n.push(Cn(e.element(),s,e.section()))}),{grid:{rows:b(t),columns:b(m)},access:d,all:n}},getAt:function(e,n,t){var r=e.access[Yn(n,t)];return r!==undefined?C.some(r):C.none()},findItem:function(e,n,t){var r=Jn(e,function(e){return t(n,e.element())});return 0<r.length?C.some(r[0]):C.none()},filterItems:Jn,justCells:function(e){var n=k(e.all,function(e){return e.cells()});return W(n)}},Zn=function(e,n){var t,i,r,u,c,a,l,o,f,s,d=function(e){return Qe(e.element(),n)},m=Gn(e),g=Qn.generate(m),p=(i=d,r=(t=g).grid.columns(),u=t.grid.rows(),c=r,l=a=0,q(t.access,function(e){if(i(e)){var n=e.row(),t=n+e.rowspan()-1,r=e.column(),o=r+e.colspan()-1;n<u?u=n:a<t&&(a=t),r<c?c=r:l<o&&(l=o)}}),{minRow:u,minCol:c,maxRow:a,maxCol:l}),h="th:not("+n+"),td:not("+n+")",v=Un(e,"th,td",function(e){return Qe(e,h)});return B(v,yn),function(e,n,t,r){for(var o,i,u,c=n.grid.columns(),a=n.grid.rows(),l=0;l<a;l++)for(var f=!1,s=0;s<c;s++){if(!(l<t.minRow||l>t.maxRow||s<t.minCol||s>t.maxCol))Qn.getAt(n,l,s).filter(r).isNone()?(o=f,0,i=e[l].element(),u=ge.fromTag("td"),pn(u,ge.fromTag("br")),(o?pn:gn)(i,u)):f=!0}}(m,g,p,d),f=p,s=N(qn(o=e,"tr"),function(e){return 0===e.dom().childElementCount}),B(s,yn),f.minCol!==f.maxCol&&f.minRow!==f.maxRow||B(qn(o,"th,td"),function(e){ue(e,"rowspan"),ue(e,"colspan")}),ue(o,"width"),ue(o,"height"),Ce(o,"width"),Ce(o,"height"),e};var et=function bf(t,r){var n=function(e){return t(e)?C.from(e.dom().nodeValue):C.none()};return{get:function(e){if(!t(e))throw new Error("Can only get "+r+" value of a "+r+" node");return n(e).getOr("")},getOption:n,set:function(e,n){if(!t(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom().nodeValue=n}}}(Z,"text"),nt=function(e){return et.get(e)},tt=function(e){return et.getOption(e)},rt=function(e,n){return et.set(e,n)},ot=function(e){return"img"===$(e)?1:tt(e).fold(function(){return fn(e).length},function(e){return e.length})},it=["img","br"],ut=function(e){return tt(e).filter(function(e){return 0!==e.trim().length||-1<e.indexOf("\xa0")}).isSome()||A(it,$(e))},ct=function(e){return o=ut,(i=function(e){for(var n=0;n<e.childNodes.length;n++){var t=ge.fromDom(e.childNodes[n]);if(o(t))return C.some(t);var r=i(e.childNodes[n]);if(r.isSome())return r}return C.none()})(e.dom());var o,i},at=function(e){return lt(e,ut)},lt=function(e,i){var u=function(e){for(var n=fn(e),t=n.length-1;0<=t;t--){var r=n[t];if(i(r))return C.some(r);var o=u(r);if(o.isSome())return o}return C.none()};return u(e)},ft=function(e,n){return ge.fromDom(e.dom().cloneNode(n))},st=function(e){return ft(e,!1)},dt=function(e){return ft(e,!0)},mt=function(e,n){var t,r,o,i,u=(t=e,r=n,o=ge.fromTag(r),i=ce(t),te(o,i),o),c=fn(dt(e));return bn(u,c),u},gt=function(){var e=ge.fromTag("td");return pn(e,ge.fromTag("br")),e},pt=function(e,n,t){var r=mt(e,n);return q(t,function(e,n){null===e?ue(r,n):ne(r,n,e)}),r},ht=function(e){return e},vt=function(e){return function(){return ge.fromTag("tr",e.dom())}},bt=function(d,e,m){return{row:vt(e),cell:function(e){var r,o,i,n,t,u,c,a=on(e.element()),l=ge.fromTag($(e.element()),a.dom()),f=m.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),s=0<f.length?(r=e.element(),o=l,i=f,ct(r).map(function(e){var n=i.join(","),t=Dn(e,n,function(e){return en(e,r)});return I(t,function(e,n){var t=st(n);return ue(t,"contenteditable"),pn(e,t),t},o)}).getOr(o)):l;return pn(s,ge.fromTag("br")),n=e.element(),t=l,u=n.dom(),c=t.dom(),de(u)&&de(c)&&(c.style.cssText=u.style.cssText),Ce(l,"height"),1!==e.colspan()&&Ce(e.element(),"width"),d(e.element(),l),l},replace:pt,gap:gt}},wt=function(e){return{row:vt(e),cell:gt,replace:ht,gap:gt}},yt=function(e,n){var t=n.column(),r=n.column()+n.colspan()-1,o=n.row(),i=n.row()+n.rowspan()-1;return t<=e.finishCol()&&r>=e.startCol()&&o<=e.finishRow()&&i>=e.startRow()},St=function(e,n){return n.column()>=e.startCol()&&n.column()+n.colspan()-1<=e.finishCol()&&n.row()>=e.startRow()&&n.row()+n.rowspan()-1<=e.finishRow()},xt=function(e,n){return t=Math.min(e.row(),n.row()),r=Math.min(e.column(),n.column()),o=Math.max(e.row()+e.rowspan()-1,n.row()+n.rowspan()-1),i=Math.max(e.column()+e.colspan()-1,n.column()+n.colspan()-1),{startRow:b(t),startCol:b(r),finishRow:b(o),finishCol:b(i)};var t,r,o,i},Ct=function(e,n,t){var r=Qn.findItem(e,n,en),o=Qn.findItem(e,t,en);return r.bind(function(n){return o.map(function(e){return xt(n,e)})})},Rt=function(n,e,t){return Ct(n,e,t).bind(function(e){return function(e,n){for(var t=!0,r=y(St,n),o=n.startRow();o<=n.finishRow();o++)for(var i=n.startCol();i<=n.finishCol();i++)t=t&&Qn.getAt(e,o,i).exists(r);return t?C.some(n):C.none()}(n,e)})},Tt=function(t,e,n){return Ct(t,e,n).map(function(e){var n=Qn.filterItems(t,y(yt,e));return k(n,function(e){return e.element()})})},Ot=function(e,n){return Qn.findItem(e,n,function(e,n){return tn(n,e)}).map(function(e){return e.element()})},Dt=function(u,c,a){return $n(u).bind(function(e){var r,n,o,i,t=Et(e);return r=t,n=u,o=c,i=a,Qn.findItem(r,n,en).bind(function(e){var n=0<o?e.row()+e.rowspan()-1:e.row(),t=0<i?e.column()+e.colspan()-1:e.column();return Qn.getAt(r,n+o,t+i).map(function(e){return e.element()})})})},At=function(e,n,t,r,o){var i=Et(e),u=en(e,t)?C.some(n):Ot(i,n),c=en(e,o)?C.some(r):Ot(i,r);return u.bind(function(n){return c.bind(function(e){return Tt(i,n,e)})})},Et=function(e){var n=Gn(e);return Qn.generate(n)},kt=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function Bt(){return{up:b({selector:Nn,closest:Mn,predicate:Bn,all:cn}),down:b({selector:En,predicate:On}),styles:b({get:ye,getRaw:xe,set:be,remove:Ce}),attrs:b({get:re,set:ne,remove:ue,copyTo:function(e,n){var t=ce(e);te(n,t)}}),insert:b({before:dn,after:mn,afterAll:vn,append:pn,appendAll:bn,prepend:gn,wrap:hn}),remove:b({unwrap:Sn,remove:yn}),create:b({nu:ge.fromTag,clone:function(e){return ge.fromDom(e.dom().cloneNode(!1))},text:ge.fromText}),query:b({comparePosition:function(e,n){return e.dom().compareDocumentPosition(n.dom())},prevSibling:an,nextSibling:ln}),property:b({children:fn,name:$,parent:un,document:function(e){return e.dom().ownerDocument},isText:Z,isComment:J,isElement:Q,getText:nt,setText:rt,isBoundary:function(e){return!!Q(e)&&("body"===$(e)||A(kt,$(e)))},isEmptyTag:function(e){return!!Q(e)&&A(["br","img","hr","input"],$(e))},isNonEditable:function(e){return Q(e)&&"false"===re(e,"contenteditable")}}),eq:en,is:rn}}var Nt=function(r,o,e,n){var t=o(r,e);return I(n,function(e,n){var t=o(r,n);return It(r,e,t)},t)},It=function(n,e,t){return e.bind(function(e){return t.filter(y(n.eq,e))})},Pt=function(e,n,t){return 0<t.length?Nt(e,n,(r=t)[0],r.slice(1)):C.none();var r},Mt=function(t,e,n,r){void 0===r&&(r=f);var o=[e].concat(t.up().all(e)),i=[n].concat(t.up().all(n)),u=function(n){return _(n,r).fold(function(){return n},function(e){return n.slice(0,e+1)})},c=u(o),a=u(i),l=M(c,function(e){return E(a,(n=e,y(t.eq,n)));var n});return{firstpath:b(c),secondpath:b(a),shared:b(l)}},_t=Bt(),Wt=function(t,e){return Pt(_t,function(e,n){return t(n)},e)},jt=function(e){return Nn(e,"table")},zt=function(l,f,s){var d=function(n){return function(e){return s!==undefined&&s(e)||en(e,n)}};return en(l,f)?C.some({boxes:C.some([l]),start:l,finish:f}):jt(l).bind(function(a){return jt(f).bind(function(i){if(en(a,i))return C.some({boxes:(o=l,u=f,c=Et(a),Tt(c,o,u)),start:l,finish:f});if(tn(a,i)){var e=0<(n=Dn(f,"td,th",d(a))).length?n[n.length-1]:f;return C.some({boxes:At(a,l,a,f,i),start:l,finish:e})}if(tn(i,a)){var n,t=0<(n=Dn(l,"td,th",d(i))).length?n[n.length-1]:l;return C.some({boxes:At(i,l,a,f,i),start:l,finish:t})}return Mt(_t,l,f,r).shared().bind(function(e){return Mn(e,"table",s).bind(function(e){var n=Dn(f,"td,th",d(e)),t=0<n.length?n[n.length-1]:f,r=Dn(l,"td,th",d(e)),o=0<r.length?r[r.length-1]:l;return C.some({boxes:At(e,l,a,f,i),start:o,finish:t})})});var r,o,u,c})})},Lt=function(e,n){var t=En(e,n);return 0<t.length?C.some(t):C.none()},Ft=function(e,n,r){return Pn(e,n).bind(function(t){return Pn(e,r).bind(function(n){return Wt(jt,[t,n]).map(function(e){return{first:b(t),last:b(n),table:b(e)}})})})},Ht=function(e,n,t,r,o){return i=o,M(e,function(e){return Qe(e,i)}).bind(function(e){return Dt(e,n,t).bind(function(e){return t=r,Nn(n=e,"table").bind(function(e){return Pn(e,t).bind(function(e){return zt(e,n).bind(function(n){return n.boxes.map(function(e){return{boxes:e,start:n.start,finish:n.finish}})})})});var n,t})});var i},qt=function(r,e,n){return Ft(r,e,n).bind(function(u){var e=function(e){return en(r,e)},n=Nn(u.first(),"thead,tfoot,tbody,table",e),t=Nn(u.last(),"thead,tfoot,tbody,table",e);return n.bind(function(i){return t.bind(function(e){return en(i,e)?(n=u.table(),t=u.first(),r=u.last(),o=Et(n),Rt(o,t,r)):C.none();var n,t,r,o})})})},Ut="data-mce-first-selected",Vt="data-mce-last-selected",Kt="td[data-mce-selected],th[data-mce-selected]",Xt="[data-mce-selected]",$t=Ut,Gt="td[data-mce-first-selected],th[data-mce-first-selected]",Yt=Vt,Jt="td[data-mce-last-selected],th[data-mce-last-selected]",Qt=/* */Object.freeze({__proto__:null,selected:"data-mce-selected",selectedSelector:Kt,attributeSelector:Xt,firstSelected:$t,firstSelectedSelector:Gt,lastSelected:Yt,lastSelectedSelector:Jt}),Zt=function(u){if(!h(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return B(u,function(e,r){var n=F(e);if(1!==n.length)throw new Error("one and only one name per case");var o=n[0],i=e[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!h(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var t=new Array(e),n=0;n<t.length;n++)t[n]=arguments[n];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(e){var n=F(e);if(c.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+n.join(","));if(!z(c,function(e){return A(n,e)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+c.join(", "));return e[o].apply(null,t)},log:function(e){p.console.log(e,{constructors:c,constructor:o,params:t})}}}}),t},er=Zt([{none:[]},{multiple:["elements"]},{single:["selection"]}]),nr=function(e,n,t,r){return e.fold(n,t,r)},tr=er.none,rr=er.multiple,or=er.single,ir=function(e,n){return nr(n.get(),b([]),o,b([e]))},ur=function(e){return{element:b(e),mergable:C.none,unmergable:C.none,selection:b([e])}},cr=function(e,n,t){return{element:b(t),mergable:b((o=n,nr(e.get(),C.none,function(n,e){return 0===n.length?C.none():qt(o,Gt,Jt).bind(function(e){return 1<n.length?C.some({bounds:b(e),cells:b(n)}):C.none()})},C.none))),unmergable:b(0<(r=ir(t,e)).length&&z(r,function(e){return ie(e,"rowspan")&&1<parseInt(re(e,"rowspan"),10)||ie(e,"colspan")&&1<parseInt(re(e,"colspan"),10)})?C.some(r):C.none()),selection:b(ir(t,e))};var r,o},ar=function(d,e,m,g){d.on("BeforeGetContent",function(t){!0===t.selection&&nr(e.get(),x,function(e){t.preventDefault(),$n(e[0]).map(dt).map(function(e){return[Zn(e,Xt)]}).each(function(e){var n;t.content="text"===t.format?k(e,function(e){return e.dom().innerText}).join(""):(n=d,k(e,function(e){return n.selection.serializer.serialize(e.dom(),{})}).join(""))})},x)}),d.on("BeforeSetContent",function(s){!0===s.selection&&!0===s.paste&&C.from(d.dom.getParent(d.selection.getStart(),"th,td")).each(function(e){var f=ge.fromDom(e);$n(f).each(function(n){var e,t,r,o,i,u=N((e=s.content,(r=(t||p.document).createElement("div")).innerHTML=e,fn(ge.fromDom(r))),function(e){return"meta"!==$(e)});if(1===u.length&&"table"===$(u[0])){s.preventDefault();var c=ge.fromDom(d.getDoc()),a=wt(c),l=(o=f,i=u[0],{element:b(o),clipboard:b(i),generators:b(a)});m.pasteCells(n,l).each(function(e){d.selection.setRng(e),d.focus(),g.clear(n)})}})})})};function lr(r,o){var e=function(e){var n=o(e);if(n<=0||null===n){var t=ye(e,r);return parseFloat(t)||0}return n},i=function(o,e){return P(e,function(e,n){var t=ye(o,n),r=t===undefined?0:parseInt(t,10);return isNaN(r)?e:e+r},0)};return{set:function(e,n){if(!R(n)&&!n.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+n);var t=e.dom();de(t)&&(t.style[r]=n+"px")},get:e,getOuter:e,aggregate:i,max:function(e,n,t){var r=i(e,t);return r<n?n-r:0}}}var fr=lr("height",function(e){var n=e.dom();return pe(e)?n.getBoundingClientRect().height:n.offsetHeight}),sr=function(e){return fr.get(e)},dr=function(e){return fr.getOuter(e)},mr=lr("width",function(e){return e.dom().offsetWidth}),gr=function(e){return mr.get(e)},pr=function(e){return mr.getOuter(e)},hr=function(e,n,t){return r=ye(e,n),o=t,i=parseFloat(r),isNaN(i)?o:i;var r,o,i},vr=function(e){return(c=Je()).browser.isIE()||c.browser.isEdge()?(t=hr(n=e,"padding-top",0),r=hr(n,"padding-bottom",0),o=hr(n,"border-top-width",0),i=hr(n,"border-bottom-width",0),u=n.dom().getBoundingClientRect().height,"border-box"===ye(n,"box-sizing")?u:u-t-r-(o+i)):hr(e,"height",sr(e));var n,t,r,o,i,u,c},br=/(\d+(\.\d+)?)(\w|%)*/,wr=/(\d+(\.\d+)?)%/,yr=/(\d+(\.\d+)?)px|em/,Sr=function(e,n){be(e,"width",n+"px")},xr=function(e,n){be(e,"width",n+"%")},Cr=function(e,n){be(e,"height",n+"px")},Rr=function(e,n,t,r){var o,i,u,c,a,l,f,s=parseInt(e,10);return ae(l=e,f="%",l.length-f.length)&&"table"!==$(n)?(i=s,u=t,c=r,a=$n(o=n).map(function(e){var n=u(e);return Math.floor(i/100*n)}).getOr(i),c(o,a),a):s},Tr=function(e){var n,t=xe(n=e,"height").getOrThunk(function(){return vr(n)+"px"});return t?Rr(t,e,sr,Cr):sr(e)},Or=function(e){return xe(e,"width").fold(function(){return C.from(re(e,"width"))},function(e){return C.some(e)})},Dr=function(e,n){return e/n.pixelWidth()*100},Ar=function(n,t){return Or(n).fold(function(){var e=gr(n);return Dr(e,t)},function(e){return function(e,n,t){var r=wr.exec(n);if(null!==r)return parseFloat(r[1]);var o=gr(e);return Dr(o,t)}(n,e,t)})},Er=function(n,t){return Or(n).fold(function(){return gr(n)},function(e){return function(e,n,t){var r=yr.exec(n);if(null!==r)return parseInt(r[1],10);var o=wr.exec(n);if(null===o)return gr(e);var i=parseFloat(o[1]);return i/100*t.pixelWidth()}(n,e,t)})},kr=function(e){return t="rowspan",Tr(n=e)/Wn(n,t);var n,t},Br=function(e,n,t){be(e,"width",n+t)},Nr=b(wr),Ir=b(yr),Pr=function(t,r){Or(t).bind(function(e){var n=br.exec(e);return null!==n?C.some({width:b(parseFloat(n[1])),unit:b(n[3])}):C.none()}).each(function(e){var n=e.width()/2;Br(t,n,e.unit()),Br(r,n,e.unit())})},Mr=function(t,r){return{left:b(t),top:b(r),translate:function(e,n){return Mr(t+e,r+n)}}},_r=Mr,Wr=function(e,n){return e!==undefined?e:n!==undefined?n:0},jr=function(e){var n=e.dom().ownerDocument,t=n.body,r=n.defaultView,o=n.documentElement;if(t===e.dom())return _r(t.offsetLeft,t.offsetTop);var i=Wr(r.pageYOffset,o.scrollTop),u=Wr(r.pageXOffset,o.scrollLeft),c=Wr(o.clientTop,t.clientTop),a=Wr(o.clientLeft,t.clientLeft);return zr(e).translate(u-a,i-c)},zr=function(e){var n,t=e.dom(),r=t.ownerDocument.body;return r===t?_r(r.offsetLeft,r.offsetTop):pe(e)?(n=t.getBoundingClientRect(),_r(n.left,n.top)):_r(0,0)},Lr=function(e,n){return{row:e,y:n}},Fr=function(e,n){return{col:e,x:n}},Hr=function(e){return jr(e).left()+pr(e)},qr=function(e){return jr(e).left()},Ur=function(e,n){return Fr(e,qr(n))},Vr=function(e,n){return Fr(e,Hr(n))},Kr=function(e){return jr(e).top()},Xr=function(e,n){return Lr(e,Kr(n))},$r=function(e,n){return Lr(e,Kr(n)+dr(n))},Gr=function(t,n,r){if(0===r.length)return[];var e=k(r.slice(1),function(e,n){return e.map(function(e){return t(n,e)})}),o=r[r.length-1].map(function(e){return n(r.length-1,e)});return e.concat([o])},Yr={delta:o,positions:function(e){return Gr(Xr,$r,e)},edge:Kr},Jr={ltr:{delta:o,edge:qr,positions:function(e){return Gr(Ur,Vr,e)}},rtl:{delta:function(e){return-e},edge:Hr,positions:function(e){return Gr(Vr,Ur,e)}}};function Qr(n){var t=function(e){return n(e).isRtl()?Jr.rtl:Jr.ltr};return{delta:function(e,n){return t(n).delta(e,n)},edge:function(e){return t(e).edge(e)},positions:function(e,n){return t(n).positions(e,n)}}}var Zr=function(e){var n=Gn(e);return Qn.generate(n).grid},eo=function(e){for(var n=[],t=function(e){n.push(e)},r=0;r<e.length;r++)e[r].each(t);return n},no=function(e,n,t,r){t===r?ue(e,n):ne(e,n,t)},to=function(o,e){var i=[],u=[],n=function(e,n){0<e.length?function(e,n){var t=In(o,n).getOrThunk(function(){var e=ge.fromTag(n,on(o).dom());return pn(o,e),e});wn(t);var r=k(e,function(e){e.isNew()&&i.push(e.element());var n=e.element();return wn(n),B(e.cells(),function(e){e.isNew()&&u.push(e.element()),no(e.element(),"colspan",e.colspan(),1),no(e.element(),"rowspan",e.rowspan(),1),pn(n,e.element())}),n});bn(t,r)}(e,n):In(o,n).each(yn)},t=[],r=[],c=[];return B(e,function(e){switch(e.section()){case"thead":t.push(e);break;case"tbody":r.push(e);break;case"tfoot":c.push(e)}}),n(t,"thead"),n(r,"tbody"),n(c,"tfoot"),{newRows:i,newCells:u}},ro=function(e,n){var t=re(e,n);return t===undefined||""===t?[]:t.split(" ")},oo=function(e){return e.dom().classList!==undefined},io=function(e,n){return o=n,i=ro(t=e,r="class").concat([o]),ne(t,r,i.join(" ")),!0;var t,r,o,i},uo=function(e,n){return o=n,0<(i=N(ro(t=e,r="class"),function(e){return e!==o})).length?ne(t,r,i.join(" ")):ue(t,r),!1;var t,r,o,i},co=function(e,n){oo(e)?e.dom().classList.add(n):io(e,n)},ao=function(e){0===(oo(e)?e.dom().classList:ro(e,"class")).length&&ue(e,"class")},lo=function(e,n){return oo(e)&&e.dom().classList.contains(n)},fo=function(e,n){for(var t=[],r=e;r<n;r++)t.push(r);return t},so=function(r,o){if(o<0||o>=r.length-1)return C.none();var e=r[o].fold(function(){var e,n,t=(e=r.slice(0,o),(n=T.call(e,0)).reverse(),n);return L(t,function(e,n){return e.map(function(e){return{value:e,delta:n+1}})})},function(e){return C.some({value:e,delta:0})}),n=r[o+1].fold(function(){var e=r.slice(o+1);return L(e,function(e,n){return e.map(function(e){return{value:e,delta:n+1}})})},function(e){return C.some({value:e,delta:1})});return e.bind(function(t){return n.map(function(e){var n=e.delta+t.delta;return Math.abs(e.value-t.value)/n})})},mo=function(t){var e=t.grid,n=fo(0,e.columns()),r=fo(0,e.rows());return k(n,function(n){return go(function(){return j(r,function(e){return Qn.getAt(t,e,n).filter(function(e){return e.column()===n}).fold(b([]),function(e){return[e]})})},function(e){return 1===e.colspan()},function(){return Qn.getAt(t,0,n)})})},go=function(e,n,t){var r=e();return M(r,n).orThunk(function(){return C.from(r[0]).orThunk(t)}).map(function(e){return e.element()})},po=function(t){var e=t.grid,n=fo(0,e.rows()),r=fo(0,e.columns());return k(n,function(n){return go(function(){return j(r,function(e){return Qn.getAt(t,n,e).filter(function(e){return e.row()===n}).fold(b([]),function(e){return[e]})})},function(e){return 1===e.rowspan()},function(){return Qn.getAt(t,n,0)})})},ho=function(e){var n=e.replace(/\./g,"-");return{resolve:function(e){return n+"-"+e}}},vo=ho("ephox-snooker").resolve,bo=vo("resizer-bar"),wo=vo("resizer-rows"),yo=vo("resizer-cols"),So=function(e){var n=En(e.parent(),"."+bo);B(n,yn)},xo=function(t,e,r){var o=t.origin();B(e,function(e){e.each(function(e){var n=r(o,e);co(n,bo),pn(t.parent(),n)})})},Co=function(e,n,l,f){xo(e,n,function(e,n){var t,r,o,i,u,c,a=(t=n.col,r=n.x-e.left(),o=l.top()-e.top(),i=7,u=f,c=ge.fromTag("div"),we(c,{position:"absolute",left:r-i/2+"px",top:o+"px",height:u+"px",width:i+"px"}),te(c,{"data-column":t,role:"presentation"}),c);return co(a,yo),a})},Ro=function(e,n,l,f){xo(e,n,function(e,n){var t,r,o,i,u,c,a=(t=n.row,r=l.left()-e.left(),o=n.y-e.top(),i=f,u=7,c=ge.fromTag("div"),we(c,{position:"absolute",left:r+"px",top:o-u/2+"px",height:u+"px",width:i+"px"}),te(c,{"data-row":t,role:"presentation"}),c);return co(a,wo),a})},To=function(e,n,t,r){So(e);var o=Gn(n),i=Qn.generate(o);!function(e,n,t,r,o,i){var u=jr(n),c=0<t.length?o.positions(t,n):[];Ro(e,c,u,pr(n));var a=0<r.length?i.positions(r,n):[];Co(e,a,u,dr(n))}(e,n,po(i),mo(i),t,r)},Oo=function(e,n){var t=En(e.parent(),"."+bo);B(t,n)},Do=function(e){Oo(e,function(e){be(e,"display","none")})},Ao=function(e){Oo(e,function(e){be(e,"display","block")})},Eo=function(e,n,t){e.cells()[n]=t},ko=function(e,n){return Tn(n,e.section())},Bo=function(e,n){var t=e.cells(),r=k(t,n);return Tn(r,e.section())},No=function(e,n){return e.cells()[n]},Io=function(e,n){return No(e,n).element()},Po=function(e){return e.cells().length},Mo=function(e,n){if(0===e.length)return 0;var t=e[0];return _(e,function(e){return!n(t.element(),e.element())}).fold(function(){return e.length},function(e){return e})},_o=function(e,n,t,r){var o,i=e[n].cells().slice(t),u=Mo(i,r),c=(o=t,k(e,function(e){return No(e,o)}).slice(n));return{colspan:u,rowspan:Mo(c,r)}},Wo=function(a,l){var f=k(a,function(e){return k(e.cells(),function(){return!1})});return k(a,function(e,c){var n,t,r=j(e.cells(),function(e,n){if(!1!==f[c][n])return[];var t,r,o,i,u=_o(a,c,n,l);return function(e,n,t,r){for(var o=e;o<e+t;o++)for(var i=n;i<n+r;i++)f[o][i]=!0}(c,n,u.rowspan,u.colspan),[(t=e.element(),r=u.rowspan,o=u.colspan,i=e.isNew(),{element:b(t),rowspan:b(r),colspan:b(o),isNew:b(i)})]});return n=r,t=e.section(),{details:b(n),section:b(t)}})},jo=function(e,n,t){for(var r=[],o=0;o<e.grid.rows();o++){for(var i=[],u=0;u<e.grid.columns();u++){var c=Qn.getAt(e,o,u).map(function(e){return Rn(e.element(),t)}).getOrThunk(function(){return Rn(n.gap(),!0)});i.push(c)}var a=Tn(i,e.all[o].section());r.push(a)}return r},zo=function(e,c){return k(e,function(e){var n,t,r,o,i,u=(n=e.details(),L(n,function(e){return un(e.element()).map(function(e){var n=un(e).isNone();return Rn(e,n)})}).getOrThunk(function(){return Rn(c.row(),!0)}));return t=u.element(),r=e.details(),o=e.section(),i=u.isNew(),{element:b(t),cells:b(r),section:b(o),isNew:b(i)}})},Lo=function(e,n){var t=Wo(e,en);return zo(t,n)},Fo=function(e,n){return L(e.all,function(e){return M(e.cells(),function(e){return en(n,e.element())})})},Ho=function(c,a,l,f,s){return function(t,r,e,o,i){var n=Gn(r),u=Qn.generate(n);return a(u,e).map(function(e){var n=jo(u,o,!1),t=c(n,e,en,s(o)),r=Lo(t.grid(),o);return{grid:b(r),cursor:t.cursor}}).fold(function(){return C.none()},function(e){var n=to(r,e.grid());return l(r,e.grid(),i),f(r),To(t,r,Yr,i),C.some({cursor:e.cursor,newRows:b(n.newRows),newCells:b(n.newCells)})})}},qo=function(n,e){return Vn(e.element()).bind(function(e){return Fo(n,e)})},Uo=function(n,e){var t=k(e.selection(),function(e){return Vn(e).bind(function(e){return Fo(n,e)})}),r=eo(t);return 0<r.length?C.some({cells:r,generators:e.generators,clipboard:e.clipboard}):C.none()},Vo=function(n,e){var t=k(e.selection(),function(e){return Vn(e).bind(function(e){return Fo(n,e)})}),r=eo(t);return 0<r.length?C.some(r):C.none()},Ko=function(t){return{is:function(e){return t===e},isValue:i,isError:f,getOr:b(t),getOrThunk:b(t),getOrDie:b(t),or:function(e){return Ko(t)},orThunk:function(e){return Ko(t)},fold:function(e,n){return n(t)},map:function(e){return Ko(e(t))},mapError:function(e){return Ko(t)},each:function(e){e(t)},bind:function(e){return e(t)},exists:function(e){return e(t)},forall:function(e){return e(t)},toOption:function(){return C.some(t)}}},Xo=function(t){return{is:f,isValue:f,isError:i,getOr:o,getOrThunk:function(e){return e()},getOrDie:function(){return e=String(t),function(){throw new Error(e)}();var e},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,n){return e(t)},map:function(e){return Xo(t)},mapError:function(e){return Xo(e(t))},each:x,bind:function(e){return Xo(t)},exists:f,forall:i,toOption:C.none}},$o={value:Ko,error:Xo,fromOption:function(e,n){return e.fold(function(){return Xo(n)},Ko)}},Go=function(e,n){return{rowDelta:0,colDelta:Po(e[0])-Po(n[0])}},Yo=function(e,n){return k(e,function(){return Rn(n.cell(),!0)})},Jo=function(n,e,t){return n.concat(function(e,n){for(var t=[],r=0;r<e;r++)t.push(n(r));return t}(e,function(e){return ko(n[n.length-1],Yo(n[n.length-1].cells(),t))}))},Qo=function(e,n,t){return k(e,function(e){return ko(e,e.cells().concat(Yo(fo(0,n),t)))})},Zo=function(e,n,t){var r=n.colDelta<0?Qo:o;return(n.rowDelta<0?Jo:o)(r(e,Math.abs(n.colDelta),t),Math.abs(n.rowDelta),t)},ei=function(e,n,t,r){for(var o=!0,i=0;i<e.length;i++)for(var u=0;u<Po(e[0]);u++){var c=t(Io(e[i],u),n);!0===c&&!1===o?Eo(e[i],u,Rn(r(),!0)):!0===c&&(o=!1)}return e},ni=function(i,t,u,c){if(0<t&&t<i.length){var e=i[t-1].cells(),n=(r=u,P(e,function(e,n){return E(e,function(e){return r(e.element(),n.element())})?e:e.concat([n])},[]));B(n,function(r){for(var o=C.none(),e=function(t){for(var e=function(n){var e=i[t].cells()[n];u(e.element(),r.element())&&(o.isNone()&&(o=C.some(c())),o.each(function(e){Eo(i[t],n,Rn(e,!0))}))},n=0;n<Po(i[0]);n++)e(n)},n=t;n<i.length;n++)e(n)})}var r;return i},ti=function(t,r,o,i,u){return function(e,n,t){if(e.row()>=n.length||e.column()>Po(n[0]))return $o.error("invalid start address out of table bounds, row: "+e.row()+", column: "+e.column());var r=n.slice(e.row()),o=r[0].cells().slice(e.column()),i=Po(t[0]),u=t.length;return $o.value({rowDelta:r.length-u,colDelta:o.length-i})}(t,r,o).map(function(e){var n=Zo(r,e,i);return function(e,n,t,r,o){for(var i,u,c,a,l,f=e.row(),s=e.column(),d=f+t.length,m=s+Po(t[0]),g=f;g<d;g++)for(var p=s;p<m;p++){c=p,l=a=void 0,a=y(o,No((i=n)[u=g],c).element()),l=i[u],1<i.length&&1<Po(l)&&(0<c&&a(Io(l,c-1))||c<l.cells().length-1&&a(Io(l,c+1))||0<u&&a(Io(i[u-1],c))||u<i.length-1&&a(Io(i[u+1],c)))&&ei(n,Io(n[g],p),o,r.cell);var h=Io(t[g-f],p-s),v=r.replace(h);Eo(n[g],p,Rn(v,!0))}return n}(t,n,o,i,u)})},ri=function(e,n,t,r,o){ni(n,e,o,r.cell);var i=Go(t,n),u=Zo(t,i,r),c=Go(n,u),a=Zo(n,c,r);return a.slice(0,e).concat(u).concat(a.slice(e,a.length))},oi=function(t,r,e,o,i){var n=t.slice(0,r),u=t.slice(r),c=Bo(t[e],function(e,n){return 0<r&&r<t.length&&o(Io(t[r-1],n),Io(t[r],n))?No(t[r],n):Rn(i(e.element(),o),!0)});return n.concat([c]).concat(u)},ii=function(e,l,f,s,d){return k(e,function(e){var n,t,r,o,i,u,c,a=0<l&&l<Po(e)&&s(Io(e,l-1),Io(e,l))?No(e,l):Rn(d(Io(e,f),s),!0);return t=l,r=a,o=(n=e).cells(),i=o.slice(0,t),u=o.slice(t),c=i.concat([r]).concat(u),ko(n,c)})},ui=function(e,t,r,o){return k(e,function(e){return Bo(e,function(e){return n=e,E(t,function(e){return r(n.element(),e.element())})?Rn(o(e.element(),r),!0):e;var n})})},ci=function(e,n,t,r){return Io(e[n],t)!==undefined&&0<n&&r(Io(e[n-1],t),Io(e[n],t))},ai=function(e,n,t){return 0<n&&t(Io(e,n-1),Io(e,n))},li=function(t,r,o,e){var n=j(t,function(e,n){return ci(t,n,r,o)||ai(e,r,o)?[]:[No(e,r)]});return ui(t,n,o,e)},fi=function(t,r,o,e){var i=t[r],n=j(i.cells(),function(e,n){return ci(t,r,n,o)||ai(i,n,o)?[]:[e]});return ui(t,n,o,e)},si=Zt([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),di=Re({},si),mi=function(e,n,i,u){var t,r,c=e.slice(0),o=(r=n,0===(t=e).length?di.none():1===t.length?di.only(0):0===r?di.left(0,1):r===t.length-1?di.right(r-1,r):0<r&&r<t.length-1?di.middle(r-1,r,r+1):di.none()),a=function(e){return k(e,b(0))},l=b(a(c)),f=function(e,n){if(0<=i){var t=Math.max(u.minCellWidth(),c[n]-i);return a(c.slice(0,e)).concat([i,t-c[n]]).concat(a(c.slice(n+1)))}var r=Math.max(u.minCellWidth(),c[e]+i),o=c[e]-r;return a(c.slice(0,e)).concat([r-c[e],o]).concat(a(c.slice(n+1)))},s=f;return o.fold(l,function(e){return u.singleColumnWidth(c[e],i)},s,function(e,n,t){return f(n,t)},function(e,n){if(0<=i)return a(c.slice(0,n)).concat([i]);var t=Math.max(u.minCellWidth(),c[n]+i);return a(c.slice(0,n)).concat([t-c[n]])})},gi=function(e,n,t,r,o){var i=mo(e),u=k(i,function(e){return e.map(n.edge)});return k(i,function(e,n){return e.filter(a(jn)).fold(function(){var e=so(u,n);return r(e)},function(e){return t(e,o)})})},pi=function(e,n,t){return gi(e,n,Ar,function(e){return e.fold(function(){return t.minCellWidth()},function(e){return e/t.pixelWidth()*100})},t)},hi=function(e,n,t){return gi(e,n,Er,function(e){return e.getOrThunk(t.minCellWidth)},t)},vi=function(e,n){return t=n,r=kr,o=function(e){return e.getOrThunk(Hn)},i=po(e),u=k(i,function(e){return e.map(t.edge)}),k(i,function(e,n){return e.filter(a(zn)).fold(function(){var e=so(u,n);return o(e)},function(e){return r(e)})});var t,r,o,i,u},bi=function(e,n,t){for(var r=0,o=e;o<n;o++)r+=t[o]!==undefined?t[o]:0;return r},wi=function(e,t){var n=Qn.justCells(e);return k(n,function(e){var n=bi(e.column(),e.column()+e.colspan(),t);return{element:e.element(),width:n,colspan:e.colspan()}})},yi=function(e){return{width:b(e),pixelWidth:b(e),getWidths:hi,getCellDelta:o,singleColumnWidth:function(e,n){return[Math.max(Fn(),e+n)-e]},minCellWidth:Fn,setElementWidth:Sr,setTableWidth:function(e,n,t){var r=I(n,function(e,n){return e+n},0);Sr(e,r)}}},Si=function(e,n){var t,r,o,i,u=Nr().exec(n);if(null!==u)return t=u[1],r=e,o=parseFloat(t),i=gr(r),{width:b(o),pixelWidth:b(i),getWidths:pi,getCellDelta:function(e){return e/i*100},singleColumnWidth:function(e,n){return[100-e]},minCellWidth:function(){return Fn()/i*100},setElementWidth:xr,setTableWidth:function(e,n,t){xr(e,o+t/100*o)}};var c=Ir().exec(n);if(null!==c){var a=parseInt(c[1],10);return yi(a)}var l=gr(e);return yi(l)},xi=function(n){return Or(n).fold(function(){var e=gr(n);return yi(e)},function(e){return Si(n,e)})},Ci=function(e){return Qn.generate(e)},Ri=function(e){var n=Gn(e);return Ci(n)},Ti=function(e,t,r,n){var o,i,u,c,a=Ri(e),l=vi(a,n),f=k(l,function(e,n){return r===n?Math.max(t+e,Hn()):e}),s=(o=a,i=f,u=Qn.justCells(o),k(u,function(e){var n=bi(e.row(),e.row()+e.rowspan(),i);return{element:e.element,height:b(n),rowspan:e.rowspan}})),d=(c=f,k(a.all,function(e,n){return{element:e.element,height:b(c[n])}}));B(d,function(e){Cr(e.element(),e.height())}),B(s,function(e){Cr(e.element(),e.height())});var m=I(f,function(e,n){return e+n},0);Cr(e,m)},Oi=function(e){return e.slice(0).sort()},Di=function(r,o,i){if(0===o.length)throw new Error("You must specify at least one required field.");var t;return function(n,e){if(!h(e))throw new Error("The "+n+" fields must be an array. Was: "+e+".");B(e,function(e){if(!g(e))throw new Error("The value "+e+" in the "+n+" fields was not a string.")})}("required",o),t=Oi(o),M(t,function(e,n){return n<t.length-1&&e===t[n+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}),function(n){var t=F(n);z(o,function(e){return A(t,e)})||function(e,n){throw new Error("All required keys ("+Oi(e).join(", ")+") were not specified. Specified keys were: "+Oi(n).join(", ")+".")}(o,t),r(o,t);var e=N(o,function(e){return!i.validate(n[e],e)});return 0<e.length&&function(e,n){throw new Error("All values need to be of type: "+n+". Keys ("+Oi(e).join(", ")+") were not.")}(e,i.label),n}},Ai=function(n,e){var t=N(e,function(e){return!A(n,e)});0<t.length&&function(e){throw new Error("Unsupported keys for object: "+Oi(e).join(", "))}(t)},Ei=function(e){return Di(Ai,e,{validate:w,label:"function"})},ki=Ei(["cell","row","replace","gap"]),Bi=function(e){var n=_n(e,"colspan",1),t=_n(e,"rowspan",1);return{element:b(e),colspan:b(n),rowspan:b(t)}},Ni=function(r,o){void 0===o&&(o=Bi),ki(r);var t=S(C.none()),i=function(e){var n,t=o(e);return n=t,r.cell(n)},u=function(e){var n=i(e);return t.get().isNone()&&t.set(C.some(n)),c=C.some({item:e,replacement:n}),n},c=C.none();return{getOrInit:function(n,t){return c.fold(function(){return u(n)},function(e){return t(n,e.item)?e.replacement:u(n)})},cursor:t.get}},Ii=function(c,a){return function(r){var o=S(C.none());ki(r);var i=[],u=function(e){var n={scope:c},t=r.replace(e,a,n);return i.push({item:e,sub:t}),o.get().isNone()&&o.set(C.some(t)),t};return{replaceOrInit:function(n,t){return r=n,o=t,M(i,function(e){return o(e.item,r)}).fold(function(){return u(n)},function(e){return t(n,e.item)?e.sub:u(n)});var r,o},cursor:o.get}}},Pi=function(t){ki(t);var e=S(C.none());return{combine:function(n){return e.get().isNone()&&e.set(C.some(n)),function(){var e=t.cell({element:b(n),colspan:b(1),rowspan:b(1)});return Ce(e,"width"),Ce(n,"width"),e}},cursor:e.get}},Mi=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],_i=Bt(),Wi=function(e){return n=e,t=_i.property().name(n),A(Mi,t);var n,t},ji=function(e){return n=e,t=_i.property().name(n),A(["ol","ul"],t);var n,t},zi=function(e){return n=e,A(["br","img","hr","input"],_i.property().name(n));var n},Li=function(e){var n,i=function(e){return"br"===$(e)},t=function(o){return at(o).bind(function(t){var r=ln(t).map(function(e){return!!Wi(e)||!!zi(e)&&"img"!==$(e)}).getOr(!1);return un(t).map(function(e){return!0===r||("li"===$(n=e)||Bn(n,ji).isSome())||i(t)||Wi(e)&&!en(o,e)?[]:[ge.fromTag("br")];var n})}).getOr([])},r=0===(n=j(e,function(e){var n=fn(e);return z(n,function(e){return i(e)||Z(e)&&0===nt(e).trim().length})?[]:n.concat(t(e))})).length?[ge.fromTag("br")]:n;wn(e[0]),bn(e[0],r)},Fi=function(e){0===Kn(e).length&&yn(e)},Hi=function(e,n){return{grid:b(e),cursor:b(n)}},qi=function(e,n,t){return Ui(e,n,t).orThunk(function(){return Ui(e,0,0)})},Ui=function(e,n,t){return C.from(e[n]).bind(function(e){return C.from(e.cells()[t]).bind(function(e){return C.from(e.element())})})},Vi=function(e,n,t){return Hi(e,Ui(e,n,t))},Ki=function(e){return P(e,function(e,n){return E(e,function(e){return e.row()===n.row()})?e:e.concat([n])},[]).sort(function(e,n){return e.row()-n.row()})},Xi=function(e){return P(e,function(e,n){return E(e,function(e){return e.column()===n.column()})?e:e.concat([n])},[]).sort(function(e,n){return e.column()-n.column()})},$i=function(e,n,t){var r,o=(r=t,k(e,function(e){var n=k(Kn(e),function(e){var n=_n(e,"rowspan",1),t=_n(e,"colspan",1);return xn(e,n,t)});return Cn(e,n,r.section())})),i=Qn.generate(o);return jo(i,n,!0)},Gi=function(e,n,t){var r=xi(e),o=Ci(n),i=r.getWidths(o,t,r),u=wi(o,i);B(u,function(e){r.setElementWidth(e.element,e.width)}),0<u.length&&r.setTableWidth(e,i,r.getCellDelta(0))},Yi=Ho(function(e,n,t,r){var o=n[0].row(),i=n[0].row(),u=Ki(n),c=P(u,function(e,n){return oi(e,i,o,t,r.getOrInit)},e);return Vi(c,i,n[0].column())},Vo,x,x,Ni),Ji=Ho(function(e,n,t,r){var o=Ki(n),i=o[o.length-1].row(),u=o[o.length-1].row()+o[o.length-1].rowspan(),c=P(o,function(e,n){return oi(e,u,i,t,r.getOrInit)},e);return Vi(c,u,n[0].column())},Vo,x,x,Ni),Qi=Ho(function(e,n,t,r){var o=Xi(n),i=o[0].column(),u=o[0].column(),c=P(o,function(e,n){return ii(e,u,i,t,r.getOrInit)},e);return Vi(c,n[0].row(),u)},Vo,Gi,x,Ni),Zi=Ho(function(e,n,t,r){var o=n[n.length-1].column(),i=n[n.length-1].column()+n[n.length-1].colspan(),u=Xi(n),c=P(u,function(e,n){return ii(e,i,o,t,r.getOrInit)},e);return Vi(c,n[0].row(),i)},Vo,Gi,x,Ni),eu=Ho(function(e,n,t,r){var o,i,u,c,a=Xi(n),l=(o=e,i=a[0].column(),u=a[a.length-1].column(),c=k(o,function(e){var n=e.cells().slice(0,i).concat(e.cells().slice(u+1));return Tn(n,e.section())}),N(c,function(e){return 0<e.cells().length})),f=qi(l,n[0].row(),n[0].column());return Hi(l,f)},Vo,Gi,Fi,Ni),nu=Ho(function(e,n,t,r){var o,i,u,c=Ki(n),a=(o=e,i=c[0].row(),u=c[c.length-1].row(),o.slice(0,i).concat(o.slice(u+1))),l=qi(a,n[0].row(),n[0].column());return Hi(a,l)},Vo,x,Fi,Ni),tu=(Ho(function(e,n,t,r){var o=li(e,n.column(),t,r.replaceOrInit);return Vi(o,n.row(),n.column())},qo,x,x,Ii("row","th")),Ho(function(e,n,t,r){var o=li(e,n.column(),t,r.replaceOrInit);return Vi(o,n.row(),n.column())},qo,x,x,Ii(null,"td")),Ho(function(e,n,t,r){var o=fi(e,n.row(),t,r.replaceOrInit);return Vi(o,n.row(),n.column())},qo,x,x,Ii("col","th")),Ho(function(e,n,t,r){var o=fi(e,n.row(),t,r.replaceOrInit);return Vi(o,n.row(),n.column())},qo,x,x,Ii(null,"td")),Ho(function(e,n,t,r){var o=n.cells();Li(o);var i=function(e,n,t){if(0===e.length)return e;for(var r=n.startRow();r<=n.finishRow();r++)for(var o=n.startCol();o<=n.finishCol();o++)Eo(e[r],o,Rn(t(),!1));return e}(e,n.bounds(),b(o[0]));return Hi(i,C.from(o[0]))},function(e,n){return n.mergable()},x,x,Pi)),ru=Ho(function(e,n,t,r){var o=I(n,function(e,n){return ei(e,n,t,r.combine(n))},e);return Hi(o,C.from(n[0]))},function(e,n){return n.unmergable()},Gi,x,Pi),ou=Ho(function(e,t,n,r){var o,i,u,c,a,l,f=(o=t.clipboard(),i=t.generators(),u=Gn(o),c=Qn.generate(u),jo(c,i,!0)),s=(a=t.row(),l=t.column(),{row:b(a),column:b(l)});return ti(s,e,f,t.generators(),n).fold(function(){return Hi(e,C.some(t.element()))},function(e){var n=qi(e,t.row(),t.column());return Hi(e,n)})},function(n,t){return Vn(t.element()).bind(function(e){return Fo(n,e).map(function(e){return Re(Re({},e),{generators:t.generators,clipboard:t.clipboard})})})},Gi,x,Ni),iu=Ho(function(e,n,t,r){var o=e[n.cells[0].row()],i=n.cells[0].row(),u=$i(n.clipboard(),n.generators(),o),c=ri(i,e,u,n.generators(),t),a=qi(c,n.cells[0].row(),n.cells[0].column());return Hi(c,a)},Uo,x,x,Ni),uu=Ho(function(e,n,t,r){var o=e[n.cells[0].row()],i=n.cells[n.cells.length-1].row()+n.cells[n.cells.length-1].rowspan(),u=$i(n.clipboard(),n.generators(),o),c=ri(i,e,u,n.generators(),t),a=qi(c,n.cells[0].row(),n.cells[0].column());return Hi(c,a)},Uo,x,x,Ni),cu=function(e){return ge.fromDom(e.getBody())},au=function(e){return e.getBoundingClientRect().width},lu=function(e){return e.getBoundingClientRect().height},fu=function(n){return function(e){return en(e,cu(n))}},su=function(e){return/^[0-9]+$/.test(e)&&(e+="px"),e},du=function(e){var n=En(e,"td[data-mce-style],th[data-mce-style]");ue(e,"data-mce-style"),B(n,function(e){ue(e,"data-mce-style")})},mu={isRtl:b(!1)},gu={isRtl:b(!0)},pu=function(e){return"rtl"==("rtl"===ye(e,"direction")?"rtl":"ltr")?gu:mu},hu={"border-collapse":"collapse",width:"100%"},vu={border:"1"},bu=function(e){return e.getParam("table_default_attributes",vu,"object")},wu=function(e){return e.getParam("table_default_styles",hu,"object")},yu=function(e){return e.getParam("table_tab_navigation",!0,"boolean")},Su=function(e){return e.getParam("table_cell_advtab",!0,"boolean")},xu=function(e){return e.getParam("table_row_advtab",!0,"boolean")},Cu=function(e){return e.getParam("table_advtab",!0,"boolean")},Ru=function(e){return e.getParam("table_style_by_css",!1,"boolean")},Tu=function(e){return e.getParam("table_class_list",[],"array")},Ou=function(e){return!1===e.getParam("table_responsive_width")},Du=function(e){var n=e.getParam("table_clone_elements");return g(n)?C.some(n.split(/[ ,]/)):Array.isArray(n)?C.some(n):C.none()},Au=function(e,n){return e.fire("newrow",{node:n})},Eu=function(e,n){return e.fire("newcell",{node:n})},ku=function(e,n,t,r,o){e.fire("TableSelectionChange",{cells:n,start:t,finish:r,otherCells:o})},Bu=function(e){e.fire("TableSelectionClear")},Nu=function(e,n){return{element:b(e),offset:b(n)}},Iu=function(n,e,t){return n.property().isText(e)&&0===n.property().getText(e).trim().length||n.property().isComment(e)?t(e).bind(function(e){return Iu(n,e,t).orThunk(function(){return C.some(e)})}):C.none()},Pu=function(e,n){return e.property().isText(n)?e.property().getText(n).length:e.property().children(n).length},Mu=function(e,n){var t=Iu(e,n,e.query().prevSibling).getOr(n);if(e.property().isText(t))return Nu(t,Pu(e,t));var r=e.property().children(t);return 0<r.length?Mu(e,r[r.length-1]):Nu(t,Pu(e,t))},_u=Mu,Wu=Bt(),ju=function(f,e){var t=function(e){return"table"===$(cu(e))},s=Du(f),n=function(u,c,a,l){return function(e,n){du(e);var t=l(),r=ge.fromDom(f.getDoc()),o=Qr(pu),i=bt(a,r,s);return c(e)?u(t,e,n,i,o).bind(function(e){return B(e.newRows(),function(e){Au(f,e.dom())}),B(e.newCells(),function(e){Eu(f,e.dom())}),e.cursor().map(function(e){var n=_u(Wu,e),t=f.dom.createRng();return t.setStart(n.element().dom(),n.offset()),t.setEnd(n.element().dom(),n.offset()),t})}):C.none()}};return{deleteRow:n(nu,function(e){var n=Zr(e);return!1===t(f)||1<n.rows()},x,e),deleteColumn:n(eu,function(e){var n=Zr(e);return!1===t(f)||1<n.columns()},x,e),insertRowsBefore:n(Yi,i,x,e),insertRowsAfter:n(Ji,i,x,e),insertColumnsBefore:n(Qi,i,Pr,e),insertColumnsAfter:n(Zi,i,Pr,e),mergeCells:n(tu,i,x,e),unmergeCells:n(ru,i,x,e),pasteRowsBefore:n(iu,i,x,e),pasteRowsAfter:n(uu,i,x,e),pasteCells:n(ou,i,x,e)}},zu=function(e,n,r){var t=Gn(e),o=Qn.generate(t);return Vo(o,n).map(function(e){var n=jo(o,r,!1).slice(e[0].row(),e[e.length-1].row()+e[e.length-1].rowspan()),t=Lo(n,r);return k(t,function(e){var t=st(e.element());return B(e.cells(),function(e){var n=dt(e.element());no(n,"colspan",e.colspan(),1),no(n,"rowspan",e.rowspan(),1),pn(t,n)}),t})})},Lu=tinymce.util.Tools.resolve("tinymce.util.Tools"),Fu=function(n){return function(e){return C.from(e.dom.getParent(e.selection.getStart(),n)).map(ge.fromDom)}},Hu=Fu("th,td"),qu=Fu("th,td,caption"),Uu=function(o,e,i){var n;return n=function(e,n){for(var t=0;t<n.length;t++){var r=o.getStyle(n[t],i);if(void 0===e&&(e=r),e!==r)return""}return e}(n,o.select("td,th",e))},Vu=function(e,n,t){t&&e.formatter.apply("align"+t,{},n)},Ku=function(e,n,t){t&&e.formatter.apply("valign"+t,{},n)},Xu=function(n,t){Lu.each("left center right".split(" "),function(e){n.formatter.remove("align"+e,{},t)})},$u=function(e,r,n){var o=function(e,t){return t=t||[],Lu.each(e,function(e){var n={text:e.text||e.title};e.menu?n.menu=o(e.menu):(n.value=e.value,r&&r(n)),t.push(n)}),t};return o(e,n||[])},Gu=function(n,e){var t=function(e){return fe(e,"rgb")?n.toHex(e):e};return{borderwidth:xe(ge.fromDom(e),"border-width").getOr(""),borderstyle:xe(ge.fromDom(e),"border-style").getOr(""),bordercolor:xe(ge.fromDom(e),"border-color").map(t).getOr(""),backgroundcolor:xe(ge.fromDom(e),"background-color").map(t).getOr("")}},Yu=function(e){var o=e[0],n=e.slice(1),t=F(o);return B(n,function(e){B(t,function(r){q(e,function(e,n){var t=o[r];""!==t&&r===n&&t!==e&&(o[r]="")})})}),o},Ju=function(e){var n=[{name:"borderstyle",type:"selectbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===e?[{name:"borderwidth",type:"input",label:"Border width"}].concat(n):n}},Qu=function(e,n,t,r,o){var i={};return Lu.each(e.split(" "),function(e){r.formatter.matchNode(o,n+e)&&(i[t]=e)}),i[t]||(i[t]=""),i},Zu=y(Qu,"left center right"),ec=y(Qu,"top middle bottom"),nc=function(e,n){var t,r,o,i,u,c,a,l,f=wu(e),s=bu(e),d=e.dom,m=n?(t=d,r=function(e){return fe(e,"rgb")?t.toHex(e):e},o=K(f,"border-style").getOr(""),i=K(f,"border-color").getOr(""),u=K(f,"background-color").getOr(""),{borderstyle:o,bordercolor:r(i),backgroundcolor:r(u)}):{};return Re(Re(Re(Re(Re(Re({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,"class":"",align:"",border:""}),f),s),m),(l=f["border-width"],Ru(e)&&l?{border:l}:K(s,"border").fold(function(){return{}},function(e){return{border:e}}))),(c=K(f,"border-spacing").or(K(s,"cellspacing")).fold(function(){return{}},function(e){return{cellspacing:e}}),a=K(f,"border-padding").or(K(s,"cellpadding")).fold(function(){return{}},function(e){return{cellpadding:e}}),Re(Re({},c),a)))},tc=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"selectbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"selectbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"selectbox",label:"H Align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"selectbox",label:"V Align",items:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],rc=function(e){return t=(n=e).getParam("table_cell_class_list",[],"array"),r=$u(t,function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"tr",classes:[e.value]})})}),(0<t.length?C.some({name:"class",type:"selectbox",label:"Class",items:r}):C.none()).fold(function(){return tc},function(e){return tc.concat(e)});var n,t,r},oc={normal:function(t,r){return{setAttrib:function(e,n){t.setAttrib(r,e,n)},setStyle:function(e,n){t.setStyle(r,e,n)}}},ifTruthy:function(t,r){return{setAttrib:function(e,n){n&&t.setAttrib(r,e,n)},setStyle:function(e,n){n&&t.setStyle(r,e,n)}}}},ic=function(e,n){e.setAttrib("scope",n.scope),e.setAttrib("class",n["class"]),e.setStyle("width",su(n.width)),e.setStyle("height",su(n.height))},uc=function(e,n){e.setStyle("background-color",n.backgroundcolor),e.setStyle("border-color",n.bordercolor),e.setStyle("border-style",n.borderstyle),e.setStyle("border-width",su(n.borderwidth))},cc=function(e,n,t){var r,o,i=e.dom,u=t.celltype&&n[0].nodeName.toLowerCase()!==t.celltype?i.rename(n[0],t.celltype):n[0],c=oc.normal(i,u);ic(c,t),Su(e)&&uc(c,t),Xu(e,u),r=e,o=u,Lu.each("top middle bottom".split(" "),function(e){r.formatter.remove("valign"+e,{},o)}),t.halign&&Vu(e,u,t.halign),t.valign&&Ku(e,u,t.valign)},ac=function(t,e,r){var o=t.dom;Lu.each(e,function(e){r.celltype&&e.nodeName.toLowerCase()!==r.celltype&&(e=o.rename(e,r.celltype));var n=oc.ifTruthy(o,e);ic(n,r),Su(t)&&uc(n,r),r.halign&&Vu(t,e,r.halign),r.valign&&Ku(t,e,r.valign)})},lc=function(e,n,t){var r=t.getData();t.close(),e.undoManager.transact(function(){(1===n.length?cc:ac)(e,n,r),e.focus()})},fc=function(i){var e,n=[];if(n=i.dom.select("td[data-mce-selected],th[data-mce-selected]"),e=i.dom.getParent(i.selection.getStart(),"td,th"),!n.length&&e&&n.push(e),e=e||n[0]){var t=Lu.map(n,function(e){return t=e,r=Su(n=i),o=n.dom,Re(Re(Re({width:o.getStyle(t,"width")||o.getAttrib(t,"width"),height:o.getStyle(t,"height")||o.getAttrib(t,"height"),scope:o.getAttrib(t,"scope"),celltype:t.nodeName.toLowerCase(),"class":o.getAttrib(t,"class","")},Zu("align","halign",n,t)),ec("valign","valign",n,t)),r?Gu(o,t):{});var n,t,r,o}),r=Yu(t),o={type:"tabpanel",tabs:[{title:"General",name:"general",items:rc(i)},Ju("cell")]},u={type:"panel",items:[{type:"grid",columns:2,items:rc(i)}]};i.windowManager.open({title:"Cell Properties",size:"normal",body:Su(i)?o:u,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:r,onSubmit:y(lc,i,n)})}},sc=[{type:"selectbox",name:"type",label:"Row type",items:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],dc=function(e){return t=(n=e).getParam("table_row_class_list",[],"array"),r=$u(t,function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"tr",classes:[e.value]})})}),(0<t.length?C.some({name:"class",type:"selectbox",label:"Class",items:r}):C.none()).fold(function(){return sc},function(e){return sc.concat(e)});var n,t,r},mc=function(f,e,s,n){var d=f.dom,m=n.getData();n.close();var g=1===e.length?oc.normal:oc.ifTruthy;f.undoManager.transact(function(){Lu.each(e,function(e){var n,t,r,o,i,u;m.type!==e.parentNode.nodeName.toLowerCase()&&(n=f.dom,t=e,r=m.type,o=n.getParent(t,"table"),i=t.parentNode,(u=n.select(r,o)[0])||(u=n.create(r),o.firstChild?"CAPTION"===o.firstChild.nodeName?n.insertAfter(u,o.firstChild):o.insertBefore(u,o.firstChild):o.appendChild(u)),"tbody"===r&&"THEAD"===i.nodeName&&u.firstChild?u.insertBefore(t,u.firstChild):u.appendChild(t),i.hasChildNodes()||n.remove(i));var c,a,l=g(d,e);l.setAttrib("scope",m.scope),l.setAttrib("class",m["class"]),l.setStyle("height",su(m.height)),xu(f)&&(a=m,(c=l).setStyle("background-color",a.backgroundcolor),c.setStyle("border-color",a.bordercolor),c.setStyle("border-style",a.borderstyle)),m.align!==s.align&&(Xu(f,e),Vu(f,e,m.align))}),f.focus()})},gc=function(i){var e,t,r=i.dom,o=[];if((e=r.getParent(i.selection.getStart(),"table"))&&(t=r.getParent(i.selection.getStart(),"td,th"),Lu.each(e.rows,function(n){Lu.each(n.cells,function(e){if((r.getAttrib(e,"data-mce-selected")||e===t)&&o.indexOf(n)<0)return o.push(n),!1})}),o[0])){var n=Lu.map(o,function(e){return t=e,r=xu(n=i),o=n.dom,Re(Re({height:o.getStyle(t,"height")||o.getAttrib(t,"height"),scope:o.getAttrib(t,"scope"),"class":o.getAttrib(t,"class",""),align:"",type:t.parentNode.nodeName.toLowerCase()},Zu("align","align",n,t)),r?Gu(o,t):{});var n,t,r,o}),u=Yu(n),c={type:"tabpanel",tabs:[{title:"General",name:"general",items:dc(i)},Ju("row")]},a={type:"panel",items:[{type:"grid",columns:2,items:dc(i)}]};i.windowManager.open({title:"Row Properties",size:"normal",body:xu(i)?c:a,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:u,onSubmit:y(mc,i,o,u)})}},pc=tinymce.util.Tools.resolve("tinymce.Env"),hc={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},percentages:!0},vc=function(e,n,t,r,o){void 0===o&&(o=hc);var i=ge.fromTag("table");we(i,o.styles),te(i,o.attributes);var u=ge.fromTag("tbody");pn(i,u);for(var c=[],a=0;a<e;a++){for(var l=ge.fromTag("tr"),f=0;f<n;f++){var s=a<t||f<r?ge.fromTag("th"):ge.fromTag("td");f<r&&ne(s,"scope","row"),a<t&&ne(s,"scope","col"),pn(s,ge.fromTag("br")),o.percentages&&be(s,"width",100/n+"%"),pn(l,s)}c.push(l)}return bn(u,c),i},bc=function(e,n){e.selection.select(n.dom(),!0),e.selection.collapse(!0)},wc=function(r,e,n){var t,o=wu(r),i={styles:o,attributes:bu(r),percentages:(t=o.width,g(t)&&-1!==t.indexOf("%")&&!Ou(r))},u=vc(n,e,0,0,i);ne(u,"data-mce-id","__mce");var c,a,l,f=(c=u,a=ge.fromTag("div"),l=ge.fromDom(c.dom().cloneNode(!0)),pn(a,l),a.dom().innerHTML);return r.insertContent(f),Pn(cu(r),'table[data-mce-id="__mce"]').map(function(e){var n,t;return Ou(r)&&be(e,"width",ye(e,"width")),ue(e,"data-mce-id"),n=r,B(En(e,"tr"),function(e){Au(n,e.dom()),B(En(e,"th,td"),function(e){Eu(n,e.dom())})}),t=r,Pn(e,"td,th").each(y(bc,t)),e.dom()}).getOr(null)},yc=function(e,n,t,r){if("TD"===n.tagName||"TH"===n.tagName)g(t)?e.setStyle(n,t,r):e.setStyle(n,t);else if(n.children)for(var o=0;o<n.children.length;o++)yc(e,n.children[o],t,r)},Sc=function(t,r,e){var o,i=t.dom,u=e.getData();e.close(),""===u["class"]&&delete u["class"],t.undoManager.transact(function(){if(!r){var e=parseInt(u.cols,10)||1,n=parseInt(u.rows,10)||1;r=wc(t,e,n)}!function(e,n,t){var r,o=e.dom,i={},u={};if(i["class"]=t["class"],u.height=su(t.height),o.getAttrib(n,"width")&&!Ru(e)?i.width=(r=t.width)?r.replace(/px$/,""):"":u.width=su(t.width),Ru(e)?(u["border-width"]=su(t.border),u["border-spacing"]=su(t.cellspacing)):(i.border=t.border,i.cellpadding=t.cellpadding,i.cellspacing=t.cellspacing),Ru(e)&&n.children)for(var c=0;c<n.children.length;c++)yc(o,n.children[c],{"border-width":su(t.border),padding:su(t.cellpadding)}),Cu(e)&&yc(o,n.children[c],{"border-color":t.bordercolor});Cu(e)&&(u["background-color"]=t.backgroundcolor,u["border-color"]=t.bordercolor,u["border-style"]=t.borderstyle),i.style=o.serializeStyle(Re(Re({},wu(e)),u)),o.setAttribs(n,Re(Re({},bu(e)),i))}(t,r,u),(o=i.select("caption",r)[0])&&!u.caption&&i.remove(o),!o&&u.caption&&((o=i.create("caption")).innerHTML=pc.ie?"\xa0":'<br data-mce-bogus="1"/>',r.insertBefore(o,r.firstChild)),""===u.align?Xu(t,r):Vu(t,r,u.align),t.focus(),t.addVisual()})},xc=function(e,n){var t,r,o,i,u,c,a,l,f=e.dom,s=nc(e,Cu(e));!1===n?(t=f.getParent(e.selection.getStart(),"table"))?(o=t,i=Cu(r=e),l=r.dom,s=Re(Re({width:l.getStyle(o,"width")||l.getAttrib(o,"width"),height:l.getStyle(o,"height")||l.getAttrib(o,"height"),cellspacing:l.getStyle(o,"border-spacing")||l.getAttrib(o,"cellspacing"),cellpadding:l.getAttrib(o,"cellpadding")||Uu(r.dom,o,"padding"),border:(u=l,c=o,a=xe(ge.fromDom(c),"border-width"),Ru(r)&&a.isSome()?a.getOr(""):u.getAttrib(c,"border")||Uu(r.dom,c,"border-width")||Uu(r.dom,c,"border")),caption:!!l.select("caption",o)[0],"class":l.getAttrib(o,"class","")},Zu("align","align",r,o)),i?Gu(l,o):{})):Cu(e)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""):(s.cols="1",s.rows="1",Cu(e)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""));var d=0<Tu(e).length;d&&s["class"]&&(s["class"]=s["class"].replace(/\s*mce\-item\-table\s*/g,""));var m,g,p,h,v,b={type:"grid",columns:2,items:(g=d,p=n?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],h=(m=e).getParam("table_appearance_options",!0,"boolean")?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],v=g?[{type:"selectbox",name:"class",label:"Class",items:$u(Tu(m),function(e){e.value&&(e.textStyle=function(){return m.formatter.getCssText({block:"table",classes:[e.value]})})})}]:[],p.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(h).concat([{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(v))},w=Cu(e)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[b]},Ju("table")]}:{type:"panel",items:[b]};e.windowManager.open({title:"Table Properties",size:"normal",body:w,onSubmit:y(Sc,e,t),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s})},Cc=Lu.each,Rc=function(s,n,d,m,t){var r=fu(s),i=function(e){return $n(e,r)},o=function(r){Hu(s).each(function(t){i(t).each(function(n){var e=cr(m,n,t);r(n,e).each(function(e){s.selection.setRng(e),s.focus(),d.clear(n),du(n)})})})},u=function(e){return Hu(s).map(function(o){return i(o).bind(function(e){var n=ge.fromDom(s.getDoc()),t=cr(m,e,o),r=bt(x,n,C.none());return zu(e,t,r)})})},c=function(f){t.get().each(function(e){var l=k(e,function(e){return dt(e)});Hu(s).each(function(a){i(a).each(function(n){var e,t,r,o,i=ge.fromDom(s.getDoc()),u=wt(i),c=(e=m,r=l,o=u,{element:b(t=a),mergable:C.none,unmergable:C.none,selection:b(ir(t,e)),clipboard:b(r),generators:b(o)});f(n,c).each(function(e){s.selection.setRng(e),s.focus(),d.clear(n)})})})})};Cc({mceTableSplitCells:function(){o(n.unmergeCells)},mceTableMergeCells:function(){o(n.mergeCells)},mceTableInsertRowBefore:function(){o(n.insertRowsBefore)},mceTableInsertRowAfter:function(){o(n.insertRowsAfter)},mceTableInsertColBefore:function(){o(n.insertColumnsBefore)},mceTableInsertColAfter:function(){o(n.insertColumnsAfter)},mceTableDeleteCol:function(){o(n.deleteColumn)},mceTableDeleteRow:function(){o(n.deleteRow)},mceTableCutRow:function(e){u().each(function(e){t.set(e),o(n.deleteRow)})},mceTableCopyRow:function(e){u().each(function(e){t.set(e)})},mceTablePasteRowBefore:function(e){c(n.pasteRowsBefore)},mceTablePasteRowAfter:function(e){c(n.pasteRowsAfter)},mceTableDelete:function(){qu(s).each(function(e){$n(e,r).filter(a(r)).each(function(e){var n=ge.fromText("");if(mn(e,n),yn(e),s.dom.isEmpty(s.getBody()))s.setContent(""),s.selection.setCursorLocation();else{var t=s.dom.createRng();t.setStart(n.dom(),0),t.setEnd(n.dom(),0),s.selection.setRng(t),s.nodeChanged()}})})}},function(e,n){s.addCommand(n,e)}),Cc({mceInsertTable:y(xc,s,!0),mceTableProps:y(xc,s,!1),mceTableRowProps:y(gc,s),mceTableCellProps:y(fc,s)},function(e,n){s.addCommand(n,function(){e()})})},Tc=function(e){var n=C.from(e.dom().documentElement).map(ge.fromDom).getOr(e);return{parent:b(n),view:b(e),origin:b(_r(0,0))}},Oc=function(e,n){return{parent:b(n),view:b(e),origin:b(_r(0,0))}},Dc=function(e){var r=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(n.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+n.length+']", got '+t.length+" arguments");var r={};return B(n,function(e,n){r[e]=b(t[n])}),r}}.apply(null,e),o=[];return{bind:function(e){if(e===undefined)throw new Error("Event bind error: undefined handler");o.push(e)},unbind:function(n){o=N(o,function(e){return e!==n})},trigger:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=r.apply(null,e);B(o,function(e){e(t)})}}},Ac=function(e){return{registry:U(e,function(e){return{bind:e.bind,unbind:e.unbind}}),trigger:U(e,function(e){return e.trigger})}},Ec=function(e){var t,r,n,o,i,u=ge.fromDom(e.target),c=function(){return e.stopPropagation()},a=function(){return e.preventDefault()},l=(t=a,r=c,function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t(r.apply(null,e))});return n=u,o=e.clientX,i=e.clientY,{target:b(n),x:b(o),y:b(i),stop:c,prevent:a,kill:l,raw:b(e)}},kc=function(e,n,t,r,o){var i,u,c=(i=t,u=r,function(e){i(e)&&u(Ec(e))});return e.dom().addEventListener(n,c,o),{unbind:y(Bc,e,n,c,o)}},Bc=function(e,n,t,r){e.dom().removeEventListener(n,t,r)},Nc=b(!0),Ic=function(e,n,t){return kc(e,n,Nc,t,!1)},Pc=Ec,Mc=ho("ephox-dragster").resolve,_c=Ei(["compare","extract","mutate","sink"]),Wc=Ei(["element","start","stop","destroy"]),jc=Ei(["forceDrop","drop","move","delayDrop"]),zc=_c({compare:function(e,n){return _r(n.left()-e.left(),n.top()-e.top())},extract:function(e){return C.some(_r(e.x(),e.y()))},sink:function(e,n){var t=function(e){var n=Re({layerClass:Mc("blocker")},e),t=ge.fromTag("div");ne(t,"role","presentation"),we(t,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),co(t,Mc("blocker")),co(t,n.layerClass);return{element:function(){return t},destroy:function(){yn(t)}}}(n),r=Ic(t.element(),"mousedown",e.forceDrop),o=Ic(t.element(),"mouseup",e.drop),i=Ic(t.element(),"mousemove",e.move),u=Ic(t.element(),"mouseout",e.delayDrop);return Wc({element:t.element,start:function(e){pn(e,t.element())},stop:function(){yn(t.element())},destroy:function(){t.destroy(),o.unbind(),i.unbind(),u.unbind(),r.unbind()}})},mutate:function(e,n){e.mutate(n.left(),n.top())}});function Lc(){var i=C.none(),u=Ac({move:Dc(["info"])});return{onEvent:function(e,o){o.extract(e).each(function(e){var n,t,r;(n=o,t=e,r=i.map(function(e){return n.compare(e,t)}),i=C.some(t),r).each(function(e){u.trigger.move(e)})})},reset:function(){i=C.none()},events:u.registry}}function Fc(){var e=function r(){return{onEvent:x,reset:x}}(),n=Lc(),t=e;return{on:function(){t.reset(),t=n},off:function(){t.reset(),t=e},isOn:function(){return t===n},onEvent:function(e,n){t.onEvent(e,n)},events:n.events}}var Hc=function(n,t,e){var r,o,i,u=!1,c=Ac({start:Dc([]),stop:Dc([])}),a=Fc(),l=function(){d.stop(),a.isOn()&&(a.off(),c.trigger.stop())},f=(r=l,o=200,i=null,{cancel:function(){null!==i&&(p.clearTimeout(i),i=null)},throttle:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];null!==i&&p.clearTimeout(i),i=p.setTimeout(function(){r.apply(null,e),i=null},o)}});a.events.move.bind(function(e){t.mutate(n,e.info())});var s=function(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];u&&t.apply(null,e)}},d=t.sink(jc({forceDrop:l,drop:s(l),move:s(function(e){f.cancel(),a.onEvent(e,t)}),delayDrop:s(f.throttle)}),e);return{element:d.element,go:function(e){d.start(e),a.on(),c.trigger.start()},on:function(){u=!0},off:function(){u=!1},destroy:function(){d.destroy()},events:c.registry}},qc=function(e){return"true"===re(e,"contenteditable")},Uc=function(){var t,r=Ac({drag:Dc(["xDelta","yDelta","target"])}),o=C.none(),e={mutate:function(e,n){t.trigger.drag(e,n)},events:(t=Ac({drag:Dc(["xDelta","yDelta"])})).registry};e.events.drag.bind(function(n){o.each(function(e){r.trigger.drag(n.xDelta(),n.yDelta(),e)})});return{assign:function(e){o=C.some(e)},get:function(){return o},mutate:e.mutate,events:r.registry}},Vc=vo("resizer-bar-dragging"),Kc=function(o,n,i){var t=Uc(),r=function(e,n){void 0===n&&(n={});var t=n.mode!==undefined?n.mode:zc;return Hc(e,t,n)}(t,{}),u=C.none(),e=function(e,n){return C.from(re(e,n))};t.events.drag.bind(function(t){e(t.target(),"data-row").each(function(e){var n=Ln(t.target(),"top");be(t.target(),"top",n+t.yDelta()+"px")}),e(t.target(),"data-column").each(function(e){var n=Ln(t.target(),"left");be(t.target(),"left",n+t.xDelta()+"px")})});var c=function(e,n){return Ln(e,n)-_n(e,"data-initial-"+n,0)};r.events.stop.bind(function(){t.get().each(function(r){u.each(function(t){e(r,"data-row").each(function(e){var n=c(r,"top");ue(r,"data-initial-top"),m.trigger.adjustHeight(t,n,parseInt(e,10))}),e(r,"data-column").each(function(e){var n=c(r,"left");ue(r,"data-initial-left"),m.trigger.adjustWidth(t,n,parseInt(e,10))}),To(o,t,i,n)})})});var a=function(e,n){m.trigger.startAdjust(),t.assign(e),ne(e,"data-initial-"+n,Ln(e,n)),co(e,Vc),be(e,"opacity","0.2"),r.go(o.parent())},l=Ic(o.parent(),"mousedown",function(e){var n,t;n=e.target(),lo(n,wo)&&a(e.target(),"top"),t=e.target(),lo(t,yo)&&a(e.target(),"left")}),f=function(e){return en(e,o.view())},s=function(e){return Mn(e,"table",f).filter(function(e){return Mn(e,"[contenteditable]",f).exists(qc)})},d=Ic(o.view(),"mouseover",function(e){s(e.target()).fold(function(){pe(e.target())&&So(o)},function(e){u=C.some(e),To(o,e,i,n)})}),m=Ac({adjustHeight:Dc(["table","delta","row"]),adjustWidth:Dc(["table","delta","column"]),startAdjust:Dc([])});return{destroy:function(){l.unbind(),d.unbind(),r.destroy(),So(o)},refresh:function(e){To(o,e,i,n)},on:r.on,off:r.off,hideBars:y(Do,o),showBars:y(Ao,o),events:m.registry}},Xc=function(e,m){var t=Yr,n=Kc(e,m,t),g=Ac({beforeResize:Dc(["table"]),afterResize:Dc(["table"]),startDrag:Dc([])});return n.events.adjustHeight.bind(function(e){g.trigger.beforeResize(e.table());var n=t.delta(e.delta(),e.table());Ti(e.table(),n,e.row(),t),g.trigger.afterResize(e.table())}),n.events.startAdjust.bind(function(e){g.trigger.startDrag()}),n.events.adjustWidth.bind(function(e){g.trigger.beforeResize(e.table());var n,t,r,o,i,u,c,a,l,f,s,d=m.delta(e.delta(),e.table());n=e.table(),t=d,r=e.column(),o=m,i=xi(n),u=i.getCellDelta(t),c=Ri(n),a=i.getWidths(c,o,i),l=mi(a,r,u,i),f=k(l,function(e,n){return e+a[n]}),s=wi(c,f),B(s,function(e){i.setElementWidth(e.element,e.width)}),r===c.grid.columns()-1&&i.setTableWidth(n,f,u),g.trigger.afterResize(e.table())}),{on:n.on,off:n.off,hideBars:n.hideBars,showBars:n.showBars,destroy:n.destroy,events:g.registry}},$c=function(){var e=ge.fromTag("div");return we(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),pn(he(ge.fromDom(p.document)),e),e},Gc=function(e,n){return au(e.dom())/au(n.dom())*100+"%"},Yc=function(c){var u,a,l=C.none(),i=C.none(),f=C.none(),s=/(\d+(\.\d+)?)%/,d=function(e){return"TABLE"===e.nodeName},m=function(e){var n=c.dom.getStyle(e,"width")||c.dom.getAttrib(e,"width");return C.from(n).filter(function(e){return 0<e.length})},e=function(){return i};return c.on("init",function(){var e,n,t=Qr(pu),r=(e=c).inline?Oc(cu(e),$c()):Tc(ge.fromDom(e.getDoc()));if(f=C.some(r),n=c.getParam("object_resizing",!0),(g(n)?"table"===n:n)&&c.getParam("table_resize_bars",!0,"boolean")){var o=Xc(r,t);o.on(),o.events.startDrag.bind(function(e){l=C.some(c.selection.getRng())}),o.events.beforeResize.bind(function(e){var n,t,r,o,i=e.table().dom();n=c,r=au(t=i),o=lu(i),n.fire("ObjectResizeStart",{target:t,width:r,height:o})}),o.events.afterResize.bind(function(e){var n,t,r,o,i=e.table(),u=i.dom();du(i),l.each(function(e){c.selection.setRng(e),c.focus()}),n=c,r=au(t=u),o=lu(u),n.fire("ObjectResized",{target:t,width:r,height:o}),c.undoManager.add()}),i=C.some(o)}}),c.on("ObjectResizeStart",function(e){var n,t,r,o=e.target;if(d(o)){var i=m(o).map(function(e){return s.test(e)}).getOr(!1);i&&Ou(c)?(r=o,be(ge.fromDom(r),"width",au(r).toString()+"px")):i||!0!==c.getParam("table_responsive_width")||(n=o,t=ge.fromDom(n),un(t).map(function(e){return Gc(t,e)}).each(function(e){be(t,"width",e),B(En(t,"tr"),function(n){B(fn(n),function(e){be(e,"width",Gc(e,n))})})})),u=e.width,a=m(o).getOr("")}}),c.on("ObjectResized",function(e){var n=e.target;if(d(n)){var t=n;if(s.test(a)){var r=parseFloat(s.exec(a)[1]),o=e.width*r/u;c.dom.setStyle(t,"width",o+"%")}else{var i=[];Lu.each(t.rows,function(e){Lu.each(e.cells,function(e){var n=c.dom.getStyle(e,"width",!0);i.push({cell:e,width:n})})}),Lu.each(i,function(e){c.dom.setStyle(e.cell,"width",e.width),c.dom.setAttrib(e.cell,"width",null)})}}}),c.on("SwitchMode",function(){i.each(function(e){c.mode.isReadOnly()?e.hideBars():e.showBars()})}),{lazyResize:e,lazyWire:function(){return f.getOr(Tc(ge.fromDom(c.getBody())))},destroy:function(){i.each(function(e){e.destroy()}),f.each(function(e){var n;n=e,c.inline&&yn(n.parent())})}}},Jc=Zt([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),Qc=Re(Re({},Jc),{none:function(e){return void 0===e&&(e=undefined),Jc.none(e)}}),Zc=function(t,e){return $n(t,e).bind(function(e){var n=Kn(e);return _(n,function(e){return en(t,e)}).map(function(e){return{index:e,all:n}})})},ea=function(e,n,t,r){return{start:b(e),soffset:b(n),finish:b(t),foffset:b(r)}},na=Zt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),ta={before:na.before,on:na.on,after:na.after,cata:function(e,n,t,r){return e.fold(n,t,r)},getStart:function(e){return e.fold(o,o,o)}},ra=Zt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),oa={domRange:ra.domRange,relative:ra.relative,exact:ra.exact,exactFromRange:function(e){return ra.exact(e.start(),e.soffset(),e.finish(),e.foffset())},getWin:function(e){var n,t=e.match({domRange:function(e){return ge.fromDom(e.startContainer)},relative:function(e,n){return ta.getStart(e)},exact:function(e,n,t,r){return e}});return n=t,ge.fromDom(n.dom().ownerDocument.defaultView)},range:ea},ia=function(e,n){return e.selectNodeContents(n.dom())},ua=function(e,n,t){var r,o,i=e.document.createRange();return r=i,n.fold(function(e){r.setStartBefore(e.dom())},function(e,n){r.setStart(e.dom(),n)},function(e){r.setStartAfter(e.dom())}),o=i,t.fold(function(e){o.setEndBefore(e.dom())},function(e,n){o.setEnd(e.dom(),n)},function(e){o.setEndAfter(e.dom())}),i},ca=function(e,n,t,r,o){var i=e.document.createRange();return i.setStart(n.dom(),t),i.setEnd(r.dom(),o),i},aa=function(e){return{left:b(e.left),top:b(e.top),right:b(e.right),bottom:b(e.bottom),width:b(e.width),height:b(e.height)}},la=Zt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),fa=function(e,n,t){return n(ge.fromDom(t.startContainer),t.startOffset,ge.fromDom(t.endContainer),t.endOffset)},sa=function(e,n){var o,t,r,i=(o=e,n.match({domRange:function(e){return{ltr:b(e),rtl:C.none}},relative:function(e,n){return{ltr:Te(function(){return ua(o,e,n)}),rtl:Te(function(){return C.some(ua(o,n,e))})}},exact:function(e,n,t,r){return{ltr:Te(function(){return ca(o,e,n,t,r)}),rtl:Te(function(){return C.some(ca(o,t,r,e,n))})}}}));return(r=(t=i).ltr()).collapsed?t.rtl().filter(function(e){return!1===e.collapsed}).map(function(e){return la.rtl(ge.fromDom(e.endContainer),e.endOffset,ge.fromDom(e.startContainer),e.startOffset)}).getOrThunk(function(){return fa(0,la.ltr,r)}):fa(0,la.ltr,r)},da=function(i,e){return sa(i,e).match({ltr:function(e,n,t,r){var o=i.document.createRange();return o.setStart(e.dom(),n),o.setEnd(t.dom(),r),o},rtl:function(e,n,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(e.dom(),n),o}})},ma=(la.ltr,la.rtl,function(e,n,t){return n>=e.left&&n<=e.right&&t>=e.top&&t<=e.bottom}),ga=function(t,r,e,n,o){var i=function(e){var n=t.dom().createRange();return n.setStart(r.dom(),e),n.collapse(!0),n},u=nt(r).length,c=function(e,n,t,r,o){if(0===o)return 0;if(n===r)return o-1;for(var i=r,u=1;u<o;u++){var c=e(u),a=Math.abs(n-c.left);if(t<=c.bottom){if(t<c.top||i<a)return u-1;i=a}}return 0}(function(e){return i(e).getBoundingClientRect()},e,n,o.right,u);return i(c)},pa=function(e,n,t,r){return Z(n)?function(n,t,r,o){var e=n.dom().createRange();e.selectNode(t.dom());var i=e.getClientRects();return L(i,function(e){return ma(e,r,o)?C.some(e):C.none()}).map(function(e){return ga(n,t,r,o,e)})}(e,n,t,r):(i=n,u=t,c=r,a=(o=e).dom().createRange(),l=fn(i),L(l,function(e){return a.selectNode(e.dom()),ma(a.getBoundingClientRect(),u,c)?pa(o,e,u,c):C.none()}));var o,i,u,c,a,l},ha=function(e,n){return n-e.left<e.right-n},va=function(e,n,t){var r=e.dom().createRange();return r.selectNode(n.dom()),r.collapse(t),r},ba=function(n,e,t){var r=n.dom().createRange();r.selectNode(e.dom());var o=r.getBoundingClientRect(),i=ha(o,t);return(!0===i?ct:at)(e).map(function(e){return va(n,e,i)})},wa=function(e,n,t){var r=n.dom().getBoundingClientRect(),o=ha(r,t);return C.some(va(e,n,o))},ya=function(e,n,t,r){var o=e.dom().createRange();o.selectNode(n.dom());var i=o.getBoundingClientRect();return function(e,n,t,r){var o=e.dom().createRange();o.selectNode(n.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,t)),c=Math.max(i.top,Math.min(i.bottom,r));return pa(e,n,u,c)}(e,n,Math.max(i.left,Math.min(i.right,t)),Math.max(i.top,Math.min(i.bottom,r)))},Sa=document.caretPositionFromPoint?function(t,e,n){return C.from(t.dom().caretPositionFromPoint(e,n)).bind(function(e){if(null===e.offsetNode)return C.none();var n=t.dom().createRange();return n.setStart(e.offsetNode,e.offset),n.collapse(),C.some(n)})}:document.caretRangeFromPoint?function(e,n,t){return C.from(e.dom().caretRangeFromPoint(n,t))}:function(o,i,n){return ge.fromPoint(o,i,n).bind(function(r){var e=function(){return e=o,t=i,(0===fn(n=r).length?wa:ba)(e,n,t);var e,n,t};return 0===fn(r).length?e():ya(o,r,i,n).orThunk(e)})},xa=function(e,n){var t=$(e);return"input"===t?ta.after(e):A(["br","img"],t)?0===n?ta.before(e):ta.after(e):ta.on(e,n)},Ca=function(e,n){var t=e.fold(ta.before,xa,ta.after),r=n.fold(ta.before,xa,ta.after);return oa.relative(t,r)},Ra=function(e,n,t,r){var o=xa(e,n),i=xa(t,r);return oa.relative(o,i)},Ta=function(e,n,t,r){var o,i,u,c,a,l=(i=n,u=t,c=r,(a=on(o=e).dom().createRange()).setStart(o.dom(),i),a.setEnd(u.dom(),c),a),f=en(e,t)&&n===r;return l.collapsed&&!f},Oa=function(e,n){C.from(e.getSelection()).each(function(e){e.removeAllRanges(),e.addRange(n)})},Da=function(e,n,t,r,o){var i=ca(e,n,t,r,o);Oa(e,i)},Aa=function(s,e){return sa(s,e).match({ltr:function(e,n,t,r){Da(s,e,n,t,r)},rtl:function(e,n,t,r){var o,i,u,c,a,l=s.getSelection();if(l.setBaseAndExtent)l.setBaseAndExtent(e.dom(),n,t.dom(),r);else if(l.extend)try{i=e,u=n,c=t,a=r,(o=l).collapse(i.dom(),u),o.extend(c.dom(),a)}catch(f){Da(s,t,r,e,n)}else Da(s,t,r,e,n)}})},Ea=function(e,n,t,r,o){var i=Ra(n,t,r,o);Aa(e,i)},ka=function(e,n,t){var r=Ca(n,t);Aa(e,r)},Ba=function(e){var o=oa.getWin(e).dom(),n=function(e,n,t,r){return ca(o,e,n,t,r)},t=e.match({domRange:function(e){var n=ge.fromDom(e.startContainer),t=ge.fromDom(e.endContainer);return Ra(n,e.startOffset,t,e.endOffset)},relative:Ca,exact:Ra});return sa(o,t).match({ltr:n,rtl:n})},Na=function(e){var n=ge.fromDom(e.anchorNode),t=ge.fromDom(e.focusNode);return Ta(n,e.anchorOffset,t,e.focusOffset)?C.some(ea(n,e.anchorOffset,t,e.focusOffset)):function(e){if(0<e.rangeCount){var n=e.getRangeAt(0),t=e.getRangeAt(e.rangeCount-1);return C.some(ea(ge.fromDom(n.startContainer),n.startOffset,ge.fromDom(t.endContainer),t.endOffset))}return C.none()}(e)},Ia=function(e,n){var t,r,o=(t=n,r=e.document.createRange(),ia(r,t),r);Oa(e,o)},Pa=function(e){return n=e,C.from(n.getSelection()).filter(function(e){return 0<e.rangeCount}).bind(Na).map(function(e){return oa.exact(e.start(),e.soffset(),e.finish(),e.foffset())});var n},Ma=function(e,n){var t,r,o,i=da(e,n);return r=(t=i).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?C.some(o).map(aa):C.none()},_a=function(e,n,t){return r=e,o=n,i=t,u=ge.fromDom(r.document),Sa(u,o,i).map(function(e){return ea(ge.fromDom(e.startContainer),e.startOffset,ge.fromDom(e.endContainer),e.endOffset)});var r,o,i,u},Wa=tinymce.util.Tools.resolve("tinymce.util.VK"),ja=function(e,n,t,r){return Ha(e,n,Zc(o=t,i).fold(function(){return Qc.none(o)},function(e){return e.index+1<e.all.length?Qc.middle(o,e.all[e.index+1]):Qc.last(o)}),r);var o,i},za=function(e,n,t,r){return Ha(e,n,Zc(o=t,i).fold(function(){return Qc.none()},function(e){return 0<=e.index-1?Qc.middle(o,e.all[e.index-1]):Qc.first(o)}),r);var o,i},La=function(e,n){var t=oa.exact(n,0,n,0);return Ba(t)},Fa=function(e,n){var t,r=En(n,"tr");return(0===(t=r).length?C.none():C.some(t[t.length-1])).bind(function(e){return Pn(e,"td,th").map(function(e){return La(0,e)})})},Ha=function(r,e,n,o,t){return n.fold(C.none,C.none,function(e,n){return ct(n).map(function(e){return La(0,e)})},function(t){return $n(t,e).bind(function(e){var n=ur(t);return r.undoManager.transact(function(){o.insertRowsAfter(e,n)}),Fa(0,e)})})},qa=["table","li","dl"],Ua=function(n,t,r,o){if(n.keyCode===Wa.TAB){var i=cu(t),u=function(e){var n=$(e);return en(e,i)||A(qa,n)},e=t.selection.getRng();if(e.collapsed){var c=ge.fromDom(e.startContainer);Vn(c,u).each(function(e){n.preventDefault(),(n.shiftKey?za:ja)(t,u,e,r,o).each(function(e){t.selection.setRng(e)})})}}},Va=function(e,n){return{selection:b(e),kill:b(n)}},Ka=function(e,n,t,r){return{start:b(ta.on(e,n)),finish:b(ta.on(t,r))}},Xa=function(e,n){var t=da(e,n);return ea(ge.fromDom(t.startContainer),t.startOffset,ge.fromDom(t.endContainer),t.endOffset)},$a=Ka,Ga=function(t,e,r,n,o){return en(r,n)?C.none():zt(r,n,e).bind(function(e){var n=e.boxes.getOr([]);return 0<n.length?(o(t,n,e.start,e.finish),C.some(Va(C.some($a(r,0,r,ot(r))),!0))):C.none()})},Ya=function(e,n){return{item:b(e),mode:b(n)}},Ja=function(e,n,t,r){return void 0===r&&(r=Qa),e.property().parent(n).map(function(e){return Ya(e,r)})},Qa=function(e,n,t,r){return void 0===r&&(r=Za),t.sibling(e,n).map(function(e){return Ya(e,r)})},Za=function(e,n,t,r){void 0===r&&(r=Za);var o=e.property().children(n);return t.first(o).map(function(e){return Ya(e,r)})},el=[{current:Ja,next:Qa,fallback:C.none()},{current:Qa,next:Za,fallback:C.some(Ja)},{current:Za,next:Za,fallback:C.some(Qa)}],nl=function(n,t,r,o,e){return void 0===e&&(e=el),M(e,function(e){return e.current===r}).bind(function(e){return e.current(n,t,o,e.next).orThunk(function(){return e.fallback.bind(function(e){return nl(n,t,e,o)})})})},tl=function(){return{sibling:function(e,n){return e.query().prevSibling(n)},first:function(e){return 0<e.length?C.some(e[e.length-1]):C.none()}}},rl=function(){return{sibling:function(e,n){return e.query().nextSibling(n)},first:function(e){return 0<e.length?C.some(e[0]):C.none()}}},ol=function(n,e,t,r,o,i){return nl(n,e,r,o).bind(function(e){return i(e.item())?C.none():t(e.item())?C.some(e.item()):ol(n,e.item(),t,e.mode(),o,i)})},il=function(n){return function(e){return 0===n.property().children(e).length}},ul=function(e,n,t,r){return ol(e,n,t,Qa,tl(),r)},cl=function(e,n,t,r){return ol(e,n,t,Qa,rl(),r)},al=Bt(),ll=function(e,n){return ul(t=al,e,il(t),n);var t},fl=function(e,n){return cl(t=al,e,il(t),n);var t},sl=Zt([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),dl=function(e){return Mn(e,"tr")},ml=Re(Re({},sl),{verify:function(c,e,n,t,r,a,o){return Mn(t,"td,th",o).bind(function(u){return Mn(e,"td,th",o).map(function(i){return en(u,i)?en(t,u)&&ot(u)===r?a(i):sl.none("in same cell"):Wt(dl,[u,i]).fold(function(){return n=i,t=u,r=(e=c).getRect(n),(o=e.getRect(t)).right>r.left&&o.left<r.right?sl.success():a(i);var e,n,t,r,o},function(e){return a(i)})})}).getOr(sl.none("default"))},cata:function(e,n,t,r,o){return e.fold(n,t,r,o)}}),gl=function(r){return un(r).bind(function(n){var t=fn(n);return pl(t,r).map(function(e){return{parent:b(n),children:b(t),element:b(r),index:b(e)}})})},pl=function(e,n){return _(e,y(en,n))},hl=function(e){return"br"===$(e)},vl=function(e,n,t){return n(e,t).bind(function(e){return Z(e)&&0===nt(e).trim().length?vl(e,n,t):C.some(e)})},bl=function(n,e,t,r){return sn(o=e,i=t).filter(hl).orThunk(function(){return sn(o,i-1).filter(hl)}).bind(function(e){return r.traverse(e).fold(function(){return vl(e,r.gather,n).map(r.relative)},function(e){return gl(e).map(function(e){return ta.on(e.parent(),e.index())})})});var o,i},wl=function(e,n,t,r){var o,i,u;return(hl(n)?(o=e,i=n,(u=r).traverse(i).orThunk(function(){return vl(i,u.gather,o)}).map(u.relative)):bl(e,n,t,r)).map(function(e){return{start:b(e),finish:b(e)}})},yl=function(e,n){return{left:e.left,top:e.top+n,right:e.right,bottom:e.bottom+n}},Sl=function(e,n){return{left:e.left,top:e.top-n,right:e.right,bottom:e.bottom-n}},xl=function(e,n,t){return{left:e.left+n,top:e.top+t,right:e.right+n,bottom:e.bottom+t}},Cl=function(e){return{left:e.left,top:e.top,right:e.right,bottom:e.bottom}},Rl=function(e,n){return C.some(e.getRect(n))},Tl=function(e,n,t){return Q(n)?Rl(e,n).map(Cl):Z(n)?(r=e,o=n,(0<=(i=t)&&i<ot(o)?r.getRangedRect(o,i,o,i+1):0<i?r.getRangedRect(o,i-1,o,i):C.none()).map(Cl)):C.none();var r,o,i},Ol=function(e,n){return Q(n)?Rl(e,n).map(Cl):Z(n)?e.getRangedRect(n,0,n,ot(n)).map(Cl):C.none()},Dl=Zt([{none:[]},{retry:["caret"]}]),Al=function(n,e,r){return kn(function(e,n){return n(e)},Bn,e,Wi,t).fold(b(!1),function(e){return Ol(n,e).exists(function(e){return t=e,(n=r).left<t.left||Math.abs(t.right-n.left)<1||n.left>t.right;var n,t})});var t},El={point:function(e){return e.bottom},adjuster:function(e,n,t,r,o){var i=yl(o,5);return Math.abs(t.bottom-r.bottom)<1||t.top>o.bottom?Dl.retry(i):t.top===o.bottom?Dl.retry(yl(o,1)):Al(e,n,o)?Dl.retry(xl(i,5,0)):Dl.none()},move:yl,gather:fl},kl=function(t,r,o,i,u){return 0===u?C.some(i):(a=t,l=i.left,f=r.point(i),a.elementFromPoint(l,f).filter(function(e){return"table"===$(e)}).isSome()?(n=i,c=u-1,kl(t,e=r,o,e.move(n,5),c)):t.situsFromPoint(i.left,r.point(i)).bind(function(e){return e.start().fold(C.none,function(n){return Ol(t,n).bind(function(e){return r.adjuster(t,n,e,o,i).fold(C.none,function(e){return kl(t,r,o,e,u-1)})}).orThunk(function(){return C.some(i)})},C.none)}));var e,n,c,a,l,f},Bl=function(n,t,e){var r,o,i,u=n.move(e,5),c=kl(t,n,e,u,100).getOr(u);return o=c,i=t,((r=n).point(o)>i.getInnerHeight()?C.some(r.point(o)-i.getInnerHeight()):r.point(o)<0?C.some(-r.point(o)):C.none()).fold(function(){return t.situsFromPoint(c.left,n.point(c))},function(e){return t.scrollBy(0,e),t.situsFromPoint(c.left,n.point(c)-e)})},Nl={tryUp:y(Bl,{point:function(e){return e.top},adjuster:function(e,n,t,r,o){var i=Sl(o,5);return Math.abs(t.top-r.top)<1||t.bottom<o.top?Dl.retry(i):t.bottom===o.top?Dl.retry(Sl(o,1)):Al(e,n,o)?Dl.retry(xl(i,5,0)):Dl.none()},move:Sl,gather:ll}),tryDown:y(Bl,El),ieTryUp:function(e,n){return e.situsFromPoint(n.left,n.top-5)},ieTryDown:function(e,n){return e.situsFromPoint(n.left,n.bottom+5)},getJumpSize:b(5)},Il=function(i,u,c){return i.getSelection().bind(function(o){return wl(u,o.finish(),o.foffset(),c).fold(function(){return C.some(Nu(o.finish(),o.foffset()))},function(e){var n,t=i.fromSitus(e),r=ml.verify(i,o.finish(),o.foffset(),t.finish(),t.foffset(),c.failure,u);return n=r,ml.cata(n,function(e){return C.none()},function(){return C.none()},function(e){return C.some(Nu(e,0))},function(e){return C.some(Nu(e,ot(e)))})})})},Pl=function(r,o,i,u,c,a){return 0===a?C.none():Wl(r,o,i,u,c).bind(function(e){var n=r.fromSitus(e),t=ml.verify(r,i,u,n.finish(),n.foffset(),c.failure,o);return ml.cata(t,function(){return C.none()},function(){return C.some(e)},function(e){return en(i,e)&&0===u?Ml(r,i,u,Sl,c):Pl(r,o,e,0,c,a-1)},function(e){return en(i,e)&&u===ot(e)?Ml(r,i,u,yl,c):Pl(r,o,e,ot(e),c,a-1)})})},Ml=function(n,e,t,r,o){return Tl(n,e,t).bind(function(e){return _l(n,o,r(e,Nl.getJumpSize()))})},_l=function(e,n,t){var r=Je().browser;return r.isChrome()||r.isSafari()||r.isFirefox()||r.isEdge()?n.otherRetry(e,t):r.isIE()?n.ieRetry(e,t):C.none()},Wl=function(n,e,t,r,o){return Tl(n,t,r).bind(function(e){return _l(n,o,e)})},jl=function(e,n){return Bn(e,function(e){return un(e).exists(function(e){return en(e,n)})},t).isSome();var t},zl=function(i,u,c,e,a){return Mn(e,"td,th",u).bind(function(o){return Mn(o,"table",u).bind(function(e){return jl(a,e)?Il(n=i,t=u,r=c).bind(function(e){return Pl(n,t,e.element(),e.offset(),r,20).map(n.fromSitus)}).bind(function(n){return Mn(n.finish(),"td,th",u).map(function(e){return{start:b(o),finish:b(e),range:b(n)}})}):C.none();var n,t,r})})},Ll=function(e,n,t,r,o,i){return Je().browser.isIE()?C.none():i(r,n).orThunk(function(){return zl(e,n,t,r,o).map(function(e){var n=e.range();return Va(C.some($a(n.start(),n.soffset(),n.finish(),n.foffset())),!0)})})},Fl=function(e,r){return Mn(e,"tr",r).bind(function(t){return Mn(t,"table",r).bind(function(e){var n=En(e,"tr");return en(t,n[0])?ul(al,e,function(e){return at(e).isSome()},r).map(function(e){var n=ot(e);return Va(C.some($a(e,n,e,n)),!0)}):C.none()})})},Hl=function(e,r){return Mn(e,"tr",r).bind(function(t){return Mn(t,"table",r).bind(function(e){var n=En(e,"tr");return en(t,n[n.length-1])?cl(al,e,function(e){return ct(e).isSome()},r).map(function(e){return Va(C.some($a(e,0,e,0)),!0)}):C.none()})})},ql=function(e,n,t,r,o,i,u){return zl(e,t,r,o,i).bind(function(e){return Ga(n,t,e.start(),e.finish(),u)})},Ul=function(e,n){return Mn(e,"td,th",n)};var Vl={traverse:ln,gather:fl,relative:ta.before,otherRetry:Nl.tryDown,ieRetry:Nl.ieTryDown,failure:ml.failedDown},Kl={traverse:an,gather:ll,relative:ta.before,otherRetry:Nl.tryUp,ieRetry:Nl.ieTryUp,failure:ml.failedUp},Xl=function(n){return function(e){return e===n}},$l=Xl(38),Gl=Xl(40),Yl=function(e){return 37<=e&&e<=40},Jl={isBackward:Xl(37),isForward:Xl(39)},Ql={isBackward:Xl(39),isForward:Xl(37)},Zl=function(e){return{left:e.left(),top:e.top(),right:e.right(),bottom:e.bottom(),width:e.width(),height:e.height()}},ef=function(c){return{elementFromPoint:function(e,n){return ge.fromPoint(ge.fromDom(c.document),e,n)},getRect:function(e){return e.dom().getBoundingClientRect()},getRangedRect:function(e,n,t,r){var o=oa.exact(e,n,t,r);return Ma(c,o).map(Zl)},getSelection:function(){return Pa(c).map(function(e){return Xa(c,e)})},fromSitus:function(e){var n=oa.relative(e.start(),e.finish());return Xa(c,n)},situsFromPoint:function(e,n){return _a(c,e,n).map(function(e){return Ka(e.start(),e.soffset(),e.finish(),e.foffset())})},clearSelection:function(){c.getSelection().removeAllRanges()},collapseSelection:function(u){void 0===u&&(u=!1),Pa(c).each(function(e){return e.fold(function(e){return e.collapse(u)},function(e,n){var t=u?e:n;ka(c,t,t)},function(e,n,t,r){var o=u?e:t,i=u?n:r;Ea(c,o,i,o,i)})})},setSelection:function(e){Ea(c,e.start(),e.soffset(),e.finish(),e.foffset())},setRelativeSelection:function(e,n){ka(c,e,n)},selectContents:function(e){Ia(c,e)},getInnerHeight:function(){return c.innerHeight},getScrollY:function(){var e,n,t,r;return(e=ge.fromDom(c.document),n=e!==undefined?e.dom():p.document,t=n.body.scrollLeft||n.documentElement.scrollLeft,r=n.body.scrollTop||n.documentElement.scrollTop,_r(t,r)).top()},scrollBy:function(e,n){var t,r,o;t=e,r=n,((o=ge.fromDom(c.document))!==undefined?o.dom():p.document).defaultView.scrollBy(t,r)}}},nf=function(e,n){return{rows:e,cols:n}},tf=function(e,n,t,r){var o=function c(o,i,n,u){var t=C.none(),r=function(){t=C.none()};return{mousedown:function(e){u.clear(i),t=Ul(e.target(),n)},mouseover:function(e){t.each(function(r){u.clearBeforeUpdate(i),Ul(e.target(),n).each(function(t){zt(r,t,n).each(function(e){var n=e.boxes.getOr([]);(1<n.length||1===n.length&&!en(r,t))&&(u.selectRange(i,n,e.start,e.finish),o.selectContents(t))})})})},mouseup:function(e){t.each(r)}}}(ef(e),n,t,r);return{mousedown:o.mousedown,mouseover:o.mouseover,mouseup:o.mouseup}},rf=function(e,g,p,h){var l=ef(e),f=function(){return h.clear(g),C.none()};return{keydown:function(e,n,t,r,o,u){var i=e.raw(),c=i.which,a=!0===i.shiftKey;return Lt(g,h.selectedSelector).fold(function(){return Gl(c)&&a?y(ql,l,g,p,Vl,r,n,h.selectRange):$l(c)&&a?y(ql,l,g,p,Kl,r,n,h.selectRange):Gl(c)?y(Ll,l,p,Vl,r,n,Hl):$l(c)?y(Ll,l,p,Kl,r,n,Fl):C.none},function(i){var e=function(e){return function(){return L(e,function(e){return n=e.rows,t=e.cols,r=g,Ht(i,n,t,(o=h).firstSelectedSelector,o.lastSelectedSelector).map(function(e){return o.clearBeforeUpdate(r),o.selectRange(r,e.boxes,e.start,e.finish),e.boxes});var n,t,r,o}).fold(function(){return Ft(g,h.firstSelectedSelector,h.lastSelectedSelector).map(function(e){var n=Gl(c)||u.isForward(c)?ta.after:ta.before;return l.setRelativeSelection(ta.on(e.first(),0),n(e.table())),h.clear(g),Va(C.none(),!0)})},function(e){return C.some(Va(C.none(),!0))})}};return Gl(c)&&a?e([nf(1,0)]):$l(c)&&a?e([nf(-1,0)]):u.isBackward(c)&&a?e([nf(0,-1),nf(-1,0)]):u.isForward(c)&&a?e([nf(0,1),nf(1,0)]):Yl(c)&&!1==a?f:C.none})()},keyup:function(l,f,s,d,m){return Lt(g,h.selectedSelector).fold(function(){var t,r,e,n,o,i,u,c=l.raw(),a=c.which;return!1!=(!0===c.shiftKey)&&Yl(a)?(t=g,r=p,e=f,n=s,o=d,i=m,u=h.selectRange,en(e,o)&&n===i?C.none():Mn(e,"td,th",r).bind(function(n){return Mn(o,"td,th",r).bind(function(e){return Ga(t,r,n,e,u)})})):C.none()},C.none)}}},of=function(e,r,n,o){var i=ef(e);return function(e,t){o.clearBeforeUpdate(r),zt(e,t,n).each(function(e){var n=e.boxes.getOr([]);o.selectRange(r,n,e.start,e.finish),i.selectContents(t),i.collapseSelection()})}},uf=function(n,e){B(e,function(e){!function(e,n){oo(e)?e.dom().classList.remove(n):uo(e,n);ao(e)}(n,e)})},cf={byClass:function(o){var n,t,i=(n=o.selected,function(e){co(e,n)}),r=(t=[o.selected,o.lastSelected,o.firstSelected],function(e){uf(e,t)}),u=function(e){var n=En(e,o.selectedSelector);B(n,r)};return{clearBeforeUpdate:u,clear:u,selectRange:function(e,n,t,r){u(e),B(n,i),co(t,o.firstSelected),co(r,o.lastSelected)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},byAttr:function(o,i,n){var t=function(e){ue(e,o.selected),ue(e,o.firstSelected),ue(e,o.lastSelected)},u=function(e){ne(e,o.selected,"1")},c=function(e){r(e),n()},r=function(e){var n=En(e,o.selectedSelector);B(n,t)};return{clearBeforeUpdate:r,clear:c,selectRange:function(e,n,t,r){c(e),B(n,u),ne(t,o.firstSelected,"1"),ne(r,o.lastSelected,"1"),i(n,t,r)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}}},af=function(e,n,s){var t=Gn(e),d=Qn.generate(t);return Vo(d,n).map(function(e){var t,n,r,o,i,u,c,a,l,f=jo(d,s,!1);return{upOrLeftCells:(t=e,n=s,r=f.slice(0,t[t.length-1].row()+1),o=Lo(r,n),j(o,function(e){var n=e.cells().slice(0,t[t.length-1].column()+1);return k(n,function(e){return e.element()})})),downOrRightCells:(u=e,c=s,a=(i=f).slice(u[0].row()+u[0].rowspan()-1,i.length),l=Lo(a,c),j(l,function(e){var n=e.cells().slice(u[0].column()+u[0].colspan()-1,+e.cells().length);return k(n,function(e){return e.element()})}))}})},lf=function(e){return!1===lo(ge.fromDom(e.target),"ephox-snooker-resizer-bar")};function ff(v,b,e){var w=C.none(),a=Du(v),y=cf.byAttr(Qt,function(i,u,c){e.targets().each(function(o){$n(u).each(function(e){var n=ge.fromDom(v.getDoc()),t=bt(x,n,a),r=af(e,o,t);ku(v,i,u,c,r)})})},function(){Bu(v)});v.on("init",function(e){var r=v.getWin(),o=cu(v),n=fu(v),t=tf(r,o,n,y),c=rf(r,o,n,y),i=of(r,o,n,y);v.on("TableSelectorChange",function(e){i(e.start,e.finish)});var u,a,l=function(e,n){!0===e.raw().shiftKey&&(n.kill()&&e.kill(),n.selection().each(function(e){var n=oa.relative(e.start(),e.finish()),t=da(r,n);v.selection.setRng(t)}))},f=function(e){var n=Pc(e);if(n.raw().shiftKey&&Yl(n.raw().which)){var t=v.selection.getRng(),r=ge.fromDom(t.startContainer),o=ge.fromDom(t.endContainer);c.keyup(n,r,t.startOffset,o,t.endOffset).each(function(e){l(n,e)})}},s=function(e){var n=Pc(e);b().each(function(e){e.hideBars()});var t=v.selection.getRng(),r=ge.fromDom(v.selection.getStart()),o=ge.fromDom(t.startContainer),i=ge.fromDom(t.endContainer),u=pu(r).isRtl()?Ql:Jl;c.keydown(n,o,t.startOffset,i,t.endOffset,u).each(function(e){l(n,e)}),b().each(function(e){e.showBars()})},d=function(e){return 0===e.button},m=function(e){d(e)&&lf(e)&&t.mousedown(Pc(e))},g=function(e){var n;((n=e).buttons===undefined||pc.browser.isEdge()&&0===n.buttons||0!=(1&n.buttons))&&lf(e)&&t.mouseover(Pc(e))},p=function(e){d(e)&&lf(e)&&t.mouseup(Pc(e))},h=(u=S(ge.fromDom(o)),a=S(0),{touchEnd:function(e){var n=ge.fromDom(e.target);if("td"===$(n)||"th"===$(n)){var t=u.get(),r=a.get();en(t,n)&&e.timeStamp-r<300&&(e.preventDefault(),i(n,n))}u.set(n),a.set(e.timeStamp)}});v.on("mousedown",m),v.on("mouseover",g),v.on("mouseup",p),v.on("touchend",h.touchEnd),v.on("keyup",f),v.on("keydown",s),v.on("NodeChange",function(){var e=v.selection,n=ge.fromDom(e.getStart()),t=ge.fromDom(e.getEnd());Wt($n,[n,t]).fold(function(){y.clear(o)},x)}),w=C.some({mousedown:m,mouseover:g,mouseup:p,keyup:f,keydown:s})});return{clear:y.clear,destroy:function(){w.each(function(e){})}}}var sf=function(n){return{get:function(){var e=cu(n);return Lt(e,Kt).fold(function(){return n.selection.getStart()===undefined?tr():or(n.selection)},function(e){return rr(e)})}}},df=function(e,t){var o=S(C.none()),i=S([]),n=function(){return qu(e).bind(function(n){return $n(n).map(function(e){return"caption"===$(n)?ur(n):cr(t,e,n)})})},r=function(){o.set(Te(n)()),B(i.get(),function(e){return e()})},u=function(n,t){var r=function(){return o.get().fold(function(){n.setDisabled(!0)},function(e){n.setDisabled(t(e))})};return r(),i.set(i.get().concat([r])),function(){i.set(N(i.get(),function(e){return e!==r}))}};return e.on("NodeChange TableSelectorChange",r),{onSetupTable:function(e){return u(e,function(e){return!1})},onSetupCellOrRow:function(e){return u(e,function(e){return"caption"===$(e.element())})},onSetupMergeable:function(e){return u(e,function(e){return e.mergable().isNone()})},onSetupUnmergeable:function(e){return u(e,function(e){return e.unmergable().isNone()})},resetTargets:r,targets:function(){return o.get()}}},mf=function(n,e){n.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(e){return e("inserttable | cell row column | advtablesort | tableprops deletetable")}});var t=function(e){return function(){return n.execCommand(e)}};n.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:t("mceTableProps"),icon:"table",onSetup:e.onSetupTable}),n.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:t("mceTableDelete"),icon:"table-delete-table",onSetup:e.onSetupTable}),n.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:t("mceTableCellProps"),icon:"table-cell-properties",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:t("mceTableMergeCells"),icon:"table-merge-cells",onSetup:e.onSetupMergeable}),n.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:t("mceTableSplitCells"),icon:"table-split-cells",onSetup:e.onSetupUnmergeable}),n.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:t("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:t("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:t("mceTableDeleteRow"),icon:"table-delete-row",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:t("mceTableRowProps"),icon:"table-row-properties",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:t("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:t("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:t("mceTableDeleteCol"),icon:"table-delete-column",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",onAction:t("mceTableCutRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",onAction:t("mceTableCopyRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",onAction:t("mceTablePasteRowBefore"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",onAction:t("mceTablePasteRowAfter"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:t("mceInsertTable"),icon:"table"})},gf=function(n){var e=n.getParam("table_toolbar","tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol");0<e.length&&n.ui.registry.addContextToolbar("table",{predicate:function(e){return n.dom.is(e,"table")&&n.getBody().contains(e)},items:e,scope:"node",position:"node"})},pf=function(r,e){var n=function(e){return function(){return r.execCommand(e)}},t=function(e){var n=e.numRows,t=e.numColumns;r.undoManager.transact(function(){wc(r,t,n)}),r.addVisual()},o={text:"Table properties",onSetup:e.onSetupTable,onAction:n("mceTableProps")},i={text:"Delete table",icon:"table-delete-table",onSetup:e.onSetupTable,onAction:n("mceTableDelete")},u=[{type:"menuitem",text:"Insert row before",icon:"table-insert-row-above",onAction:n("mceTableInsertRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert row after",icon:"table-insert-row-after",onAction:n("mceTableInsertRowAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete row",icon:"table-delete-row",onAction:n("mceTableDeleteRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Row properties",icon:"table-row-properties",onAction:n("mceTableRowProps"),onSetup:e.onSetupCellOrRow},{type:"separator"},{type:"menuitem",text:"Cut row",onAction:n("mceTableCutRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Copy row",onAction:n("mceTableCopyRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row before",onAction:n("mceTablePasteRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row after",onAction:n("mceTablePasteRowAfter"),onSetup:e.onSetupCellOrRow}],c={type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return u}},a=[{type:"menuitem",text:"Insert column before",icon:"table-insert-column-before",onAction:n("mceTableInsertColBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert column after",icon:"table-insert-column-after",onAction:n("mceTableInsertColAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete column",icon:"table-delete-column",onAction:n("mceTableDeleteCol"),onSetup:e.onSetupCellOrRow}],l={type:"nestedmenuitem",text:"Column",getSubmenuItems:function(){return a}},f=[{type:"menuitem",text:"Cell properties",icon:"table-cell-properties",onAction:n("mceTableCellProps"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Merge cells",icon:"table-merge-cells",onAction:n("mceTableMergeCells"),onSetup:e.onSetupMergeable},{type:"menuitem",text:"Split cell",icon:"table-split-cells",onAction:n("mceTableSplitCells"),onSetup:e.onSetupUnmergeable}],s={type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return f}};!1===r.getParam("table_grid",!0,"boolean")?r.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:n("mceInsertTable")}):r.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:t}]}}),r.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:n("mceInsertTable")}),r.ui.registry.addMenuItem("tableprops",o),r.ui.registry.addMenuItem("deletetable",i),r.ui.registry.addNestedMenuItem("row",c),r.ui.registry.addNestedMenuItem("column",l),r.ui.registry.addNestedMenuItem("cell",s),r.ui.registry.addContextMenu("table",{update:function(){return e.resetTargets(),e.targets().fold(function(){return""},function(e){return"caption"===$(e.element())?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})},hf=function(t,r,e,n){return{insertTable:function(e,n){return wc(t,e,n)},setClipboardRows:function(e){return n=r,t=k(e,ge.fromDom),void n.set(C.from(t));var n,t},getClipboardRows:function(){return r.get().fold(function(){},function(e){return k(e,function(e){return e.dom()})})},resizeHandler:e,selectionTargets:n}};function vf(n){var e=sf(n),t=df(n,e),r=Yc(n),o=ff(n,r.lazyResize,t),i=ju(n,r.lazyWire),u=S(C.none());return Rc(n,i,o,e,u),ar(n,e,i,o),pf(n,t),mf(n,t),gf(n),n.on("PreInit",function(){n.serializer.addTempAttr($t),n.serializer.addTempAttr(Yt)}),yu(n)&&n.on("keydown",function(e){Ua(e,n,i,r.lazyWire)}),n.on("remove",function(){r.destroy(),o.destroy()}),hf(n,u,r,t)}!function wf(){s.add("table",vf)}()}(window);