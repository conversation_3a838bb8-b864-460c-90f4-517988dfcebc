/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.2 (2020-06-10)
 */
!function(p){"use strict";var t,e,n,l=function(t){var e=t;return{get:function(){return e},set:function(t){e=t}}},r=tinymce.util.Tools.resolve("tinymce.PluginManager"),d=tinymce.util.Tools.resolve("tinymce.util.Tools"),o=function(){},f=function(t){return function(){return t}},i=f(!1),u=f(!0),a=function(){return c},c=(t=function(t){return t.isNone()},{fold:function(t,e){return t()},is:i,isSome:i,isNone:u,getOr:n=function(t){return t},getOrThunk:e=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:f(null),getOrUndefined:f(undefined),or:n,orThunk:e,map:a,each:o,bind:a,exists:i,forall:u,filter:a,equals:t,equals_:t,toArray:function(){return[]},toString:f("none()")}),s=function(n){var t=f(n),e=function(){return o},r=function(t){return t(n)},o={fold:function(t,e){return e(n)},is:function(t){return n===t},isSome:u,isNone:i,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:e,orThunk:e,map:function(t){return s(t(n))},each:function(t){t(n)},bind:r,exists:r,forall:r,filter:function(t){return t(n)?o:c},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(t){return t.is(n)},equals_:function(t,e){return t.fold(i,function(t){return e(n,t)})}};return o},v={some:s,none:a,from:function(t){return null===t||t===undefined?c:s(t)}};function m(t,e){return y(p.document.createElement("canvas"),t,e)}function h(t){var e=m(t.width,t.height);return g(e).drawImage(t,0,0),e}function g(t){return t.getContext("2d")}function y(t,e,n){return t.width=e,t.height=n,t}var w,b,I,T=window.Promise?window.Promise:(b=(w=function(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],L(t,_(U,this),_(A,this))}).immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(t){p.setTimeout(t,1)},I=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},w.prototype["catch"]=function(t){return this.then(null,t)},w.prototype.then=function(n,r){var o=this;return new w(function(t,e){R.call(o,new E(n,r,t,e))})},w.all=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var c=Array.prototype.slice.call(1===t.length&&I(t[0])?t[0]:t);return new w(function(o,i){if(0===c.length)return o([]);var u=c.length;function a(e,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void n.call(t,function(t){a(e,t)},i)}c[e]=t,0==--u&&o(c)}catch(r){i(r)}}for(var t=0;t<c.length;t++)a(t,c[t])})},w.resolve=function(e){return e&&"object"==typeof e&&e.constructor===w?e:new w(function(t){t(e)})},w.reject=function(n){return new w(function(t,e){e(n)})},w.race=function(o){return new w(function(t,e){for(var n=0,r=o;n<r.length;n++)r[n].then(t,e)})},w);function _(t,e){return function(){return t.apply(e,arguments)}}function R(r){var o=this;null!==this._state?b(function(){var t=o._state?r.onFulfilled:r.onRejected;if(null!==t){var e;try{e=t(o._value)}catch(n){return void r.reject(n)}r.resolve(e)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function U(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void L(_(e,t),_(U,this),_(A,this))}this._state=!0,this._value=t,x.call(this)}catch(n){A.call(this,n)}}function A(t){this._state=!1,this._value=t,x.call(this)}function x(){for(var t=0,e=this._deferreds;t<e.length;t++){var n=e[t];R.call(this,n)}this._deferreds=[]}function E(t,e,n,r){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.resolve=n,this.reject=r}function L(t,e,n){var r=!1;try{t(function(t){r||(r=!0,e(t))},function(t){r||(r=!0,n(t))})}catch(o){if(r)return;r=!0,n(o)}}function j(t){var e=t.src;return(0===e.indexOf("data:")?k:function n(r){return new T(function(t,n){var e=new p.XMLHttpRequest;e.open("GET",r,!0),e.responseType="blob",e.onload=function(){200===this.status&&t(this.response)},e.onerror=function(){var t,e=this;n(0===this.status?((t=new Error("No access to download image")).code=18,t.name="SecurityError",t):new Error("Error "+e.status+" downloading image"))},e.send()})})(e)}function C(a){return new T(function(t,e){var n=p.URL.createObjectURL(a),r=new p.Image,o=function(){r.removeEventListener("load",i),r.removeEventListener("error",u)};function i(){o(),t(r)}function u(){o(),e("Unable to load data of type "+a.type+": "+n)}r.addEventListener("load",i),r.addEventListener("error",u),r.src=n,r.complete&&i()})}function k(n){return new T(function(t,e){(function g(t){var e=t.split(","),n=/data:([^;]+)/.exec(e[0]);if(!n)return v.none();for(var r=n[1],o=e[1],i=p.atob(o),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var f=1024*s,l=Math.min(1024+f,u),d=new Array(l-f),m=f,h=0;m<l;++h,++m)d[h]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return v.some(new p.Blob(c,{type:r}))})(n).fold(function(){e("uri is not base64: "+n)},t)})}function O(t,r,o){return r=r||"image/png",p.HTMLCanvasElement.prototype.toBlob?new T(function(e,n){t.toBlob(function(t){t?e(t):n()},r,o)}):k(t.toDataURL(r,o))}function P(t){return C(t).then(function(t){!function n(t){p.URL.revokeObjectURL(t.src)}(t);var e=m(function r(t){return t.naturalWidth||t.width}(t),function o(t){return t.naturalHeight||t.height}(t));return g(e).drawImage(t,0,0),e})}function S(t,e,n){var r=e.type;function o(e,n){return t.then(function(t){return function r(t,e,n){return e=e||"image/png",t.toDataURL(e,n)}(t,e,n)})}return{getType:f(r),toBlob:function i(){return T.resolve(e)},toDataURL:f(n),toBase64:function u(){return n.split(",")[1]},toAdjustedBlob:function a(e,n){return t.then(function(t){return O(t,e,n)})},toAdjustedDataURL:o,toAdjustedBase64:function c(t,e){return o(t,e).then(function(t){return t.split(",")[1]})},toCanvas:function s(){return t.then(h)}}}function M(e){return function t(n){return new T(function(t){var e=new p.FileReader;e.onloadend=function(){t(e.result)},e.readAsDataURL(n)})}(e).then(function(t){return S(P(e),e,t)})}function B(e,t){return O(e,t).then(function(t){return S(T.resolve(e),t,e.toDataURL())})}function N(e,n){return e.toCanvas().then(function(t){return function a(t,e,n){var r=m(t.width,t.height),o=g(r),i=0,u=0;90!==(n=n<0?360+n:n)&&270!==n||y(r,r.height,r.width);90!==n&&180!==n||(i=r.width);270!==n&&180!==n||(u=r.height);return o.translate(i,u),o.rotate(n*Math.PI/180),o.drawImage(t,0,0),B(r,e)}(t,e.getType(),n)})}function D(e,n){return e.toCanvas().then(function(t){return function i(t,e,n){var r=m(t.width,t.height),o=g(r);"v"===n?(o.scale(1,-1),o.drawImage(t,0,-r.height)):(o.scale(-1,1),o.drawImage(t,-r.width,0));return B(r,e)}(t,e.getType(),n)})}var F=function(t,e){return function(t,e,n){for(var r=0,o=t.length;r<o;r++){var i=t[r];if(e(i,r))return v.some(i);if(n(i,r))break}return v.none()}(t,e,i)},H=function(t){return M(t)},q=tinymce.util.Tools.resolve("tinymce.util.Delay"),z=tinymce.util.Tools.resolve("tinymce.util.Promise"),$=tinymce.util.Tools.resolve("tinymce.util.URI");function G(t){var e,n;function r(t){return/^[0-9\.]+px$/.test(t)}return e=t.style.width,n=t.style.height,e||n?r(e)&&r(n)?{w:parseInt(e,10),h:parseInt(n,10)}:null:(e=t.width,n=t.height,e&&n?{w:parseInt(e,10),h:parseInt(n,10)}:null)}function J(t){return{w:t.naturalWidth,h:t.naturalHeight}}var K=function(t){return null!==t&&t!==undefined},V=function(e,r,o){return new z(function(t){var n;(n=new p.XMLHttpRequest).onreadystatechange=function(){4===n.readyState&&t({status:n.status,blob:this.response})},n.open("GET",e,!0),n.withCredentials=o,d.each(r,function(t,e){n.setRequestHeader(e,t)}),n.responseType="blob",n.send()})},W=[{code:404,message:"Could not find Image Proxy"},{code:403,message:"Rejected request"},{code:0,message:"Incorrect Image Proxy URL"}],X=[{type:"key_missing",message:"The request did not include an api key."},{type:"key_not_found",message:"The provided api key could not be found."},{type:"domain_not_trusted",message:"The api key is not valid for the request origins."}],Q=function(t){var e,n=(e=t,"ImageProxy HTTP error: "+F(W,function(t){return e===t.code}).fold(f("Unknown ImageProxy error"),function(t){return t.message}));return z.reject(n)},Y=function(t){var e,n,r=function(t){var e;try{e=JSON.parse(t)}catch(n){}return e}(t),o=(e=["error","type"].reduce(function(t,e){return K(t)?t[e]:undefined},r),K(e)?e:null);return"ImageProxy Service error: "+(o?(n=o,F(X,function(t){return t.type===n}).fold(f("Unknown service error"),function(t){return t.message})):"Invalid JSON in service error message")},Z=function(t,e){return r=e,new z(function(n){var t=new p.FileReader;t.onload=function(t){var e=t.target;n(e.result)},t.readAsText(r)}).then(function(t){var e=Y(t);return z.reject(e)});var r},tt=function(t,e){var n,r,o,i={"Content-Type":"application/json;charset=UTF-8","tiny-api-key":e};return V((r=e,o=-1===(n=t).indexOf("?")?"?":"&",/[?&]apiKey=/.test(n)||!r?n:n+o+"apiKey="+encodeURIComponent(r)),i,!1).then(function(t){return t.status<200||300<=t.status?(e=t.status,n=t.blob,400===(r=e)||403===r||500===r?Z(0,n):Q(e)):z.resolve(t.blob);var e,n,r})};var et=function(t,e,n){return e?tt(t,e):function r(t,e){return V(t,{},e).then(function(t){return t.status<200||300<=t.status?Q(t.status):z.resolve(t.blob)})}(t,n)},nt=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:f(t)}},rt={fromHtml:function(t,e){var n=(e||p.document).createElement("div");if(n.innerHTML=t,!n.hasChildNodes()||1<n.childNodes.length)throw p.console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return nt(n.childNodes[0])},fromTag:function(t,e){var n=(e||p.document).createElement(t);return nt(n)},fromText:function(t,e){var n=(e||p.document).createTextNode(t);return nt(n)},fromDom:nt,fromPoint:function(t,e,n){var r=t.dom();return v.from(r.elementFromPoint(e,n)).map(nt)}},ot=("undefined"!=typeof p.window?p.window:Function("return this;")(),function(t,e){return n=function(t){return function(t,e){var n=t.dom();if(1!==n.nodeType)return!1;var r=n;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}(t,e)},F(t.dom().childNodes,function(t){return n(rt.fromDom(t))}).map(rt.fromDom);var n}),it=0,ut=function(t){return ot(rt.fromDom(t),"img")},at=function(t,e){return t.dom.is(e,"figure")},ct=function(n,t){var e=function(t){return e=t,n.dom.is(e,"img:not([data-mce-object],[data-mce-placeholder])")&&(lt(n,t)||dt(n,t)||n.settings.imagetools_proxy);var e};return at(n,t)?ut(t).map(function(t){return e(t.dom())?v.some(t.dom()):v.none()}):e(t)?v.some(t):v.none()},st=function(t,e){t.notificationManager.open({text:e,type:"error"})},ft=function(t){var e=t.selection.getNode();return at(t,e)?ut(e):v.some(rt.fromDom(e))},lt=function(t,e){var n=e.src;return 0===n.indexOf("data:")||0===n.indexOf("blob:")||new $(n).host===t.documentBaseURI.host},dt=function(t,e){return-1!==d.inArray(t.getParam("imagetools_cors_hosts",[],"string[]"),new $(e.src).host)},mt=function(t,e){var n,r,o,i,u=e.src;return dt(t,e)?et(e.src,null,(r=t,o=e,-1!==d.inArray(r.getParam("imagetools_credentials_hosts",[],"string[]"),new $(o.src).host))):lt(t,e)?j(e):(u=t.getParam("imagetools_proxy"),u+=(-1===u.indexOf("?")?"?":"&")+"url="+encodeURIComponent(e.src),n=(i=t).getParam("api_key",i.getParam("imagetools_api_key","","string"),"string"),et(u,n,!1))},ht=function(t,e){return n=t,v.from(n.getParam("imagetools_fetch_image",null,"function")).fold(function(){return mt(t,e)},function(t){return t(e)});var n},gt=function(t,e){var n;return(n=t.editorUpload.blobCache.getByUri(e.src))?z.resolve(n.blob()):ht(t,e)},pt=function(t){q.clearTimeout(t.get())},vt=function(a,c,s,f,l,d){return c.toBlob().then(function(t){var e,n,r,o,i,u;return r=a.editorUpload.blobCache,e=l.src,a.getParam("images_reuse_filename",!1,"boolean")&&(n=(o=r.getByUri(e))?(e=o.uri(),o.name()):(i=a,(u=e.match(/\/([^\/\?]+)?\.(?:jpeg|jpg|png|gif)(?:\?|$)/i))?i.dom.encode(u[1]):null)),o=r.create({id:"imagetools"+it++,blob:t,base64:c.toBase64(),uri:e,name:n}),r.add(o),a.undoManager.transact(function(){a.$(l).on("load",function r(){var t,e,n;a.$(l).off("load",r),a.nodeChanged(),s?a.editorUpload.uploadImagesAuto():(pt(f),t=a,e=f,n=q.setEditorTimeout(t,function(){t.editorUpload.uploadImagesAuto()},t.getParam("images_upload_timeout",3e4,"number")),e.set(n))}),d&&a.$(l).attr({width:d.w,height:d.h}),a.$(l).attr({src:o.blobUri()}).removeAttr("data-mce-src")}),o})},yt=function(n,r,t,o){return function(){return ft(n).fold(function(){st(n,"Could not find selected image")},function(e){return n._scanForImages().then(function(){return gt(n,e.dom())}).then(H).then(t).then(function(t){return vt(n,t,!1,r,e.dom(),o)},function(t){st(n,t)})})}},wt=function(e,n,r){return function(){var t=ft(e).fold(function(){return null},function(t){var e=G(t.dom());return e?{w:e.h,h:e.w}:null});return yt(e,n,function(t){return N(t,r)},t)()}},bt=function(t,e,n){return function(){return yt(t,e,function(t){return D(t,n)})()}},It=function(e,n,r,i,u){return C(u).then(function(t){var e=J(t);return i.w===e.w&&i.h===e.h||G(r)&&function o(t,e){var n,r;e&&(n=t.style.width,r=t.style.height,(n||r)&&(t.style.width=e.w+"px",t.style.height=e.h+"px",t.removeAttribute("data-mce-style")),n=t.width,r=t.height,(n||r)&&(t.setAttribute("width",e.w),t.setAttribute("height",e.h)))}(r,e),p.URL.revokeObjectURL(t.src),u}).then(H).then(function(t){return vt(e,t,!0,n,r)},function(){})},Tt=function(i,u){return function(){var r=ft(i),o=r.map(function(t){return J(t.dom())});ft(i).each(function(e){ct(i,e.dom()).each(function(t){gt(i,e.dom()).then(function(t){var e,n={blob:e=t,url:p.URL.createObjectURL(e)};i.windowManager.open({title:"Edit Image",size:"large",body:{type:"panel",items:[{type:"imagetools",name:"imagetools",label:"Edit Image",currentState:n}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0,disabled:!0}],onSubmit:function(t){var n=t.getData().imagetools.blob;r.each(function(e){o.each(function(t){It(i,u,e.dom(),t,n)})}),t.close()},onCancel:function(){},onAction:function(t,e){switch(e.name){case"save-state":e.value?t.enable("save"):t.disable("save");break;case"disable":t.disable("save"),t.disable("cancel");break;case"enable":t.enable("cancel")}}})})})})}};!function _t(){r.add("imagetools",function(t){var n,e,r,o,i,u,a,c,s=l(0),f=l(null);n=t,e=s,d.each({mceImageRotateLeft:wt(n,e,-90),mceImageRotateRight:wt(n,e,90),mceImageFlipVertical:bt(n,e,"v"),mceImageFlipHorizontal:bt(n,e,"h"),mceEditImage:Tt(n,e)},function(t,e){n.addCommand(e,t)}),o=function(t){return function(){return r.execCommand(t)}},(r=t).ui.registry.addButton("rotateleft",{tooltip:"Rotate counterclockwise",icon:"rotate-left",onAction:o("mceImageRotateLeft")}),r.ui.registry.addButton("rotateright",{tooltip:"Rotate clockwise",icon:"rotate-right",onAction:o("mceImageRotateRight")}),r.ui.registry.addButton("flipv",{tooltip:"Flip vertically",icon:"flip-vertically",onAction:o("mceImageFlipVertical")}),r.ui.registry.addButton("fliph",{tooltip:"Flip horizontally",icon:"flip-horizontally",onAction:o("mceImageFlipHorizontal")}),r.ui.registry.addButton("editimage",{tooltip:"Edit image",icon:"edit-image",onAction:o("mceEditImage"),onSetup:function(n){var t=function(){ft(r).each(function(t){var e=ct(r,t.dom()).isNone();n.setDisabled(e)})};return r.on("NodeChange",t),function(){r.off("NodeChange",t)}}}),r.ui.registry.addButton("imageoptions",{tooltip:"Image options",icon:"image-options",onAction:o("mceImage")}),r.ui.registry.addContextMenu("imagetools",{update:function(t){return ct(r,t).fold(function(){return[]},function(t){return[{text:"Edit image",icon:"edit-image",onAction:o("mceEditImage")}]})}}),(i=t).ui.registry.addContextToolbar("imagetools",{items:i.getParam("imagetools_toolbar","rotateleft rotateright flipv fliph editimage imageoptions"),predicate:function(t){return ct(i,t).isSome()},position:"node",scope:"node"}),a=s,c=f,(u=t).on("NodeChange",function(t){var e=c.get();e&&e.src!==t.element.src&&(pt(a),u.editorUpload.uploadImagesAuto(),c.set(null)),ct(u,t.element).each(c.set)})})}()}(window);