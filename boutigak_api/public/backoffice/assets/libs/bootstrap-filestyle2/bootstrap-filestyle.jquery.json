{"name": "bootstrap-filestyle", "title": "Bootstrap styling input file", "version": "v2.1.0", "description": "jQuery FileStyle for Bootstrap - Customization of input html file for Bootstrap 4", "keywords": ["j<PERSON>y", "file", "upload", "boostrap", "multiple", "input", "style"], "homepage": "http://markusslima.github.io/bootstrap-filestyle/", "demo": "http://markusslima.github.io/bootstrap-filestyle/", "bugs": "https://github.com/markusslima/bootstrap-filestyle/issues", "author": {"name": "<PERSON>", "url": "https://github.com/markusslima"}, "maintainers": [{"name": "<PERSON>", "url": "https://github.com/markusslima"}], "repository": {"type": "git", "url": "https://github.com/markusslima/bootstrap-filestyle"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "devDependencies": {"jquery": ">=3"}}