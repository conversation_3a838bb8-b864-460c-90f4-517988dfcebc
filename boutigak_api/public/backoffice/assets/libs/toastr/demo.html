<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>toastr examples</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="//netdna.bootstrapcdn.com/twitter-bootstrap/2.3.1/css/bootstrap-combined.min.css" rel="stylesheet">
    <link href="build/toastr.css" rel="stylesheet" type="text/css" />
    <style>
        .row {
            margin-left: 0;
        }
    </style>
</head>

<body class="container">
<section class="row">
    <h1>toastr</h1>

    <div class="well row">
        <div class="row">
            <div class="span4">
                <div class="control-group">
                    <div class="controls">
                        <label class="control-label" for="title">Title</label>
                        <input id="title" type="text" class="input-large" placeholder="Enter a title ..." />
                        <label class="control-label" for="message">Message</label>
                        <textarea class="input-large" id="message" rows="3" placeholder="Enter a message ..."></textarea>
                    </div>
                </div>
                <div class="control-group">
                    <div class="controls">
                        <label class="checkbox" for="closeButton">
                            <input id="closeButton" type="checkbox" value="checked" class="input-mini" />Close Button
                        </label>
                    </div>
                    <div class="controls">
                        <label class="checkbox" for="addBehaviorOnToastClick">
                            <input id="addBehaviorOnToastClick" type="checkbox" value="checked" class="input-mini" />Add behavior on toast click
                        </label>
                    </div>
                    <div class="controls">
                        <label class="checkbox" for="addBehaviorOnToastCloseClick">
                            <input disabled id="addBehaviorOnToastCloseClick" type="checkbox" value="checked" class="input-mini" />Add behavior on toast close button click
                        </label>
                    </div>
                    <div class="controls">
                        <label class="checkbox" for="debugInfo">
                            <input id="debugInfo" type="checkbox" value="checked" class="input-mini" />Debug
                        </label>
                    </div>
                    <div class="controls">
                        <label class="checkbox" for="progressBar">
                            <input id="progressBar" type="checkbox" value="checked" class="input-mini" />Progress Bar
                        </label>
                    </div>
                    <div class="controls">
                        <label class="checkbox" for="rtl">
                            <input id="rtl" type="checkbox" value="checked" class="input-mini" />Right-To-Left
                        </label>
                    </div>
                    <div class="controls">
                        <label class="checkbox" for="preventDuplicates">
                            <input id="preventDuplicates" type="checkbox" value="checked" class="input-mini" />Prevent Duplicates
                        </label>
                    </div>
                    <div class="controls">
                        <label class="checkbox" for="addClear">
                            <input id="addClear" type="checkbox" value="checked" class="input-mini" />Add button to force clearing a toast, ignoring focus
                        </label>
                    </div>
                    <div class="controls">
                        <label class="checkbox" for="newestOnTop">
                            <input id="newestOnTop" type="checkbox" value="checked" class="input-mini" />Newest on top
                        </label>
                    </div>
                </div>
            </div>

            <div class="span2">
                <div class="control-group" id="toastTypeGroup">
                    <div class="controls">
                        <label>Toast Type</label>
                        <label class="radio">
                            <input type="radio" name="toasts" value="success" checked />Success
                        </label>
                        <label class="radio">
                            <input type="radio" name="toasts" value="info" />Info
                        </label>
                        <label class="radio">
                            <input type="radio" name="toasts" value="warning" />Warning
                        </label>
                        <label class="radio">
                            <input type="radio" name="toasts" value="error" />Error
                        </label>
                    </div>
                </div>
                <div class="control-group" id="positionGroup">
                    <div class="controls">
                        <label>Position</label>
                        <label class="radio">
                            <input type="radio" name="positions" value="toast-top-right" checked />Top Right
                        </label>
                        <label class="radio">
                            <input type="radio" name="positions" value="toast-bottom-right" />Bottom Right
                        </label>
                        <label class="radio">
                            <input type="radio" name="positions" value="toast-bottom-left" />Bottom Left
                        </label>
                        <label class="radio">
                            <input type="radio" name="positions" value="toast-top-left" />Top Left
                        </label>
                        <label class="radio">
                            <input type="radio" name="positions" value="toast-top-full-width" />Top Full Width
                        </label>
                        <label class="radio">
                            <input type="radio" name="positions" value="toast-bottom-full-width" />Bottom Full Width
                        </label>
                        <label class="radio">
                            <input type="radio" name="positions" value="toast-top-center" />Top Center
                        </label>
                        <label class="radio">
                            <input type="radio" name="positions" value="toast-bottom-center" />Bottom Center
                        </label>
                    </div>
                </div>
            </div>

            <div class="span2">
                <div class="control-group">
                    <div class="controls">
                        <label class="control-label" for="showEasing">Show Easing</label>
                        <input id="showEasing" type="text" placeholder="swing, linear" class="input-mini" value="swing" />

                        <label class="control-label" for="hideEasing">Hide Easing</label>
                        <input id="hideEasing" type="text" placeholder="swing, linear" class="input-mini" value="linear" />

                        <label class="control-label" for="showMethod">Show Method</label>
                        <input id="showMethod" type="text" placeholder="show, fadeIn, slideDown" class="input-mini" value="fadeIn" />

                        <label class="control-label" for="hideMethod">Hide Method</label>
                        <input id="hideMethod" type="text" placeholder="hide, fadeOut, slideUp" class="input-mini" value="fadeOut" />
                    </div>
                </div>
            </div>

            <div class="span3">
                <div class="control-group">
                    <div class="controls">
                        <label class="control-label" for="showDuration">Show Duration</label>
                        <input id="showDuration" type="number" placeholder="ms" class="input-mini" value="300"     />

                        <label class="control-label" for="hideDuration">Hide Duration</label>
                        <input id="hideDuration" type="number" placeholder="ms" class="input-mini" value="1000" />

                        <label class="control-label" for="timeOut">Time out</label>
                        <input id="timeOut" type="number" placeholder="ms" class="input-mini" value="5000" />

                        <label class="control-label" for="extendedTimeOut">Extended time out</label>
                        <input id="extendedTimeOut" type="number" placeholder="ms" class="input-mini" value="1000" />
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <button type="button" class="btn btn-primary" id="showtoast">Show Toast</button>
            <button type="button" class="btn btn-danger" id="cleartoasts">Clear Toasts</button>
            <button type="button" class="btn btn-danger" id="clearlasttoast">Clear Last Toast</button>
        </div>

        <div class="row" style='margin-top: 25px;'>
            <pre id='toastrOptions'></pre>
        </div>
    </div>
</section>

<footer class="row">
    <h2>Links</h2>

    <ul>
        <li><a href="http://nuget.org/packages/toastr">NuGet</a></li>
        <li><a href="https://github.com/CodeSeven/toastr">GitHub</a></li>
    </ul>
</footer>

<script src="//ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
<script src="toastr.js"></script>

<script type="text/javascript">
    $(function () {
        var i = -1;
        var toastCount = 0;
        var $toastlast;

        var getMessage = function () {
            var msgs = ['My name is Inigo Montoya. You killed my father. Prepare to die!',
                '<div><input class="input-small" value="textbox"/>&nbsp;<a href="http://johnpapa.net" target="_blank">This is a hyperlink</a></div><div><button type="button" id="okBtn" class="btn btn-primary">Close me</button><button type="button" id="surpriseBtn" class="btn" style="margin: 0 8px 0 8px">Surprise me</button></div>',
                'Are you the six fingered man?',
                'Inconceivable!',
                'I do not think that means what you think it means.',
                'Have fun storming the castle!'
            ];
            i++;
            if (i === msgs.length) {
                i = 0;
            }

            return msgs[i];
        };

        var getMessageWithClearButton = function (msg) {
            msg = msg ? msg : 'Clear itself?';
            msg += '<br /><br /><button type="button" class="btn clear">Yes</button>';
            return msg;
        };

        $('#closeButton').click(function() {
            if($(this).is(':checked')) {
                $('#addBehaviorOnToastCloseClick').prop('disabled', false);
            } else {
                $('#addBehaviorOnToastCloseClick').prop('disabled', true);
                $('#addBehaviorOnToastCloseClick').prop('checked', false);
            }
        });

        $('#showtoast').click(function () {
            var shortCutFunction = $("#toastTypeGroup input:radio:checked").val();
            var msg = $('#message').val();
            var title = $('#title').val() || '';
            var $showDuration = $('#showDuration');
            var $hideDuration = $('#hideDuration');
            var $timeOut = $('#timeOut');
            var $extendedTimeOut = $('#extendedTimeOut');
            var $showEasing = $('#showEasing');
            var $hideEasing = $('#hideEasing');
            var $showMethod = $('#showMethod');
            var $hideMethod = $('#hideMethod');
            var toastIndex = toastCount++;
            var addClear = $('#addClear').prop('checked');

            toastr.options = {
                closeButton: $('#closeButton').prop('checked'),
                debug: $('#debugInfo').prop('checked'),
                newestOnTop: $('#newestOnTop').prop('checked'),
                progressBar: $('#progressBar').prop('checked'),
                rtl: $('#rtl').prop('checked'),
                positionClass: $('#positionGroup input:radio:checked').val() || 'toast-top-right',
                preventDuplicates: $('#preventDuplicates').prop('checked'),
                onclick: null
            };

            if ($('#addBehaviorOnToastClick').prop('checked')) {
                toastr.options.onclick = function () {
                    alert('You can perform some custom action after a toast goes away');
                };
            }

            if ($('#addBehaviorOnToastCloseClick').prop('checked')) {
                toastr.options.onCloseClick = function () {
                    alert('You can perform some custom action when the close button is clicked');
                };
            }

            if ($showDuration.val().length) {
                toastr.options.showDuration = parseInt($showDuration.val());
            }

            if ($hideDuration.val().length) {
                toastr.options.hideDuration = parseInt($hideDuration.val());
            }

            if ($timeOut.val().length) {
                toastr.options.timeOut = addClear ? 0 : parseInt($timeOut.val());
            }

            if ($extendedTimeOut.val().length) {
                toastr.options.extendedTimeOut = addClear ? 0 : parseInt($extendedTimeOut.val());
            }

            if ($showEasing.val().length) {
                toastr.options.showEasing = $showEasing.val();
            }

            if ($hideEasing.val().length) {
                toastr.options.hideEasing = $hideEasing.val();
            }

            if ($showMethod.val().length) {
                toastr.options.showMethod = $showMethod.val();
            }

            if ($hideMethod.val().length) {
                toastr.options.hideMethod = $hideMethod.val();
            }

            if (addClear) {
                msg = getMessageWithClearButton(msg);
                toastr.options.tapToDismiss = false;
            }
            if (!msg) {
                msg = getMessage();
            }

            $('#toastrOptions').text('Command: toastr["'
                    + shortCutFunction
                    + '"]("'
                    + msg
                    + (title ? '", "' + title : '')
                    + '")\n\ntoastr.options = '
                    + JSON.stringify(toastr.options, null, 2)
            );

            var $toast = toastr[shortCutFunction](msg, title); // Wire up an event handler to a button in the toast, if it exists
            $toastlast = $toast;

            if(typeof $toast === 'undefined'){
                return;
            }

            if ($toast.find('#okBtn').length) {
                $toast.delegate('#okBtn', 'click', function () {
                    alert('you clicked me. i was toast #' + toastIndex + '. goodbye!');
                    $toast.remove();
                });
            }
            if ($toast.find('#surpriseBtn').length) {
                $toast.delegate('#surpriseBtn', 'click', function () {
                    alert('Surprise! you clicked me. i was toast #' + toastIndex + '. You could perform an action here.');
                });
            }
            if ($toast.find('.clear').length) {
                $toast.delegate('.clear', 'click', function () {
                    toastr.clear($toast, { force: true });
                });
            }
        });

        function getLastToast(){
            return $toastlast;
        }
        $('#clearlasttoast').click(function () {
            toastr.clear(getLastToast());
        });
        $('#cleartoasts').click(function () {
            toastr.clear();
        });
    })
</script>
</body>
</html>
