<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
    <metadata>
        <id>toastr</id>
        <version>1.3.1</version>
        <title>toastr</title>
        <authors><PERSON>,<PERSON></authors>
        <owners><PERSON>,<PERSON></owners>
        <licenseUrl>http://www.opensource.org/licenses/mit-license.php</licenseUrl>
        <projectUrl>https://github.com/CodeSeven/toastr</projectUrl>
        <iconUrl>http://johnpapa.net/wp-content/images/toastr-icon.png</iconUrl>
        <requireLicenseAcceptance>false</requireLicenseAcceptance>
        <description>3 Easy Steps:

(1) Link to toastr.css  
(2) Link to toastr.js 
(3) Use toastr to display a toast for info, success, warning or error

// Display an info toast with no title
toastr.info('Are you the 6 fingered man?')

*** For other API calls, see the demo</description>
        <summary>toastr is a Javascript library for Gnome / Growl type non-blocking notifications. jQuery is required. The goal is to create a simple core library that can be customized and extended.</summary>
        <releaseNotes>v.1.3.1
Added new API for toastr.subscribe so you can be alerted when toasts appear and disappear.

toastr.subscribe(function (args) {
    // args contains the toast options and data
}

Added support for glimpse.toastr.js plugin. https://github.com/johnpapa/glimpse.toastr

Added toastId, state (visible/hidden),  startTime and endTime to the toastr info response (available in the coneol, glimpse.toastr plugin, or via the subscribe callback).

Fixed bug with stacking toasts when clear all toasts occurs first.

v1.2.2

Added onFadeIn and onFadeOut callbacks. 
  toastr.options.onFadeIn = function() { console.log('hello'); }
Added option &lt;code&gt;newestOnTop&lt;/code&gt; to show toasts in oldest or newest first order.
  toastr.options.newestOnTop = false;
Fixed margins on full width toasts
Added LESS file.
Added min file for JS and CSS
Added missing vendor prefixes in CSS.
Various minor bug fixes.
Added unit tests for new features.</releaseNotes>
        <copyright>Copyright © 2012 Hans Fjällemark &amp; John Papa.</copyright>
        <language>en-US</language>
        <tags>toastr, toast, notification, dialog, jquery</tags>
        <dependencies>
            <dependency id="jQuery" version="1.6.3" />
        </dependencies>
    </metadata>
    <files>
        <file src="content\content\toastr.css" target="content\content\toastr.css" />
        <file src="content\content\toastr.less" target="content\content\toastr.less" />
        <file src="content\content\toastr.min.css" target="content\content\toastr.min.css" />
        <file src="content\scripts\toastr.js" target="content\scripts\toastr.js" />
        <file src="content\scripts\toastr.min.js" target="content\scripts\toastr.min.js" />
    </files>
</package>