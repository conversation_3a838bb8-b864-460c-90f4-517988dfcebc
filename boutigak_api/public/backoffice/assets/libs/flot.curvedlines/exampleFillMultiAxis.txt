### html ###

<h4>CurvedLines with multi axis and fill</h4>
<div id="flotContainer" class="chart-style"></div>

### css ###

.chart-style {
    width: 500px;
    height: 300px;
}

### javascript ###

//data
var d1 = [[20,20], [42,60], [54, 20], [80,80]];
var d2 = [[20,700], [80,300]];

//flot options
var options = {
                 series: {
                     curvedLines: {active: true}
                 },
                 yaxes: [{ min:10, max: 90}, {position: 'right'}]
              };

//plotting
$.plot($("#flotContainer"),[
    {
        data: d1,
        lines: { show: true, fill: true, fillColor: "#C3C3C3", lineWidth: 3},
        //curve the line  (old pre 1.0.0 plotting function)
        curvedLines: {
                        apply: true,
                        legacyOverride: true // <- use legacy plotting function
                     }
    }, { 
        data: d1,
        points: { show: true }
    }, {
        data: d2,
        yaxis: 2,
        lines: { show: true, lineWidth: 3},
    }, {
        data: d2,
        yaxis: 2,
        points: { show: true }
    }
], options);