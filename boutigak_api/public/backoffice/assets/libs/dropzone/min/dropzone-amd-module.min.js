!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):e(jQuery)}(function(e){var t={exports:{}};function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){return(i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function r(i){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=a(i);if(r){var n=a(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return s(this,e)}}function s(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?l(e):t}function l(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function w(e,t){var n;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);function i(){}var r=0;return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function d(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}var p=function(){function e(){c(this,e)}return d(e,[{key:"on",value:function(e,t){return this._callbacks=this._callbacks||{},this._callbacks[e]||(this._callbacks[e]=[]),this._callbacks[e].push(t),this}},{key:"emit",value:function(e){this._callbacks=this._callbacks||{};var t=this._callbacks[e];if(t){for(var n=arguments.length,i=new Array(1<n?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];var a,o=w(t);try{for(o.s();!(a=o.n()).done;){a.value.apply(this,i)}}catch(e){o.e(e)}finally{o.f()}}return this}},{key:"off",value:function(e,t){if(!this._callbacks||0===arguments.length)return this._callbacks={},this;var n=this._callbacks[e];if(!n)return this;if(1===arguments.length)return delete this._callbacks[e],this;for(var i=0;i<n.length;i++){if(n[i]===t){n.splice(i,1);break}}return this}}]),e}(),h=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&i(e,t)}(F,p);var o=r(F);function F(e,t){var n,i,r;if(c(this,F),(n=o.call(this)).element=e,n.version=F.version,n.defaultOptions.previewTemplate=n.defaultOptions.previewTemplate.replace(/\n*/g,""),n.clickableElements=[],n.listeners=[],n.files=[],"string"==typeof n.element&&(n.element=document.querySelector(n.element)),!n.element||null==n.element.nodeType)throw new Error("Invalid dropzone element.");if(n.element.dropzone)throw new Error("Dropzone already attached.");F.instances.push(l(n)),n.element.dropzone=l(n);var a=null!=(r=F.optionsForElement(n.element))?r:{};if(n.options=F.extend({},n.defaultOptions,a,null!=t?t:{}),n.options.forceFallback||!F.isBrowserSupported())return s(n,n.options.fallback.call(l(n)));if(null==n.options.url&&(n.options.url=n.element.getAttribute("action")),!n.options.url)throw new Error("No URL provided.");if(n.options.acceptedFiles&&n.options.acceptedMimeTypes)throw new Error("You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.");if(n.options.uploadMultiple&&n.options.chunking)throw new Error("You cannot set both: uploadMultiple and chunking.");return n.options.acceptedMimeTypes&&(n.options.acceptedFiles=n.options.acceptedMimeTypes,delete n.options.acceptedMimeTypes),null!=n.options.renameFilename&&(n.options.renameFile=function(e){return n.options.renameFilename.call(l(n),e.name,e)}),"string"==typeof n.options.method&&(n.options.method=n.options.method.toUpperCase()),(i=n.getExistingFallback())&&i.parentNode&&i.parentNode.removeChild(i),!1!==n.options.previewsContainer&&(n.options.previewsContainer?n.previewsContainer=F.getElement(n.options.previewsContainer,"previewsContainer"):n.previewsContainer=n.element),n.options.clickable&&(!0===n.options.clickable?n.clickableElements=[n.element]:n.clickableElements=F.getElements(n.options.clickable,"clickable")),n.init(),n}return d(F,null,[{key:"initClass",value:function(){this.prototype.Emitter=p,this.prototype.events=["drop","dragstart","dragend","dragenter","dragover","dragleave","addedfile","addedfiles","removedfile","thumbnail","error","errormultiple","processing","processingmultiple","uploadprogress","totaluploadprogress","sending","sendingmultiple","success","successmultiple","canceled","canceledmultiple","complete","completemultiple","reset","maxfilesexceeded","maxfilesreached","queuecomplete"],this.prototype.defaultOptions={url:null,method:"post",withCredentials:!1,timeout:3e4,parallelUploads:2,uploadMultiple:!1,chunking:!1,forceChunking:!1,chunkSize:2e6,parallelChunkUploads:!1,retryChunks:!1,retryChunksLimit:3,maxFilesize:256,paramName:"file",createImageThumbnails:!0,maxThumbnailFilesize:10,thumbnailWidth:120,thumbnailHeight:120,thumbnailMethod:"crop",resizeWidth:null,resizeHeight:null,resizeMimeType:null,resizeQuality:.8,resizeMethod:"contain",filesizeBase:1e3,maxFiles:null,headers:null,clickable:!0,ignoreHiddenFiles:!0,acceptedFiles:null,acceptedMimeTypes:null,autoProcessQueue:!0,autoQueue:!0,addRemoveLinks:!1,previewsContainer:null,hiddenInputContainer:"body",capture:null,renameFilename:null,renameFile:null,forceFallback:!1,dictDefaultMessage:"Drop files here to upload",dictFallbackMessage:"Your browser does not support drag'n'drop file uploads.",dictFallbackText:"Please use the fallback form below to upload your files like in the olden days.",dictFileTooBig:"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.",dictInvalidFileType:"You can't upload files of this type.",dictResponseError:"Server responded with {{statusCode}} code.",dictCancelUpload:"Cancel upload",dictUploadCanceled:"Upload canceled.",dictCancelUploadConfirmation:"Are you sure you want to cancel this upload?",dictRemoveFile:"Remove file",dictRemoveFileConfirmation:null,dictMaxFilesExceeded:"You can not upload any more files.",dictFileSizeUnits:{tb:"TB",gb:"GB",mb:"MB",kb:"KB",b:"b"},init:function(){},params:function(e,t,n){if(n)return{dzuuid:n.file.upload.uuid,dzchunkindex:n.index,dztotalfilesize:n.file.size,dzchunksize:this.options.chunkSize,dztotalchunkcount:n.file.upload.totalChunkCount,dzchunkbyteoffset:n.index*this.options.chunkSize}},accept:function(e,t){return t()},chunksUploaded:function(e,t){t()},fallback:function(){var e;this.element.className="".concat(this.element.className," dz-browser-not-supported");var t,n=w(this.element.getElementsByTagName("div"));try{for(n.s();!(t=n.n()).done;){var i=t.value;if(/(^| )dz-message($| )/.test(i.className)){(e=i).className="dz-message";break}}}catch(e){n.e(e)}finally{n.f()}e||(e=F.createElement('<div class="dz-message"><span></span></div>'),this.element.appendChild(e));var r=e.getElementsByTagName("span")[0];return r&&(null!=r.textContent?r.textContent=this.options.dictFallbackMessage:null!=r.innerText&&(r.innerText=this.options.dictFallbackMessage)),this.element.appendChild(this.getFallbackForm())},resize:function(e,t,n,i){var r={srcX:0,srcY:0,srcWidth:e.width,srcHeight:e.height},a=e.width/e.height;null==t&&null==n?(t=r.srcWidth,n=r.srcHeight):null==t?t=n*a:null==n&&(n=t/a);var o=(t=Math.min(t,r.srcWidth))/(n=Math.min(n,r.srcHeight));if(r.srcWidth>t||r.srcHeight>n)if("crop"===i)o<a?(r.srcHeight=e.height,r.srcWidth=r.srcHeight*o):(r.srcWidth=e.width,r.srcHeight=r.srcWidth/o);else{if("contain"!==i)throw new Error("Unknown resizeMethod '".concat(i,"'"));o<a?n=t/a:t=n*a}return r.srcX=(e.width-r.srcWidth)/2,r.srcY=(e.height-r.srcHeight)/2,r.trgWidth=t,r.trgHeight=n,r},transformFile:function(e,t){return(this.options.resizeWidth||this.options.resizeHeight)&&e.type.match(/image.*/)?this.resizeImage(e,this.options.resizeWidth,this.options.resizeHeight,this.options.resizeMethod,t):t(e)},previewTemplate:'<div class="dz-preview dz-file-preview">\n  <div class="dz-image"><img data-dz-thumbnail /></div>\n  <div class="dz-details">\n    <div class="dz-size"><span data-dz-size></span></div>\n    <div class="dz-filename"><span data-dz-name></span></div>\n  </div>\n  <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>\n  <div class="dz-error-message"><span data-dz-errormessage></span></div>\n  <div class="dz-success-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Check</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z" stroke-opacity="0.198794158" stroke="#747474" fill-opacity="0.816519475" fill="#FFFFFF"></path>\n      </g>\n    </svg>\n  </div>\n  <div class="dz-error-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Error</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g stroke="#747474" stroke-opacity="0.198794158" fill="#FFFFFF" fill-opacity="0.816519475">\n          <path d="M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z"></path>\n        </g>\n      </g>\n    </svg>\n  </div>\n</div>',drop:function(){return this.element.classList.remove("dz-drag-hover")},dragstart:function(){},dragend:function(){return this.element.classList.remove("dz-drag-hover")},dragenter:function(){return this.element.classList.add("dz-drag-hover")},dragover:function(){return this.element.classList.add("dz-drag-hover")},dragleave:function(){return this.element.classList.remove("dz-drag-hover")},paste:function(){},reset:function(){return this.element.classList.remove("dz-started")},addedfile:function(t){var n=this;if(this.element===this.previewsContainer&&this.element.classList.add("dz-started"),this.previewsContainer){t.previewElement=F.createElement(this.options.previewTemplate.trim()),t.previewTemplate=t.previewElement,this.previewsContainer.appendChild(t.previewElement);var e,i=w(t.previewElement.querySelectorAll("[data-dz-name]"));try{for(i.s();!(e=i.n()).done;){var r=e.value;r.textContent=t.name}}catch(e){i.e(e)}finally{i.f()}var a,o=w(t.previewElement.querySelectorAll("[data-dz-size]"));try{for(o.s();!(a=o.n()).done;)(r=a.value).innerHTML=this.filesize(t.size)}catch(e){o.e(e)}finally{o.f()}this.options.addRemoveLinks&&(t._removeLink=F.createElement('<a class="dz-remove" href="javascript:undefined;" data-dz-remove>'.concat(this.options.dictRemoveFile,"</a>")),t.previewElement.appendChild(t._removeLink));function s(e){return e.preventDefault(),e.stopPropagation(),t.status===F.UPLOADING?F.confirm(n.options.dictCancelUploadConfirmation,function(){return n.removeFile(t)}):n.options.dictRemoveFileConfirmation?F.confirm(n.options.dictRemoveFileConfirmation,function(){return n.removeFile(t)}):n.removeFile(t)}var l,u=w(t.previewElement.querySelectorAll("[data-dz-remove]"));try{for(u.s();!(l=u.n()).done;){l.value.addEventListener("click",s)}}catch(e){u.e(e)}finally{u.f()}}},removedfile:function(e){return null!=e.previewElement&&null!=e.previewElement.parentNode&&e.previewElement.parentNode.removeChild(e.previewElement),this._updateMaxFilesReachedClass()},thumbnail:function(e,t){if(e.previewElement){e.previewElement.classList.remove("dz-file-preview");var n,i=w(e.previewElement.querySelectorAll("[data-dz-thumbnail]"));try{for(i.s();!(n=i.n()).done;){var r=n.value;r.alt=e.name,r.src=t}}catch(e){i.e(e)}finally{i.f()}return setTimeout(function(){return e.previewElement.classList.add("dz-image-preview")},1)}},error:function(e,t){if(e.previewElement){e.previewElement.classList.add("dz-error"),"string"!=typeof t&&t.error&&(t=t.error);var n,i=w(e.previewElement.querySelectorAll("[data-dz-errormessage]"));try{for(i.s();!(n=i.n()).done;){n.value.textContent=t}}catch(e){i.e(e)}finally{i.f()}}},errormultiple:function(){},processing:function(e){if(e.previewElement&&(e.previewElement.classList.add("dz-processing"),e._removeLink))return e._removeLink.innerHTML=this.options.dictCancelUpload},processingmultiple:function(){},uploadprogress:function(e,t){if(e.previewElement){var n,i=w(e.previewElement.querySelectorAll("[data-dz-uploadprogress]"));try{for(i.s();!(n=i.n()).done;){var r=n.value;"PROGRESS"===r.nodeName?r.value=t:r.style.width="".concat(t,"%")}}catch(e){i.e(e)}finally{i.f()}}},totaluploadprogress:function(){},sending:function(){},sendingmultiple:function(){},success:function(e){if(e.previewElement)return e.previewElement.classList.add("dz-success")},successmultiple:function(){},canceled:function(e){return this.emit("error",e,this.options.dictUploadCanceled)},canceledmultiple:function(){},complete:function(e){if(e._removeLink&&(e._removeLink.innerHTML=this.options.dictRemoveFile),e.previewElement)return e.previewElement.classList.add("dz-complete")},completemultiple:function(){},maxfilesexceeded:function(){},maxfilesreached:function(){},queuecomplete:function(){},addedfiles:function(){}},this.prototype._thumbnailQueue=[],this.prototype._processingThumbnail=!1}},{key:"extend",value:function(e){for(var t=arguments.length,n=new Array(1<t?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];for(var r=0,a=n;r<a.length;r++){var o=a[r];for(var s in o){var l=o[s];e[s]=l}}return e}}]),d(F,[{key:"getAcceptedFiles",value:function(){return this.files.filter(function(e){return e.accepted}).map(function(e){return e})}},{key:"getRejectedFiles",value:function(){return this.files.filter(function(e){return!e.accepted}).map(function(e){return e})}},{key:"getFilesWithStatus",value:function(t){return this.files.filter(function(e){return e.status===t}).map(function(e){return e})}},{key:"getQueuedFiles",value:function(){return this.getFilesWithStatus(F.QUEUED)}},{key:"getUploadingFiles",value:function(){return this.getFilesWithStatus(F.UPLOADING)}},{key:"getAddedFiles",value:function(){return this.getFilesWithStatus(F.ADDED)}},{key:"getActiveFiles",value:function(){return this.files.filter(function(e){return e.status===F.UPLOADING||e.status===F.QUEUED}).map(function(e){return e})}},{key:"init",value:function(){var a=this;if("form"===this.element.tagName&&this.element.setAttribute("enctype","multipart/form-data"),this.element.classList.contains("dropzone")&&!this.element.querySelector(".dz-message")&&this.element.appendChild(F.createElement('<div class="dz-default dz-message"><button class="dz-button" type="button">'.concat(this.options.dictDefaultMessage,"</button></div>"))),this.clickableElements.length){!function r(){return a.hiddenFileInput&&a.hiddenFileInput.parentNode.removeChild(a.hiddenFileInput),a.hiddenFileInput=document.createElement("input"),a.hiddenFileInput.setAttribute("type","file"),(null===a.options.maxFiles||1<a.options.maxFiles)&&a.hiddenFileInput.setAttribute("multiple","multiple"),a.hiddenFileInput.className="dz-hidden-input",null!==a.options.acceptedFiles&&a.hiddenFileInput.setAttribute("accept",a.options.acceptedFiles),null!==a.options.capture&&a.hiddenFileInput.setAttribute("capture",a.options.capture),a.hiddenFileInput.style.visibility="hidden",a.hiddenFileInput.style.position="absolute",a.hiddenFileInput.style.top="0",a.hiddenFileInput.style.left="0",a.hiddenFileInput.style.height="0",a.hiddenFileInput.style.width="0",F.getElement(a.options.hiddenInputContainer,"hiddenInputContainer").appendChild(a.hiddenFileInput),a.hiddenFileInput.addEventListener("change",function(){var e=a.hiddenFileInput.files;if(e.length){var t,n=w(e);try{for(n.s();!(t=n.n()).done;){var i=t.value;a.addFile(i)}}catch(e){n.e(e)}finally{n.f()}}return a.emit("addedfiles",e),r()})}()}this.URL=null!==window.URL?window.URL:window.webkitURL;var e,t=w(this.events);try{for(t.s();!(e=t.n()).done;){var n=e.value;this.on(n,this.options[n])}}catch(e){t.e(e)}finally{t.f()}this.on("uploadprogress",function(){return a.updateTotalUploadProgress()}),this.on("removedfile",function(){return a.updateTotalUploadProgress()}),this.on("canceled",function(e){return a.emit("complete",e)}),this.on("complete",function(e){if(0===a.getAddedFiles().length&&0===a.getUploadingFiles().length&&0===a.getQueuedFiles().length)return setTimeout(function(){return a.emit("queuecomplete")},0)});function i(e){return function(e){if(e.dataTransfer.types)for(var t=0;t<e.dataTransfer.types.length;t++)if("Files"===e.dataTransfer.types[t])return!0;return!1}(e)&&(e.stopPropagation(),e.preventDefault?e.preventDefault():e.returnValue=!1)}return this.listeners=[{element:this.element,events:{dragstart:function(e){return a.emit("dragstart",e)},dragenter:function(e){return i(e),a.emit("dragenter",e)},dragover:function(e){var t;try{t=e.dataTransfer.effectAllowed}catch(e){}return e.dataTransfer.dropEffect="move"===t||"linkMove"===t?"move":"copy",i(e),a.emit("dragover",e)},dragleave:function(e){return a.emit("dragleave",e)},drop:function(e){return i(e),a.drop(e)},dragend:function(e){return a.emit("dragend",e)}}}],this.clickableElements.forEach(function(t){return a.listeners.push({element:t,events:{click:function(e){return t===a.element&&e.target!==a.element&&!F.elementInside(e.target,a.element.querySelector(".dz-message"))||a.hiddenFileInput.click(),!0}}})}),this.enable(),this.options.init.call(this)}},{key:"destroy",value:function(){return this.disable(),this.removeAllFiles(!0),null!=this.hiddenFileInput&&this.hiddenFileInput.parentNode&&(this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput),this.hiddenFileInput=null),delete this.element.dropzone,F.instances.splice(F.instances.indexOf(this),1)}},{key:"updateTotalUploadProgress",value:function(){var e,t=0,n=0;if(this.getActiveFiles().length){var i,r=w(this.getActiveFiles());try{for(r.s();!(i=r.n()).done;){var a=i.value;t+=a.upload.bytesSent,n+=a.upload.total}}catch(e){r.e(e)}finally{r.f()}e=100*t/n}else e=100;return this.emit("totaluploadprogress",e,n,t)}},{key:"_getParamName",value:function(e){return"function"==typeof this.options.paramName?this.options.paramName(e):"".concat(this.options.paramName).concat(this.options.uploadMultiple?"[".concat(e,"]"):"")}},{key:"_renameFile",value:function(e){return"function"!=typeof this.options.renameFile?e.name:this.options.renameFile(e)}},{key:"getFallbackForm",value:function(){var e,t;if(e=this.getExistingFallback())return e;var n='<div class="dz-fallback">';this.options.dictFallbackText&&(n+="<p>".concat(this.options.dictFallbackText,"</p>")),n+='<input type="file" name="'.concat(this._getParamName(0),'" ').concat(this.options.uploadMultiple?'multiple="multiple"':void 0,' /><input type="submit" value="Upload!"></div>');var i=F.createElement(n);return"FORM"!==this.element.tagName?(t=F.createElement('<form action="'.concat(this.options.url,'" enctype="multipart/form-data" method="').concat(this.options.method,'"></form>'))).appendChild(i):(this.element.setAttribute("enctype","multipart/form-data"),this.element.setAttribute("method",this.options.method)),null!=t?t:i}},{key:"getExistingFallback",value:function(){function e(e){var t,n=w(e);try{for(n.s();!(t=n.n()).done;){var i=t.value;if(/(^| )fallback($| )/.test(i.className))return i}}catch(e){n.e(e)}finally{n.f()}}for(var t=0,n=["div","form"];t<n.length;t++){var i,r=n[t];if(i=e(this.element.getElementsByTagName(r)))return i}}},{key:"setupEventListeners",value:function(){return this.listeners.map(function(i){return function(){var e=[];for(var t in i.events){var n=i.events[t];e.push(i.element.addEventListener(t,n,!1))}return e}()})}},{key:"removeEventListeners",value:function(){return this.listeners.map(function(i){return function(){var e=[];for(var t in i.events){var n=i.events[t];e.push(i.element.removeEventListener(t,n,!1))}return e}()})}},{key:"disable",value:function(){var t=this;return this.clickableElements.forEach(function(e){return e.classList.remove("dz-clickable")}),this.removeEventListeners(),this.disabled=!0,this.files.map(function(e){return t.cancelUpload(e)})}},{key:"enable",value:function(){return delete this.disabled,this.clickableElements.forEach(function(e){return e.classList.add("dz-clickable")}),this.setupEventListeners()}},{key:"filesize",value:function(e){var t=0,n="b";if(0<e){for(var i=["tb","gb","mb","kb","b"],r=0;r<i.length;r++){var a=i[r];if(Math.pow(this.options.filesizeBase,4-r)/10<=e){t=e/Math.pow(this.options.filesizeBase,4-r),n=a;break}}t=Math.round(10*t)/10}return"<strong>".concat(t,"</strong> ").concat(this.options.dictFileSizeUnits[n])}},{key:"_updateMaxFilesReachedClass",value:function(){return null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(this.getAcceptedFiles().length===this.options.maxFiles&&this.emit("maxfilesreached",this.files),this.element.classList.add("dz-max-files-reached")):this.element.classList.remove("dz-max-files-reached")}},{key:"drop",value:function(e){if(e.dataTransfer){this.emit("drop",e);for(var t=[],n=0;n<e.dataTransfer.files.length;n++)t[n]=e.dataTransfer.files[n];if(t.length){var i=e.dataTransfer.items;i&&i.length&&null!=i[0].webkitGetAsEntry?this._addFilesFromItems(i):this.handleFiles(t)}this.emit("addedfiles",t)}}},{key:"paste",value:function(e){if(null!=(t=null!=e?e.clipboardData:void 0,n=function(e){return e.items},null!=t?n(t):void 0)){var t,n;this.emit("paste",e);var i=e.clipboardData.items;return i.length?this._addFilesFromItems(i):void 0}}},{key:"handleFiles",value:function(e){var t,n=w(e);try{for(n.s();!(t=n.n()).done;){var i=t.value;this.addFile(i)}}catch(e){n.e(e)}finally{n.f()}}},{key:"_addFilesFromItems",value:function(a){var o=this;return function(){var e,t=[],n=w(a);try{for(n.s();!(e=n.n()).done;){var i,r=e.value;null!=r.webkitGetAsEntry&&(i=r.webkitGetAsEntry())?i.isFile?t.push(o.addFile(r.getAsFile())):i.isDirectory?t.push(o._addFilesFromDirectory(i,i.name)):t.push(void 0):null!=r.getAsFile&&(null==r.kind||"file"===r.kind)?t.push(o.addFile(r.getAsFile())):t.push(void 0)}}catch(e){n.e(e)}finally{n.f()}return t}()}},{key:"_addFilesFromDirectory",value:function(e,a){function t(t){return e=console,n="log",i=function(e){return e.log(t)},null!=e&&"function"==typeof e[n]?i(e,n):void 0;var e,n,i}var o=this,n=e.createReader();return function r(){return n.readEntries(function(e){if(0<e.length){var t,n=w(e);try{for(n.s();!(t=n.n()).done;){var i=t.value;i.isFile?i.file(function(e){if(!o.options.ignoreHiddenFiles||"."!==e.name.substring(0,1))return e.fullPath="".concat(a,"/").concat(e.name),o.addFile(e)}):i.isDirectory&&o._addFilesFromDirectory(i,"".concat(a,"/").concat(i.name))}}catch(e){n.e(e)}finally{n.f()}r()}return null},t)}()}},{key:"accept",value:function(e,t){this.options.maxFilesize&&e.size>1024*this.options.maxFilesize*1024?t(this.options.dictFileTooBig.replace("{{filesize}}",Math.round(e.size/1024/10.24)/100).replace("{{maxFilesize}}",this.options.maxFilesize)):F.isValidFile(e,this.options.acceptedFiles)?null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(t(this.options.dictMaxFilesExceeded.replace("{{maxFiles}}",this.options.maxFiles)),this.emit("maxfilesexceeded",e)):this.options.accept.call(this,e,t):t(this.options.dictInvalidFileType)}},{key:"addFile",value:function(t){var n=this;t.upload={uuid:F.uuidv4(),progress:0,total:t.size,bytesSent:0,filename:this._renameFile(t)},this.files.push(t),t.status=F.ADDED,this.emit("addedfile",t),this._enqueueThumbnail(t),this.accept(t,function(e){e?(t.accepted=!1,n._errorProcessing([t],e)):(t.accepted=!0,n.options.autoQueue&&n.enqueueFile(t)),n._updateMaxFilesReachedClass()})}},{key:"enqueueFiles",value:function(e){var t,n=w(e);try{for(n.s();!(t=n.n()).done;){var i=t.value;this.enqueueFile(i)}}catch(e){n.e(e)}finally{n.f()}return null}},{key:"enqueueFile",value:function(e){var t=this;if(e.status!==F.ADDED||!0!==e.accepted)throw new Error("This file can't be queued because it has already been processed or was rejected.");if(e.status=F.QUEUED,this.options.autoProcessQueue)return setTimeout(function(){return t.processQueue()},0)}},{key:"_enqueueThumbnail",value:function(e){var t=this;if(this.options.createImageThumbnails&&e.type.match(/image.*/)&&e.size<=1024*this.options.maxThumbnailFilesize*1024)return this._thumbnailQueue.push(e),setTimeout(function(){return t._processThumbnailQueue()},0)}},{key:"_processThumbnailQueue",value:function(){var t=this;if(!this._processingThumbnail&&0!==this._thumbnailQueue.length){this._processingThumbnail=!0;var n=this._thumbnailQueue.shift();return this.createThumbnail(n,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.thumbnailMethod,!0,function(e){return t.emit("thumbnail",n,e),t._processingThumbnail=!1,t._processThumbnailQueue()})}}},{key:"removeFile",value:function(e){if(e.status===F.UPLOADING&&this.cancelUpload(e),this.files=f(this.files,e),this.emit("removedfile",e),0===this.files.length)return this.emit("reset")}},{key:"removeAllFiles",value:function(e){null==e&&(e=!1);var t,n=w(this.files.slice());try{for(n.s();!(t=n.n()).done;){var i=t.value;i.status===F.UPLOADING&&!e||this.removeFile(i)}}catch(e){n.e(e)}finally{n.f()}return null}},{key:"resizeImage",value:function(r,e,t,n,a){var o=this;return this.createThumbnail(r,e,t,n,!0,function(e,t){if(null==t)return a(r);var n=o.options.resizeMimeType;null==n&&(n=r.type);var i=t.toDataURL(n,o.options.resizeQuality);return"image/jpeg"!==n&&"image/jpg"!==n||(i=g.restore(r.dataURL,i)),a(F.dataURItoBlob(i))})}},{key:"createThumbnail",value:function(e,t,n,i,r,a){var o=this,s=new FileReader;s.onload=function(){e.dataURL=s.result,"image/svg+xml"!==e.type?o.createThumbnailFromUrl(e,t,n,i,r,a):null!=a&&a(s.result)},s.readAsDataURL(e)}},{key:"displayExistingFile",value:function(t,e,n,i,r){var a=this,o=!(4<arguments.length&&void 0!==r)||r;if(this.emit("addedfile",t),this.emit("complete",t),o){t.dataURL=e,this.createThumbnailFromUrl(t,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.resizeMethod,this.options.fixOrientation,function(e){a.emit("thumbnail",t,e),n&&n()},i)}else this.emit("thumbnail",t,e),n&&n()}},{key:"createThumbnailFromUrl",value:function(a,o,s,l,t,u,e){var c=this,d=document.createElement("img");return e&&(d.crossOrigin=e),t="from-image"!=getComputedStyle(document.body).imageOrientation&&t,d.onload=function(){var e=function(e){return e(1)};return"undefined"!=typeof EXIF&&null!==EXIF&&t&&(e=function(e){return EXIF.getData(d,function(){return e(EXIF.getTag(this,"Orientation"))})}),e(function(e){a.width=d.width,a.height=d.height;var t=c.options.resize.call(c,a,o,s,l),n=document.createElement("canvas"),i=n.getContext("2d");switch(n.width=t.trgWidth,n.height=t.trgHeight,4<e&&(n.width=t.trgHeight,n.height=t.trgWidth),e){case 2:i.translate(n.width,0),i.scale(-1,1);break;case 3:i.translate(n.width,n.height),i.rotate(Math.PI);break;case 4:i.translate(0,n.height),i.scale(1,-1);break;case 5:i.rotate(.5*Math.PI),i.scale(1,-1);break;case 6:i.rotate(.5*Math.PI),i.translate(0,-n.width);break;case 7:i.rotate(.5*Math.PI),i.translate(n.height,-n.width),i.scale(-1,1);break;case 8:i.rotate(-.5*Math.PI),i.translate(-n.height,0)}v(i,d,null!=t.srcX?t.srcX:0,null!=t.srcY?t.srcY:0,t.srcWidth,t.srcHeight,null!=t.trgX?t.trgX:0,null!=t.trgY?t.trgY:0,t.trgWidth,t.trgHeight);var r=n.toDataURL("image/png");if(null!=u)return u(r,n)})},null!=u&&(d.onerror=u),d.src=a.dataURL}},{key:"processQueue",value:function(){var e=this.options.parallelUploads,t=this.getUploadingFiles().length,n=t;if(!(e<=t)){var i=this.getQueuedFiles();if(0<i.length){if(this.options.uploadMultiple)return this.processFiles(i.slice(0,e-t));for(;n<e;){if(!i.length)return;this.processFile(i.shift()),n++}}}}},{key:"processFile",value:function(e){return this.processFiles([e])}},{key:"processFiles",value:function(e){var t,n=w(e);try{for(n.s();!(t=n.n()).done;){var i=t.value;i.processing=!0,i.status=F.UPLOADING,this.emit("processing",i)}}catch(e){n.e(e)}finally{n.f()}return this.options.uploadMultiple&&this.emit("processingmultiple",e),this.uploadFiles(e)}},{key:"_getFilesWithXhr",value:function(t){return this.files.filter(function(e){return e.xhr===t}).map(function(e){return e})}},{key:"cancelUpload",value:function(e){if(e.status===F.UPLOADING){var t,n=this._getFilesWithXhr(e.xhr),i=w(n);try{for(i.s();!(t=i.n()).done;){t.value.status=F.CANCELED}}catch(e){i.e(e)}finally{i.f()}void 0!==e.xhr&&e.xhr.abort();var r,a=w(n);try{for(a.s();!(r=a.n()).done;){var o=r.value;this.emit("canceled",o)}}catch(e){a.e(e)}finally{a.f()}this.options.uploadMultiple&&this.emit("canceledmultiple",n)}else e.status!==F.ADDED&&e.status!==F.QUEUED||(e.status=F.CANCELED,this.emit("canceled",e),this.options.uploadMultiple&&this.emit("canceledmultiple",[e]));if(this.options.autoProcessQueue)return this.processQueue()}},{key:"resolveOption",value:function(e){if("function"!=typeof e)return e;for(var t=arguments.length,n=new Array(1<t?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return e.apply(this,n)}},{key:"uploadFile",value:function(e){return this.uploadFiles([e])}},{key:"uploadFiles",value:function(l){var u=this;this._transformFiles(l,function(e){if(u.options.chunking){var t=e[0];l[0].upload.chunked=u.options.chunking&&(u.options.forceChunking||t.size>u.options.chunkSize),l[0].upload.totalChunkCount=Math.ceil(t.size/u.options.chunkSize)}if(l[0].upload.chunked){var r=l[0],a=e[0];r.upload.chunks=[];function i(){for(var e=0;void 0!==r.upload.chunks[e];)e++;if(!(e>=r.upload.totalChunkCount)){0;var t=e*u.options.chunkSize,n=Math.min(t+u.options.chunkSize,a.size),i={name:u._getParamName(0),data:a.webkitSlice?a.webkitSlice(t,n):a.slice(t,n),filename:r.upload.filename,chunkIndex:e};r.upload.chunks[e]={file:r,index:e,dataBlock:i,status:F.UPLOADING,progress:0,retries:0},u._uploadData(l,[i])}}if(r.upload.finishedChunkUpload=function(e){var t=!0;e.status=F.SUCCESS,e.dataBlock=null,e.xhr=null;for(var n=0;n<r.upload.totalChunkCount;n++){if(void 0===r.upload.chunks[n])return i();r.upload.chunks[n].status!==F.SUCCESS&&(t=!1)}t&&u.options.chunksUploaded(r,function(){u._finished(l,"",null)})},u.options.parallelChunkUploads)for(var n=0;n<r.upload.totalChunkCount;n++)i();else i()}else{for(var o=[],s=0;s<l.length;s++)o[s]={name:u._getParamName(s),data:e[s],filename:l[s].upload.filename};u._uploadData(l,o)}})}},{key:"_getChunk",value:function(e,t){for(var n=0;n<e.upload.totalChunkCount;n++)if(void 0!==e.upload.chunks[n]&&e.upload.chunks[n].xhr===t)return e.upload.chunks[n]}},{key:"_uploadData",value:function(t,e){var n,i=this,r=new XMLHttpRequest,a=w(t);try{for(a.s();!(n=a.n()).done;){n.value.xhr=r}}catch(e){a.e(e)}finally{a.f()}t[0].upload.chunked&&(t[0].upload.chunks[e[0].chunkIndex].xhr=r);var o=this.resolveOption(this.options.method,t),s=this.resolveOption(this.options.url,t);r.open(o,s,!0),r.timeout=this.resolveOption(this.options.timeout,t),r.withCredentials=!!this.options.withCredentials,r.onload=function(e){i._finishedUploading(t,r,e)},r.ontimeout=function(){i._handleUploadError(t,r,"Request timedout after ".concat(i.options.timeout/1e3," seconds"))},r.onerror=function(){i._handleUploadError(t,r)},(null!=r.upload?r.upload:r).onprogress=function(e){return i._updateFilesUploadProgress(t,r,e)};var l={Accept:"application/json","Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"};for(var u in this.options.headers&&F.extend(l,this.options.headers),l){var c=l[u];c&&r.setRequestHeader(u,c)}var d=new FormData;if(this.options.params){var p=this.options.params;for(var h in"function"==typeof p&&(p=p.call(this,t,r,t[0].upload.chunked?this._getChunk(t[0],r):null)),p){var f=p[h];if(Array.isArray(f))for(var m=0;m<f.length;m++)d.append(h,f[m]);else d.append(h,f)}}var v,g=w(t);try{for(g.s();!(v=g.n()).done;){var y=v.value;this.emit("sending",y,r,d)}}catch(e){g.e(e)}finally{g.f()}this.options.uploadMultiple&&this.emit("sendingmultiple",t,r,d),this._addFormElementData(d);for(var k=0;k<e.length;k++){var b=e[k];d.append(b.name,b.data,b.filename)}this.submitRequest(r,d,t)}},{key:"_transformFiles",value:function(n,i){function e(t){r.options.transformFile.call(r,n[t],function(e){a[t]=e,++o===n.length&&i(a)})}for(var r=this,a=[],o=0,t=0;t<n.length;t++)e(t)}},{key:"_addFormElementData",value:function(e){if("FORM"===this.element.tagName){var t,n=w(this.element.querySelectorAll("input, textarea, select, button"));try{for(n.s();!(t=n.n()).done;){var i=t.value,r=i.getAttribute("name"),a=i.getAttribute("type");if(a=a&&a.toLowerCase(),null!=r)if("SELECT"===i.tagName&&i.hasAttribute("multiple")){var o,s=w(i.options);try{for(s.s();!(o=s.n()).done;){var l=o.value;l.selected&&e.append(r,l.value)}}catch(e){s.e(e)}finally{s.f()}}else(!a||"checkbox"!==a&&"radio"!==a||i.checked)&&e.append(r,i.value)}}catch(e){n.e(e)}finally{n.f()}}}},{key:"_updateFilesUploadProgress",value:function(e,t,n){var i;if(void 0!==n){if(i=100*n.loaded/n.total,e[0].upload.chunked){var r=e[0],a=this._getChunk(r,t);a.progress=i,a.total=n.total,a.bytesSent=n.loaded;r.upload.progress=0,r.upload.total=0;for(var o=r.upload.bytesSent=0;o<r.upload.totalChunkCount;o++)void 0!==r.upload.chunks[o]&&void 0!==r.upload.chunks[o].progress&&(r.upload.progress+=r.upload.chunks[o].progress,r.upload.total+=r.upload.chunks[o].total,r.upload.bytesSent+=r.upload.chunks[o].bytesSent);r.upload.progress=r.upload.progress/r.upload.totalChunkCount}else{var s,l=w(e);try{for(l.s();!(s=l.n()).done;){var u=s.value;u.upload.progress=i,u.upload.total=n.total,u.upload.bytesSent=n.loaded}}catch(e){l.e(e)}finally{l.f()}}var c,d=w(e);try{for(d.s();!(c=d.n()).done;){var p=c.value;this.emit("uploadprogress",p,p.upload.progress,p.upload.bytesSent)}}catch(e){d.e(e)}finally{d.f()}}else{var h=!0;i=100;var f,m=w(e);try{for(m.s();!(f=m.n()).done;){var v=f.value;100===v.upload.progress&&v.upload.bytesSent===v.upload.total||(h=!1),v.upload.progress=i,v.upload.bytesSent=v.upload.total}}catch(e){m.e(e)}finally{m.f()}if(h)return;var g,y=w(e);try{for(y.s();!(g=y.n()).done;){var k=g.value;this.emit("uploadprogress",k,i,k.upload.bytesSent)}}catch(e){y.e(e)}finally{y.f()}}}},{key:"_finishedUploading",value:function(e,t,n){var i;if(e[0].status!==F.CANCELED&&4===t.readyState){if("arraybuffer"!==t.responseType&&"blob"!==t.responseType&&(i=t.responseText,t.getResponseHeader("content-type")&&~t.getResponseHeader("content-type").indexOf("application/json")))try{i=JSON.parse(i)}catch(e){n=e,i="Invalid JSON response from server."}this._updateFilesUploadProgress(e),200<=t.status&&t.status<300?e[0].upload.chunked?e[0].upload.finishedChunkUpload(this._getChunk(e[0],t)):this._finished(e,i,n):this._handleUploadError(e,t,i)}}},{key:"_handleUploadError",value:function(e,t,n){if(e[0].status!==F.CANCELED){if(e[0].upload.chunked&&this.options.retryChunks){var i=this._getChunk(e[0],t);if(i.retries++<this.options.retryChunksLimit)return void this._uploadData(e,[i.dataBlock]);console.warn("Retried this chunk too often. Giving up.")}this._errorProcessing(e,n||this.options.dictResponseError.replace("{{statusCode}}",t.status),t)}}},{key:"submitRequest",value:function(e,t){e.send(t)}},{key:"_finished",value:function(e,t,n){var i,r=w(e);try{for(r.s();!(i=r.n()).done;){var a=i.value;a.status=F.SUCCESS,this.emit("success",a,t,n),this.emit("complete",a)}}catch(e){r.e(e)}finally{r.f()}if(this.options.uploadMultiple&&(this.emit("successmultiple",e,t,n),this.emit("completemultiple",e)),this.options.autoProcessQueue)return this.processQueue()}},{key:"_errorProcessing",value:function(e,t,n){var i,r=w(e);try{for(r.s();!(i=r.n()).done;){var a=i.value;a.status=F.ERROR,this.emit("error",a,t,n),this.emit("complete",a)}}catch(e){r.e(e)}finally{r.f()}if(this.options.uploadMultiple&&(this.emit("errormultiple",e,t,n),this.emit("completemultiple",e)),this.options.autoProcessQueue)return this.processQueue()}}],[{key:"uuidv4",value:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}}]),F}();h.initClass(),h.version="5.7.2",h.options={},h.optionsForElement=function(e){return e.getAttribute("id")?h.options[m(e.getAttribute("id"))]:void 0},h.instances=[],h.forElement=function(e){if("string"==typeof e&&(e=document.querySelector(e)),null==(null!=e?e.dropzone:void 0))throw new Error("No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.");return e.dropzone},h.autoDiscover=!0,h.discover=function(){var a;if(document.querySelectorAll)a=document.querySelectorAll(".dropzone");else{a=[];function e(r){return function(){var e,t=[],n=w(r);try{for(n.s();!(e=n.n()).done;){var i=e.value;/(^| )dropzone($| )/.test(i.className)?t.push(a.push(i)):t.push(void 0)}}catch(e){n.e(e)}finally{n.f()}return t}()}e(document.getElementsByTagName("div")),e(document.getElementsByTagName("form"))}return function(){var e,t=[],n=w(a);try{for(n.s();!(e=n.n()).done;){var i=e.value;!1!==h.optionsForElement(i)?t.push(new h(i)):t.push(void 0)}}catch(e){n.e(e)}finally{n.f()}return t}()},h.blacklistedBrowsers=[/opera.*(Macintosh|Windows Phone).*version\/12/i],h.isBrowserSupported=function(){var e=!0;if(window.File&&window.FileReader&&window.FileList&&window.Blob&&window.FormData&&document.querySelector)if("classList"in document.createElement("a")){var t,n=w(h.blacklistedBrowsers);try{for(n.s();!(t=n.n()).done;){t.value.test(navigator.userAgent)&&(e=!1)}}catch(e){n.e(e)}finally{n.f()}}else e=!1;else e=!1;return e},h.dataURItoBlob=function(e){for(var t=atob(e.split(",")[1]),n=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(t.length),r=new Uint8Array(i),a=0,o=t.length,s=0<=o;s?a<=o:o<=a;s?a++:a--)r[a]=t.charCodeAt(a);return new Blob([i],{type:n})};var f=function(e,t){return e.filter(function(e){return e!==t}).map(function(e){return e})},m=function(e){return e.replace(/[\-_](\w)/g,function(e){return e.charAt(1).toUpperCase()})};h.createElement=function(e){var t=document.createElement("div");return t.innerHTML=e,t.childNodes[0]},h.elementInside=function(e,t){if(e===t)return!0;for(;e=e.parentNode;)if(e===t)return!0;return!1},h.getElement=function(e,t){var n;if("string"==typeof e?n=document.querySelector(e):null!=e.nodeType&&(n=e),null==n)throw new Error("Invalid `".concat(t,"` option provided. Please provide a CSS selector or a plain HTML element."));return n},h.getElements=function(e,t){var n,i;if(e instanceof Array){i=[];try{var r,a=w(e);try{for(a.s();!(r=a.n()).done;)n=r.value,i.push(this.getElement(n,t))}catch(e){a.e(e)}finally{a.f()}}catch(e){i=null}}else if("string"==typeof e){i=[];var o,s=w(document.querySelectorAll(e));try{for(s.s();!(o=s.n()).done;)n=o.value,i.push(n)}catch(e){s.e(e)}finally{s.f()}}else null!=e.nodeType&&(i=[e]);if(null==i||!i.length)throw new Error("Invalid `".concat(t,"` option provided. Please provide a CSS selector, a plain HTML element or a list of those."));return i},h.confirm=function(e,t,n){return window.confirm(e)?t():null!=n?n():void 0},h.isValidFile=function(e,t){if(!t)return!0;t=t.split(",");var n,i=e.type,r=i.replace(/\/.*$/,""),a=w(t);try{for(a.s();!(n=a.n()).done;){var o=n.value;if("."===(o=o.trim()).charAt(0)){if(-1!==e.name.toLowerCase().indexOf(o.toLowerCase(),e.name.length-o.length))return!0}else if(/\/\*$/.test(o)){if(r===o.replace(/\/.*$/,""))return!0}else if(i===o)return!0}}catch(e){a.e(e)}finally{a.f()}return!1},null!=e&&(e.fn.dropzone=function(e){return this.each(function(){return new h(this,e)})}),null!=t?t.exports=h:window.Dropzone=h,h.ADDED="added",h.QUEUED="queued",h.ACCEPTED=h.QUEUED,h.UPLOADING="uploading",h.PROCESSING=h.UPLOADING,h.CANCELED="canceled",h.ERROR="error",h.SUCCESS="success";var v=function(e,t,n,i,r,a,o,s,l,u){var c=function(e){e.naturalWidth;var t=e.naturalHeight,n=document.createElement("canvas");n.width=1,n.height=t;var i=n.getContext("2d");i.drawImage(e,0,0);for(var r=i.getImageData(1,0,1,t).data,a=0,o=t,s=t;a<s;){0===r[4*(s-1)+3]?o=s:a=s,s=o+a>>1}var l=s/t;return 0==l?1:l}(t);return e.drawImage(t,n,i,r,a,o,s,l,u/c)},g=function(){function e(){c(this,e)}return d(e,null,[{key:"initClass",value:function(){this.KEY_STR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}},{key:"encode64",value:function(e){for(var t="",n=void 0,i=void 0,r="",a=void 0,o=void 0,s=void 0,l="",u=0;a=(n=e[u++])>>2,o=(3&n)<<4|(i=e[u++])>>4,s=(15&i)<<2|(r=e[u++])>>6,l=63&r,isNaN(i)?s=l=64:isNaN(r)&&(l=64),t=t+this.KEY_STR.charAt(a)+this.KEY_STR.charAt(o)+this.KEY_STR.charAt(s)+this.KEY_STR.charAt(l),n=i=r="",a=o=s=l="",u<e.length;);return t}},{key:"restore",value:function(e,t){if(!e.match("data:image/jpeg;base64,"))return t;var n=this.decode64(e.replace("data:image/jpeg;base64,","")),i=this.slice2Segments(n),r=this.exifManipulation(t,i);return"data:image/jpeg;base64,".concat(this.encode64(r))}},{key:"exifManipulation",value:function(e,t){var n=this.getExifArray(t),i=this.insertExif(e,n);return new Uint8Array(i)}},{key:"getExifArray",value:function(e){for(var t=void 0,n=0;n<e.length;){if(255===(t=e[n])[0]&225===t[1])return t;n++}return[]}},{key:"insertExif",value:function(e,t){var n=e.replace("data:image/jpeg;base64,",""),i=this.decode64(n),r=i.indexOf(255,3),a=i.slice(0,r),o=i.slice(r),s=a;return s=(s=s.concat(t)).concat(o)}},{key:"slice2Segments",value:function(e){for(var t=0,n=[];;){if(255===e[t]&218===e[t+1])break;if(255===e[t]&216===e[t+1])t+=2;else{var i=t+(256*e[t+2]+e[t+3])+2,r=e.slice(t,i);n.push(r),t=i}if(t>e.length)break}return n}},{key:"decode64",value:function(e){var t=void 0,n=void 0,i="",r=void 0,a=void 0,o="",s=0,l=[];for(/[^A-Za-z0-9\+\/\=]/g.exec(e)&&console.warn("There were invalid base64 characters in the input text.\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\nExpect errors in decoding."),e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");t=this.KEY_STR.indexOf(e.charAt(s++))<<2|(r=this.KEY_STR.indexOf(e.charAt(s++)))>>4,n=(15&r)<<4|(a=this.KEY_STR.indexOf(e.charAt(s++)))>>2,i=(3&a)<<6|(o=this.KEY_STR.indexOf(e.charAt(s++))),l.push(t),64!==a&&l.push(n),64!==o&&l.push(i),t=n=i="",r=a=o="",s<e.length;);return l}}]),e}();g.initClass();return h._autoDiscoverFunction=function(){if(h.autoDiscover)return h.discover()},function(t,n){function i(e){if("readystatechange"!==e.type||"complete"===a.readyState)return("load"===e.type?t:a)[l](u+e.type,i,!1),!r&&(r=!0)?n.call(t,e.type||e):void 0}var r=!1,e=!0,a=t.document,o=a.documentElement,s=a.addEventListener?"addEventListener":"attachEvent",l=a.addEventListener?"removeEventListener":"detachEvent",u=a.addEventListener?"":"on";if("complete"!==a.readyState){if(a.createEventObject&&o.doScroll){try{e=!t.frameElement}catch(e){}e&&!function t(){try{o.doScroll("left")}catch(e){return void setTimeout(t,50)}return i("poll")}()}a[s](u+"DOMContentLoaded",i,!1),a[s](u+"readystatechange",i,!1),t[s](u+"load",i,!1)}}(window,h._autoDiscoverFunction),t.exports});