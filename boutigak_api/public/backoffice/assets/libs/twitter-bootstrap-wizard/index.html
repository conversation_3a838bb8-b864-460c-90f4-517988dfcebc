<!DOCTYPE html>
<html>
  <head>
    <title>Twitter Bootstrap Wizard</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="description" content="Twitter Bootstrap Wizard Plugin">
	<meta name="author" content="<PERSON>">
    <!-- Bootstrap -->
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<style type="text/css">
	      body {
	        padding-top: 60px;
	        padding-bottom: 40px;
	      }
	      .sidebar-nav {
	        padding: 9px 0;
	      }
	    </style>

		<!-- Le HTML5 shim, for IE6-8 support of HTML5 elements -->
		<!--[if lt IE 9]>
	      <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
	    <![endif]-->
  </head>
  <body>
    <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
      <div class="container-fluid">
        <div class="navbar-header">
          <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="#">Twitter Bootstrap Wizard</a>
        </div>
        <div id="navbar" class="collapse navbar-collapse">
          <ul class="nav navbar-nav">
            <li class="active"><a href="#">Home</a></li>
            <li><a href="#examples">Examples</a></li>
            <li><a href="#docs">Documentation</a></li>
            <li><a href="#demo">Demo</a></li>
          </ul>
        </div><!--/.nav-collapse -->
      </div>
    </nav>

    <div class="container-fluid">
      <div class="row-fluid">
        <div class="col-xs-12">
          <div class="hero-unit">
            <h1>Twitter Bootstrap Wizard Plugin</h1>
            <p>This twitter bootstrap plugin builds a wizard out of a formatter tabbable structure. It allows to build a wizard functionality using buttons to go through the different wizard steps and using events allows to hook into each step individually.</p>
            <p><a class="btn btn-primary btn-large" href="https://github.com/VinceG/twitter-bootstrap-wizard" target="_blank">Fork It &raquo;</a></p>
          </div>
          <div class="row-fluid">
          <div class="row-fluid">

            <div class="col-xs-12">
			<a name="examples"></a>
              <h2>Examples</h2>
              <p>Following are just a set of examples using this plugin.</p>
              	<ul>
					<li><a href="examples/basic.html">Basic Usage</a></li>
					<li><a href="examples/basic-pills.html">Basic Pills Usage</a></li>
					<li><a href="examples/basic-inverse.html">Basic Inverse Usage</a></li>
					<li><a href="examples/basic-tabsleft.html">Basic Tabs Left Usage</a></li>
					<li><a href="examples/basic-custombuttons.html">Wizard With Custom Next/Previous Buttons</a></li>
					<li><a href="examples/basic-custombuttonsfirstlast.html">Wizard With Custom Next/Previous Buttons & First and Last buttons</a></li>
					<li><a href="examples/basic-progressbar.html">Wizard With Progress Bar using events</a></li>
					<li><a href="examples/basic-formvalidation.html">Wizard With Form Validation</a></li>
					<li><a href="examples/basic-disabletabclick.html">Wizard With Disabled Tab Click</a></li>
					<li><a href="examples/basic-finishbutton.html">Wizard With Finish Button On last tab</a></li>
					<li><a href="examples/multiple.html">Multiple Wizards</a></li>
					<li><a href="examples/remove-step.html">Disabling/Enabling/Showing/Hiding/Removing Steps</a></li>
					<li><a href="examples/validation.html">jQuery Validation Plugin With Step Validation</a></li>
				</ul>
            </div><!--/span-->


			<div class="span11">
			<a name="docs"></a>
              <h2>Documentation</h2>
              	<table class="table table-bordered table-striped">
					<thead>
						<tr>
							<th>Key</th>
							<th>Default</th>
							<th>Description</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>tabClass</td>
							<td>nav nav-pills</td>
							<td>ul navigation class</td>
						</tr>
						<tr>
							<td>nextSelector</td>
							<td>.wizard li.next</td>
							<td>next element selector</td>
						</tr>
						<tr>
							<td>previousSelector</td>
							<td>.wizard li.previous</td>
							<td>previous element selector</td>
						</tr>
						<tr>
							<td>firstSelector</td>
							<td>.wizard li.first</td>
							<td>first element selector</td>
						</tr>
						<tr>
							<td>lastSelector</td>
							<td>.wizard li.last</td>
							<td>last element selector</td>
						</tr>
						<tr>
							<td>onInit</td>
							<td>null</td>
							<td>Fired when plugin is initialized</td>
						</tr>
						<tr>
							<td>onShow</td>
							<td>null</td>
							<td>Fired when plugin data is shown</td>
						</tr>
						<tr>
							<td>onNext</td>
							<td>null</td>
							<td>Fired when next button is clicked (return false to disable moving to the next step)</td>
						</tr>
						<tr>
							<td>onPrevious</td>
							<td>null</td>
							<td>Fired when previous button is clicked (return false to disable moving to the previous step)</td>
						</tr>
						<tr>
							<td>onFirst</td>
							<td>null</td>
							<td>Fired when first button is clicked (return false to disable moving to the first step)</td>
						</tr>
						<tr>
							<td>onLast</td>
							<td>null</td>
							<td>Fired when last button is clicked (return false to disable moving to the last step)</td>
						</tr>
						<tr>
							<td>onTabClick</td>
							<td>null</td>
							<td>Fired when a tab is clicked (return false to disable moving to that tab and showing it's contents)</td>
						</tr>
						<tr>
							<td>onTabShow</td>
							<td>null</td>
							<td>Fired when a tab content is shown (return false to disable showing that tab content)</td>
						</tr>
					</tbody>
				</table>
            </div><!--/span-->

			<div class="span11">
			<a name="demo"></a>
              <h2>Demo</h2>
              <p>Basic Demo Example.</p>
              	<div id="rootwizard">
					<div class="navbar">
					  <div class="navbar-inner">
					    <div class="container">
					<ul>
					  	<li><a href="#tab1" data-toggle="tab">First</a></li>
						<li><a href="#tab2" data-toggle="tab">Second</a></li>
						<li><a href="#tab3" data-toggle="tab">Third</a></li>
						<li><a href="#tab4" data-toggle="tab">Forth</a></li>
						<li><a href="#tab5" data-toggle="tab">Fifth</a></li>
						<li><a href="#tab6" data-toggle="tab">Sixth</a></li>
						<li><a href="#tab7" data-toggle="tab">Seventh</a></li>
					</ul>
					 </div>
					  </div>
					</div>
					<div id="bar" class="progress progress-striped active">
					  <div class="bar"></div>
					</div>
					<div class="tab-content">
					    <div class="tab-pane" id="tab1">
					      1
					    </div>
					    <div class="tab-pane" id="tab2">
					      2
					    </div>
						<div class="tab-pane" id="tab3">
							3
					    </div>
						<div class="tab-pane" id="tab4">
							4
					    </div>
						<div class="tab-pane" id="tab5">
							5
					    </div>
						<div class="tab-pane" id="tab6">
							6
					    </div>
						<div class="tab-pane" id="tab7">
							7
					    </div>
						<ul class="pager wizard">
							<li class="previous first" style="display:none;"><a href="javascript:;">First</a></li>
							<li class="previous"><a href="javascript:;">Previous</a></li>
							<li class="next last" style="display:none;"><a href="javascript:;">Last</a></li>
						  	<li class="next"><a href="javascript:;">Next</a></li>
						</ul>
					</div>
				</div>
            </div><!--/span-->

          </div><!--/row-->
        </div><!--/span-->
      </div><!--/row-->



      <hr>

      <footer>
        <p>&copy; <a href='http://vadimg.com' target="_blank">Vadim Vincent Gabriel</a> 2012</p>
      </footer>

    </div><!--/.fluid-container-->
    <script src="http://code.jquery.com/jquery-latest.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
	<script src="jquery.bootstrap.wizard.js"></script>
	<script>
	$(document).ready(function() {
	  	$('#rootwizard').bootstrapWizard({onTabShow: function(tab, navigation, index) {
			var $total = navigation.find('li').length;
			var $current = index+1;
			var $percent = ($current/$total) * 100;
			$('#rootwizard').find('.bar').css({width:$percent+'%'});
		}});
	});
	</script>
  </body>
</html>
