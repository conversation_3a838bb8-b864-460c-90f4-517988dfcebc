<?php

/**
 * Test script for Rich Data Seeder
 * Run this to test the seeder without affecting your main database
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Database\Seeders\RichDataSeeder;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Testing Rich Data Seeder...\n";

try {
    // Test database connection
    echo "📡 Testing database connection...\n";
    DB::connection()->getPdo();
    echo "✅ Database connection successful\n";

    // Check required tables exist
    echo "🔍 Checking required tables...\n";
    $requiredTables = [
        'users', 'store', 'item', 'category', 'brand', 'store_type', 
        'location', 'e_payment_providers', 'media', 'item_media', 
        'store_media', 'order', 'payment_proofs', 'discution', 'discution_message'
    ];

    foreach ($requiredTables as $table) {
        if (DB::getSchemaBuilder()->hasTable($table)) {
            echo "✅ Table '{$table}' exists\n";
        } else {
            echo "❌ Table '{$table}' missing\n";
        }
    }

    // Check if required seeders have run
    echo "\n🌱 Checking if basic seeders have run...\n";
    
    $categoryCount = DB::table('category')->count();
    $brandCount = DB::table('brand')->count();
    $storeTypeCount = DB::table('store_type')->count();
    $paymentProviderCount = DB::table('e_payment_providers')->count();

    echo "Categories: {$categoryCount}\n";
    echo "Brands: {$brandCount}\n";
    echo "Store Types: {$storeTypeCount}\n";
    echo "Payment Providers: {$paymentProviderCount}\n";

    if ($categoryCount == 0 || $brandCount == 0 || $storeTypeCount == 0 || $paymentProviderCount == 0) {
        echo "\n⚠️  Warning: Some basic data is missing. Run basic seeders first:\n";
        echo "php artisan db:seed --class=CategorySeeder\n";
        echo "php artisan db:seed --class=BrandSeeder\n";
        echo "php artisan db:seed --class=StoreTypeSeeder\n";
        echo "php artisan db:seed --class=EPaymentProviderSeeder\n";
        echo "\nOr run: php artisan db:seed\n";
    } else {
        echo "✅ Basic data is available\n";
    }

    echo "\n🎯 Rich Data Seeder test completed!\n";
    echo "\nTo run the rich data seeder:\n";
    echo "php artisan db:seed-rich\n";
    echo "\nOr to clean database first:\n";
    echo "php artisan db:seed-rich --clean\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
