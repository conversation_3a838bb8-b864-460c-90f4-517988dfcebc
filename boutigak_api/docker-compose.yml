version: "3.8"

services:
  boutigak_server:
    container_name: 'boutigak_server'
    build: 
      context: .
      dockerfile: Dockerfile
    volumes:
      - './:/var/www/html'
      - '/var/www/html/vendor'
      - '/var/www/html/node_modules'
      - '/etc/letsencrypt/live/boutigak.com:/etc/letsencrypt/live/boutigak.com:ro'
      - '/etc/letsencrypt/archive/boutigak.com:/etc/letsencrypt/archive/boutigak.com:ro'
      - '/etc/letsencrypt/renewal:/etc/letsencrypt/renewal:ro'
    ports:
      - "80:80"
      - "443:443"
    environment:
      - PHP_OPCACHE_ENABLE=1
      - PHP_OPCACHE_REVALIDATE_FREQ=0
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s