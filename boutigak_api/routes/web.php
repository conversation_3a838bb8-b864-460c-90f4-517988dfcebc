<?php

use App\Http\Controllers\BrandController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CategoryPriceController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DashboardAnalyticsController;
use App\Http\Controllers\ImportController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\NewUserSubscriptionController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PromoCodeController;
use App\Http\Controllers\StoreTypeController;
use App\services\SmsBuilder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Validation\ValidationException;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public routes
Route::get('/', function () {
    return view('welcome');
});

Route::get("/redirect-page", function () {
    return view('redirect-page');
});

Route::get('/privacy-policy', function () {
    return view('privacy-policy');
})->name('privacy-policy');

// // Authentication routes
require __DIR__ . '/auth.php';

// Authenticated routes
Route::middleware('auth')->group(function () {
    // User profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Password routes
    Route::get('/change-password', [App\Http\Controllers\Auth\PasswordController::class, 'edit'])->name('password.edit');
    Route::put('/change-password', [App\Http\Controllers\Auth\PasswordController::class, 'update'])->name('password.update');

    // Dashboard route
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Dashboard Analytics API routes
    Route::prefix('api/dashboard')->group(function () {
        Route::get('/analytics', [DashboardAnalyticsController::class, 'getDashboardAnalytics']);
        Route::get('/daily-active-users', [DashboardAnalyticsController::class, 'getDailyActiveUsers']);
        Route::get('/current-active-users', [DashboardAnalyticsController::class, 'getCurrentActiveUsers']);
        Route::get('/active-users-stats', [DashboardAnalyticsController::class, 'getActiveUsersStats']);
        Route::get('/user-activity-summary', [DashboardAnalyticsController::class, 'getUserActivitySummary']);
        Route::get('/hourly-active-users', [DashboardAnalyticsController::class, 'getHourlyActiveUsers']);
    });

    // Permission management routes
    Route::resource('permissions', App\Http\Controllers\PermissionController::class);
    Route::get('permissions/{permissionId}/delete', [App\Http\Controllers\PermissionController::class, 'destroy']);

    // Role management routes
    Route::resource('roles', App\Http\Controllers\RoleController::class);
    Route::get('roles/{roleId}/delete', [App\Http\Controllers\RoleController::class, 'destroy']);
    Route::get('roles/{roleId}/give-permissions', [App\Http\Controllers\RoleController::class, 'addPermissionToRole']);
    Route::put('roles/{roleId}/give-permissions', [App\Http\Controllers\RoleController::class, 'givePermissionToRole']);

    // User management routes
    Route::resource('users', App\Http\Controllers\UserController::class);
    // Store routes
    // Route::middleware('can:manage all')->group(function () {
        Route::resource('stores', App\Http\Controllers\StoreController::class);
        Route::post('stores/create-new-store', [App\Http\Controllers\StoreController::class, 'createStore'])->name('stores.create-store');
        Route::delete('stores/{storeId}/delete', [App\Http\Controllers\StoreController::class, 'destroy'])->name('stores.destroy');
        Route::post('stores/promote', [App\Http\Controllers\StoreController::class, 'promoteStore'])->name('stores.promote');
        Route::post('stores/update-promotion-positions', [App\Http\Controllers\StoreController::class, 'updatePromotionPositions'])->name('stores.updatePromotionPositions');
        Route::post('stores/remove-promotion', [App\Http\Controllers\StoreController::class, 'removePromotion'])->name('stores.removePromotion');
        Route::get('stores/{storeId}', [App\Http\Controllers\StoreController::class, 'redirectToApp'])->name('stores.redirect-to-app');
    // });

    // Item routes
    Route::resource('items', ItemController::class);
    Route::get('items/show/{id}', [ItemController::class, 'show'])->name('items.show');
    Route::put('add-matterport-link', [ItemController::class, 'addMatterportLink'])->name('items.addMatterportLink');
    Route::put('/items/{item}/update-status', [ItemController::class, 'updateStatusBo'])->name('items.updateStatus');
    Route::put('/items/{item}/toggle-promote', [ItemController::class, 'togglePromote'])->name('items.togglePromote');
    Route::delete('items/{itemId}/delete', [ItemController::class, 'destroy'])->name('items.destroy');
    Route::get('items/{itemId}/edit', [ItemController::class, 'edit'])->name('items.edit');
    Route::put('items/{itemId}/update', [ItemController::class, 'updateBo'])->name('items.update');

    // Promo code routes
    Route::resource('promo-codes', PromoCodeController::class);
    Route::post('promo-codes/stop/{id}', [PromoCodeController::class, 'stop'])->name('promo-codes.stop');

    // Subscription routes
    Route::resource('users-subscription', NewUserSubscriptionController::class);

    // Category price routes
    Route::resource('category_prices', CategoryPriceController::class);

    // Payment routes
    Route::resource('payments', PaymentController::class);

    // Order routes
    Route::resource('orders', OrderController::class);
    Route::put('orders/{id}/change-status', [OrderController::class, 'changeStatus'])->name('orders.changeStatus');

    // Category routes
    Route::get('categories', [CategoryController::class, 'index'])->name('categories.index');
    Route::post('categories', [CategoryController::class, 'storeBo'])->name('categories.store');
    Route::get('categories/{id}/edit', [CategoryController::class, 'edit'])->name('categories.edit');
    Route::get('categories/{id}/show', [CategoryController::class, 'show'])->name('categories.show');
    Route::get('categories/create', [CategoryController::class, 'create'])->name('categories.create');
    Route::put('categories/{id}', [CategoryController::class, 'storeBo'])->name('categories.update');
    Route::delete('categories/{id}', [CategoryController::class, 'destroy'])->name('categories.destroy');

    // Brand routes
    Route::get('brands', [BrandController::class, 'indexBo'])->name('brands.index');
    Route::get('brands/create', [BrandController::class, 'create'])->name('brands.create');
    Route::post('brands', [BrandController::class, 'storeBo'])->name('brands.store');
    Route::get('brands/{id}/edit', [BrandController::class, 'edit'])->name('brands.edit');
    Route::put('brands/{id}', [BrandController::class, 'updateBo'])->name('brands.update');
    Route::delete('brands/{id}', [BrandController::class, 'destroyBo'])->name('brands.destroy');

    // Store type routes
    Route::resource('store-types', StoreTypeController::class);

    // Import routes
    Route::get('import-brands', [ImportController::class, 'importBrandsView'])->name('import.brands.view');
    Route::post('import-brands', [ImportController::class, 'importBrands'])->name('import.brands');
    Route::get('import-categories', [ImportController::class, 'importCategoriesView'])->name('import.categories.view');
    Route::post('import-categories', [ImportController::class, 'importCategories'])->name('import.categories');

    // Ad management routes
    Route::get('ads', [App\Http\Controllers\AdController::class, 'indexBo'])->name('ads.index');
    Route::get('ads/create', [App\Http\Controllers\AdController::class, 'create'])->name('ads.create');
    Route::post('ads', [App\Http\Controllers\AdController::class, 'storeBo'])->name('ads.store');
    Route::get('ads/{id}/edit', [App\Http\Controllers\AdController::class, 'edit'])->name('ads.edit');
    Route::put('ads/{id}', [App\Http\Controllers\AdController::class, 'updateBo'])->name('ads.update');
    Route::delete('ads/{id}', [App\Http\Controllers\AdController::class, 'destroyBo'])->name('ads.destroy');
    Route::get('ads/{id}/show', [App\Http\Controllers\AdController::class, 'showBo'])->name('ads.show');
    Route::get('ads/store-items/{storeId}', [App\Http\Controllers\AdController::class, 'getStoreItems'])->name('ads.store-items');
    Route::get('ads/test-creation', [App\Http\Controllers\AdController::class, 'testAdCreation'])->name('ads.test-creation');
    Route::get('ads/test-ajax', [App\Http\Controllers\AdController::class, 'testAjax'])->name('ads.test-ajax');
    Route::get('ads/simple-index', [App\Http\Controllers\AdController::class, 'simpleIndex'])->name('ads.simple-index');

    // SMS test route
    Route::get('/test-sms', function () {
        try {
            Log::info('Attempting to create SMPP connection...');
            $smsBuilder = new SmsBuilder(
                '************',
                2775,
                'becod',
                'b1c2d',
                20000,
                true
            );

            Log::info('SMPP connection created successfully, attempting to send SMS...');
            $smsBuilder->setSender(env('SMPP_ALIAS', 'MyAppSender'), SMPP::TON_ALPHANUMERIC)
                ->setRecipient('47100763', SMPP::TON_INTERNATIONAL)
                ->sendMessage('Hello, this is a test message from MyApp!');

            Log::info('SMS sent successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to send SMS on first attempt: ' . $e->getMessage());

            // Retry once more if it's a socket-related issue
            if (str_contains($e->getMessage(), 'socket')) {
                Log::info('Retrying SMPP connection due to socket error...');
                try {
                    $smsBuilder = new SmsBuilder('************', 2775, 'becod', 'b1c2d', 20000, true);
                    $smsBuilder->setSender(env('SMPP_ALIAS', 'MyAppSender'), SMPP::TON_ALPHANUMERIC)
                        ->setRecipient('47100763', SMPP::TON_INTERNATIONAL)
                        ->sendMessage('Hello, this is a test message from MyApp!');
                    Log::info('SMS sent successfully on retry.');

                    return response()->json(['message' => 'SMS sent successfully on retry.']);
                } catch (\Exception $e) {
                    Log::error('Retry failed: ' . $e->getMessage());
                    return response()->json(['message' => 'Failed to send SMS on retry: ' . $e->getMessage()]);
                }
            }
        }

        return response()->json(['message' => 'SMS sent successfully.']);
    });
});




Route::get('/files/{filename}', function ($filename) {
    $disk = Storage::disk('sftp');
    
    if (!$disk->exists($filename)) {
        abort(404);
    }
    
    $content = $disk->get($filename);
    $mime = $disk->mimeType($filename);
    return response($content)->header('Content-Type', $mime);
});





Route::get('/debug-session', function () {
    return response()->json([
        'server_hostname' => gethostname(),
        'session_id' => session()->getId(),
        'session_driver' => config('session.driver'),
        'session_table' => config('session.table'),
        'session_domain' => config('session.domain'),
        'session_secure' => config('session.secure'),
        'session_same_site' => config('session.same_site'),
        'session_cookie_name' => config('session.cookie'),
        'app_key_present' => !empty(config('app.key')),
        'app_key_hash' => substr(md5(config('app.key')), 0, 8),
        'user_agent' => request()->header('User-Agent'),
        'ip_address' => request()->ip(),
        'has_session_data' => !empty(session()->all()),
        'session_data' => session()->all(),
        'headers' => [
            'x-forwarded-for' => request()->header('x-forwarded-for'),
            'x-real-ip' => request()->header('x-real-ip'),
            'cf-connecting-ip' => request()->header('cf-connecting-ip'),
        ]
    ]);
});

Route::get('/session-test', function () {
    $count = session('visit_count', 0);
    session(['visit_count' => $count + 1]);
    session(['server_name' => gethostname()]);
    
    return response()->json([
        'message' => 'Session test',
        'server_hostname' => gethostname(),
        'session_id' => session()->getId(),
        'visit_count' => session('visit_count'),
        'original_server' => session('server_name'),
        'timestamp' => now()->toDateTimeString()
    ]);
});

Route::get('/session-write', function () {
    session(['test_data' => [
        'timestamp' => now()->toDateTimeString(),
        'server' => gethostname(),
        'random' => rand(1000, 9999)
    ]]);
    
    return response()->json([
        'message' => 'Data written to session',
        'session_id' => session()->getId(),
        'server' => gethostname()
    ]);
});

Route::get('/session-read', function () {
    return response()->json([
        'message' => 'Reading from session',
        'session_id' => session()->getId(),
        'server' => gethostname(),
        'test_data' => session('test_data'),
        'all_session_data' => session()->all()
    ]);
});