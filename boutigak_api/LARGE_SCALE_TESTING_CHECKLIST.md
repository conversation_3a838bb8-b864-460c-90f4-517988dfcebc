# 🚀 Large Scale Testing Checklist for Boutigak App

## 📊 **Scale Overview**
- **100 Users** (all with stores)
- **100 Stores** (various categories)
- **100 Subscriptions** (active subscriptions for all store owners)
- **300 Items** (mixed ownership: 60% store items, 40% user items)
- **50-80 Orders** (with payment proofs)
- **30-50 Discussions** (realistic conversations)

## 📱 **Setup & Login (5 minutes)**

### Primary Test Users:
- **<PERSON>**: `38184156` / `12345678` (Store: "Boutique Ahmed Electronics")
- **<PERSON>**: `36666688` / `12345678` (Store: "Fashion Mohamed")

### Additional Test Users:
- **Random Users**: `20100003` to `20100100` / `12345678`
- **All users have stores** with various categories

### Quick Setup:
```bash
php artisan db:seed-rich --clean
```

---

## ⚡ **5-Minute Quick Test**

### 1. Authentication ✅
- [ ] Login with <PERSON> (`38184156`)
- [ ] Login with <PERSON> (`36666688`)
- [ ] Try random user (`20100050`)
- [ ] Verify user data loads correctly

### 2. Home Page ✅
- [ ] See 300 items in grid
- [ ] Scroll through items (infinite scroll)
- [ ] Tap item to view details
- [ ] Check multiple images (first 100 items)

### 3. Stores ✅
- [ ] Navigate to Store tab
- [ ] See 100 stores listed
- [ ] Tap "Boutique Ahmed Electronics"
- [ ] See store items (3 per store)
- [ ] Browse different store categories

### 4. Search & Filter ✅
- [ ] Search for "iPhone"
- [ ] Filter by category
- [ ] Test pagination/infinite scroll
- [ ] Check search performance

### 5. Orders & Discussions ✅
- [ ] Go to Profile → My Orders
- [ ] See 50-80 orders with various statuses
- [ ] Navigate to Inbox
- [ ] See 30-50 discussions

---

## 🔍 **15-Minute Performance Test**

### Load Testing:
1. **Home Page Performance**:
   - [ ] Initial load time < 3 seconds
   - [ ] Smooth scrolling through 300 items
   - [ ] Image loading performance
   - [ ] Memory usage reasonable

2. **Store Browsing**:
   - [ ] Store list loads quickly (100 stores)
   - [ ] Store details load fast
   - [ ] Store items display correctly
   - [ ] No lag when switching stores

3. **Search Performance**:
   - [ ] Search results < 1 second
   - [ ] Filter application fast
   - [ ] Large dataset handling
   - [ ] No crashes with complex queries

### Data Integrity:
- [ ] All 100 users have stores
- [ ] All 100 store owners have active subscriptions
- [ ] 300 items distributed correctly (3 per user)
- [ ] ~180 items belong to stores (60%)
- [ ] ~120 items belong directly to users (40%)
- [ ] Orders reference correct items/users
- [ ] Discussions link buyers/sellers properly
- [ ] Store ownership correct

---

## 🎯 **Feature-Specific Tests**

### Store Management (Store Owners):
- [ ] Login as Ahmed (`38184156`)
- [ ] View store dashboard
- [ ] See store statistics
- [ ] Check store items (~2 store items, ~1 user item)
- [ ] View store orders
- [ ] Check subscription status (should be active)

### Subscription Testing:
- [ ] All store owners have active subscriptions
- [ ] Subscription dates are realistic (started 1-30 days ago)
- [ ] Various subscription durations (30, 60, 90, 180, 365 days)
- [ ] Subscription prices match duration
- [ ] No expired subscriptions in test data

### User Experience:
- [ ] Browse as different users
- [ ] Test multilingual content (AR/FR/EN)
- [ ] Check user preferences
- [ ] Test profile management

### Image Optimization:
- [ ] First 100 items have real images
- [ ] Images load from SFTP server
- [ ] WebP format used
- [ ] Image quality good
- [ ] Loading speed acceptable

---

## 📊 **Expected Data Counts**

After seeding, verify:
- **Users**: 100 (all verified, all have stores)
- **Stores**: 100 (various categories and names)
- **Subscriptions**: 100 (all active, various durations)
- **Items**: 300 (3 per user, mixed ownership, first 100 with images)
  - **Store Items**: ~180 (60% belong to stores)
  - **User Items**: ~120 (40% belong directly to users)
- **Orders**: 50-80 (various statuses)
- **Discussions**: 30-50 (with realistic conversations)
- **Payment Proofs**: ~20 (with screenshots)
- **Locations**: 4 (Nouakchott areas)

---

## 🚨 **Performance Benchmarks**

### Must Meet:
- [ ] **App startup**: < 5 seconds
- [ ] **Home page load**: < 3 seconds
- [ ] **Store browsing**: < 2 seconds per store
- [ ] **Search results**: < 1 second
- [ ] **Image loading**: < 2 seconds per image
- [ ] **No crashes** during normal usage

### Memory Usage:
- [ ] **Initial load**: < 200MB
- [ ] **After browsing**: < 400MB
- [ ] **No memory leaks** during extended use

---

## 🔧 **Troubleshooting Large Scale**

### If Performance Issues:
```bash
# Check database performance
php artisan tinker
>>> DB::table('item')->count()  // Should return 300
>>> DB::table('store')->count() // Should return 100
>>> DB::table('users')->count() // Should return 100
```

### If Images Don't Load:
- Check first 100 items only have images
- Verify SFTP server connection
- Check ImageOptimizationService logs

### If App Crashes:
- Increase PHP memory limit
- Check Laravel logs for errors
- Monitor device memory usage

---

## 📝 **Large Scale Test Report Template**

```
Date: ___________
Tester: ___________
Device: ___________

✅ PASSED / ❌ FAILED

Scale Verification:
- 100 Users: ___
- 100 Stores: ___
- 300 Items: ___
- Orders (50-80): ___
- Discussions (30-50): ___

Performance:
- App startup time: ___ seconds
- Home page load: ___ seconds
- Search performance: ___ seconds
- Memory usage: ___ MB

Critical Features:
- Authentication: ___
- Store browsing: ___
- Item viewing: ___
- Image loading: ___
- Search/filter: ___

Issues Found:
1. ________________
2. ________________
3. ________________

Overall Performance: ✅ GOOD / ⚠️ ACCEPTABLE / ❌ POOR
Ready for Production: ✅ YES / ❌ NO
```

---

## 🎯 **Success Criteria for Large Scale**

Your app is ready when:
- ✅ All 100 users can login
- ✅ 300 items display correctly
- ✅ 100 stores are accessible
- ✅ Performance meets benchmarks
- ✅ No crashes with large dataset
- ✅ Memory usage reasonable
- ✅ Search works with 300 items
- ✅ Image optimization effective

**Time to complete full test: 20-30 minutes**

---

This large scale testing ensures your app can handle realistic production loads! 🚀
