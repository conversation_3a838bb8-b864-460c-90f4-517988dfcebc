<VirtualHost *:80>
    ServerName boutigak.com
    DocumentRoot /var/www/html/public

    # Redirect all HTTP to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    <Directory /var/www/html/public/>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>

<VirtualHost *:443>
    ServerName boutigak.com
    DocumentRoot /var/www/html/public

    SSLEngine On
    SSLCertificateFile /etc/letsencrypt/live/boutigak.com/fullchain.pem
    SSLCertificateKeyFile /etc/letsencrypt/live/boutigak.com/privkey.pem
    # Optionally add intermediate cert
    # SSLCertificateChainFile /etc/letsencrypt/live/boutigak.com/chain.pem

    <Directory /var/www/html/public/>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error_ssl.log
    CustomLog ${APACHE_LOG_DIR}/access_ssl.log combined
</VirtualHost>
