<?php

namespace Database\Seeders;

use Carbon\Carbon;
use App\Models\Item;
use App\Models\User;
use App\Models\Brand;
use App\Models\Media;
use App\Models\Order;
use App\Models\Store;
use App\Models\Category;
use App\Models\Location;
use App\Models\Discution;
use App\Models\ItemMedia;
use App\Models\OrderItem;
use App\Models\StoreType;
use App\Enums\eItemStatus;
use App\Models\StoreMedia;
use App\Models\PaymentProof;
use Illuminate\Database\Seeder;
use App\Models\DiscutionMessage;
use App\Models\EPaymentProvider;
use Illuminate\Support\Facades\DB;
use App\Models\NewUserSubscription;
use Illuminate\Support\Facades\Log;
use App\Models\NewStoreSubscription;
use Illuminate\Support\Facades\Hash;
use App\Models\StoreFavoriteCategory;
use App\services\ImageDownloadService;

class RichDataSeeder extends Seeder
{
    protected $imageDownloadService;

    public function __construct()
    {
        $this->imageDownloadService = app(ImageDownloadService::class);
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Log::info('🚀 Starting Large Scale Rich Data Seeding...');
        Log::info('📊 This will create: 100 users, 100 stores, 100 subscriptions, 300 items (mixed ownership), 50-80 orders, 30-50 discussions');

        // Clean database first
        // $this->cleanDatabase();

        // Create locations first
        $this->createLocations();

        // Create users (100 users)
        $users = $this->createUsers();

        // Create stores (100 stores)
        $stores = $this->createStores($users);

        // Create subscriptions for all store owners
        $this->createSubscriptions($users);

        // Create items (300 items - 3 per user, mixed store ownership)
        $this->createItems($users, $stores);

        // Create orders and payments (50-80 orders)
        $this->createOrdersAndPayments($users, $stores);

        // Create discussions (30-50 discussions)
        $this->createDiscussions($users);

        Log::info('✅ Large Scale Rich Data Seeding completed successfully!');
        Log::info('📈 Created: 100 users, 100 stores, 100 subscriptions, 300 items (mixed ownership), orders, and discussions');
    }

    /**
     * Clean the database
     */
    private function cleanDatabase(): void
    {
        Log::info('🧹 Cleaning database...');

        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clean tables in correct order
        $tables = [
            'discution_message',
            'discution',
            'payment_proofs',
            'order_payment',
            'order_item',
            'order',
            'new_store_subscriptions',
            'new_user_subscriptions',
            'store_favorite_categories',
            'store_favirote_categories', // Handle typo in table name
            'item_media',
            'store_media',
            'item',
            'store',
            'media',
            'user_locations',
            'users',
            'location'
        ];

        foreach ($tables as $table) {
            if (DB::getSchemaBuilder()->hasTable($table)) {
                DB::table($table)->truncate();
                Log::info("Cleaned table: {$table}");
            }
        }

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    /**
     * Create locations
     */
    private function createLocations(): array
    {
        Log::info('📍 Creating locations...');

        $locations = [
            ['name' => 'Nouakchott Centre', 'address' => 'Centre ville, Nouakchott', 'latitude' => 18.0735, 'longitude' => -15.9582],
            ['name' => 'Tevragh Zeina', 'address' => 'Tevragh Zeina, Nouakchott', 'latitude' => 18.0892, 'longitude' => -15.9777],
            ['name' => 'Ksar', 'address' => 'Ksar, Nouakchott', 'latitude' => 18.0669, 'longitude' => -15.9464],
            ['name' => 'Sebkha', 'address' => 'Sebkha, Nouakchott', 'latitude' => 18.1006, 'longitude' => -15.9572],
        ];

        $createdLocations = [];
        foreach ($locations as $locationData) {
            $location = Location::create($locationData);
            $createdLocations[] = $location;
            Log::info("Created location: {$location->name}");
        }

        return $createdLocations;
    }

    /**
     * Create users (100 users)
     */
    private function createUsers(): array
    {
        Log::info('👥 Creating 100 users...');

        // Mauritanian first names
        $maleFirstNames = [
            'Ahmed', 'Mohamed', 'Omar', 'Ali', 'Hassan', 'Ibrahim', 'Mahmoud', 'Abdallah', 'Youssef', 'Khalil',
            'Sidi', 'Ould', 'Moctar', 'Cheikh', 'Brahim', 'Salim', 'Hamza', 'Bilal', 'Ismail', 'Yahya',
            'Abderrahmane', 'Abdel', 'Moussa', 'Oumar', 'Mamadou', 'Amadou', 'Boubacar', 'Samba', 'Demba', 'Lamine'
        ];

        $femaleFirstNames = [
            'Fatima', 'Aicha', 'Khadija', 'Mariam', 'Aminata', 'Zeynab', 'Halima', 'Safiya', 'Rokaya', 'Maryam',
            'Mint', 'Vatma', 'Coumba', 'Awa', 'Rama', 'Nana', 'Khady', 'Maimouna', 'Astou', 'Dieynaba',
            'Fatoumata', 'Oumou', 'Salma', 'Nour', 'Leila', 'Zahra', 'Amina', 'Houda', 'Souad', 'Nawal'
        ];

        $lastNames = [
            'Mohamed', 'Abdallah', 'Hassan', 'Salem', 'Ahmed', 'Ali', 'Mahmoud', 'Ibrahim', 'Youssef', 'Omar',
            'Ould Ahmed', 'Mint Salem', 'Ould Mohamed', 'Mint Abdallah', 'Ba', 'Diallo', 'Sy', 'Kane', 'Sow', 'Fall',
            'Traore', 'Coulibaly', 'Keita', 'Toure', 'Sidibe', 'Camara', 'Kone', 'Sangare', 'Ouattara', 'Diarra'
        ];

        $languages = ['ar', 'fr', 'en'];
        $users = [];
        $locations = Location::all();

        // First create your original test users
        $originalUsers = [
            [
                'firstname' => 'Ahmed',
                'lastname' => 'Moloude',
                'phone' => '38184156',
                'gender' => 'male',
                'lang' => 'fr',
                'has_store' => true
            ],
            [
                'firstname' => 'Mohamed',
                'lastname' => 'AbdelKade',
                'phone' => '36666688',
                'gender' => 'male',
                'lang' => 'fr',
                'has_store' => true
            ]
        ];

        foreach ($originalUsers as $index => $data) {
            $user = User::create([
                'firstname' => $data['firstname'],
                'lastname' => $data['lastname'],
                'phone' => $data['phone'],
                'password' => Hash::make('12345678'),
                'gender' => $data['gender'],
                'lang' => $data['lang'],
                'has_store' => $data['has_store'],
                'is_verified' => true,
                'invitationcode' => 'INV' . str_pad($index + 1, 6, '0', STR_PAD_LEFT),
                'location_id' => $locations->random()->id,
                'last_activity' => Carbon::now()->subHours(rand(1, 24)),
                'is_deleted' => false
            ]);

            $users[] = $user;
            Log::info("Created original user: {$user->firstname} {$user->lastname}");
        }

        // Then create 98 more users to reach 100 total
        for ($i = 3; $i <= 100; $i++) {
            $gender = rand(0, 1) ? 'male' : 'female';
            $firstName = $gender === 'male' ? $maleFirstNames[array_rand($maleFirstNames)] : $femaleFirstNames[array_rand($femaleFirstNames)];
            $lastName = $lastNames[array_rand($lastNames)];

            $user = User::create([
                'firstname' => $firstName,
                'lastname' => $lastName,
                'phone' => '201' . str_pad($i, 5, '0', STR_PAD_LEFT), // 20100003 to 20100100
                'password' => Hash::make('12345678'),
                'gender' => $gender,
                'lang' => $languages[array_rand($languages)],
                'has_store' => true, // All users will have stores
                'is_verified' => true,
                'invitationcode' => 'INV' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'location_id' => $locations->random()->id,
                'last_activity' => Carbon::now()->subHours(rand(1, 168)), // Last week activity
                'is_deleted' => false
            ]);

            $users[] = $user;

            if ($i % 20 === 0) {
                Log::info("Created {$i}/100 users");
            }
        }

        Log::info("✅ Successfully created 100 users");
        return $users;
    }

    /**
     * Create stores for users with has_store = true (100 stores)
     */
    private function createStores(array $users): array
    {
        Log::info('🏪 Creating 100 stores...');

        $stores = [];
        $storeTypes = StoreType::all();
        $locations = Location::all();

        // Store name templates
        $storeNameTemplates = [
            'Electronics' => ['Electronics Store', 'Tech Hub', 'Digital World', 'Gadget Center', 'Mobile Shop'],
            'Fashion' => ['Fashion Boutique', 'Style Store', 'Trendy Clothes', 'Fashion House', 'Wardrobe'],
            'Food' => ['Food Market', 'Grocery Store', 'Fresh Market', 'Food Corner', 'Mini Market'],
            'Books' => ['Book Store', 'Library Shop', 'Reading Corner', 'Book World', 'Knowledge Hub'],
            'Sports' => ['Sports Store', 'Fitness Shop', 'Athletic Gear', 'Sports World', 'Active Life'],
            'Beauty' => ['Beauty Salon', 'Cosmetics Shop', 'Beauty Corner', 'Glamour Store', 'Style Studio'],
            'Home' => ['Home Decor', 'Furniture Store', 'Home Center', 'Living Space', 'Interior Shop'],
            'Auto' => ['Auto Parts', 'Car Service', 'Vehicle Shop', 'Auto Center', 'Motor World']
        ];

        $storeDescriptions = [
            'Electronics' => 'Magasin spécialisé en électronique et accessoires high-tech',
            'Fashion' => 'Boutique de mode avec les dernières tendances',
            'Food' => 'Épicerie avec produits frais et de qualité',
            'Books' => 'Librairie avec une large sélection de livres',
            'Sports' => 'Équipements sportifs et articles de fitness',
            'Beauty' => 'Produits de beauté et cosmétiques de qualité',
            'Home' => 'Décoration et mobilier pour la maison',
            'Auto' => 'Pièces automobiles et services mécaniques'
        ];

        $storeIndex = 0;
        foreach ($users as $user) {
            if ($user->has_store) {
                // Handle original users with specific store names
                if ($user->firstname === 'Ahmed' && $user->lastname === 'Moloude') {
                    $storeName = 'Boutique Ahmed Electronics';
                    $description = 'Magasin d\'électronique et d\'accessoires high-tech';
                } elseif ($user->firstname === 'Mohamed' && $user->lastname === 'AbdelKade') {
                    $storeName = 'Fashion Mohamed';
                    $description = 'Boutique de mode masculine et accessoires';
                } else {
                    // Generate store name for other users
                    $categoryKeys = array_keys($storeNameTemplates);
                    $category = $categoryKeys[array_rand($categoryKeys)];
                    $nameTemplate = $storeNameTemplates[$category][array_rand($storeNameTemplates[$category])];
                    $storeName = $nameTemplate . ' ' . $user->firstname;
                    $description = $storeDescriptions[$category];
                }

                $store = Store::create([
                    'name' => $storeName,
                    'description' => $description,
                    'user_id' => $user->id,
                    'type_id' => $storeTypes->random()->id,
                    'location_id' => $locations->random()->id,
                    'opening_time' => sprintf('%02d:00', rand(7, 9)), // 07:00 to 09:00
                    'closing_time' => sprintf('%02d:00', rand(18, 22)), // 18:00 to 22:00
                    'is_promoted' => 0,
                    'is_open' => 1, // 90% chance to be open
                ]);

                $this->addStoreImages($store);

                $stores[] = $store;
                $storeIndex++;

                if ($storeIndex % 20 === 0) {
                    Log::info("Created {$storeIndex}/100 stores");
                }
            }
        }

        Log::info("✅ Successfully created 100 stores");
        return $stores;
    }

    /**
     * Create active subscriptions for all store owners
     */
    private function createSubscriptions(array $users): void
    {
        Log::info('💳 Creating subscriptions for store owners...');

        $subscriptionCount = 0;

        foreach ($users as $user) {
            if ($user->has_store) {
                // Create active subscription (30-365 days)
                $startDate = Carbon::now()->subDays(rand(1, 30)); // Started 1-30 days ago
                $durationDays = [30, 60, 90, 180, 365][rand(0, 4)]; // Various subscription lengths
                $endDate = $startDate->copy()->addDays($durationDays);

                // Subscription prices based on duration
                $prices = [
                    30 => 50.00,   // 1 month
                    60 => 90.00,   // 2 months
                    90 => 120.00,  // 3 months
                    180 => 200.00, // 6 months
                    365 => 350.00  // 1 year
                ];

                NewUserSubscription::create([
                    'user_id' => $user->id,
                    'price' => $prices[$durationDays],
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]);

                $subscriptionCount++;

                if ($subscriptionCount % 20 === 0) {
                    Log::info("Created {$subscriptionCount}/100 subscriptions");
                }
            }
        }

        Log::info("✅ Successfully created {$subscriptionCount} active subscriptions");
    }

    /**
     * Add images to store
     */
    private function addStoreImages(Store $store): void
    {
        try {
            Log::info("Adding images to store: {$store->name}");

            // Get random store image URLs
            $imageUrls = $this->imageDownloadService->getRandomStoreImages(3);

            // Download and optimize through ImageOptimizationService
            $imageResults = $this->imageDownloadService->downloadMultipleImages($imageUrls, 'store_images');

            foreach ($imageResults as $imageData) {
                // Create media record with URL returned from ImageOptimizationService
                $media = Media::create([
                    'type' => 'image/webp',
                    'url' => $imageData['url'], // This is the final optimized URL from SFTP
                ]);

                // Link media to store
                StoreMedia::create([
                    'store_id' => $store->id,
                    'media_id' => $media->id
                ]);

                Log::info("Added store image", [
                    'store' => $store->name,
                    'media_id' => $media->id,
                    'final_url' => $imageData['url']
                ]);
            }

            Log::info("Successfully added " . count($imageResults) . " optimized images to store: {$store->name}");

        } catch (\Exception $e) {
            Log::error("Failed to add images to store {$store->name}: " . $e->getMessage());
        }
    }

    /**
     * Create items for all users (300+ items total - 3 per user + 50 for Ahmed's special store)
     */
    private function createItems(array $users, array $stores): void
    {
        Log::info('📦 Creating 300+ items (3 per user + 50 for Ahmed\'s special store)...');

        $categories = Category::whereNotNull('parent_id')->get(); // Get leaf categories
        $brands = Brand::all();

        // Find Ahmed's user and store
        $ahmedUser = collect($users)->firstWhere(function($user) {
            return $user->firstname === 'Ahmed' && $user->lastname === 'Moloude';
        });
        $ahmedStore = $ahmedUser ? collect($stores)->firstWhere('user_id', $ahmedUser->id) : null;

        // Define condition options that match our translation system
        $conditions = [
            'New with packaging',
            'New without packaging',
            'Very good',
            'Good',
            'Satisfactory'
        ];

        $itemTemplates = [
            // Electronics
            ['title' => 'iPhone 14 Pro Max', 'title_ar' => 'آيفون 14 برو ماكس', 'description' => 'Latest iPhone with advanced camera system', 'description_ar' => 'أحدث آيفون مع نظام كاميرا متقدم', 'price' => 1200.00, 'condition' => 'New with packaging'],
            ['title' => 'Samsung Galaxy S23', 'title_ar' => 'سامسونج جالاكسي S23', 'description' => 'Flagship Android phone with excellent display', 'description_ar' => 'هاتف أندرويد رائد مع شاشة ممتازة', 'price' => 900.00, 'condition' => 'New with packaging'],
            ['title' => 'MacBook Air M2', 'title_ar' => 'ماك بوك إير M2', 'description' => 'Lightweight laptop with M2 chip', 'description_ar' => 'لابتوب خفيف مع معالج M2', 'price' => 1500.00, 'condition' => 'New with packaging'],
            ['title' => 'iPad Pro', 'title_ar' => 'آيباد برو', 'description' => 'Professional tablet for creative work', 'description_ar' => 'جهاز لوحي مهني للعمل الإبداعي', 'price' => 800.00, 'condition' => 'New with packaging'],
            ['title' => 'AirPods Pro', 'title_ar' => 'إيربودز برو', 'description' => 'Wireless earbuds with noise cancellation', 'description_ar' => 'سماعات لاسلكية مع إلغاء الضوضاء', 'price' => 250.00, 'condition' => 'New with packaging'],

            // Fashion
            ['title' => 'Nike Air Jordan', 'title_ar' => 'نايك إير جوردان', 'description' => 'Classic basketball shoes', 'description_ar' => 'أحذية كرة سلة كلاسيكية', 'price' => 180.00, 'condition' => 'Very good'],
            ['title' => 'Designer Handbag', 'title_ar' => 'حقيبة يد مصممة', 'description' => 'Luxury leather handbag', 'description_ar' => 'حقيبة يد جلدية فاخرة', 'price' => 450.00, 'condition' => 'New without packaging'],
            ['title' => 'Vintage Watch', 'title_ar' => 'ساعة عتيقة', 'description' => 'Classic vintage watch', 'description_ar' => 'ساعة عتيقة كلاسيكية', 'price' => 280.00, 'condition' => 'Good'],
            ['title' => 'Leather Jacket', 'title_ar' => 'جاكيت جلدي', 'description' => 'Genuine leather jacket', 'description_ar' => 'جاكيت جلد طبيعي', 'price' => 320.00, 'condition' => 'New without packaging'],
            ['title' => 'Sunglasses', 'title_ar' => 'نظارات شمسية', 'description' => 'Designer sunglasses with UV protection', 'description_ar' => 'نظارات شمسية مصممة مع حماية من الأشعة', 'price' => 150.00, 'condition' => 'New with packaging'],

            // Home & Furniture
            ['title' => 'Gaming Chair', 'title_ar' => 'كرسي ألعاب', 'description' => 'Ergonomic gaming chair with RGB lighting', 'description_ar' => 'كرسي ألعاب مريح مع إضاءة RGB', 'price' => 320.00, 'condition' => 'New with packaging'],
            ['title' => 'Coffee Table', 'title_ar' => 'طاولة قهوة', 'description' => 'Modern wooden coffee table', 'description_ar' => 'طاولة قهوة خشبية حديثة', 'price' => 200.00, 'condition' => 'New without packaging'],
            ['title' => 'Floor Lamp', 'title_ar' => 'مصباح أرضي', 'description' => 'Modern LED floor lamp', 'description_ar' => 'مصباح أرضي LED حديث', 'price' => 120.00, 'condition' => 'New with packaging'],
            ['title' => 'Bookshelf', 'title_ar' => 'رف كتب', 'description' => 'Wooden bookshelf with 5 shelves', 'description_ar' => 'رف كتب خشبي بـ 5 أرفف', 'price' => 180.00, 'condition' => 'Good'],

            // Sports & Fitness
            ['title' => 'Yoga Mat', 'title_ar' => 'سجادة يوغا', 'description' => 'Non-slip yoga mat for exercise', 'description_ar' => 'سجادة يوغا مانعة للانزلاق', 'price' => 45.00, 'condition' => 'New with packaging'],
            ['title' => 'Dumbbells Set', 'title_ar' => 'مجموعة دمبل', 'description' => 'Adjustable dumbbells for home gym', 'description_ar' => 'دمبل قابل للتعديل للصالة المنزلية', 'price' => 150.00, 'condition' => 'Very good'],
            ['title' => 'Basketball', 'title_ar' => 'كرة سلة', 'description' => 'Official size basketball', 'description_ar' => 'كرة سلة بالحجم الرسمي', 'price' => 35.00, 'condition' => 'New with packaging'],
            ['title' => 'Running Shoes', 'title_ar' => 'أحذية جري', 'description' => 'Lightweight running shoes', 'description_ar' => 'أحذية جري خفيفة الوزن', 'price' => 120.00, 'condition' => 'New without packaging'],

            // Books & Education
            ['title' => 'Programming Book', 'title_ar' => 'كتاب برمجة', 'description' => 'Learn programming fundamentals', 'description_ar' => 'تعلم أساسيات البرمجة', 'price' => 50.00, 'condition' => 'Good'],
            ['title' => 'Arabic Literature', 'title_ar' => 'أدب عربي', 'description' => 'Classic Arabic literature collection', 'description_ar' => 'مجموعة أدب عربي كلاسيكي', 'price' => 75.00, 'condition' => 'New with packaging'],

            // Automotive
            ['title' => 'Car Phone Holder', 'title_ar' => 'حامل هاتف للسيارة', 'description' => 'Universal car phone mount', 'description_ar' => 'حامل هاتف عالمي للسيارة', 'price' => 25.00, 'condition' => 'New with packaging'],
            ['title' => 'Car Charger', 'title_ar' => 'شاحن سيارة', 'description' => 'Fast charging car adapter', 'description_ar' => 'محول شحن سريع للسيارة', 'price' => 30.00, 'condition' => 'New with packaging'],

            // Beauty & Personal Care
            ['title' => 'Perfume', 'title_ar' => 'عطر', 'description' => 'Luxury fragrance for men/women', 'description_ar' => 'عطر فاخر للرجال والنساء', 'price' => 80.00, 'condition' => 'New without packaging'],
            ['title' => 'Hair Dryer', 'title_ar' => 'مجفف شعر', 'description' => 'Professional hair dryer', 'description_ar' => 'مجفف شعر مهني', 'price' => 60.00, 'condition' => 'Very good'],

            // Kitchen & Appliances
            ['title' => 'Blender', 'title_ar' => 'خلاط', 'description' => 'High-speed blender for smoothies', 'description_ar' => 'خلاط عالي السرعة للعصائر', 'price' => 90.00, 'condition' => 'New with packaging'],
            ['title' => 'Coffee Maker', 'title_ar' => 'صانعة قهوة', 'description' => 'Automatic coffee maker', 'description_ar' => 'صانعة قهوة أوتوماتيكية', 'price' => 120.00, 'condition' => 'Good']
        ];

        $itemIndex = 0;
        $totalItems = 0;

        // First, create regular items for all users (3 per user, except Ahmed gets 0)
        foreach ($users as $userIndex => $user) {
            // Skip Ahmed for regular items - he'll get special treatment
            $itemsToCreate = ($user->firstname === 'Ahmed' && $user->lastname === 'Moloude') ? 0 : 3;

            for ($i = 0; $i < $itemsToCreate; $i++) {
                $template = $itemTemplates[$itemIndex % count($itemTemplates)];

                // Mixed store ownership: 60% belong to store, 40% belong directly to user
                $storeId = null;
                if ($user->has_store && rand(1, 100) <= 50) {
                    // 60% chance to assign to store if user has one
                    $userStore = collect($stores)->firstWhere('user_id', $user->id);
                    $storeId = $userStore?->id;
                }

                $item = $this->createSingleItem($template, $user, $storeId, $categories, $brands, $totalItems);

                // Add favorite category to store if item belongs to store
                if ($storeId) {
                    $this->addCategoryToStoreFavorites($storeId, $item->category_id);
                }

                    $this->addItemImages($item);

                $totalItems++;
                $itemIndex++;

                if ($totalItems % 50 === 0) {
                    Log::info("Created {$totalItems} items");
                }
            }
        }

        // Create Ahmed's special store with 50 items
        if ($ahmedUser && $ahmedStore) {
            Log::info("🎯 Creating Ahmed's special store with 50 items...");

            for ($i = 0; $i < 50; $i++) {
                $template = $itemTemplates[$itemIndex % count($itemTemplates)];

                $item = $this->createSingleItem($template, $ahmedUser, $ahmedStore->id, $categories, $brands, $totalItems);

                // Add favorite category to Ahmed's store
                $this->addCategoryToStoreFavorites($ahmedStore->id, $item->category_id);

                $this->addItemImages($item);

                $totalItems++;
                $itemIndex++;

                if (($i + 1) % 10 === 0) {
                    Log::info("Created " . ($i + 1) . "/50 items for Ahmed's store");
                }
            }

            Log::info("✅ Successfully created Ahmed's special store with 50 items");
        }

        Log::info("✅ Successfully created {$totalItems} items total");
    }

    /**
     * Create a single item
     */
    private function createSingleItem($template, $user, $storeId, $categories, $brands, $totalItems): Item
    {
        return Item::create([
            'title' => $template['title'] . " #{$totalItems}",
            'title_ar' => $template['title_ar'] . " #{$totalItems}",
            'description' => $template['description'],
            'description_ar' => $template['description_ar'],
            'price' => $template['price'] + rand(-50, 100),
            'condition' => $template['condition'],
            'quantity' => rand(1, 10),
            'brand_id' => $brands->random()->id,
            'category_id' => $categories->random()->id,
            'user_id' => $user->id,
            'store_id' => $storeId,
            'status' => eItemStatus::APPROVED,
            'sold_out' => rand(0, 20) === 0, // 5% chance to be sold out
            'is_promoted' => rand(0, 4) === 0, // 25% chance to be promoted
            'has_promotion' => rand(0, 3) === 0, // 33% chance to have promotion
            'promotion_percentage' => rand(0, 3) === 0 ? rand(5, 30) : 0
        ]);
    }

    /**
     * Add category to store's favorite categories if not already added
     */
    private function addCategoryToStoreFavorites($storeId, $categoryId): void
    {
        try {
            // Check if this category is already in store's favorites
            $exists = DB::table('store_favorite_categories')
                ->where('store_id', $storeId)
                ->where('category_id', $categoryId)
                ->exists();

            if (!$exists) {
                DB::table('store_favorite_categories')->insert([
                    'store_id' => $storeId,
                    'category_id' => $categoryId,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);

                Log::info("Added category {$categoryId} to store {$storeId} favorites");
            }
        } catch (\Exception $e) {
            Log::error("Failed to add category to store favorites: " . $e->getMessage());
        }
    }

    /**
     * Add images to item
     */
    private function addItemImages(Item $item): void
    {
        try {
            Log::info("Adding images to item: {$item->title}");

            // Get random product image URLs
            $imageUrls = $this->imageDownloadService->getRandomProductImages(rand(2, 4));

            // Download and optimize through ImageOptimizationService
            $imageResults = $this->imageDownloadService->downloadMultipleImages($imageUrls, 'item_images');

            foreach ($imageResults as $index => $imageData) {
                // Create media record with URL returned from ImageOptimizationService
                $media = Media::create([
                    'type' => 'image/webp',
                    'url' => $imageData['url'], // This is the final optimized URL from SFTP
                ]);

                // Link media to item with order
                ItemMedia::create([
                    'item_id' => $item->id,
                    'media_id' => $media->id,
                    'order' => $index
                ]);

                Log::info("Added item image", [
                    'item' => $item->title,
                    'media_id' => $media->id,
                    'order' => $index,
                    'final_url' => $imageData['url']
                ]);
            }

            Log::info("Successfully added " . count($imageResults) . " optimized images to item: {$item->title}");

        } catch (\Exception $e) {
            Log::error("Failed to add images to item {$item->title}: " . $e->getMessage());
        }
    }

    /**
     * Create orders and payments (50-80 orders)
     */
    private function createOrdersAndPayments(array $users, array $stores): void
    {
        Log::info('💳 Creating 50-80 orders and payments...');

        $items = Item::all();
        $paymentProviders = EPaymentProvider::all();
        $orderCount = 0;
        $maxOrders = rand(50, 80);

        // Create orders
        for ($i = 0; $i < $maxOrders; $i++) {
            $buyer = $users[array_rand($users)];
            $item = $items->random();
            $seller = $item->user;

            // Don't let users buy from themselves
            if ($buyer->id === $seller->id) {
                continue;
            }

            $deliveryCharge = rand(5, 20);
            $itemTotal = $item->price + $deliveryCharge;

            $order = Order::create([
                'user_id' => $buyer->id,
                'store_id' => $item->store_id,
                'status' => 'pending',
                'total' => $itemTotal,
                'delivery_charge' => $deliveryCharge,
                'is_paid' => 0,
                'is_cash_on_delivery' => 0,
                'location_id' => $buyer->location_id
            ]);

            // Create order item relationship
            $order->orderItems()->create([
                'item_id' => $item->id,
                'name' => $item->title,
                'description' => $item->description,
                'price' => $item->price,
                'quantity' => 1,
                'total' => $item->price,
                'image_url' => $item->images->first()?->url ?? null
            ]);



            $orderCount++;

            if ($orderCount % 20 === 0) {
                Log::info("Created {$orderCount}/{$maxOrders} orders");
            }
        }

        Log::info("✅ Successfully created {$orderCount} orders");
    }

    /**
     * Create payment proof with screenshot
     */
    private function createPaymentProof(Order $order, EPaymentProvider $provider): void
    {
        try {
            Log::info("Creating payment proof for order #{$order->id}");

            // Get payment screenshot URL
            $screenshotUrl = $this->imageDownloadService->getPaymentScreenshot();

            // Download and optimize through ImageOptimizationService
            $imageData = $this->imageDownloadService->downloadAndOptimize($screenshotUrl, 'payment_proofs');

            // Get the first item from the order
            $firstOrderItem = $order->orderItems()->first();

            PaymentProof::create([
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'item_id' => $firstOrderItem?->item_id,
                'store_id' => $order->store_id,
                'provider_id' => $provider->id,
                'screenshot' => $imageData['url'], // This is the final optimized URL from SFTP
                'amount' => $order->total,
                'reference_number' => 'REF' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT),
                'status' => ['pending', 'verified', 'rejected'][rand(0, 2)],
                'to_store' => $order->store_id ? true : false
            ]);

            Log::info("Created payment proof for order #{$order->id}", [
                'screenshot_url' => $imageData['url'],
                'provider' => $provider->name
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to create payment proof for order #{$order->id}: " . $e->getMessage());
        }
    }

    /**
     * Create discussions between users (30-50 discussions)
     */
    private function createDiscussions(array $users): void
    {
        Log::info('💬 Creating 30-50 discussions...');

        $items = Item::all();
        $discussionCount = 0;
        $maxDiscussions = rand(30, 50);

        // Create discussions
        for ($i = 0; $i < $maxDiscussions; $i++) {
            $buyer = $users[array_rand($users)];
            $item = $items->random();
            $seller = $item->user;

            // Don't let users discuss with themselves
            if ($buyer->id === $seller->id) {
                continue;
            }

            $discussion = Discution::create([
                'item_id' => $item->id,
                'buyer_id' => $buyer->id,
                'seller_id' => $seller->id,
                'store_id' => $item->store_id,
                'is_store_discussion' => $item->store_id ? true : false
            ]);

            // Add messages to discussion
            $this->addDiscussionMessages($discussion, $buyer, $seller, $item);

            $discussionCount++;

            if ($discussionCount % 10 === 0) {
                Log::info("Created {$discussionCount}/{$maxDiscussions} discussions");
            }
        }

        Log::info("✅ Successfully created {$discussionCount} discussions");
    }

    /**
     * Add messages to discussion
     */
    private function addDiscussionMessages(Discution $discussion, User $buyer, User $seller, Item $item): void
    {
        $messages = [
            [
                'sender_id' => $buyer->id,
                'content' => "Hello! I'm interested in your {$item->title}. Is it still available?",
                'is_an_offer' => false
            ],
            [
                'sender_id' => $seller->id,
                'content' => "Yes, it's still available! It's in excellent condition.",
                'is_an_offer' => false
            ],
            [
                'sender_id' => $buyer->id,
                'content' => "Would you accept " . ($item->price - 50) . " for it?",
                'is_an_offer' => true,
                'price' => $item->price - 50
            ],
            [
                'sender_id' => $seller->id,
                'content' => "I can do " . ($item->price - 25) . ". That's my best price.",
                'is_an_offer' => true,
                'price' => $item->price - 25
            ],
            [
                'sender_id' => $buyer->id,
                'content' => "Deal! When can I pick it up?",
                'is_an_offer' => false
            ]
        ];

        foreach ($messages as $index => $messageData) {
            DiscutionMessage::create([
                'discution_id' => $discussion->id,
                'sender_id' => $messageData['sender_id'],
                'content' => $messageData['content'],
                'is_an_offer' => $messageData['is_an_offer'],
                'price' => $messageData['price'] ?? null,
                'is_store_discussion' => $discussion->is_store_discussion,
                'created_at' => Carbon::now()->subMinutes((count($messages) - $index) * 10)
            ]);
        }
    }
}
