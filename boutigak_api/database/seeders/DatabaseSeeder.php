<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Database\Seeders\BrandSeeder;
use Database\Seeders\CategorySeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {

        $this->call(CategorySeeder::class);
        $this->call(BrandSeeder::class);
        $this->call(StoreTypeSeeder::class);
        $this->call(UserRolePermissionSeeder::class);
        $this->call(PromoCodeSeeder::class);
        $this->call(EPaymentProviderSeeder::class);
        $this->call(DeliveryChargeSeeder::class);

        // // Rich data seeder - comment out for production
        // $this->call(RichDataSeeder::class);
    }
}
