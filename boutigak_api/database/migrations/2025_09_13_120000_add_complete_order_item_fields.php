<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('order_item', function (Blueprint $table) {
            // Check if columns exist before adding them
            if (!Schema::hasColumn('order_item', 'title')) {
                $table->string('title')->nullable()->after('name');
            }
            if (!Schema::hasColumn('order_item', 'title_ar')) {
                $table->string('title_ar')->nullable()->after('title');
            }
            if (!Schema::hasColumn('order_item', 'description_ar')) {
                $table->text('description_ar')->nullable()->after('description');
            }
            if (!Schema::hasColumn('order_item', 'condition')) {
                $table->string('condition')->nullable()->after('description_ar');
            }
            if (!Schema::hasColumn('order_item', 'brand_id')) {
                $table->unsignedBigInteger('brand_id')->nullable()->after('condition');
            }
            if (!Schema::hasColumn('order_item', 'category_id')) {
                $table->unsignedBigInteger('category_id')->nullable()->after('brand_id');
            }
            if (!Schema::hasColumn('order_item', 'store_id')) {
                $table->unsignedBigInteger('store_id')->nullable()->after('category_id');
            }
            if (!Schema::hasColumn('order_item', 'brand_name')) {
                $table->string('brand_name')->nullable()->after('store_id');
            }
            if (!Schema::hasColumn('order_item', 'category_name')) {
                $table->string('category_name')->nullable()->after('brand_name');
            }
            if (!Schema::hasColumn('order_item', 'store_name')) {
                $table->string('store_name')->nullable()->after('category_name');
            }
            if (!Schema::hasColumn('order_item', 'category_details')) {
                $table->json('category_details')->nullable()->after('store_name');
            }
            if (!Schema::hasColumn('order_item', 'has_promotion')) {
                $table->boolean('has_promotion')->default(false)->after('category_details');
            }
            if (!Schema::hasColumn('order_item', 'promotion_percentage')) {
                $table->decimal('promotion_percentage', 5, 2)->nullable()->after('has_promotion');
            }
            if (!Schema::hasColumn('order_item', 'original_price')) {
                $table->decimal('original_price', 10, 2)->nullable()->after('promotion_percentage');
            }
        });
    }

    public function down()
    {
        Schema::table('order_item', function (Blueprint $table) {
            $columnsToCheck = [
                'title', 'title_ar', 'description_ar', 'condition',
                'brand_id', 'category_id', 'store_id',
                'brand_name', 'category_name', 'store_name',
                'category_details', 'has_promotion', 'promotion_percentage', 'original_price'
            ];
            
            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('order_item', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
