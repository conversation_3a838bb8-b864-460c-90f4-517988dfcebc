<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('order_modifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->enum('type', ['removed', 'quantity_changed', 'added']);
            $table->unsignedBigInteger('item_id')->nullable();
            $table->string('item_name')->default('Unknown Item');
            $table->integer('old_quantity')->nullable();
            $table->integer('new_quantity')->nullable();
            $table->decimal('old_price', 10, 2)->nullable();
            $table->decimal('new_price', 10, 2)->nullable();
            $table->text('reason')->nullable();
            $table->timestamps();

            $table->foreign('order_id')->references('id')->on('order')->onDelete('cascade');
            $table->foreign('item_id')->references('id')->on('item')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('order_modifications');
    }
}; 