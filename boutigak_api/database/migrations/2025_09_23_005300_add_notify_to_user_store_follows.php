<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_store_follows', function (Blueprint $table) {
            if (!Schema::hasColumn('user_store_follows', 'notify')) {
                $table->boolean('notify')->default(true)->after('user_id');
                $table->index('notify');
            }
        });
    }

    public function down(): void
    {
        Schema::table('user_store_follows', function (Blueprint $table) {
            if (Schema::hasColumn('user_store_follows', 'notify')) {
                $table->dropIndex(['notify']);
                $table->dropColumn('notify');
            }
        });
    }
};
