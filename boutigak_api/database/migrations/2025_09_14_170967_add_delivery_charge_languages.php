<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeliveryChargeLanguages extends Migration
{
    public function up()
    {
        Schema::table('delivery_charges', function (Blueprint $table) {
            $table->string('type_en')->nullable();
            $table->string('type_fr')->nullable();
            $table->string('type_ar')->nullable();
        });
    }

    public function down()
    {
        Schema::table('delivery_charges', function (Blueprint $table) {
            $table->dropColumn(['type_en', 'type_fr', 'type_ar']);
        });
    }
}