<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsReadToDiscutionMessage extends Migration
{
    public function up()
    {
        Schema::table('discution_message', function (Blueprint $table) {
            $table->boolean('is_read')->default(false)->after('is_store_discussion');
        });
    }

    public function down()
    {
        Schema::table('discution_message', function (Blueprint $table) {
            $table->dropColumn('is_read');
        });
    }
}