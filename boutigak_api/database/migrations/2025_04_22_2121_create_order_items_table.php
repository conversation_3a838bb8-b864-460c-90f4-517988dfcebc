<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('order_item', function (Blueprint $table) {
            // Basic item information (already exists)
            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->string('image_url')->nullable();

            // Add localized fields
            $table->string('title')->nullable()->after('name');
            $table->string('title_ar')->nullable()->after('title');
            $table->text('description_ar')->nullable()->after('description');

            // Add item context fields
            $table->string('condition')->nullable()->after('description_ar');
            $table->unsignedBigInteger('brand_id')->nullable()->after('condition');
            $table->unsignedBigInteger('category_id')->nullable()->after('brand_id');
            $table->unsignedBigInteger('store_id')->nullable()->after('category_id');

            // Add cached names for performance
            $table->string('brand_name')->nullable()->after('store_id');
            $table->string('category_name')->nullable()->after('brand_name');
            $table->string('store_name')->nullable()->after('category_name');

            // Add category details as JSON
            $table->json('category_details')->nullable()->after('store_name');

            // Add promotion fields
            $table->boolean('has_promotion')->default(false)->after('category_details');
            $table->decimal('promotion_percentage', 5, 2)->nullable()->after('has_promotion');
            $table->decimal('original_price', 10, 2)->nullable()->after('promotion_percentage');
        });
    }

    public function down()
    {
        Schema::table('order_item', function (Blueprint $table) {
            $table->dropColumn([
                'title', 'title_ar', 'description_ar', 'condition',
                'brand_id', 'category_id', 'store_id',
                'brand_name', 'category_name', 'store_name',
                'category_details', 'has_promotion', 'promotion_percentage', 'original_price'
            ]);
        });
    }
};
