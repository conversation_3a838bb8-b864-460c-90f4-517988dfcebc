<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\OrderItem;
use App\Models\Item;

return new class extends Migration
{
    public function up(): void
    {
        // Update existing OrderItem records that have null names
        // $orderItems = OrderItem::whereNull('name')->orWhere('name', '')->get();
        
        // foreach ($orderItems as $orderItem) {
        //     $item = Item::find($orderItem->item_id);
        //     if ($item) {
        //         $orderItem->update([
        //             'name' => $item->title,
        //             'description' => $item->description ?? $orderItem->description
        //         ]);
        //     }
        // }
    }

    public function down(): void
    {
        // No rollback needed for this data fix
    }
}; 