<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('order', function (Blueprint $table) {
            $table->boolean('modified_by_store')->default(false)->after('status');
            $table->text('modification_reason_en')->nullable()->after('modified_by_store');
            $table->text('modification_reason_fr')->nullable()->after('modification_reason_en');
            $table->text('modification_reason_ar')->nullable()->after('modification_reason_fr');
        });
    }

    public function down(): void
    {
        Schema::table('order', function (Blueprint $table) {
            $table->dropColumn('modified_by_store');
            $table->dropColumn('modification_reason_en');
            $table->dropColumn('modification_reason_fr');
            $table->dropColumn('modification_reason_ar');
        });
    }
};