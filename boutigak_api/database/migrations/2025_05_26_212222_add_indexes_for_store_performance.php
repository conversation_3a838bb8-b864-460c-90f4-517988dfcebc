<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store', function (Blueprint $table) {
            // Index for promoted store query
            $table->index(['is_promoted', 'promotion_position'], 'stores_promoted_position_idx');
            
            // Index for user_id foreign key if not exists
            if (!$this->indexExists('store', 'stores_user_id_index')) {
                $table->index('user_id', 'stores_user_id_idx');
            }
            
            // Composite index for type queries
            $table->index(['type_id', 'is_promoted'], 'stores_type_promoted_idx');
        });

        Schema::table('new_user_subscriptions', function (Blueprint $table) {
            // Index for active new_user_subscriptions query
            $table->index(['user_id', 'end_date'], 'subscriptions_user_end_date_idx');
        });

        Schema::table('store_followers', function (Blueprint $table) {
            // Index for followers relationship if table exists
            if (Schema::hasTable('store_followers')) {
                $table->index(['user_id', 'store_id'], 'store_followers_user_store_idx');
            }
        });

        // Media table doesn't have mediable_type/mediable_id columns
        // Relationships are handled through store_media and item_media pivot tables
        Schema::table('store_media', function (Blueprint $table) {
            // Index for store media relationship
            if (!$this->indexExists('store_media', 'store_media_store_id_index')) {
                $table->index('store_id', 'store_media_store_id_idx');
            }
            if (!$this->indexExists('store_media', 'store_media_media_id_index')) {
                $table->index('media_id', 'store_media_media_id_idx');
            }
        });

        if (Schema::hasTable('item_media')) {
            Schema::table('item_media', function (Blueprint $table) {
                // Index for item media relationship
                if (!$this->indexExists('item_media', 'item_media_item_id_index')) {
                    $table->index('item_id', 'item_media_item_id_idx');
                }
                if (!$this->indexExists('item_media', 'item_media_media_id_index')) {
                    $table->index('media_id', 'item_media_media_id_idx');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('store', function (Blueprint $table) {
            $table->dropIndex('stores_promoted_position_idx');
            if ($this->indexExists('store', 'stores_user_id_idx')) {
                $table->dropIndex('stores_user_id_idx');
            }
            $table->dropIndex('stores_type_promoted_idx');
        });

        Schema::table('new_user_subscriptions', function (Blueprint $table) {
            $table->dropIndex('subscriptions_user_end_date_idx');
        });

        if (Schema::hasTable('store_followers')) {
            Schema::table('store_followers', function (Blueprint $table) {
                $table->dropIndex('store_followers_user_store_idx');
            });
        }

        Schema::table('store_media', function (Blueprint $table) {
            $table->dropIndex('store_media_store_id_idx');
            $table->dropIndex('store_media_media_id_idx');
        });

        if (Schema::hasTable('item_media')) {
            Schema::table('item_media', function (Blueprint $table) {
                $table->dropIndex('item_media_item_id_idx');
                $table->dropIndex('item_media_media_id_idx');
            });
        }
    }

    /**
     * Check if an index exists on a table using native database queries
     */
    private function indexExists(string $table, string $indexName): bool
    {
        $connection = Schema::getConnection();
        $databaseName = $connection->getDatabaseName();
        
        // For PostgreSQL
        if ($connection->getDriverName() === 'pgsql') {
            $result = DB::select("
                SELECT 1 
                FROM pg_indexes 
                WHERE tablename = ? AND indexname = ?
            ", [$table, $indexName]);
            
            return count($result) > 0;
        }
        
        // For MySQL
        if ($connection->getDriverName() === 'mysql') {
            $result = DB::select("
                SELECT 1 
                FROM information_schema.statistics 
                WHERE table_schema = ? AND table_name = ? AND index_name = ?
            ", [$databaseName, $table, $indexName]);
            
            return count($result) > 0;
        }
        
        // For SQLite
        if ($connection->getDriverName() === 'sqlite') {
            $result = DB::select("
                SELECT 1 
                FROM sqlite_master 
                WHERE type = 'index' AND name = ?
            ", [$indexName]);
            
            return count($result) > 0;
        }
        
        // Default to false if database type is not supported
        return false;
    }
};