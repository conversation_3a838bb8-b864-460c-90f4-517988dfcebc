# 🧪 Comprehensive Testing Guide for Boutigak App

## 📋 Pre-Testing Setup

### 1. Database Seeding
```bash
# Run basic seeders first
php artisan db:seed --class=CategorySeeder
php artisan db:seed --class=BrandSeeder
php artisan db:seed --class=StoreTypeSeeder
php artisan db:seed --class=EPaymentProviderSeeder

# Run the rich data seeder
php artisan db:seed-rich --clean
```

### 2. Test User Credentials
Based on your updated seeder:
- **<PERSON>**: `********` / `********` (Store Owner)
- **Fatima Abdallah**: `********` / `********` (Store Owner)
- **<PERSON>**: `********` / `********` (Regular User)
- **Aicha Mint Salem**: `********` / `********` (Regular User)

---

## 🎯 Test Cases by Feature

### 🔐 **AUTHENTICATION TESTING**

#### TC-001: User Login Flow
**Objective**: Test login functionality with seeded users
**Steps**:
1. Open the app
2. Navigate to Login page
3. Enter phone: `********`
4. Enter password: `********`
5. Tap Login button

**Expected Results**:
- ✅ Login successful
- ✅ Redirected to main app (ZoomDrawerWrapper)
- ✅ User data loaded (Ahmed Mohamed)
- ✅ Authentication state updated

#### TC-002: Test All User Accounts
**Objective**: Verify all seeded users can login
**Steps**: Repeat TC-001 for each user:
- `********` (Ahmed - Store Owner, Arabic)
- `********` (Fatima - Store Owner, French)
- `********` (Omar - Regular User, English)
- `********` (Aicha - Regular User, Arabic)

**Expected Results**:
- ✅ All users login successfully
- ✅ Correct user data displayed
- ✅ Language preferences applied
- ✅ Store ownership status correct

---

### 🏠 **HOME PAGE TESTING**

#### TC-003: Home Page Data Loading
**Objective**: Test home page displays seeded data correctly
**Steps**:
1. Login as any user
2. Navigate to Home tab
3. Pull to refresh
4. Scroll through items

**Expected Results**:
- ✅ 16 items displayed in grid
- ✅ Real product images loaded
- ✅ Item titles in correct language
- ✅ Prices displayed correctly
- ✅ Promotion badges visible (some items)
- ✅ Infinite scroll works

#### TC-004: Item Details View
**Objective**: Test item detail page functionality
**Steps**:
1. From home page, tap any item
2. Swipe through images
3. Check item details
4. Test like/unlike functionality
5. Test share functionality

**Expected Results**:
- ✅ Multiple real images displayed
- ✅ Item details correct (title, price, description)
- ✅ Arabic/English content based on language
- ✅ Like button works (if authenticated)
- ✅ Share functionality works
- ✅ Seller information displayed

---

### 🏪 **STORE TESTING**

#### TC-005: Store Listing and Details
**Objective**: Test store functionality with seeded stores
**Steps**:
1. Navigate to Store tab
2. View promoted stores section
3. Tap on "Boutique Ahmed Electronics"
4. Browse store items
5. Test follow/unfollow functionality

**Expected Results**:
- ✅ 2 stores displayed (Ahmed's & Fatima's)
- ✅ Store images loaded correctly
- ✅ Store details accurate
- ✅ Store items displayed (4 items each)
- ✅ Follow/unfollow works
- ✅ Opening hours displayed

#### TC-006: Store Owner View
**Objective**: Test store management for store owners
**Steps**:
1. Login as Ahmed (`********`)
2. Navigate to Profile → My Store
3. View store statistics
4. Check store items
5. Test store settings

**Expected Results**:
- ✅ Store dashboard accessible
- ✅ Store statistics displayed
- ✅ Store items listed (4 items)
- ✅ Store images displayed
- ✅ Store settings editable

---

### 🔍 **SEARCH TESTING**

#### TC-007: Search Functionality
**Objective**: Test search with seeded data
**Steps**:
1. Navigate to Search tab
2. Search for "iPhone"
3. Search for "Samsung"
4. Search for "Nike"
5. Test category filters

**Expected Results**:
- ✅ Relevant items returned
- ✅ Search results accurate
- ✅ Images displayed correctly
- ✅ Category filtering works
- ✅ No results handled gracefully

---

### 💬 **MESSAGING/DISCUSSIONS TESTING**

#### TC-008: View Existing Discussions
**Objective**: Test discussion functionality with seeded conversations
**Steps**:
1. Login as any user
2. Navigate to Inbox tab
3. View discussion list
4. Open a discussion
5. Read conversation history

**Expected Results**:
- ✅ 5-8 discussions visible
- ✅ Discussion previews correct
- ✅ Conversation history loaded
- ✅ Messages timestamped correctly
- ✅ Offer messages highlighted

#### TC-009: Create New Discussion
**Objective**: Test creating new discussions
**Steps**:
1. Login as Omar (`********`)
2. Find an item from Ahmed or Fatima
3. Tap "Contact Seller"
4. Send a message
5. Check if discussion created

**Expected Results**:
- ✅ Discussion created successfully
- ✅ Message sent and displayed
- ✅ Real-time updates work
- ✅ Seller receives notification

---

### 🛒 **ORDER TESTING**

#### TC-010: View Existing Orders
**Objective**: Test order management with seeded orders
**Steps**:
1. Login as any user
2. Navigate to Profile → My Orders
3. View order list
4. Open order details
5. Check payment status

**Expected Results**:
- ✅ 8-10 orders displayed
- ✅ Order statuses varied (pending, confirmed, delivered, cancelled)
- ✅ Order details accurate
- ✅ Payment screenshots visible (for paid orders)
- ✅ Order history complete

#### TC-011: Store Owner Order Management
**Objective**: Test order management for store owners
**Steps**:
1. Login as Ahmed (`********`)
2. Navigate to store orders
3. View incoming orders
4. Test order status updates
5. Check payment proofs

**Expected Results**:
- ✅ Store orders displayed
- ✅ Order details accessible
- ✅ Status update functionality works
- ✅ Payment proofs visible
- ✅ Customer information displayed

---

### 💳 **PAYMENT TESTING**

#### TC-012: Payment Proof Viewing
**Objective**: Test payment proof functionality
**Steps**:
1. Login as any user
2. Find a paid order
3. View payment screenshot
4. Check payment details
5. Test payment verification status

**Expected Results**:
- ✅ Payment screenshots displayed
- ✅ Payment details accurate
- ✅ Reference numbers visible
- ✅ Payment status correct
- ✅ Provider information shown

#### TC-013: Create New Payment Proof
**Objective**: Test payment proof submission
**Steps**:
1. Create a new order
2. Navigate to payment
3. Upload payment screenshot
4. Fill payment details
5. Submit payment proof

**Expected Results**:
- ✅ Payment form accessible
- ✅ Image upload works
- ✅ Payment details saved
- ✅ Proof submitted successfully
- ✅ Status tracking works

---

### 👤 **PROFILE TESTING**

#### TC-014: User Profile Management
**Objective**: Test profile functionality
**Steps**:
1. Login as any user
2. Navigate to Profile
3. View user information
4. Test profile editing
5. Check activity tracking

**Expected Results**:
- ✅ User information displayed correctly
- ✅ Profile editing works
- ✅ Language preferences applied
- ✅ Activity timestamps updated
- ✅ Settings accessible

#### TC-015: My Items Management
**Objective**: Test item management for users
**Steps**:
1. Login as any user
2. Navigate to Profile → My Items
3. View item list (4 items per user)
4. Edit an item
5. Test item status changes

**Expected Results**:
- ✅ 4 items displayed per user
- ✅ Item images loaded
- ✅ Item editing works
- ✅ Status updates functional
- ✅ Item statistics accurate

---

### 🌐 **LOCALIZATION TESTING**

#### TC-016: Language Switching
**Objective**: Test multilingual functionality
**Steps**:
1. Login as Ahmed (Arabic user)
2. Check Arabic content display
3. Switch to French
4. Login as Fatima (French user)
5. Switch to English

**Expected Results**:
- ✅ Arabic content displays correctly
- ✅ French content displays correctly
- ✅ English content displays correctly
- ✅ Language switching works
- ✅ User preferences saved

---

### 📱 **IMAGE OPTIMIZATION TESTING**

#### TC-017: Image Loading Performance
**Objective**: Test image optimization and loading through ImageOptimizationService
**Steps**:
1. Browse items with multiple images
2. Check image loading speed from SFTP server
3. Test image quality (WebP format)
4. Check different image sizes
5. Verify images are served from your SFTP server
6. Test offline image caching

**Expected Results**:
- ✅ Images load quickly from SFTP server
- ✅ WebP format used (optimized by ImageOptimizationService)
- ✅ Image quality good despite optimization
- ✅ Images served from your configured SFTP base URL
- ✅ Responsive image sizes
- ✅ Caching works properly

#### TC-017b: Image Optimization Flow Verification
**Objective**: Verify images went through proper optimization pipeline
**Steps**:
1. Check Laravel logs for image processing details
2. Verify images are stored on SFTP server
3. Check image URLs point to SFTP server
4. Verify WebP conversion happened
5. Check file sizes are optimized

**Expected Results**:
- ✅ Logs show "Image processing details" entries
- ✅ Images stored on SFTP with correct paths
- ✅ URLs start with your SFTP base URL
- ✅ All images converted to WebP format
- ✅ File sizes optimized (smaller than originals)

---

## 🚨 **EDGE CASE TESTING**

#### TC-018: Network Connectivity
**Objective**: Test app behavior with poor connectivity
**Steps**:
1. Disable internet connection
2. Try to browse items
3. Enable connection
4. Test data refresh
5. Check offline functionality

**Expected Results**:
- ✅ Graceful offline handling
- ✅ Cached data displayed
- ✅ Sync on reconnection
- ✅ Error messages appropriate
- ✅ No app crashes

#### TC-019: Data Validation
**Objective**: Test data integrity and validation
**Steps**:
1. Check all seeded data consistency
2. Verify foreign key relationships
3. Test data constraints
4. Check image URL validity
5. Verify user permissions

**Expected Results**:
- ✅ All data relationships intact
- ✅ No broken image links
- ✅ User permissions correct
- ✅ Data validation works
- ✅ No orphaned records

---

## 📊 **PERFORMANCE TESTING**

#### TC-020: App Performance
**Objective**: Test app performance with rich data
**Steps**:
1. Measure app startup time
2. Test scrolling performance
3. Check memory usage
4. Test image loading speed
5. Monitor battery usage

**Expected Results**:
- ✅ Fast app startup (< 3 seconds)
- ✅ Smooth scrolling
- ✅ Reasonable memory usage
- ✅ Quick image loading
- ✅ Acceptable battery consumption

---

## ✅ **TESTING CHECKLIST**

### Before Testing:
- [ ] Database seeded successfully
- [ ] All images downloaded and optimized
- [ ] App compiled and installed
- [ ] Test environment configured

### During Testing:
- [ ] Document all bugs found
- [ ] Take screenshots of issues
- [ ] Note performance problems
- [ ] Check console logs for errors

### After Testing:
- [ ] Verify all test cases passed
- [ ] Document any failures
- [ ] Clean up test data if needed
- [ ] Report results to development team

---

## 🐛 **COMMON ISSUES TO WATCH FOR**

1. **Image Loading Issues**
   - Broken image URLs
   - Slow loading times
   - Image optimization failures

2. **Authentication Problems**
   - Token expiration
   - Login failures
   - Permission issues

3. **Data Inconsistencies**
   - Missing relationships
   - Incorrect user data
   - Order/payment mismatches

4. **UI/UX Issues**
   - Layout problems
   - Language display issues
   - Navigation problems

5. **Performance Issues**
   - Memory leaks
   - Slow API responses
   - App crashes

---

This comprehensive testing guide covers all major functionality of your Boutigak app using the rich seeded data. Follow each test case systematically to ensure your app works correctly with realistic data scenarios.
