<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StoreMedia extends Model
{
    protected $table = 'store_media';
    protected $with = ['media'];


    protected $fillable = [
        'media_id',
        'store_id',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    public function media()
    {
        return $this->belongsTo(Media::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }
}
