<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\services\BadgeService; 
use Illuminate\Support\Facades\Log;

class Notification extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'payment_id',
        'order_id',
        'store_id',
        'title',
        'message',
        'title_en',
        'title_fr',
        'title_ar',
        'message_en',
        'message_fr',
        'message_ar',
        'type',
        'is_read',
        'params'
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'params' => 'array'
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted()
    {
        static::created(function ($notification) {
            Log::info('Notification created', [
                'id' => $notification->id,
                'user_id' => $notification->user_id,
                'type' => $notification->type
            ]);
            self::incrementBadge($notification);
        });
    }

    /**
     * Increment badge count after notification creation
     */
    protected static function incrementBadge($notification)
    {
        try {
            $user = User::find($notification->user_id);
            if ($user) {
                $badgeService = app(BadgeService::class);
                
                $module = 'notifications';


                Log::info('notification type'.$notification->type);
                Log::info('module'.$module);
                Log::info('user id'.$user->id);



                
                $itemTypes = ['item_status_update', 'item_promoted', 'payment_approved', 'payment_rejected'];
                $messageTypes = ['new_offer' , 'new_message'];
                $storeOrderTypes = ['new_order', 'payment_received'];
                $userOrderTypes = ['order_status_updated', 'order_modified', 'order_auto_cancelled'];
                $notificationTypes = ['new_item', 'new_follower'];
                
                if (in_array($notification->type, $itemTypes)) {
                    $module = 'items';
                } elseif (in_array($notification->type, $messageTypes)) {
                    $module = 'messages';
                } elseif (in_array($notification->type, $storeOrderTypes)) {
                    $module = 'store-order';
                } elseif (in_array($notification->type, $userOrderTypes)) {
                    $module = 'user-order';
                } elseif (in_array($notification->type, $notificationTypes)) {
                    $module = 'notifications';
                }
                
                Log::info('Incrementing badge', [
                    'notification_id' => $notification->id,
                    'user_id' => $user->id,
                    'module' => $module
                ]);
                
                $badgeService->increment($user, $module);
            }
        } catch (\Exception $e) {
            Log::error('Failed to increment badge: ' . $e->getMessage(), [
                'notification_id' => $notification->id,
                'user_id' => $notification->user_id
            ]);
        }
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function payment()
    {
        return $this->belongsTo(PaymentProof::class, 'payment_id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }
}
