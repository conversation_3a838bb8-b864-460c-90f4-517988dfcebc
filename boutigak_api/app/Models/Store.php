<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Store extends Model
{
    use HasFactory;

    protected $table = 'store';

    protected $with = ['user' , 'images' , 'socialMediaLinks'];

    protected $fillable = [
        'name',
        'description',
        'user_id',
        'type_id',
        'location_id',
        'is_promoted',
        'promotion_position',
        'opening_time',
        'closing_time',
        'promotion_percentage',
        'has_promotion',
        'is_open'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'is_open' => 'boolean',
    ];

    public function setIsPromotedAttribute($value): void
    {
        $this->attributes['is_promoted'] = $value;

        if ($value === false || $value === 0 || $value === '0') {
            $this->attributes['promotion_position'] = null;
        }
    }

    public function scopePromoted($query)
    {
        return $query->where('is_promoted', true)
            ->orderBy('promotion_position', 'asc');
    }

    public function scopeNotPromoted($query)
    {
        return $query->where('is_promoted', false)
            ->orWhereNull('is_promoted');
    }

    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function type(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(StoreType::class, 'type_id');
    }

    public function location(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Location::class, 'location_id');
    }

    public function favoriteCategories(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'store_favorite_categories', 'store_id', 'category_id');
    }

    public function followers(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_store_follows', 'store_id', 'user_id');
    }

    public function images(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(StoreMedia::class, 'store_id');
    }

    public function items(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Item::class, 'store_id');
    }

    public function orders(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Order::class, 'store_id');
    }

    public function paymentProviders()
    {
        return $this->hasMany(StorePaymentProvider::class);
    }

    public function socialMediaLinks(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(StoreSocialMediaLink::class, 'store_id');
    }

    public function activeSocialMediaLinks(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(StoreSocialMediaLink::class, 'store_id')->where('is_active', true);
    }

    protected static function booted()
    {
        static::addGlobalScope('user_not_deleted', function ($query) {
            $query->whereHas('user', function ($q) {
                $q->where('is_deleted', false);
            });
        });

        // Clear cache when store is updated
        static::updated(function ($store) {
            self::clearPromotedStoresCache();
        });

        // Clear cache when store is created
        static::created(function ($store) {
            self::clearPromotedStoresCache();
        });

        // Clear cache when store is deleted
        static::deleted(function ($store) {
            self::clearPromotedStoresCache();
        });
    }

    /**
     * Clear promoted stores cache
     */
    public static function clearPromotedStoresCache()
    {
        // Clear cache for all languages and users
        $languages = ['fr', 'ar', 'en'];

        foreach ($languages as $lang) {
            // Clear guest cache
            Cache::forget("promoted_stores_{$lang}_");

            // Clear authenticated user caches (we can't clear all user caches efficiently,
            // so we'll rely on the shorter cache duration for authenticated users)
        }

        // Also clear general cache patterns
        Cache::forget('promoted_stores_*');
    }
}
