<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StoreSocialMediaLink extends Model
{
    use HasFactory;

    protected $table = 'store_social_media_links';

    protected $fillable = [
        'store_id',
        'platform',
        'link',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the store that owns the social media link.
     */
    public function store(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    /**
     * Scope to get only active links
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by platform
     */
    public function scopePlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }
}
