<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DiscutionMessage extends Model
{
    protected $table = 'discution_message';

    

    
    protected $fillable = [
        'discution_id',
        'sender_id',
        'content',
        'is_an_offer',
        'price',
        'reply_discution_id',
        'is_store_discussion',
        'is_read' 
    ];

    public function discution()
    {
        return $this->belongsTo(Discution::class, 'discution_id');
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }
}
