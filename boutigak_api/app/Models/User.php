<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, SoftDeletes;

    protected $with = ['deviceToken'];

    protected static function booted()
    {
        static::addGlobalScope('not_deleted', function ($query) {
            $query->where('is_deleted', false);
        });
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */

    protected $fillable = [
        'password',
        'firstname',
        'lastname',
        'phone',
        'invitationcode',
        'gender',
        'delete_reason',
        'has_store',
        'lang',
        'location_id',
        'deleted_at',
        'is_verified',
        'last_activity',
        'is_deleted'
    ];


    protected $dates = [
        'created_at',
        'updated_at',
        'last_activity',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'last_activity' => 'datetime',
    ];


    // liked items

    public function likedItems()
    {
        return $this->belongsToMany(Item::class, 'liked_item', 'user_id', 'item_id');
    }


    public function followedStores()
    {
        return $this->belongsToMany(Store::class, 'user_store_follows', 'user_id', 'store_id');
    }


    public function notifications(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Notification::class);
    }

    public function deviceToken(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(DeviceToken::class);
    }

    public function store(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Store::class, 'user_id');
    }

    public function orders(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function locations(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Location::class, 'user_locations', 'user_id', 'location_id')
                    ->withTimestamps();
    }

    public function subscriptions(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(NewUserSubscription::class, 'user_id');
    }

    public function roles(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'model_has_roles', 'model_id', 'role_id');
    }

    public function items(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Item::class);
    }

    public function badges()
    {
        return $this->hasMany(Badge::class);
    }

    // Blocking relationships
    public function blockedUsers()
    {
        return $this->belongsToMany(User::class, 'user_blocks', 'blocker_id', 'blocked_id')
                    ->withTimestamps();
    }

    public function blockedByUsers()
    {
        return $this->belongsToMany(User::class, 'user_blocks', 'blocked_id', 'blocker_id')
                    ->withTimestamps();
    }

    // Helper methods for blocking functionality
    public function hasBlocked($userId)
    {
        return $this->blockedUsers()->where('blocked_id', $userId)->exists();
    }

    public function isBlockedBy($userId)
    {
        return $this->blockedByUsers()->where('blocker_id', $userId)->exists();
    }

    public function isBlockedWith($userId)
    {
        return $this->hasBlocked($userId) || $this->isBlockedBy($userId);
    }
}
