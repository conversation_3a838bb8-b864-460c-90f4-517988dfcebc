<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    protected $table = 'order_item';

    protected $fillable = [
        'order_id',
        'item_id',
        'name',
        'title',
        'title_ar',
        'description',
        'description_ar',
        'price',
        'original_price',
        'quantity',
        'image_url',
        'total',
        'condition',
        'brand_id',
        'category_id',
        'store_id',
        'brand_name',
        'category_name',
        'store_name',
        'category_details',
        'has_promotion',
        'promotion_percentage'
    ];

    protected $casts = [
        'category_details' => 'array',
        'has_promotion' => 'boolean',
        'promotion_percentage' => 'decimal:2',
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'total' => 'decimal:2'
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function originalItem()
    {
        return $this->belongsTo(Item::class, 'item_id');
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Get title based on user language preference
     */
    public function getTitle($language = 'en')
    {
        switch ($language) {
            case 'ar':
                return !empty($this->title_ar) ? $this->title_ar : $this->title;
            default:
                return $this->title;
        }
    }

    /**
     * Get description based on user language preference
     */
    public function getDescription($language = 'en')
    {
        switch ($language) {
            case 'ar':
                return !empty($this->description_ar) ? $this->description_ar : $this->description;
            default:
                return $this->description;
        }
    }

    /**
     * Get localized category details
     */
    public function getLocalizedCategoryDetails($language = 'en')
    {
        if (!$this->category_details) {
            return [];
        }

        return collect($this->category_details)->map(function ($detail) use ($language) {
            $label = $detail['label_en'] ?? '';

            switch ($language) {
                case 'ar':
                    $label = $detail['label_ar'] ?? $label;
                    break;
                case 'fr':
                    $label = $detail['label_fr'] ?? $label;
                    break;
            }

            return [
                'label' => $label,
                'value' => $detail['value'] ?? ''
            ];
        })->toArray();
    }
}
