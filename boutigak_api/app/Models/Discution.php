<?php

namespace App\Models;

use App\Models\Item;
use Illuminate\Database\Eloquent\Model;

class Discution extends Model
{


    protected $with = ['seller', 'messages', 'item' , 'latestMessage' , 'buyer' , 'store'];
    protected $table = 'discution';

    protected $fillable = [
        'item_id',
        'buyer_id',
        'seller_id',
        'store_id',
        'is_store_discussion'
    ];




    protected $casts = [
        'is_store_discussion' => 'boolean',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    public function messages()
    {
        return $this->hasMany(DiscutionMessage::class);
    }

    public function seller()
    {
        return $this->belongsTo(User::class);
    }



    public function item(){

        return $this->belongsTo(Item::class);
    }


    public function buyer()
    {
        return $this->belongsTo(User::class, 'buyer_id');
    }

    public function latestMessage()
    {
        return $this->hasOne(DiscutionMessage::class, 'discution_id')
            ->latestOfMany()
            ->select([
                'discution_message.id',
                'discution_message.discution_id',
                'discution_message.content',
                'discution_message.is_an_offer',
                'discution_message.price',
                'discution_message.created_at',
                'discution_message.sender_id'
            ]);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }
    
}
