<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class NewStoreSubscription extends Model
{
    protected $fillable = ['user_id', 'price', 'start_date', 'end_date'];

    protected $table = 'new_store_subscriptions';

    protected $dates = ['start_date', 'end_date'];

    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
