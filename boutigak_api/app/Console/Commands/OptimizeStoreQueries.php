<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class OptimizeStoreQueries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stores:optimize';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize store queries by adding indexes and analyzing tables';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting store query optimization...');

        // Run the migration if not already run
        $this->info('Running performance migration...');
        $this->call('migrate', ['--path' => 'database/migrations/2024_01_15_000000_add_indexes_for_store_performance.php']);

        // Analyze tables for better query planning
        $this->info('Analyzing tables for better query planning...');
        
        $tables = ['stores', 'subscriptions', 'store_followers', 'media', 'users'];
        
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                DB::statement("ANALYZE TABLE {$table}");
                $this->info("Analyzed table: {$table}");
            }
        }

        // Show some statistics
        $this->info('Gathering statistics...');
        
        $promotedStoresCount = DB::table('stores')
            ->where('is_promoted', true)
            ->whereNotNull('promotion_position')
            ->count();
            
        $activeSubscriptionsCount = DB::table('subscriptions')
            ->where('end_date', '>', now())
            ->count();

        $this->info("Promoted stores with position: {$promotedStoresCount}");
        $this->info("Active subscriptions: {$activeSubscriptionsCount}");

        // Clear any existing cache
        $this->info('Clearing promoted stores cache...');
        \App\Models\Store::clearPromotedStoresCache();

        $this->info('Store query optimization completed successfully!');
        
        return 0;
    }
}
