<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\RichDataSeeder;
use Illuminate\Support\Facades\Log;

class SeedRichData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:seed-rich {--clean : Clean database before seeding}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed the database with large scale rich test data: 100 users, 100 stores, 300 items, orders, and discussions with real images';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Large Scale Rich Data Seeding...');
        $this->info('This will create:');
        $this->info('- 100 verified users (all with stores)');
        $this->info('- 100 stores with real images and favorite categories');
        $this->info('- 100 active subscriptions for store owners');
        $this->info('- 350+ items: 297 regular + 50 for <PERSON>\'s special store');
        $this->info('- Mixed ownership (60% store, 40% user) + store favorite categories');
        $this->info('- 50-80 orders and payment proofs');
        $this->info('- 30-50 discussions between users');
        $this->newLine();

        if ($this->option('clean')) {
            $this->warn('⚠️  This will CLEAN your database first!');
        }

        if (!$this->confirm('Do you want to continue?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        try {
            $this->info('Running rich data seeder...');
            
            // Configure logging to show in console
            Log::listen(function ($level, $message, $context) {
                if (str_contains($message, '🚀') || str_contains($message, '✅') || 
                    str_contains($message, '📍') || str_contains($message, '👥') || 
                    str_contains($message, '🏪') || str_contains($message, '📦') || 
                    str_contains($message, '💳') || str_contains($message, '💬') ||
                    str_contains($message, '🧹')) {
                    $this->line($message);
                }
            });

            $seeder = new RichDataSeeder();
            $seeder->run();

            $this->newLine();
            $this->info('✅ Large scale rich data seeding completed successfully!');
            $this->newLine();
            $this->info('You can now test your app with:');
            $this->info('- 100 users (phone numbers: 38184156, 36666688, 20100003-20100100)');
            $this->info('- Password for all users: 12345678');
            $this->info('- 100 stores with favorite categories based on their items');
            $this->info('- 100 active subscriptions (30-365 days)');
            $this->info('- 350+ items: Ahmed has 50 items, others have 3 each');
            $this->info('- Mixed ownership + store favorite categories automatically set');
            $this->info('- 50-80 orders and payment proofs');
            $this->info('- 30-50 discussions between users');

        } catch (\Exception $e) {
            $this->error('❌ Error during seeding: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }
}
