<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\ItemPayment;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Notification;
use App\services\FCMService;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\services\BPayService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;


class BPayController extends Controller
{
    protected BPayService $bPayService;
    protected FCMService $firebaseService;

    public function __construct(BPayService $bPayService, FCMService $firebaseService)
    {
        $this->bPayService = $bPayService;
        $this->firebaseService = $firebaseService;
    }

    /**
     * Initialize payment session
     * 
     * @OA\Post(
     *     path="/api/payments/bpay/initialize",
     *     tags={"Payments"},
     *     summary="Initialize B-Pay payment session",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"amount"},
     *             @OA\Property(property="amount", type="number", example="100.00")
     *         )
     *     ),
     *     @OA\Response(response=200, description="Payment session initialized"),
     *     @OA\Response(response=422, description="Validation error")
     * )
     */
    public function initialize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $transaction = Transaction::create([
                'amount' => $request->amount,
                'operation_id' => 'OP-' . Str::random(10),
                'status' => 'INITIALIZED',
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->id,
                    'operation_id' => $transaction->operation_id,
                    'amount' => $transaction->amount,
                    'merchant_code' => config('services.bpay.merchant_code')
                ]
            ]);



        } catch (\Exception $e) {
            Log::error('Payment initialization failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to initialize payment'
            ], 500);
        }
    }

    /**
     * Process payment
     * 
     * @OA\Post(
     *     path="/api/payments/bpay/process",
     *     tags={"Payments"},
     *     summary="Process B-Pay payment",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"phone", "passcode", "amount", "item_id"},
     *             @OA\Property(property="phone", type="string"),
     *             @OA\Property(property="passcode", type="string"),
     *             @OA\Property(property="amount", type="number"),
     *             @OA\Property(property="item_id", type="string")
     *         )
     *     ),
     *     @OA\Response(response=200, description="Payment processed successfully"),
     *     @OA\Response(response=422, description="Validation error")
     * )
     */
    public function process(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'passcode' => 'required|string',
            'amount' => 'required|numeric|min:0',
            'item_id' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create transaction first
            $transaction = Transaction::create([
                'amount' => $request->amount,
                'operation_id' => 'OP-' . Str::random(10),
                'status' => 'INITIALIZED',
                'user_id' => auth()->id(),
                'item_id' => $request->item_id
            ]);

            // Authenticate with B-Pay
            $auth = $this->bPayService->authenticate(
                config('services.bpay.username'),
                config('services.bpay.password')
            );

            Log::info('auth response'.json_encode($auth));
            if (isset($auth['error'])) {
                throw new \Exception($auth['message']);
            }

            // Process payment
            $payment = $this->bPayService->processPayment(
                $request->phone,
                $request->passcode,
                $transaction->operation_id,
                $transaction->amount,
                $request->user()->lang ?? 'FR'
            );

            Log::info('payment response '.json_encode($payment));

            if ($payment['errorCode'] != 0 ) {
                $transaction->update([
                    'status' => 'FAILED',
                    'error_message' => $payment['errorMessage']
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $payment['errorMessage']
                ], 400);
            }

            else {

                $transaction->update([
                    'status' => 'COMPLETED',
                    'payment_id' => $payment['transactionId'] ?? null
                ]);


                $itemPayment = ItemPayment::create([
                    'item_id' => $request->item_id,
                    'amount' => $transaction->amount,
                    'type' => 'e-pay',
                ]);

                // Send admin notification for e-payment
                $item = Item::with('user')->find($request->item_id);
                if ($item) {
                    $this->sendAdminPaymentNotifications($item, $itemPayment, 'e-payment');
                }
            }
            

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->id,
                    'operation_id' => $transaction->operation_id,
                    'status' => 'PROCESSING',
                    'payment_id' => $payment['transactionId'] ?? null,
                    'amount' => $transaction->amount,
                    'merchant_code' => config('services.bpay.merchant_code')
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'error' => $e->getMessage(),
                'item_id' => $request->item_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed'
            ], 500);
        }
    }

    /**
     * Verify payment status
     * 
     * @OA\Get(
     *     path="/api/payments/bpay/{transactionId}/status",
     *     tags={"Payments"},
     *     summary="Check B-Pay payment status",
     *     @OA\Parameter(
     *         name="transactionId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(response=200, description="Payment status retrieved"),
     *     @OA\Response(response=404, description="Transaction not found")
     * )
     */
    public function status(string $transactionId): JsonResponse
    {
        try {
            $transaction = Transaction::findOrFail($transactionId);

            // Verify transaction belongs to authenticated user
            if ($transaction->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }
            
            $status = $this->bPayService->checkTransaction($transaction->operation_id);

            if (isset($status['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $status['message']
                ], 400);
            }

            // Map B-Pay status to our status
            $newStatus = match($status['status']) {
                'TS' => 'COMPLETED',
                'TF' => 'FAILED',
                'TA' => 'PENDING',
                default => 'UNKNOWN'
            };

            $transaction->update([
                'status' => $newStatus,
                'error_message' => $status['errorMessage'] ?? null
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->id,
                    'status' => $newStatus,
                    'payment_id' => $status['transactionId'] ?? null,
                    'amount' => $transaction->amount,
                    'error_message' => $status['errorMessage'] ?? null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Payment status check failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check payment status'
            ], 500);
        }
    }





    /**
     * Test B-Pay payment processing
     * 
     * @OA\Post(
     *     path="/api/test-bpay",
     *     tags={"Payments"},
     *     summary="Test B-Pay payment processing",
     *     description="Test endpoint for processing B-Pay payments",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"phone", "passcode", "operation_id", "amount"},
     *             @OA\Property(property="phone", type="string", description="Customer phone number"),
     *             @OA\Property(property="passcode", type="string", description="Payment passcode"),
     *             @OA\Property(property="operation_id", type="string", description="Operation ID"),
     *             @OA\Property(property="amount", type="string", description="Payment amount")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Payment processed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="transaction_id", type="string"),
     *                 @OA\Property(property="status", type="string", example="PROCESSING"),
     *                 @OA\Property(property="payment_id", type="string", nullable=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Payment processing failed",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Payment processing failed")
     *         )
     *     )
     * )
     */
    public function testBpay(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'passcode' => 'required|string',
            'operation_id' => 'required|string',
            'amount' => 'required|string',
        ]);


        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {

            // Authenticate with B-Pay
            $auth = $this->bPayService->authenticate(
                config('services.bpay.username'),
                config('services.bpay.password')
            );


            Log::info('auth response'.json_encode($auth));
            if (isset($auth['error'])) {
                throw new \Exception($auth['message']);
            }

            // Process payment
            $payment = $this->bPayService->processPayment(
                $request->phone,
                $request->passcode,
                $request->operation_id,
                $request->amount,
                'FR'
            );

            Log::info('payment response '.json_encode($payment));

            if (isset($payment['errorCode']) && $payment['errorCode'] != 0 ) {
                return response()->json([
                    'success' => false,
                    'message' => $payment
                ], 400);
            }

            return response()->json([
                'success' => true,
                'msg' => $payment
            ]);

        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $request->transaction_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed'
            ], 500);
        }
    }

    /**
     * Send notifications to all admin users when a payment is made for an item
     */
    private function sendAdminPaymentNotifications(Item $item, $payment, string $paymentType): void
    {
        try {
            // Get all users with admin role using the web guard (where admin role is defined)
            $adminUsers = User::role('admin', 'web')->with('deviceToken')->get();

            Log::info('Found ' . $adminUsers->count() . ' admin users to notify about payment');

            foreach ($adminUsers as $adminUser) {
                $this->sendLocalizedNotification(
                    $adminUser,
                    'payment_received_admin',
                    [
                        'item_title' => $item->title,
                        'user_name' => $item->user->firstname . ' ' . $item->user->lastname,
                        'amount' => $payment->amount ?? $payment['amount'] ?? 0,
                        'payment_type' => $paymentType
                    ]
                );
            }

            Log::info('Admin payment notifications sent successfully for item: ' . $item->id);
        } catch (\Exception $e) {
            Log::error('Failed to send admin payment notifications', [
                'item_id' => $item->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send localized notification to a user
     */
    private function sendLocalizedNotification(User $user, string $type, array $params = []): void
    {
        $token = $user->deviceToken->token ?? null;

        if (!$token) {
            Log::warning('No device token found for user', [
                'user_id' => $user->id
            ]);
            return;
        }

        try {
            $userLang = $user->lang ?? 'en'; // Default to English if no language set

            // Get messages for all languages
            $messages = $this->getLocalizedNotificationMessages($type, $params);

            // Get the message in user's preferred language for push notification
            $localizedMessage = $messages[$userLang] ?? $messages['en']; // Fallback to English

            // Create notification with all language versions
            Notification::create([
                'user_id' => $user->id,
                'type' => $type,
                'is_read' => false,
                // Store titles in all languages
                'title_en' => $messages['en']['title'],
                'title_fr' => $messages['fr']['title'],
                'title_ar' => $messages['ar']['title'],
                // Store messages in all languages
                'message_en' => $messages['en']['body'],
                'message_fr' => $messages['fr']['body'],
                'message_ar' => $messages['ar']['body'],
                // Store additional params as JSON for potential future use
                'params' => json_encode($params)
            ]);

            // Send push notification in user's preferred language
            $this->firebaseService->sendNotification(
                $token,
                $localizedMessage['title'],
                $localizedMessage['body']
            );

            Log::info('Notification sent successfully', [
                'user_id' => $user->id,
                'language' => $userLang,
                'type' => $type
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'type' => $type
            ]);
        }
    }

    /**
     * Get localized notification messages
     */
    private function getLocalizedNotificationMessages(string $type, array $params): array
    {
        $formatArabic = function($template, $values) {
            return sprintf($template, ...$values);
        };

        $messages = [
            'payment_received_admin' => [
                'en' => [
                    'title' => 'Payment Received',
                    'body' => sprintf("Payment of %s received for item '%s' by %s via %s",
                        $params['amount'] ?? '0',
                        $params['item_title'] ?? 'Unknown Item',
                        $params['user_name'] ?? 'Unknown User',
                        $params['payment_type'] ?? 'payment'
                    )
                ],
                'fr' => [
                    'title' => 'Paiement Reçu',
                    'body' => sprintf("Paiement de %s reçu pour l'article '%s' par %s via %s",
                        $params['amount'] ?? '0',
                        $params['item_title'] ?? 'Article Inconnu',
                        $params['user_name'] ?? 'Utilisateur Inconnu',
                        $params['payment_type'] ?? 'paiement'
                    )
                ],
                'ar' => [
                    'title' => 'تم استلام الدفع',
                    'body' => $formatArabic("تم استلام دفعة بقيمة %s للعنصر '%s' من %s عبر %s", [
                        $params['amount'] ?? '0',
                        $params['item_title'] ?? 'عنصر غير معروف',
                        $params['user_name'] ?? 'مستخدم غير معروف',
                        $params['payment_type'] ?? 'الدفع'
                    ])
                ]
            ]
        ];

        return $messages[$type] ?? [];
    }
}
