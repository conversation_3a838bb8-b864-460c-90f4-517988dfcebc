<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ServerInfoController extends Controller
{
    /**
     * Get server information without authentication
     * This endpoint helps identify which server is responding in a load-balanced environment
     */
    public function getServerInfo(Request $request): JsonResponse
    {
        try {
            // Get server hostname
            $hostname = gethostname();
            
            // Get various IP addresses
            $serverIp = $this->getServerIp();
            $hostMachineIp = $this->getHostMachineIp();
            $requestIp = $request->ip();
            
            // Get server environment info
            $serverInfo = [
                'server_id' => $this->generateServerId($hostname, $hostMachineIp),
                'hostname' => $hostname,
                'server_ip' => $serverIp,
                'host_machine_ip' => $hostMachineIp,
                'request_ip' => $requestIp,
                'environment' => app()->environment(),
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'is_docker' => $this->isRunningInDocker(),
                'timestamp' => now()->toISOString(),
                'timezone' => config('app.timezone'),
                'headers' => [
                    'x-forwarded-for' => $request->header('x-forwarded-for'),
                    'x-real-ip' => $request->header('x-real-ip'),
                    'cf-connecting-ip' => $request->header('cf-connecting-ip'),
                    'x-forwarded-proto' => $request->header('x-forwarded-proto'),
                    'host' => $request->header('host'),
                ],
                'server_vars' => [
                    'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
                    'server_addr' => $_SERVER['SERVER_ADDR'] ?? 'unknown',
                    'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'http_host' => $_SERVER['HTTP_HOST'] ?? 'unknown',
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $serverInfo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve server information',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the server's IP address
     */
    private function getServerIp(): string
    {
        // Try multiple methods to get server IP
        $methods = [
            fn() => $_SERVER['SERVER_ADDR'] ?? null,
            fn() => gethostbyname(gethostname()),
            fn() => $this->getIpFromNetworkInterface(),
        ];

        foreach ($methods as $method) {
            $ip = $method();
            if ($ip && $ip !== '127.0.0.1' && filter_var($ip, FILTER_VALIDATE_IP)) {
                return $ip;
            }
        }

        return 'unknown';
    }

    /**
     * Get the host machine IP address (especially useful for Docker containers)
     */
    private function getHostMachineIp(): string
    {
        // If running in Docker, try to get host machine IP
        if ($this->isRunningInDocker()) {
            // Try different methods to get host IP from Docker container
            $hostIpMethods = [
                // Method 1: Check for host.docker.internal (Docker Desktop)
                fn() => gethostbyname('host.docker.internal'),
                // Method 2: Get default gateway IP
                fn() => $this->getDockerHostIp(),
                // Method 3: Check environment variables
                fn() => $_SERVER['HOST_IP'] ?? null,
                fn() => $_SERVER['DOCKER_HOST_IP'] ?? null,
            ];

            foreach ($hostIpMethods as $method) {
                $ip = $method();
                if ($ip && $ip !== '127.0.0.1' && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // If not in Docker or Docker methods failed, use server IP
        return $this->getServerIp();
    }

    /**
     * Get IP from network interface
     */
    private function getIpFromNetworkInterface(): ?string
    {
        try {
            // Try to get IP from network interfaces
            $output = shell_exec("ip route get ******* | awk '{print $7; exit}'");
            if ($output) {
                $ip = trim($output);
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        } catch (\Exception $e) {
            // Ignore errors and try next method
        }

        return null;
    }

    /**
     * Get Docker host IP by checking the default gateway
     */
    private function getDockerHostIp(): ?string
    {
        try {
            // Get default gateway IP (usually the Docker host)
            $output = shell_exec("ip route | grep default | awk '{print $3}' | head -1");
            if ($output) {
                $ip = trim($output);
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        } catch (\Exception $e) {
            // Ignore errors
        }

        return null;
    }

    /**
     * Check if running inside Docker container
     */
    private function isRunningInDocker(): bool
    {
        // Check multiple indicators of Docker environment
        $dockerIndicators = [
            // Check for .dockerenv file
            file_exists('/.dockerenv'),
            // Check for Docker in cgroup
            $this->checkCgroupForDocker(),
            // Check environment variables
            !empty($_SERVER['DOCKER_CONTAINER']),
            // Check hostname pattern (Docker containers often have random hostnames)
            strlen(gethostname()) === 12 && ctype_alnum(gethostname()),
        ];

        return in_array(true, $dockerIndicators, true);
    }

    /**
     * Check cgroup for Docker indicators
     */
    private function checkCgroupForDocker(): bool
    {
        try {
            if (file_exists('/proc/1/cgroup')) {
                $cgroup = file_get_contents('/proc/1/cgroup');
                return strpos($cgroup, 'docker') !== false || strpos($cgroup, '/lxc/') !== false;
            }
        } catch (\Exception $e) {
            // Ignore errors
        }

        return false;
    }

    /**
     * Generate a unique server ID based on hostname and IP
     */
    private function generateServerId(string $hostname, string $ip): string
    {
        return substr(md5($hostname . $ip), 0, 8);
    }
}
