<?php

namespace App\Http\Controllers;

use App\Models\DeliveryCharge;
use Illuminate\Http\Request;

/**
 * @OA\Schema(
 *     schema="DeliveryCharge",
 *     type="object",
 *     title="Delivery Charge",
 *     description="Delivery Charge model",
 *     properties={
 *         @OA\Property(property="id", type="integer", description="ID of the delivery charge"),
 *         @OA\Property(property="amount", type="number", format="float", description="Amount of the delivery charge"),
 *         @OA\Property(property="created_at", type="string", format="date-time", description="Creation timestamp"),
 *         @OA\Property(property="updated_at", type="string", format="date-time", description="Update timestamp")
 *     }
 * )
 */
class DeliveryChargeController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/delivery-charges",
     *     tags={"DeliveryCharge"},
     *     summary="Get all delivery charges",
     *     description="Retrieve a list of all delivery charges.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(type="array", @OA\Items(ref="#/components/schemas/DeliveryCharge"))
     *     ),
     *     @OA\Response(response=401, description="Unauthenticated")
     * )
     */
    public function index(Request $request)
    {
        $lang = $request->header('Accept-Language', 'en');
        $charges = DeliveryCharge::all()->map(function($charge) use ($lang) {
            $type = $charge->{'type_' . $lang} ?? $charge->type_en;
            return [
                'id' => $charge->id,
                'amount' => $charge->amount,
                'type' => $type,
                'type_en' => $charge->type_en,
                'type_fr' => $charge->type_fr,
                'type_ar' => $charge->type_ar,
            ];
        });
        return response()->json($charges);
    }


    /**
     * @OA\Post(
     *     path="/api/delivery-charges",
     *     tags={"DeliveryCharge"},
     *     summary="Create a new delivery charge",
     *     description="Create a new delivery charge.",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="amount", type="number", format="float", example=50.00),
     *             @OA\Property(property="type", type="string", example="delivery normal")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Delivery charge created successfully",
     *         @OA\JsonContent(ref="#/components/schemas/DeliveryCharge")
     *     ),
     *     @OA\Response(response=400, description="Invalid input"),
     *     @OA\Response(response=401, description="Unauthenticated")
     * )
     */
    public function store(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric',
        ]);

        $deliveryCharge = DeliveryCharge::create($request->all());
        return response()->json($deliveryCharge, 201);
    }

    /**
     * @OA\Get(
     *     path="/api/delivery-charges/{id}",
     *     tags={"DeliveryCharge"},
     *     summary="Get a delivery charge by ID",
     *     description="Retrieve a delivery charge by its ID.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Delivery charge ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/DeliveryCharge")
     *     ),
     *     @OA\Response(response=404, description="Delivery charge not found"),
     *     @OA\Response(response=401, description="Unauthenticated")
     * )
     */
    public function show($id)
    {
        $deliveryCharge = DeliveryCharge::find($id);

        if (!$deliveryCharge) {
            return response()->json(['message' => 'Delivery charge not found'], 404);
        }

        return response()->json($deliveryCharge);
    }

    /**
     * @OA\Put(
     *     path="/api/delivery-charges/{id}",
     *     tags={"DeliveryCharge"},
     *     summary="Update a delivery charge",
     *     description="Update the details of a delivery charge.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Delivery charge ID"
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="amount", type="number", format="float", example=50.00),
     *             @OA\Property(property="type", type="string", example="delivery normal")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Delivery charge updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/DeliveryCharge")
     *     ),
     *     @OA\Response(response=400, description="Invalid input"),
     *     @OA\Response(response=404, description="Delivery charge not found"),
     *     @OA\Response(response=401, description="Unauthenticated")
     * )
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'amount' => 'required|numeric',
        ]);

        $deliveryCharge = DeliveryCharge::find($id);

        if (!$deliveryCharge) {
            return response()->json(['message' => 'Delivery charge not found'], 404);
        }

        $deliveryCharge->update($request->all());
        return response()->json($deliveryCharge);
    }

    /**
     * @OA\Delete(
     *     path="/api/delivery-charges/{id}",
     *     tags={"DeliveryCharge"},
     *     summary="Delete a delivery charge",
     *     description="Delete a delivery charge by its ID.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Delivery charge ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Delivery charge deleted successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Delivery charge deleted successfully")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Delivery charge not found"),
     *     @OA\Response(response=401, description="Unauthenticated")
     * )
     */
    public function destroy($id)
    {
        $deliveryCharge = DeliveryCharge::find($id);

        if (!$deliveryCharge) {
            return response()->json(['message' => 'Delivery charge not found'], 404);
        }

        $deliveryCharge->delete();
        return response()->json(['message' => 'Delivery charge deleted successfully']);
    }
}
