<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use App\Models\Item;
use App\Models\Store;
use App\services\ImageOptimizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Yajra\DataTables\Facades\DataTables;

class AdController extends Controller
{
    protected ImageOptimizationService $imageOptimizationService;

    public function __construct(ImageOptimizationService $imageOptimizationService)
    {
        $this->imageOptimizationService = $imageOptimizationService;
    }

    /**
     * Get active ads
     */
    public function getActiveAds()
    {


        $ads = Ad::where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $ads
        ]);
    }

    /**
     * Get all ads (for admin)
     */
    public function index()
    {
        $ads = Ad::orderBy('created_at', 'desc')->get();

        return response()->json([
            'status' => 'success',
            'data' => $ads
        ]);
    }

    /**
     * Store a new ad
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif',
            'target_type' => 'required|in:item,store,url',
            'target_id' => 'required_if:target_type,item,store|nullable|integer',
            'target_url' => 'required_if:target_type,url|nullable|url',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);


        
        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => $validator->errors()->first()
            ], 422);
        }

        // Upload and optimize image
        $optimizedImageData = $this->imageOptimizationService->optimizeAndStore(
            $request->file('image'),
            'ads'
        );

        $ad = Ad::create([
            'title' => $request->title,
            'image_url' => $optimizedImageData['url'],
            'target_type' => $request->target_type,
            'target_id' => $request->target_id,
            'target_url' => $request->target_url,
            'is_active' => true,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Ad created successfully',
            'data' => $ad
        ], 201);
    }

    /**
     * Update an ad
     */
    public function update(Request $request, $id)
    {
        $ad = Ad::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'image' => 'sometimes|image|mimes:jpeg,png,jpg,gif',
            'target_type' => 'sometimes|required|in:item,store,url',
            'target_id' => 'required_if:target_type,item,store|nullable|integer',
            'target_url' => 'required_if:target_type,url|nullable|url',
            'is_active' => 'sometimes|boolean',
            'start_date' => 'sometimes|required|date',
            'end_date' => 'sometimes|required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => $validator->errors()->first()
            ], 422);
        }

        // Update image if provided
        if ($request->hasFile('image')) {
            // Delete old image
            if ($ad->image_url) {
                Storage::disk('public')->delete($ad->image_url);
            }

            // Upload and optimize new image
            $optimizedImageData = $this->imageOptimizationService->optimizeAndStore(
                $request->file('image'),
                'ads'
            );
            $ad->image_url = $optimizedImageData['url'];
        }

        // Update other fields
        if ($request->has('title')) $ad->title = $request->title;
        if ($request->has('target_type')) $ad->target_type = $request->target_type;
        if ($request->has('target_id')) $ad->target_id = $request->target_id;
        if ($request->has('target_url')) $ad->target_url = $request->target_url;
        if ($request->has('is_active')) $ad->is_active = $request->is_active;
        if ($request->has('start_date')) $ad->start_date = $request->start_date;
        if ($request->has('end_date')) $ad->end_date = $request->end_date;

        $ad->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Ad updated successfully',
            'data' => $ad
        ]);
    }

    /**
     * Delete an ad
     */
    public function destroy($id)
    {
        $ad = Ad::findOrFail($id);

        // Delete image
        if ($ad->image_url) {
            Storage::disk('public')->delete($ad->image_url);
        }

        $ad->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Ad deleted successfully'
        ]);
    }

    // ==================== BACKOFFICE METHODS ====================

    /**
     * Display ads listing for backoffice
     */
    public function indexBo(Request $request)
    {
        if ($request->ajax()) {
            try {
                \Log::info('DataTables Ajax request received');

                // Check if DataTables package is available
                if (!class_exists('Yajra\DataTables\Facades\DataTables')) {
                    \Log::error('DataTables package not found');
                    return response()->json(['error' => 'DataTables package not available'], 500);
                }

                $data = Ad::with(['item', 'store', 'createdByStore', 'targetStore'])->get();

                \Log::info('Ads data retrieved:', ['count' => $data->count()]);

                return DataTables::of($data)
                    ->addColumn('image', function ($row) {
                        if ($row->image_url) {
                            // Use direct URL instead of Storage::disk()->url() which might fail
                            $imageUrl = $row->image_url;
                            return '<img src="' . $imageUrl . '" alt="Ad Image" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;">';
                        }
                        return '<span class="text-muted">No Image</span>';
                    })
                    ->addColumn('target', function ($row) {
                        try {
                            switch ($row->target_type) {
                                case 'item':
                                    // Only check item relationship if target_type is 'item'
                                    if ($row->target_id) {
                                        $item = $row->item;
                                        return $item ?
                                            '<span class="badge bg-primary">Item: ' . htmlspecialchars($item->title) . '</span>' :
                                            '<span class="badge bg-danger">Item Not Found (ID: ' . $row->target_id . ')</span>';
                                    }
                                    return '<span class="badge bg-danger">No Item ID</span>';

                                case 'store':
                                    // Only check store relationship if target_type is 'store'
                                    if ($row->target_id) {
                                        $store = $row->store;
                                        return $store ?
                                            '<span class="badge bg-success">Store: ' . htmlspecialchars($store->name) . '</span>' :
                                            '<span class="badge bg-danger">Store Not Found (ID: ' . $row->target_id . ')</span>';
                                    }
                                    return '<span class="badge bg-danger">No Store ID</span>';

                                case 'item-store':
                                    $storeName = 'Unknown Store';
                                    if ($row->store_id && $row->targetStore) {
                                        $storeName = htmlspecialchars($row->targetStore->name);
                                    }

                                    $itemName = ' (All Items)';
                                    if ($row->target_id && $row->item) {
                                        $itemName = ' - ' . htmlspecialchars($row->item->title);
                                    }

                                    return '<span class="badge bg-warning">Store Items: ' . $storeName . $itemName . '</span>';

                                case 'url':
                                    $url = $row->target_url ? htmlspecialchars(substr($row->target_url, 0, 30)) . '...' : 'No URL';
                                    return '<span class="badge bg-info">URL: ' . $url . '</span>';

                                default:
                                    return '<span class="badge bg-secondary">Unknown Type: ' . htmlspecialchars($row->target_type) . '</span>';
                            }
                        } catch (\Exception $e) {
                            \Log::error('Error in target column:', ['error' => $e->getMessage(), 'ad_id' => $row->id, 'target_type' => $row->target_type]);
                            return '<span class="badge bg-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
                        }
                    })
                ->addColumn('status', function ($row) {
                    $statusClass = $row->is_active ? 'bg-success' : 'bg-danger';
                    $statusText = $row->is_active ? 'Active' : 'Inactive';
                    return '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
                })
                    ->addColumn('duration', function ($row) {
                        try {
                            if ($row->start_date && $row->end_date) {
                                return $row->start_date->format('M d, Y') . ' - ' . $row->end_date->format('M d, Y');
                            } elseif ($row->start_date) {
                                return 'From: ' . $row->start_date->format('M d, Y');
                            } elseif ($row->end_date) {
                                return 'Until: ' . $row->end_date->format('M d, Y');
                            }
                            return '<span class="text-muted">No Duration Set</span>';
                        } catch (\Exception $e) {
                            \Log::error('Error in duration column:', ['error' => $e->getMessage(), 'ad_id' => $row->id]);
                            return '<span class="text-muted">Date Error</span>';
                        }
                    })
                    ->addColumn('created_by', function ($row) {
                        try {
                            // Check if store_id exists and load the relationship
                            if ($row->store_id && $row->createdByStore) {
                                return htmlspecialchars($row->createdByStore->name);
                            }
                            return '<span class="text-muted">Admin</span>';
                        } catch (\Exception $e) {
                            \Log::error('Error in created_by column:', ['error' => $e->getMessage(), 'ad_id' => $row->id]);
                            return '<span class="text-muted">Unknown</span>';
                        }
                    })
                    ->addColumn('actions', function ($row) {
                        try {
                            $btn = '<div class="btn-group" role="group">';
                            $btn .= '<a href="' . route('ads.edit', $row->id) . '" class="btn btn-warning btn-sm" title="Edit">
                                        <i class="ri-edit-line"></i>
                                     </a>';
                            $btn .= '<button type="button" class="btn btn-info btn-sm mx-1" onclick="viewAd(' . $row->id . ')" title="View">
                                        <i class="ri-eye-line"></i>
                                     </button>';
                            $btn .= '<button type="button" class="btn btn-danger btn-sm delete-ad" data-id="' . $row->id . '" title="Delete">
                                        <i class="ri-delete-bin-line"></i>
                                     </button>';
                            $btn .= '</div>';
                            return $btn;
                        } catch (\Exception $e) {
                            \Log::error('Error in actions column:', ['error' => $e->getMessage(), 'ad_id' => $row->id]);
                            return '<span class="text-muted">Actions Error</span>';
                        }
                    })
                    ->rawColumns(['image', 'target', 'status', 'duration', 'created_by', 'actions'])
                    ->make(true);

            } catch (\Exception $e) {
                \Log::error('DataTables Ajax error:', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'error' => 'Failed to load advertisements: ' . $e->getMessage()
                ], 500);
            }
        }

        return view('backoffice.ads.index');
    }

    /**
     * Show the form for creating a new ad
     */
    public function create()
    {
        $items = Item::select('id', 'title')->get();
        $stores = Store::select('id', 'name')->get();

        return view('backoffice.ads.create', compact('items', 'stores'));
    }

    /**
     * Store a newly created ad from backoffice
     */
    public function storeBo(Request $request)
    {
        try {
            // Log incoming request data for debugging
            \Log::info('Ad Creation Request Data:', $request->all());

            // Process target_id based on target_type before validation
            $targetId = null;
            if ($request->target_type === 'store' && $request->has('target_id_store')) {
                $targetId = $request->target_id_store;
            } elseif ($request->target_type === 'item' && $request->has('target_id_item')) {
                $targetId = $request->target_id_item;
            } elseif ($request->target_type === 'item-store' && $request->has('target_store_item_id')) {
                $targetId = $request->target_store_item_id;
            }

            // Merge the processed target_id back into request
            $request->merge(['target_id' => $targetId]);

            \Log::info('Processed target_id:', ['target_type' => $request->target_type, 'target_id' => $targetId]);

            $request->validate([
                'title' => 'required|string|max:255',
                'image' => 'required|image|mimes:jpeg,png,jpg,gif',
                'target_type' => 'required|in:item,store,url,item-store',
                'target_id' => 'required_if:target_type,item,store,item-store|nullable|integer',
                'target_url' => 'required_if:target_type,url|nullable|url',
                'target_store_id' => 'nullable',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'store_id' => 'nullable',
            ]);

            \Log::info('Ad validation passed successfully');

            // Check if image file exists
            if (!$request->hasFile('image')) {
                \Log::error('No image file found in request');
                return redirect()->back()->withErrors(['image' => 'Image file is required'])->withInput();
            }

            // Upload and optimize image
            try {
                $optimizedImageData = $this->imageOptimizationService->optimizeAndStore(
                    $request->file('image'),
                    'ads'
                );
                \Log::info('Image optimization successful:', $optimizedImageData);
            } catch (\Exception $e) {
                \Log::error('Image optimization failed:', ['error' => $e->getMessage()]);
                return redirect()->back()->withErrors(['image' => 'Failed to process image: ' . $e->getMessage()])->withInput();
            }

            // Handle store_id based on target_type
            $storeId = $request->target_type === 'item-store' ? $request->target_store_id : $request->store_id;

            \Log::info('Processed store ID:', ['store_id' => $storeId, 'target_type' => $request->target_type]);

            // Prepare data for creation
            $adData = [
                'title' => $request->title,
                'image_url' => $optimizedImageData['url'],
                'target_type' => $request->target_type,
                'target_id' => $request->target_id,
                'target_url' => $request->target_url,
                'is_active' => $request->has('is_active') ? true : false, // Convert checkbox to boolean
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'store_id' => $storeId,
            ];

            \Log::info('Ad data prepared for creation:', $adData);

            // Create the ad
            $ad = Ad::create($adData);

            if ($ad) {
                \Log::info('Ad created successfully:', ['ad_id' => $ad->id]);
                return redirect()->route('ads.index')->with('success', 'Advertisement created successfully!');
            } else {
                \Log::error('Ad creation failed - Ad::create returned null');
                return redirect()->back()->withErrors(['error' => 'Failed to create advertisement. Please try again.'])->withInput();
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation failed:', ['errors' => $e->errors()]);
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Ad creation failed with exception:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->back()->withErrors(['error' => 'An unexpected error occurred: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Show the form for editing the specified ad
     */
    public function edit($id)
    {
        $ad = Ad::findOrFail($id);
        $items = Item::select('id', 'title')->get();
        $stores = Store::select('id', 'name')->get();

        return view('backoffice.ads.edit', compact('ad', 'items', 'stores'));
    }

    /**
     * Update the specified ad from backoffice
     */
    public function updateBo(Request $request, $id)
    {
        try {
            // Log incoming request data for debugging
            \Log::info('Ad Update Request Data:', ['ad_id' => $id, 'data' => $request->all()]);

            $ad = Ad::findOrFail($id);
            \Log::info('Ad found for update:', ['ad_id' => $ad->id, 'current_title' => $ad->title]);

            // Process target_id based on target_type before validation
            $targetId = null;
            if ($request->target_type === 'store' && $request->has('target_id_store')) {
                $targetId = $request->target_id_store;
            } elseif ($request->target_type === 'item' && $request->has('target_id_item')) {
                $targetId = $request->target_id_item;
            } elseif ($request->target_type === 'item-store' && $request->has('target_store_item_id')) {
                $targetId = $request->target_store_item_id;
            }

            // Merge the processed target_id back into request
            $request->merge(['target_id' => $targetId]);

            \Log::info('Processed target_id for update:', ['target_type' => $request->target_type, 'target_id' => $targetId]);

            $request->validate([
                'title' => 'required|string|max:255',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
                'target_type' => 'required|in:item,store,url,item-store',
                'target_id' => 'required_if:target_type,item,store,item-store|nullable|integer',
                'target_url' => 'required_if:target_type,url|nullable|url',
                'target_store_id' => 'nullable',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'store_id' => 'nullable',
            ]);

            \Log::info('Ad update validation passed successfully');

            // Update image if provided
            if ($request->hasFile('image')) {
                \Log::info('New image file detected, processing...');

                try {
                    // Delete old image
                    if ($ad->image_url) {
                        \Log::info('Deleting old image:', ['old_image' => $ad->image_url]);
                        Storage::disk('sftp')->delete($ad->image_url);
                    }

                    // Upload and optimize new image
                    $optimizedImageData = $this->imageOptimizationService->optimizeAndStore(
                        $request->file('image'),
                        'ads'
                    );
                    $ad->image_url = $optimizedImageData['url'];
                    \Log::info('New image processed successfully:', $optimizedImageData);
                } catch (\Exception $e) {
                    \Log::error('Image update failed:', ['error' => $e->getMessage()]);
                    return redirect()->back()->withErrors(['image' => 'Failed to process new image: ' . $e->getMessage()])->withInput();
                }
            }

            // Handle store_id based on target_type
            $storeId = $request->target_type === 'item-store' ? $request->target_store_id : $request->store_id;

            \Log::info('Processed store ID for update:', ['store_id' => $storeId, 'target_type' => $request->target_type]);

            // Prepare update data
            $updateData = [
                'title' => $request->title,
                'target_type' => $request->target_type,
                'target_id' => $request->target_id,
                'target_url' => $request->target_url,
                'is_active' => $request->has('is_active') ? true : false, // Convert checkbox to boolean
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'store_id' => $storeId,
            ];

            // Add image_url to update data if it was changed
            if ($request->hasFile('image') && isset($optimizedImageData)) {
                $updateData['image_url'] = $optimizedImageData['url'];
            }

            \Log::info('Ad update data prepared:', $updateData);

            // Update other fields
            $updateResult = $ad->update($updateData);

            if ($updateResult) {
                \Log::info('Ad updated successfully:', ['ad_id' => $ad->id]);
                return redirect()->route('ads.index')->with('success', 'Advertisement updated successfully!');
            } else {
                \Log::error('Ad update failed - update() returned false');
                return redirect()->back()->withErrors(['error' => 'Failed to update advertisement. Please try again.'])->withInput();
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Update validation failed:', ['errors' => $e->errors()]);
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            \Log::error('Ad not found for update:', ['ad_id' => $id]);
            return redirect()->route('ads.index')->withErrors(['error' => 'Advertisement not found.']);
        } catch (\Exception $e) {
            \Log::error('Ad update failed with exception:', [
                'ad_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->back()->withErrors(['error' => 'An unexpected error occurred: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified ad from backoffice
     */
    public function destroyBo($id)
    {
        $ad = Ad::findOrFail($id);

        // Delete image
        if ($ad->image_url) {
            Storage::disk('sftp')->delete($ad->image_url);
        }

        $ad->delete();

        return response()->json(['success' => true, 'message' => 'Ad deleted successfully!']);
    }

    /**
     * Show the specified ad details for modal view
     */
    public function showBo($id)
    {
        $ad = Ad::with(['item', 'store', 'createdByStore', 'targetStore'])->findOrFail($id);
        return response()->json($ad);
    }

    /**
     * Get items from a specific store for AJAX
     */
    public function getStoreItems($storeId)
    {
        try {
            \Log::info('Getting items for store:', ['store_id' => $storeId]);

            $items = Item::where('store_id', $storeId)
                        ->select('id', 'title')
                        ->get();

            \Log::info('Store items retrieved:', ['count' => $items->count()]);

            return response()->json($items);
        } catch (\Exception $e) {
            \Log::error('Failed to get store items:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to load store items'], 500);
        }
    }

    /**
     * Test database connection and Ad model functionality
     */
    public function testAdCreation()
    {
        try {
            \Log::info('Testing Ad model and database connection...');

            // Test database connection
            $adsCount = Ad::count();
            \Log::info('Current ads count:', ['count' => $adsCount]);

            // Test basic Ad creation (without image)
            $testData = [
                'title' => 'Test Ad - ' . now()->format('Y-m-d H:i:s'),
                'image_url' => 'test/image.jpg',
                'target_type' => 'url',
                'target_url' => 'https://example.com',
                'is_active' => true,
            ];

            \Log::info('Creating test ad with data:', $testData);

            $testAd = Ad::create($testData);

            if ($testAd) {
                \Log::info('Test ad created successfully:', ['id' => $testAd->id]);

                // Clean up test ad
                $testAd->delete();
                \Log::info('Test ad deleted successfully');

                return response()->json([
                    'success' => true,
                    'message' => 'Ad model and database are working correctly',
                    'test_ad_id' => $testAd->id
                ]);
            } else {
                \Log::error('Test ad creation failed');
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create test ad'
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('Ad test failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Database or model error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test Ajax endpoint without DataTables
     */
    public function testAjax()
    {
        try {
            \Log::info('Testing basic Ajax endpoint...');

            $ads = Ad::with(['item', 'store', 'createdByStore', 'targetStore'])->get();

            \Log::info('Basic Ajax test successful:', ['count' => $ads->count()]);

            return response()->json([
                'success' => true,
                'message' => 'Ajax endpoint working correctly',
                'data' => $ads->map(function($ad) {
                    return [
                        'id' => $ad->id,
                        'title' => $ad->title,
                        'target_type' => $ad->target_type,
                        'is_active' => $ad->is_active,
                        'created_at' => $ad->created_at->format('Y-m-d H:i:s'),
                        'item_title' => $ad->item ? $ad->item->title : null,
                        'store_name' => $ad->store ? $ad->store->name : null,
                        'target_store_name' => $ad->targetStore ? $ad->targetStore->name : null,
                    ];
                })
            ]);

        } catch (\Exception $e) {
            \Log::error('Basic Ajax test failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Ajax endpoint error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Simple ads listing without DataTables
     */
    public function simpleIndex(Request $request)
    {
        try {
            \Log::info('Simple ads index requested');

            $ads = Ad::with(['item', 'store', 'createdByStore', 'targetStore'])
                     ->orderBy('id', 'desc')
                     ->get();

            $formattedAds = $ads->map(function($ad) {
                // Format target display
                $target = '';
                switch ($ad->target_type) {
                    case 'item':
                        $target = $ad->item ? 'Item: ' . $ad->item->title : 'Item Not Found';
                        break;
                    case 'store':
                        $target = $ad->store ? 'Store: ' . $ad->store->name : 'Store Not Found';
                        break;
                    case 'item-store':
                        $storeName = $ad->targetStore ? $ad->targetStore->name : 'Unknown Store';
                        $itemName = $ad->item ? ' - ' . $ad->item->title : ' (All Items)';
                        $target = 'Store Items: ' . $storeName . $itemName;
                        break;
                    case 'url':
                        $target = 'URL: ' . substr($ad->target_url, 0, 30) . '...';
                        break;
                    default:
                        $target = 'Unknown';
                }

                $imageUrl = $ad->image_url;


                return [
                    'id' => $ad->id,
                    'title' => $ad->title,
                    'image_url' => $imageUrl,
                    'target' => $target,
                    'status' => $ad->is_active ? 'Active' : 'Inactive',
                    'created_by' => $ad->createdByStore ? $ad->createdByStore->name : 'Admin',
                    'created_at' => $ad->created_at->format('M d, Y H:i'),
                    'edit_url' => route('ads.edit', $ad->id),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedAds
            ]);

        } catch (\Exception $e) {
            \Log::error('Simple ads index failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load ads: ' . $e->getMessage()
            ], 500);
        }
    }
}