<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\User;
use App\Models\Media;
use App\Models\Store;
use App\Models\Location;
use App\Models\ItemMedia;
use App\Models\StoreType;
use App\Enums\eItemStatus;
use App\Models\StoreMedia;
use App\Models\Notification;
use App\services\FCMService;
use App\services\ImageOptimizationService;
use Exception;
use Illuminate\Http\Request;
use App\Models\CategoryDetail;
use App\Models\DeliveryCharge;
use Illuminate\Http\JsonResponse;
use App\Models\CategoryItemDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\StoreFavoriteCategory;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class StoreController extends Controller
{
    
    protected FCMService $firebaseService;
    protected ImageOptimizationService $imageOptimizationService;

    public function __construct(FCMService $firebaseService, ImageOptimizationService $imageOptimizationService)
    {
        $this->firebaseService = $firebaseService;
        $this->imageOptimizationService = $imageOptimizationService;
    }

    private function getLocalizedNotificationMessages(string $type, array $params = []): array
    {
        // Set default values for parameters
        $defaultParams = [
            'follower_name' => '',
            'store_name' => '',
            'item_title' => '',
            'item_price' => '',
            'category_name' => '',
            'store_id' => '',
            'item_id' => ''
        ];

        // Merge provided params with defaults and filter out null values
        $params = array_merge($defaultParams, array_filter($params));

        // Helper function for Arabic text formatting
        $formatArabic = function (string $text, array $values = []): string {
            return vsprintf(str_replace('%s', '%s', $text), array_map(fn($v) => $v ?: '', $values));
        };

        $messages = [
            'new_follower' => [
                'en' => [
                    'title' => 'New Follower',
                    'body' => sprintf("%s is now following your store", $params['follower_name'])
                ],
                'fr' => [
                    'title' => 'Nouvel Abonné',
                    'body' => sprintf("%s suit maintenant votre boutique", $params['follower_name'])
                ],
                'ar' => [
                    'title' => 'متابع جديد',
                    'body' => $formatArabic("يتابع متجرك الآن %s", [$params['follower_name']])
                ]
            ],
            'store_item_promoted' => [
                'en' => [
                    'title' => 'Promotion at %s',
                    'body' => sprintf("%s is promoting: %s", $params['store_name'], $params['item_title'])
                ],
                'fr' => [
                    'title' => 'Promotion chez %s',
                    'body' => sprintf("%s fait la promotion de : %s", $params['store_name'], $params['item_title'])
                ],
                'ar' => [
                    'title' => 'عرض ترويجي في %s',
                    'body' => $formatArabic("%s يروج لـ: %s", [$params['store_name'], $params['item_title']])
                ]
            ],
            'new_item' => [
                'en' => [
                    'title' => 'New Item Added',
                    'body' => sprintf("%s has added a new item: %s", $params['store_name'], $params['item_title'])
                ],
                'fr' => [
                    'title' => 'Nouvel Article Ajouté',
                    'body' => sprintf("%s a ajouté un nouvel article: %s", $params['store_name'], $params['item_title'])
                ],
                'ar' => [
                    'title' => 'تمت إضافة عنصر جديد',
                    'body' => $formatArabic("%s :%s أضاف عنصرًا جديدًا", [$params['item_title'], $params['store_name']])
                ]
            ],
            'item_price_updated' => [
                'en' => [
                    'title' => 'Price Updated',
                    'body' => sprintf("Price updated for %s to %s", $params['item_title'], $params['item_price'])
                ],
                'fr' => [
                    'title' => 'Prix Mis à Jour',
                    'body' => sprintf("Prix mis à jour pour %s à %s", $params['item_title'], $params['item_price'])
                ],
                'ar' => [
                    'title' => 'تم تحديث السعر',
                    'body' => $formatArabic("%s إلى %s تم تحديث سعر", [$params['item_price'], $params['item_title']])
                ]
            ]
        ];

        Log::debug('Store notification parameters', [
            'type' => $type,
            'params' => $params,
            'user_id' => auth()->id() ?? 'unauthenticated'
        ]);

        return $messages[$type] ?? [];
    }

    private function sendLocalizedNotification(User $user, string $type, array $params = [], ?int $storeId = null): void
    {
        $token = $user->deviceToken->token ?? null;
        $userLang = $user->lang ?? 'en';

        try {
            // Get all language versions
            $messages = $this->getLocalizedNotificationMessages($type, $params);

            // Get user's preferred language for push notification
            $userMessage = $messages[$userLang] ?? $messages['en'];


            Log::info('user message' . json_encode($userMessage));

            // Create notification with all language versions
            $notification = Notification::create([
                'user_id' => $user->id,
                'type' => $type,
                'store_id' => $storeId,
                'item_id' => $params['item_id'] ?? null,
                'is_read' => false,
                // Store titles in all languages
                'title_en' => $messages['en']['title'],
                'title_fr' => $messages['fr']['title'],
                'title_ar' => $messages['ar']['title'],
                // Store messages in all languages
                'message_en' => $messages['en']['body'],
                'message_fr' => $messages['fr']['body'],
                'message_ar' => $messages['ar']['body'],
                // Store current language version for backwards compatibility
                // 'title' => $userMessage['title'],
                // 'message' => $userMessage['body'],
                // Store additional parameters
                'params' => json_encode(array_merge($params, [
                    'notification_type' => $type,
                    'language' => $userLang,
                    'notification_date' => now()->toISOString()
                ]))
            ]);

            // Send push notification if device token exists
            if ($token) {
                $this->firebaseService->sendNotification(
                    $token,
                    $userMessage['title'],
                    $userMessage['body']
                );

                Log::info('Store notification sent successfully', [
                    'user_id' => $user->id,
                    'language' => $userLang,
                    'type' => $type,
                    'notification_id' => $notification->id,
                    'store_id' => $storeId
                ]);
            } else {
                Log::warning('No device token found for user, notification stored in database only', [
                    'user_id' => $user->id
                ]);
            }
        } catch (Exception $e) {
            Log::error('Failed to process store notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'type' => $type,
                'params' => $params,
                'store_id' => $storeId
            ]);
        }
    }

    /**
     * Get stores based on user preferences.
     *
     * @OA\Get(
     *     path="/api/stores/recommended",
     *     tags={"Store"},
     *     summary="Get recommended stores",
     *     description="Get stores based on user preferences, gender matching, and boutique scores. First shows promoted stores, then prioritizes by user gender and boutique score.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(response=404, description="No stores found")
     * )
     */
    public function getRecommendedStores(Request $request): JsonResponse
    {
        $user = Auth::user();
        $hasStore = (bool)$user->store;
        $userGender = $user->gender; // 'male' or 'female'

        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 10);
        $search = $request->get('search');

        $promotedQuery = Store::query()
            ->where('store.is_promoted', true)
            ->whereNotNull('store.promotion_position')
            ->join('users', 'store.user_id', '=', 'users.id')
            ->join('new_user_subscriptions', 'users.id', '=', 'new_user_subscriptions.user_id')
            ->where('new_user_subscriptions.end_date', '>', now())
            ->orderBy('store.promotion_position', 'asc')
            ->withCount(['followers', 'items'])
            ->with(['images.media:id,url', 'type:id,name_fr,name_ar,name_en'])
            ->distinct();

        if ($search) {
            $promotedQuery->where(function ($q) use ($search) {
                $q->where('store.name', 'LIKE', "%{$search}%")
                  ->orWhere('store.description', 'LIKE', "%{$search}%");
            });
        }

        $promotedStores = $promotedQuery->get();

        // Get all other stores (excluding promoted ones)
        $promotedStoreIds = $promotedStores->pluck('id');
        $otherQuery = Store::query()
            ->whereNotIn('store.id', $promotedStoreIds)
            ->join('users', 'store.user_id', '=', 'users.id')
            ->join('new_user_subscriptions', 'users.id', '=', 'new_user_subscriptions.user_id')
            ->where('new_user_subscriptions.end_date', '>', now())
            ->withCount(['followers', 'items'])
            ->with(['images.media:id,url', 'type:id,name_fr,name_ar,name_en'])
            ->distinct();

        // Apply search filter to other stores
        if ($search) {
            $otherQuery->where(function ($q) use ($search) {
                $q->where('store.name', 'LIKE', "%{$search}%")
                  ->orWhere('store.description', 'LIKE', "%{$search}%");
            });
        }

        $otherStores = $otherQuery->get();

        // Calculate boutique score and apply gender preference for other stores
        $otherStores = $otherStores->map(function ($store) use ($userGender) {
            $store->boutique_score = $this->calculateBoutiqueScore($store);

            // Apply gender preference bonus
            if ($userGender && $this->isStoreGenderMatch($store, $userGender)) {
                $store->boutique_score += 20; // Bonus for gender match
            }

            return $store;
        });

        // Sort other stores by boutique score (descending)
        $otherStores = $otherStores->sortByDesc('boutique_score');

        // Combine promoted stores first, then sorted other stores
        $result = $promotedStores->merge($otherStores);

        // Apply pagination to the combined result
        $offset = ($page - 1) * $perPage;
        $paginatedStores = $result->slice($offset, $perPage)->values();

        // Map additional store attributes
        $userLang = $user ? ($user->lang ?? 'fr') : 'fr';
        $followedStoreIds = $user->followedStores()->pluck('store_id')->toArray();

        $stores = $paginatedStores->map(function ($store) use ($hasStore, $userLang, $followedStoreIds) {
            $store->has_store = $hasStore;
            $store->followers_count = $store->followers_count ?? 0;
            $store->items_count = $store->items_count ?? 0;
            $store->is_followed = in_array($store->id, $followedStoreIds);
            $store->subscription_status = 'active';
            $store->type_name = $store->type->{'name_' . $userLang} ?? $store->type->name_fr;

            return $store;
        });

        return response()->json($stores->values(), 200);
    }



        private function isStoreOpen(string $openingTime, string $closingTime): bool
    {
        try {
            $now = now();
            $currentTime = $now->format('H:i');

            // Handle stores that close after midnight
            if ($closingTime < $openingTime) {
                return $currentTime >= $openingTime || $currentTime <= $closingTime;
            }

            return $currentTime >= $openingTime && $currentTime <= $closingTime;
        } catch (Exception $e) {
            Log::error('Error checking store open status', [
                'error' => $e->getMessage(),
                'opening_time' => $openingTime,
                'closing_time' => $closingTime
            ]);
            return false;
        }
    }
    /**
     * Create a new store with associated images.
     *
     * @OA\Post(
     *     path="/api/stores",
     *     tags={"Store"},
     *     summary="Create a new store",
     *     description="Create a new store with associated images.",
     *     operationId="createStore",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"name", "description", "type_id", "images", "opening_time", "closing_time"},
     *                 @OA\Property(property="name", type="string", example="My Store"),
     *                 @OA\Property(property="description", type="string", example="A great store"),
     *                 @OA\Property(property="type_id", type="integer", example=1),
     *                 @OA\Property(property="opening_time", type="string", example="08:00"),
     *                 @OA\Property(property="closing_time", type="string", example="20:00"),
     *                 @OA\Property(
     *                     property="images",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="image", type="string", format="binary"),
     *                         @OA\Property(property="dimmension", type="string", example="1024x768")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Store created successfully",
     *         @OA\JsonContent(type="object", @OA\Property(property="store_id", type="integer"))
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="User already has a store"
     *     )
     * )
     */
    public function createStore(Request $request)
    {
        Log::info('Store creation request: ' . json_encode($request->all()));

        $user = Auth::user();

        // Check if the user already has a store
        if ($user->store) {
            return response()->json(['error' => 'User already has a store'], 403);
        }

        // Validate the request data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'type_id' => 'required|integer',
            'opening_time' => 'required|string',
            'closing_time' => 'required|string',
            'images' => 'required|array',
            'images.*' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Create the store
            $store = Store::query()->create([
                'name' => $request->name,
                'description' => $request->description,
                'user_id' => $user->id,
                'type_id' => $request->type_id,
                'opening_time' => $request->opening_time,
                'closing_time' => $request->closing_time,
            ]);

            // Associate images with the store
            foreach ($request->file('images') as $image) {

                Log::info('Processing image: ' . json_encode($image));
                // Use the ImageOptimizationService to optimize and store the image
                $optimizedImageData = $this->imageOptimizationService->optimizeAndStore($image['image'], 'store_images');

                $media = Media::query()->create([
                    'type' => $image['image']->getClientMimeType(),
                    'url' => $optimizedImageData['url'],
                ]);

                StoreMedia::query()->create([
                    'store_id' => $store->id,
                    'media_id' => $media->id,
                ]);
            }

            $user->has_store = true;
            $user->save();

            // Log the request data
            Log::info('Store created: ' . json_encode($request->all()));
            return response()->json(['store_id' => $store->id], 201);
        } catch (Exception $e) {
            // Return a response with the error message
            return response()->json(['error' => 'An error occurred while creating the store: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Update an existing store with associated images.
     *
     * @OA\Put(
     *     path="/api/stores",
     *     tags={"Store"},
     *     summary="Update an existing store",
     *     description="Update an existing store with associated images.",
     *     operationId="updateStore",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"name", "description", "type_id", "images"},
     *                 @OA\Property(property="name", type="string", example="Updated Store"),
     *                 @OA\Property(property="description", type="string", example="An updated description"),
     *                 @OA\Property(property="type_id", type="integer", example=2),
     *                 @OA\Property(
     *                     property="images",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="image", type="string", format="binary"),
     *                         @OA\Property(property="dimmension", type="string", example="1920x1080")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Store updated successfully",
     *         @OA\JsonContent(type="object", @OA\Property(property="store_id", type="integer"))
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store not found"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function updateStore(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'type_id' => 'required|integer|exists:store_type,id',
            'opening_time' => 'required|string',
            'closing_time' => 'required|string',
            'images' => 'nullable|array',
            'images.*.image' => 'nullable|mimes:jpeg,png,jpg,gif,svg,heif',
            'images.*.dimmension' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Find the store
        $store = Store::firstWhere('user_id', Auth::id());

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        // Update store details
        $store->update([
            'name' => $request->name,
            'description' => $request->description,
            'type_id' => $request->type_id,
            'opening_time' => $request->opening_time,
            'closing_time' => $request->closing_time,
        ]);

        // Handle image uploads if provided
        if ($request->hasFile('images')) {

            Log::info('Updating store images: ' . json_encode($request->file('images')));

            foreach ($request->file('images') as $image) {

                // delete old images
                StoreMedia::where('store_id', $store->id)->delete();
                Media::whereIn('id', StoreMedia::where('store_id', $store->id)->pluck('media_id'))->delete();

                // Use the ImageOptimizationService to optimize and store the image
                $optimizedImageData = $this->imageOptimizationService->optimizeAndStore($image, 'store_images');

                $media = Media::create([
                    'type' => $image->getClientMimeType(),
                    'url' => $optimizedImageData['url'],
                ]);

                StoreMedia::create([
                    'store_id' => $store->id,
                    'media_id' => $media->id,
                    // 'dimmension' => $image['dimmension'] ?? null,
                ]);
            }
        }

        return response()->json(['store_id' => $store->id], 200);
    }

    /**
     * Create or update a store's location.
     *
     * @OA\Post(
     *     path="/api/stores/my-store/location",
     *     tags={"Store"},
     *     summary="Create or update a store's location",
     *     description="Create or update a store's location.",
     *     operationId="updateStoreLocation",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"address", "latitude", "longitude", "name"},
     *             @OA\Property(property="address", type="string", example="123 Main St"),
     *             @OA\Property(property="latitude", type="string", example="40.7128"),
     *             @OA\Property(property="longitude", type="string", example="-74.0060"),
     *             @OA\Property(property="name", type="string", example="Store Name")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Location updated successfully",
     *         @OA\JsonContent(type="object", @OA\Property(property="location_id", type="integer"))
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store not found"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function updateStoreLocation(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'address' => 'required|string|max:255',
            'latitude' => 'required|string|max:255',
            'longitude' => 'required|string|max:255',
            'name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Get the authenticated user
        $user = Auth::user();

        // Find the store associated with the authenticated user
        $store = $user->store;
        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        // Create or update the store's location
        $location = $store->location ?: new Location();

        $location->fill([
            'address' => $request->address,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'name' => $request->name,
        ]);

        $location->save();

        // Associate the location with the store if not already associated
        if (!$store->location_id) {
            $store->location_id = $location->id;
            $store->save();
        }

        return response()->json(['location_id' => $location->id], 200);
    }


    /**
     * Add a category to the store's favorite categories.
     *
     * @OA\Post(
     *     path="/api/stores/favorites-categories",
     *     tags={"Store"},
     *     summary="Add a category to store's favorite categories",
     *     description="Adds a category to the list of the store's favorite categories.",
     *     operationId="addCategoryToFavorite",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"category_id"},
     *             @OA\Property(property="category_id", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Category added to favorites successfully",
     *         @OA\JsonContent(type="object", @OA\Property(property="id", type="integer"))
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store or category not found"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function addCategoryToFavorite(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'category_id' => 'required|integer|exists:categories,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $store = Store::firstWhere('user_id', auth()->user()->id);
        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }


        // Check if the category is already in the store's favorites
        $existingFavorite = StoreFavoriteCategory::where('store_id', $store->id)
            ->where('category_id', $request->category_id)
            ->first();
        if ($existingFavorite) {
            return response()->json(['message' => 'Category already in favorites'], 422);
        }

        // Add the category to the store's favorite categories
        $favorite = StoreFavoriteCategory::create([
            'store_id' => $store->id,
            'category_id' => $request->category_id,
        ]);

        return response()->json(['id' => $favorite->id], 201);
    }

    /**
     * @OA\Post(
     *     path="/api/store-item",
     *     tags={"Store"},
     *     summary="Create a new item for a store",
     *     description="Create a new item with the provided details for a specific store.",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(property="title", type="string", example="Sample Item"),
     *                 @OA\Property(property="description", type="string", example="This is a sample item."),
     *                 @OA\Property(property="price", type="number", format="float", example=99.99),
     *                 @OA\Property(property="condition", type="string", example="new"),
     *                 @OA\Property(property="quantity", type="integer", example=10),
     *                 @OA\Property(property="brand_id", type="integer", example=1),
     *                 @OA\Property(property="category_id", type="integer", example=1),
     *                 @OA\Property(property="category_item_details", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="value", type="string", example="Sample value")
     *                     )
     *                 ),
     *                 @OA\Property(property="images", type="array",
     *                     @OA\Items(type="string", format="binary")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=201, description="Successful creation"),
     *     @OA\Response(response=400, description="Validation error"),
     *     @OA\Response(response=500, description="Failed to create item")
     * )
     */
    public function storeItem(Request $request): JsonResponse
    {
        Log::info('Store item request: ' . json_encode($request->all()));

        // Define validation rules
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric',
            'condition' => 'required|string',
            'quantity' => 'required|integer',
            'brand_id' => 'required|integer|exists:brand,id',
            'category_id' => 'required|integer|exists:category,id',
            'category_item_details' => 'nullable|array',
            'category_item_details.*.id' => 'nullable|integer|exists:category_detail,id',
            'category_item_details.*.value' => 'nullable|string',
            'images' => 'nullable|array',
            'images.*' => 'mimes:jpeg,png,jpg,gif,svg,heif',
                       'title_ar' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        // Start a database transaction
        DB::beginTransaction();

        try {


            $validator->validated()['status'] = eItemStatus::APPROVED;
            $item = Item::create($validator->validated());

            $store = Auth::user()->store;
            $item->store_id = $store->id;
            $item->status = eItemStatus::APPROVED;
            $item->save();

            // Fetch category details based on the provided category_id
            $categoryDetails = CategoryDetail::where('category_id', $validator->validated()['category_id'])->get();

            // Prepare data for CategoryItemDetail
            $categoryItemDetails = $categoryDetails->map(function ($categoryDetail) use ($item) {
                return [
                    'label_en' => $categoryDetail->label_en,
                    'label_ar' => $categoryDetail->label_ar,
                    'label_fr' => $categoryDetail->label_fr,
                    'value' => $categoryDetail->label_en, // Adjust if needed
                    'item_id' => $item->id,
                ];
            })->toArray();

            // Add category item details to the database
            CategoryItemDetail::insert($categoryItemDetails);

            // If there are additional category item details from the request, insert them
            if ($request->has('category_item_details')) {
                foreach ($request->input('category_item_details') as $detail) {
                    CategoryItemDetail::updateOrCreate(
                        ['id' => $detail['id'], 'item_id' => $item->id],
                        ['value' => $detail['value']]
                    );
                }
            }

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    // Use the ImageOptimizationService to optimize and store the image
                    $optimizedImageData = $this->imageOptimizationService->optimizeAndStore($image, 'item_images');

                    $media = Media::create([
                        'type' => $image->getClientMimeType(),
                        'url' => $optimizedImageData['url'],
                    ]);

                    ItemMedia::create([
                        'media_id' => $media->id,
                        'item_id' => $item->id,
                    ]);
                }
            }


            // Notify only followers who opted in (pivot notify = true)
            $followers = $store->followers()
                ->wherePivot('notify', true)
                ->with('deviceToken')
                ->get();

            foreach ($followers as $follower) {
                // Throttle: only one 'new_item' notification per follower per store within 24 hours
                $alreadyNotified = \App\Models\Notification::query()
                    ->where('user_id', $follower->id)
                    ->where('store_id', $store->id)
                    ->where('type', 'new_item')
                    ->where('created_at', '>=', now()->subDay())
                    ->exists();

                if ($alreadyNotified) {
                    continue; // Skip to avoid spamming this follower within 24h
                }

                $this->sendLocalizedNotification(
                    $follower,
                    'new_item',
                    [
                        'store_name' => $store->name,
                        'item_title' => $item->title
                    ],
                    $store->id
                );
            }
            // Commit the transaction
            DB::commit();

            return response()->json($item, 201);
        } catch (Exception $e) {
            // Rollback the transaction if something went wrong
            DB::rollback();

            Log::error('Error creating item for store: ' . $e->getMessage());

            return response()->json(['error' => 'Failed to create item'], 500);
        }
    }


    /**
     * Get favorite categories of a store.
     *
     * @OA\Get(
     *     path="/api/stores/favorites-categories",
     *     tags={"Store"},
     *     summary="Get favorite categories of a store",
     *     description="Returns a list of favorite categories for the authenticated store.",
     *     operationId="getFavoriteCategories",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="name", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store not found"
     *     )
     * )
     */
    public function getFavoriteCategories(): JsonResponse
    {
        $store = Store::query()->where('user_id', auth()->id())->first();

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $favoriteCategories = $store->favoriteCategories()
            ->select('category.id', 'category.title_en', 'category.title_ar', 'category.title_fr', 'category.parent_id')
            ->get();

        return response()->json($favoriteCategories);
    }

    /**
     * Get store types.
     *
     * @OA\Get(
     *     path="/api/store-types",
     *     tags={"Store"},
     *     summary="Get store types",
     *     description="Returns a list of store types.",
     *     operationId="getStoreTypes",
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="name", type="string")
     *             )
     *         )
     *     )
     * )
     */
    public function getStoreTypes(): \Illuminate\Http\JsonResponse
    {
        $user = null;
        if (request()->bearerToken()) {
            try {
                // Attempt to authenticate using Sanctum
                $user = auth('sanctum')->user();
            } catch (Exception $e) {
                // If token is invalid, continue as guest user
                $user = null;
            }
        }

        $userLang = $user ? ($user->lang ?? 'fr') : 'fr';

        $storeTypes = StoreType::all()->map(function ($storeType) use ($userLang) {
            return [
                'id' => $storeType->id,
                'name' => $storeType->{'name_' . $userLang} ?? $storeType->name_fr
            ];
        });

        return response()->json($storeTypes);
    }

    /**
     * Get liked items for a specific store.
     *
     * @OA\Get(
     *     path="/api/stores/{storeId}/liked-items",
     *     tags={"Store"},
     *     summary="Get liked items for a specific store",
     *     description="Returns a list of liked items for a specific store.",
     *     operationId="getLikedItemsForStore",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="storeId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="price", type="number", format="float")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store not found"
     *     )
     * )
     */
    public function getLikedItemsForStore($storeId): \Illuminate\Http\JsonResponse
    {
        // Validate the store ID
        $store = Store::query()->find($storeId);

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        // Fetch liked items for the specified store
        $likedItems = $store->likedItems()->get(['id', 'title', 'price']);

        return response()->json($likedItems, 200);
    }

    /**
     * Follow or unfollow a store.
     *
     * @OA\Put(
     *     path="/api/stores/follow/{storeId}",
     *     tags={"Store"},
     *     summary="Follow or unfollow a store",
     *     description="Toggle follow status for a store by ID.",
     *     operationId="followStore",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="storeId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Store follow status toggled successfully",
     *         @OA\JsonContent(type="object", @OA\Property(property="message", type="string"))
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store not found",
     *         @OA\JsonContent(type="object", @OA\Property(property="message", type="string"))
     *     )
     * )
     */
    public function followStore($storeId): \Illuminate\Http\JsonResponse
    {
        $store = Store::query()->find($storeId);
        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $user = Auth::user();
        $isFollowing = $user->followedStores()->where('store_id', $storeId)->exists();

        try {
            if ($isFollowing) {
                $user->followedStores()->detach($store->id);

                // Get updated followers count
                $followersCount = $store->followers()->count();

                return response()->json([
                    'message' => 'Store unfollowed successfully',
                    'followers_count' => $followersCount,
                    'is_followed' => false
                ], 200);
            } else {

                try {


                    $user->followedStores()->attach($store->id);

                    $this->sendLocalizedNotification(
                        $store->user,
                        'new_follower',
                        [
                            'follower_name' => "{$user->firstname} {$user->lastname}",
                            'item_title' => '',
                            'store_name' => '',

                        ]
                    );
                } catch (Exception $e) {
                    Log::warning('Failed to send follow notification', [
                        'store_id' => $store->id,
                        'follower_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);
                }

                // Get updated followers count
                $followersCount = $store->followers()->count();

                return response()->json([
                    'message' => 'Store followed successfully',
                    'followers_count' => $followersCount,
                    'is_followed' => true
                ], 200);

            }
        } catch (Exception $e) {
            Log::error('Error in followStore: ' . $e->getMessage(), [
                'store_id' => $store->id,
                'user_id' => $user->id,
                'exception' => $e
            ]);
            return response()->json(['message' => 'An error occurred while processing your request'], 500);
        }
    }

    // backoffice

    /**
     * @throws Exception
     */
    public function index(Request $request): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|JsonResponse|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        if ($request->ajax()) {
            $stores = Store::with(['user', 'type'])->get();
            return DataTables::of($stores)
                ->addIndexColumn()
                ->addColumn('action', function ($store) {
                    $viewUrl = route('stores.show', $store->id);
                    $deleteUrl = route('stores.destroy', $store->id);

                    $btn = '<a href="' . $viewUrl . '" class="btn btn-info btn-sm">View</a>';
                    $btn .= ' <form action="' . $deleteUrl . '" method="POST" style="display: inline;" class="delete-form">
                            ' . csrf_field() . '
                            ' . method_field('DELETE') . '
                            <button type="button" class="btn btn-danger btn-sm delete-btn">Delete</button>
                         </form>';

                    return $btn;
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        $stores = Store::query()->with(['user', 'type'])->get();
        return view('backoffice.stores.index', compact('stores'));
    }

    public function show($id): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        $store = Store::with(['items', 'user', 'type', 'location', 'images.media'])->findOrFail($id);
        return view('backoffice.stores.show', compact('store'));
    }

    /**
     * @OA\Get(
     *     path="/api/stores/promoted-recommended",
     *     tags={"Store"},
     *     summary="Get recommended stores",
     *     description="Get recommended stores (promoted) in a 4x3 display format. If fewer than 12 promoted stores, fill with stores having the most followers.",
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Store Name"),
     *                 @OA\Property(property="description", type="string", example="Store Description"),
     *                 @OA\Property(property="followers_count", type="integer", example=100),
     *                 @OA\Property(property="is_promoted", type="boolean", example=true),
     *                 @OA\Property(
     *                     property="images",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="url", type="string", example="http://example.com/image.jpg")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthenticated"),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function getRecommendedPromotedStores(): \Illuminate\Http\JsonResponse
    {
        $user = null;
        $followedStoreIds = collect();
        $notifyEnabledStoreIds = collect();

        // Authenticate user if token is provided
        if (request()->bearerToken()) {
            try {
                $user = auth('sanctum')->user();
                if ($user) {
                    // Get followed store IDs and those with notifications enabled
                    $followedStoreIds = $user->followedStores()->pluck('store.id');
                    $notifyEnabledStoreIds = $user->followedStores()->wherePivot('notify', true)->pluck('store.id');
                }
            } catch (Exception $e) {
                $user = null;
            }
        }

        $userLang = $user ? ($user->lang ?? 'fr') : 'fr';
        $userId = $user?->id;

        $stores = Store::query()
            ->select([
                'store.id',
                'store.name',
                'store.description',
                'store.is_promoted',
                'store.type_id',
                'store.opening_time',
                'store.closing_time',
                'store.promotion_position'
            ])
            ->where('store.is_promoted', true)
            ->whereNotNull('store.promotion_position')
            ->join('users', 'store.user_id', '=', 'users.id')
            ->join('new_user_subscriptions', 'users.id', '=', 'new_user_subscriptions.user_id')
            ->where('new_user_subscriptions.end_date', '>', now())
            ->orderBy('store.promotion_position', 'asc')
            ->withCount(['followers', 'items'])
            ->with([
                'images.media:id,url',
                'type:id,name_fr,name_ar,name_en'
            ])
            ->distinct()
            ->get();

        $response = $stores->map(function ($store) use ($followedStoreIds, $userLang ,  $notifyEnabledStoreIds) {
            return [
                'id' => $store->id,
                'name' => $store->name,
                'description' => $store->description,
                'followers_count' => $store->followers_count ?? 0,
                'items_count' => $store->items_count ?? 0,
                'is_promoted' => $store->is_promoted,
                'is_followed' => $followedStoreIds->contains($store->id),
                'is_notifications_enabled' => $notifyEnabledStoreIds->contains($store->id),
                'type' => $store->type,
                'type_name' => $store->type->{'name_' . $userLang} ?? $store->type->name_fr,
                'type_id' => $store->type_id,
                'opening_time' => $store->opening_time,
                'closing_time' => $store->closing_time,
                'is_open'  => $store->is_open,
                'images' => $store->images->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'url' => $image->media?->url,
                    ];
                }),
            ];
        });

        return response()->json($response, 200);
    }

    /**
     * Toggle store notifications for the authenticated user on a followed store.
     *
     * @OA\Put(
     *     path="/api/stores/{storeId}/notifications",
     *     tags={"Store"},
     *     summary="Enable/disable notifications for a followed store",
     *     description="Set notify flag on the user_store_follows pivot. User must be following the store.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(name="storeId", in="path", required=true, @OA\Schema(type="integer")),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"notify"},
     *             @OA\Property(property="notify", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(response=200, description="Updated", @OA\JsonContent(type="object",
     *         @OA\Property(property="is_notifications_enabled", type="boolean")
     *     )),
     *     @OA\Response(response=404, description="Store not found or not followed")
     * )
     */
    public function setStoreNotification(Request $request, $storeId): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'notify' => 'required|boolean',
        ]);

        $store = Store::find($storeId);
        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $user = Auth::user();
       

        // Update pivot notify flag
        $user->followedStores()->updateExistingPivot($storeId, ['notify' => $request->boolean('notify')]);

        return response()->json([
            'is_notifications_enabled' => (bool) $request->boolean('notify')
        ], 200);
    }

    /**
     * Get general recommended stores.
     *
     * @OA\Get(
     *     path="/api/stores/general-recommended",
     *     tags={"Store"},
     *     summary="Get general recommended stores",
     *     description="Get general recommended stores for public access. First shows promoted stores, then stores with best boutique scores.",
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Store Name"),
     *                 @OA\Property(property="description", type="string", example="Store Description"),
     *                 @OA\Property(property="followers_count", type="integer", example=100),
     *                 @OA\Property(property="items_count", type="integer", example=25),
     *                 @OA\Property(property="is_promoted", type="boolean", example=true),
     *                 @OA\Property(property="is_followed", type="boolean", example=false),
     *                 @OA\Property(property="boutique_score", type="number", format="float", example=85.5)
     *             )
     *         )
     *     ),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function getGeneralRecommendedStores(Request $request): \Illuminate\Http\JsonResponse
    {
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 10);
        $search = $request->get('search');

        // Build base query for promoted stores
        $promotedQuery = Store::query()
            ->where('store.is_promoted', true)
            ->whereNotNull('store.promotion_position')
            ->join('users', 'store.user_id', '=', 'users.id')
            ->join('new_user_subscriptions', 'users.id', '=', 'new_user_subscriptions.user_id')
            ->where('new_user_subscriptions.end_date', '>', now())
            ->orderBy('store.promotion_position', 'asc')
            ->withCount(['followers', 'items'])
            ->with(['images.media:id,url', 'type:id,name_fr,name_ar,name_en'])
            ->distinct();

        // Apply search filter to promoted stores
        if ($search) {
            $promotedQuery->where(function ($q) use ($search) {
                $q->where('store.name', 'LIKE', "%{$search}%")
                  ->orWhere('store.description', 'LIKE', "%{$search}%");
            });
        }

        $promotedStores = $promotedQuery->get();

        // Build base query for other stores
        $promotedStoreIds = $promotedStores->pluck('id');
        $otherQuery = Store::query()
            ->whereNotIn('store.id', $promotedStoreIds)
            ->join('users', 'store.user_id', '=', 'users.id')
            ->join('new_user_subscriptions', 'users.id', '=', 'new_user_subscriptions.user_id')
            ->where('new_user_subscriptions.end_date', '>', now())
            ->withCount(['followers', 'items'])
            ->with(['images.media:id,url', 'type:id,name_fr,name_ar,name_en'])
            ->distinct();

        // Apply search filter to other stores
        if ($search) {
            $otherQuery->where(function ($q) use ($search) {
                $q->where('store.name', 'LIKE', "%{$search}%")
                  ->orWhere('store.description', 'LIKE', "%{$search}%");
            });
        }

        $otherStores = $otherQuery->get();

        // Calculate boutique score for other stores
        $otherStores = $otherStores->map(function ($store) {
            $store->boutique_score = $this->calculateBoutiqueScore($store);
            return $store;
        });

        // Sort other stores by boutique score (descending)
        $otherStores = $otherStores->sortByDesc('boutique_score');

        // Combine promoted stores first, then sorted other stores
        $result = $promotedStores->merge($otherStores);

        // Apply pagination to the combined result
        $offset = ($page - 1) * $perPage;
        $paginatedStores = $result->slice($offset, $perPage)->values();

        // Try to authenticate user if token is provided
        $user = null;
        if (request()->bearerToken()) {
            try {
                // Attempt to authenticate using Sanctum
                $user = auth('sanctum')->user();
            } catch (Exception $e) {
                // If token is invalid, continue as guest user
                $user = null;
            }
        }

        $followedStoreIds = $user ? $user->followedStores()->pluck('store_id')->toArray() : [];
        $userLang = $user ? ($user->lang ?? 'fr') : 'fr';

        // Determine notification-enabled store IDs for current user
        $notifyEnabledStoreIds = [];
        if ($user) {
            $notifyEnabledStoreIds = $user->followedStores()->wherePivot('notify', true)->pluck('store.id')->toArray();
        }

        $stores = $paginatedStores->map(function ($store) use ($followedStoreIds, $notifyEnabledStoreIds, $userLang) {
            $store->is_followed = in_array($store->id, $followedStoreIds);
            $store->is_notifications_enabled = in_array($store->id, $notifyEnabledStoreIds);
            $store->followers_count = $store->followers_count ?? 0;
            $store->items_count = $store->items_count ?? 0;
            $store->type_name = $store->type->{'name_' . $userLang} ?? $store->type->name_fr;

            return $store;
        });

        return response()->json($stores->values(), 200);
    }

    public function create()
    {
        $storeTypes = StoreType::all();
        return view('backoffice.stores.create', compact('storeTypes'));
    }

    public function destroy($id): \Illuminate\Http\RedirectResponse
    {
        try {
            $store = Store::query()->findOrFail($id);

            // Delete related images
            $store->images()->delete();

            // Delete the store
            $store->delete();

            // Redirect to the stores index page with success message
            return redirect()->route('stores.index')->with('success', 'Store deleted successfully.');
        } catch (Exception $e) {
            // Log the error message (optional)
            Log::error('Error deleting store: ' . $e->getMessage());

            // Redirect back with an error message
            return redirect()->route('stores.index')->with('error', 'An error occurred while deleting the store.');
        }
    }

    /**
     * Add a category to the store's favorite categories.
     *
     * @OA\Post(
     *     path="/api/stores/add-favorites-category",
     *     tags={"Store"},
     *     summary="Add favorite category",
     *     description="Add a category to the authenticated user's store's favorite categories.",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="category_id", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Category added to favorites successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Category added to favorites successfully")
     *         )
     *     ),
     *     @OA\Response(response=400, description="Bad Request"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=404, description="User does not have a store")
     * )
     */
    public function addFavoriteCategory(Request $request)
    {
        $user = Auth::user();

        // Retrieve the store associated with the authenticated user
        $store = $user->store;

        if (!$store) {
            return response()->json(['error' => 'User does not have a store'], 404);
        }

        // Validate the category ID
        $request->validate([
            'category_id' => 'required|exists:category,id',
        ]);

        $categoryId = $request->input('category_id');

        // Check if the category is already in the store's favorites
        if ($store->favoriteCategories()->where('category_id', $categoryId)->exists()) {
            return response()->json(['error' => 'Category already in favorites'], 400);
        }

        // Add the category to the store's favorites
        $store->favoriteCategories()->attach($categoryId);

        return response()->json(['message' => 'Category added to favorites successfully'], 201);
    }

    /**
     * Get store information including images, number of followers, number of items, and items.
     *
     * @OA\Get(
     *     path="/api/stores/info",
     *     tags={"Store"},
     *     summary="Get store information",
     *     description="Returns store information including images, number of followers, number of items, and items.",
     *     operationId="getStoreInfo",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="name", type="string"),
     *             @OA\Property(property="description", type="string"),
     *             @OA\Property(property="images", type="array", @OA\Items(type="string")),
     *             @OA\Property(property="followers_count", type="integer"),
     *             @OA\Property(property="items_count", type="integer"),
     *             @OA\Property(property="items", type="array", @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="price", type="number", format="float")
     *             ))
     *         )
     *     ),
     *     @OA\Response(response=404, description="Store not found")
     * )
     */
    public function getStoreInfo(): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $store = Store::with(['images.media', 'followers', 'items', 'location'])->withCount('followers')->where('user_id', $user->id)->first();


        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        return response()->json($store, 200);
    }

    /**
     * Get favorite categories of a store by store ID.
     *
     * @OA\Get(
     *     path="/api/stores/{storeId}/favorites-categories",
     *     tags={"Store"},
     *     summary="Get favorite categories of a store by store ID",
     *     description="Returns a list of favorite categories for the specified store.",
     *     operationId="getFavoriteCategoriesByStoreId",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="storeId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="title_en", type="string"),
     *                 @OA\Property(property="title_ar", type="string"),
     *                 @OA\Property(property="title_fr", type="string"),
     *                 @OA\Property(property="parent", type="object",
     *                     @OA\Property(property="id", type="integer"),
     *                     @OA\Property(property="title_en", type="string"),
     *                     @OA\Property(property="title_ar", type="string"),
     *                     @OA\Property(property="title_fr", type="string")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="Store not found")
     * )
     */
    public function getFavoriteCategoriesByStoreId($storeId): \Illuminate\Http\JsonResponse
    {
        $store = Store::query()->find($storeId);

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $favoriteCategories = $store->favoriteCategories()
            ->with('parent:id,title_en,title_ar,title_fr')
            ->get(['category.id', 'title_en', 'title_ar', 'title_fr', 'parent_id']);

        return response()->json($favoriteCategories, 200);
    }

    /**
     * Get items by favorite category ID for the authenticated user.
     *
     * @OA\Get(
     *     path="/api/items/favorite-category/{categoryId}",
     *     tags={"Item"},
     *     summary="Get items by favorite category ID",
     *     description="Returns a list of items for the authenticated user by favorite category ID.",
     *     operationId="getItemsByFavoriteCategoryId",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="categoryId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Favorite Category ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="price", type="number", format="float"),
     *                 @OA\Property(property="description", type="string"),
     *                 @OA\Property(property="condition", type="string"),
     *                 @OA\Property(property="quantity", type="integer"),
     *                 @OA\Property(property="brand_id", type="integer"),
     *                 @OA\Property(property="category_id", type="integer"),
     *                 @OA\Property(property="images", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer"),
     *                         @OA\Property(property="url", type="string")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="Items not found")
     * )
     */
    public function getItemsByFavoriteCategoryId($categoryId): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $items = Item::with(['images'])
            ->where('user_id', $user->id)
            ->where('category_id', $categoryId)
            ->get();

        if ($items->isEmpty()) {
            return response()->json(['message' => 'Items not found'], 404);
        }

        return response()->json($items, 200);
    }

    /**
     * Get items by category ID for a specific store.
     *
     * @OA\Get(
     *     path="/api/stores/{storeId}/items/category/{categoryId}",
     *     tags={"Store"},
     *     summary="Get items by category ID for a specific store",
     *     description="Returns a list of items for a specific store by category ID.",
     *     operationId="getItemsByCategoryId",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="storeId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Parameter(
     *         name="categoryId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Category ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="price", type="number", format="float"),
     *                 @OA\Property(property="description", type="string"),
     *                 @OA\Property(property="condition", type="string"),
     *                 @OA\Property(property="quantity", type="integer"),
     *                 @OA\Property(property="brand_id", type="integer"),
     *                 @OA\Property(property="category_id", type="integer"),
     *                 @OA\Property(property="images", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer"),
     *                         @OA\Property(property="url", type="string")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="Items not found")
     * )
     */
    public function getItemsByCategoryId($storeId, $categoryId): \Illuminate\Http\JsonResponse
    {
        $store = Store::query()->find($storeId);

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $items = $store->items()
            ->with(['images'])
            ->where('category_id', $categoryId)
            ->get();

        if ($items->isEmpty()) {
            return response()->json(['message' => 'Items not found'], 404);
        }

        return response()->json($items, 200);
    }

    /**
     * Get items by store ID.
     *
     * @OA\Get(
     *     path="/api/stores/{storeId}/items",
     *     tags={"Store"},
     *     summary="Get items by store ID",
     *     description="Returns a list of items for a specific store by store ID.",
     *     operationId="getItemsByStoreId",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="storeId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="price", type="number", format="float"),
     *                 @OA\Property(property="description", type="string"),
     *                 @OA\Property(property="condition", type="string"),
     *                 @OA\Property(property="quantity", type="integer"),
     *                 @OA\Property(property="brand_id", type="integer"),
     *                 @OA\Property(property="category_id", type="integer"),
     *                 @OA\Property(property="images", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer"),
     *                         @OA\Property(property="url", type="string")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="Items not found")
     * )
     */
    public function getItemsByStoreId($storeId): \Illuminate\Http\JsonResponse
    {
        $store = Store::query()->find($storeId);

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $items = $store->items()
            ->with(['images'])
            ->with(['brand', 'category',])
            ->get();

        if ($items->isEmpty()) {
            return response()->json(['message' => 'No items found'], 404);
        }

        return response()->json($items, 200);
    }

    /**
     * Get items for the authenticated user's store.
     *
     * @OA\Get(
     *     path="/api/stores/my-store/items",
     *     tags={"Store"},
     *     summary="Get items for the authenticated user's store",
     *     description="Returns a list of items for the authenticated user's store.",
     *     operationId="getMyStoreItems",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="price", type="number", format="float"),
     *                 @OA\Property(property="description", type="string"),
     *                 @OA\Property(property="condition", type="string"),
     *                 @OA\Property(property="quantity", type="integer"),
     *                 @OA\Property(property="brand_id", type="integer"),
     *                 @OA\Property(property="category_id", type="integer"),
     *                 @OA\Property(property="images", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer"),
     *                         @OA\Property(property="url", type="string")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="Items not found")
     * )
     */
    public function getMyStoreItems(): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $items = $store->items()->with('images')->get();

        if ($items->isEmpty()) {
            return response()->json(['message' => 'No Items found'], 404);
        }

        return response()->json($items, 200);
    }

    /**
     * @OA\Get(
     *     path="/followed-stores",
     *     summary="Get stores followed by the authenticated user",
     *     tags={"Stores"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Store Name"),
     *                 @OA\Property(property="description", type="string", example="Store Description"),
     *                 @OA\Property(property="followers_count", type="integer", example=100)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated"
     *     )
     * )
     */
    public function getFollowedStoresByUser(): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $followedStores = $user->followedStores()->withCount('followers')->with('images.media')->get();




        $userLang = $user ? ($user->lang ?? 'fr') : 'fr';

        $response = $followedStores->map(function ($store) use ($userLang) {


            return [
                'id' => $store->id,
                'name' => $store->name,
                'description' => $store->description,
                'user_id' => $store->user_id,
                'type_name' => $store->type->{'name_' . $userLang} ?? $store->type->name_fr,
                'location_id' => $store->location_id,
                'created_at' => $store->created_at,
                'updated_at' => $store->updated_at,
                'is_promoted' => $store->is_promoted,
                'type_id' => $store->type_id,
                'opening_time' => $store->opening_time,
                'closing_time' => $store->closing_time,
                    'is_open'  => $store->is_open,
                'followers_count' => $store->followers_count,
                'is_followed' => true,
                'images' => $store->images->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'media_id' => $image->media_id,
                        'store_id' => $image->store_id,
                        'created_at' => $image->created_at,
                        'updated_at' => $image->updated_at,
                        'media' => [
                            'id' => $image->media->id,
                            'type' => $image->media->type,
                            'url' => $image->media->url,
                            'aspect_ratio' => $image->media->aspect_ratio,
                            'created_at' => $image->media->created_at,
                            'updated_at' => $image->media->updated_at,
                        ],
                    ];
                }),
            ];
        });

        return response()->json($response, 200);
    }

    /**
     * Delete a favorite category from the authenticated user's store.
     *
     * @OA\Delete(
     *     path="/api/stores/favorite-category/{categoryId}",
     *     tags={"Store"},
     *     summary="Delete favorite category",
     *     description="Delete a favorite category from the authenticated user's store.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="categoryId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Category ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Category removed from favorites successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Category removed from favorites successfully")
     *         )
     *     ),
     *     @OA\Response(response=400, description="Bad Request"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=404, description="Store or category not found")
     * )
     */
    public function deleteFavoriteCategory($categoryId): JsonResponse
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $store = $user->store()->withCount('followers')->with('images.media')->first();

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        // Check if the category is a favorite category of the store
        if (!$store->favoriteCategories()->where('category_id', $categoryId)->exists()) {
            return response()->json(['message' => 'Category not found in favorites'], 404);
        }

        // Remove the category from the store's favorites
        $store->favoriteCategories()->detach($categoryId);

        return response()->json(['message' => 'Category removed from favorites successfully'], 200);
    }

    /**
     * @OA\Get(
     *     path="/api/stores/{id}",
     *     tags={"Store"},
     *     summary="Get store details",
     *     description="Get detailed information about a store, including whether the authenticated user follows it.",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="Store Name"),
     *             @OA\Property(property="description", type="string", example="Store Description"),
     *             @OA\Property(property="followers_count", type="integer", example=100),
     *             @OA\Property(property="is_promoted", type="boolean", example=true),
     *             @OA\Property(property="is_followed", type="boolean", example=false),
     *             @OA\Property(
     *                 property="images",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="url", type="string", example="http://example.com/image.jpg")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthenticated"),
     *     @OA\Response(response=404, description="Store not found"),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function getStore($id): \Illuminate\Http\JsonResponse
    {
        $store = Store::with('images.media')->find($id);

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        // Try to authenticate user if token is provided
        $user = null;
        if (request()->bearerToken()) {
            try {
                // Attempt to authenticate using Sanctum
                $user = auth('sanctum')->user();
            } catch (Exception $e) {
                // If token is invalid, continue as guest user
                $user = null;
            }
        }

        $isFollowed = $user ? $user->followedStores()->where('store_id', $id)->exists() : false;
        $isNotificationsEnabled = false;
        if ($user && $isFollowed) {
            $isNotificationsEnabled = $user->followedStores()->where('store_id', $id)->wherePivot('notify', true)->exists();
        }



        $userLang = $user ? ($user->lang ?? 'fr') : 'fr';

        $response = [
            'id' => $store->id,
            'name' => $store->name,
            'description' => $store->description,
            'user_id' => $store->user_id,
            'type_id' => $store->type_id,
            'type_name' => $store->type->{'name_' . $userLang} ?? $store->type->name_fr,
            'location_id' => $store->location_id,
            'created_at' => $store->created_at,
            'updated_at' => $store->updated_at,
            'is_promoted' => $store->is_promoted,
            'opening_time' => $store->opening_time,
            'closing_time' => $store->closing_time,
                    'is_open'  => $store->is_open,

            'followers_count' => $store->followers_count,
            'is_followed' => $isFollowed,
            'is_notifications_enabled' => $isNotificationsEnabled,
            'images' => $store->images->map(function ($image) {
                return [
                    'id' => $image->id,
                    'media_id' => $image->media_id,
                    'store_id' => $image->store_id,
                    'created_at' => $image->created_at,
                    'updated_at' => $image->updated_at,
                    'media' => [
                        'id' => $image->media->id,
                        'type' => $image->media->type,
                        'url' => $image->media->url,
                        'aspect_ratio' => $image->media->aspect_ratio,
                        'created_at' => $image->media->created_at,
                        'updated_at' => $image->media->updated_at,
                    ],
                ];
            }),
        ];

        return response()->json($response, 200);
    }

    /**
     * Delete an item from the authenticated user's store.
     *
     * @OA\Delete(
     *     path="/api/stores/my-store/items/{itemId}",
     *     tags={"Store"},
     *     summary="Delete an item from the authenticated user's store",
     *     description="Delete an item from the authenticated user's store by item ID.",
     *     operationId="deleteItemFromMyStore",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="itemId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Item ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Item deleted successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Item deleted successfully")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Item not found"),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function deleteItemFromMyStore($itemId): JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $item = $store->items()->find($itemId);

        if (!$item) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        $item->delete();

        return response()->json(['message' => 'Item deleted successfully'], 200);
    }

    /**
     * @OA\Get(
     *     path="/api/store/orders",
     *     summary="Get all orders for the authenticated user's store",
     *     description="Returns a list of orders with their items and images for the store associated with the authenticated user.",
     *     tags={"Orders"},
     *     security={{"bearerAuth":{}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="List of store orders",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(property="id", type="integer", description="Order ID"),
     *                 @OA\Property(property="store_id", type="integer", description="Store ID"),
     *                 @OA\Property(property="status", type="string", description="Order status"),
     *                 @OA\Property(property="delivery_charge", type="number", format="float", description="Delivery charge"),
     *                 @OA\Property(property="delivery_charge_id", type="integer", description="Delivery charge ID"),
     *                 @OA\Property(property="location_id", type="integer", description="Location ID"),
     *                 @OA\Property(property="user_id", type="integer", description="User ID"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", description="Creation timestamp"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", description="Update timestamp"),
     *                 @OA\Property(
     *                     property="items",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", description="Item ID"),
     *                         @OA\Property(property="title", type="string", description="Item title"),
     *                         @OA\Property(property="description", type="string", description="Item description"),
     *                         @OA\Property(property="price", type="number", format="float", description="Item price"),
     *                         @OA\Property(property="sold_out", type="boolean", description="Is the item sold out"),
     *                         @OA\Property(property="condition", type="string", description="Item condition"),
     *                         @OA\Property(property="status", type="string", description="Item status"),
     *                         @OA\Property(property="rejection_reason", type="string", nullable=true, description="Rejection reason if any"),
     *                         @OA\Property(property="quantity", type="integer", description="Item quantity in the order"),
     *                         @OA\Property(property="brand_id", type="integer", description="Brand ID"),
     *                         @OA\Property(property="user_id", type="integer", description="User ID"),
     *                         @OA\Property(property="store_id", type="integer", description="Store ID"),
     *                         @OA\Property(property="category_id", type="integer", description="Category ID"),
     *                         @OA\Property(property="created_at", type="string", format="date-time", description="Creation timestamp"),
     *                         @OA\Property(property="updated_at", type="string", format="date-time", description="Update timestamp"),
     *                         @OA\Property(property="is_promoted", type="boolean", description="Is the item promoted"),
     *                         @OA\Property(
     *                             property="images",
     *                             type="array",
     *                             @OA\Items(
     *                                 @OA\Property(property="id", type="integer", description="Image ID"),
     *                                 @OA\Property(property="type", type="string", description="Image type"),
     *                                 @OA\Property(property="url", type="string", description="Image URL"),
     *                                 @OA\Property(property="aspect_ratio", type="string", description="Image aspect ratio"),
     *                                 @OA\Property(property="created_at", type="string", format="date-time", description="Creation timestamp"),
     *                                 @OA\Property(property="updated_at", type="string", format="date-time", description="Update timestamp")
     *                             )
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=404,
     *         description="Store or orders not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Store not found")
     *         )
     *     )
     * )
     */
    public function getStoreOrders(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $query = $store->orders()->with(['items.images', 'user', 'location', 'orderPayment', 'orderItems']);

        // Add date range filter
        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }
        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        // Add payment status filter
        if ($request->has('is_paid')) {
            $query->where('is_paid', $request->boolean('is_paid'));
        }

        $orders = $query->get();

        if ($orders->isEmpty()) {
            return response()->json(['message' => 'Orders not found'], 404);
        }

        $response = $orders->map(function ($order) {
            // Calculate total orders (sum of all items)
            $totalOrders = $order->items->sum(function ($item) {
                return $item->pivot->price * $item->pivot->quantity;
            });

            return [
                'id' => $order->id,
                'store_id' => $order->store_id,
                'status' => $order->status,
                'is_paid' => $order->is_paid,
                'delivery_charge' => $order->delivery_charge,
                'delivery_charge_id' => DeliveryCharge::query()->where('amount', $order->delivery_charge)->first()->id ?? 0,
                'location_id' => $order->location_id,
                'user_id' => $order->user_id,
                'userfirstname' => $order->user->firstname,
                'userlastname' => $order->user->lastname,
                'userphone' => $order->user->phone,
                'totalorders' => $totalOrders,
                'created_at' => $order->created_at,
                'updated_at' => $order->updated_at,
                'payment_screenshot' => $order->orderPayment->photo_url ?? null,
                'delivery_latitude' => $order->location ? $order->location->latitude : null,
                'delivery_longitude' => $order->location ? $order->location->longitude : null,
                'delivery_address' => $order->location ? $order->location->address : null,
                'is_cash_on_delivery' => $order->is_cash_on_delivery,

                'items' => $order->orderItems->map(function ($item) {
                    return [

                        'item_id' => $item->id,
                        'id' => $item->id,
                        'title' => $item->name,
                        'description' => $item->description,
                        'price' => $item->price,
                        'title_ar' => $item->title_ar,
                        'description_ar' => $item->description_ar,
                        'sold_out' => $item->sold_out,
                        'condition' => $item->condition,
                        'status' => $item->status,
                        'rejection_reason' => $item->rejection_reason,
                        'quantity' => $item->quantity,
                        'brand_id' => $item->brand_id,
                        'user_id' => $item->user_id,
                        'store_id' => $item->store_id,
                        'category_id' => $item->category_id,
                        'created_at' => $item->created_at,
                        'updated_at' => $item->updated_at,
                        'is_promoted' => $item->is_promoted,
                        'images' => $item->originalItem->images->pluck('url')
                    ];

                }),
            ];
        });

        return response()->json($response, 200);
    }

    /**
     * @OA\Put(
     *     path="/api/store/item/{itemId}/promotion",
     *     operationId="setPromotionForItemInMyStore",
     *     tags={"Store"},
     *     summary="Set a promotion for a specific item in the store",
     *     description="Sets a promotion percentage for a specific item in the user's store.",
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="itemId",
     *         in="path",
     *         description="ID of the item",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"promotion_percentage"},
     *             @OA\Property(
     *                 property="promotion_percentage",
     *                 type="number",
     *                 format="float",
     *                 example=15.5,
     *                 description="The promotion percentage to be set"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Promotion set successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Promotion set successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store or item not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Store not found or Item not found")
     *         )
     *     )
     * )
     */
    public function setPromotionForItemInMyStore(Request $request, $itemId): JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $item = $store->items()->find($itemId);

        if (!$item) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        $item->promotion_percentage = $request->promotion_percentage;
        $item->has_promotion = true;
        $item->save();

        // Notify followers (who opted-in) about the promotion with a 24h throttle per store
        $store = $item->store; // assuming relation exists
        if ($store) {
            $followers = $store->followers()
                ->wherePivot('notify', true)
                ->with('deviceToken')
                ->get();

            foreach ($followers as $follower) {
                // Throttle: only one 'store_item_promoted' per follower per store in the last 24 hours
                $alreadyNotified = Notification::query()
                    ->where('user_id', $follower->id)
                    ->where('store_id', $store->id)
                    ->where('type', 'store_item_promoted')
                    ->where('created_at', '>=', now()->subDay())
                    ->exists();

                if ($alreadyNotified) {
                    continue;
                }

                $this->sendLocalizedNotification(
                    $follower,
                    'store_item_promoted',
                    [
                        'store_name' => $store->name,
                        'item_title' => $item->title ?? $item->name ?? '',
                        'item_id' => $item->id,
                    ],
                    $store->id
                );
            }
        }

        return response()->json(['message' => 'Promotion set successfully'], 200);
    }

    /**
     * @OA\Delete(
     *     path="/api/store/item/{itemId}/promotion",
     *     operationId="removePromotionForItemInMyStore",
     *     tags={"Store"},
     *     summary="Remove a promotion from a specific item in the store",
     *     description="Removes the promotion for a specific item in the user's store.",
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="itemId",
     *         in="path",
     *         description="ID of the item",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Promotion removed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Promotion removed successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store or item not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Store not found or Item not found")
     *         )
     *     )
     * )
     */
    public function removePromotionForItemInMyStore($itemId): JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $item = $store->items()->find($itemId);

        if (!$item) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        $item->promotion_percentage = 0;
        $item->has_promotion = false;
        $item->save();

        return response()->json(['message' => 'Promotion removed successfully'], 200);
    }

    /**
     * @OA\Post(
     *     path="/api/stores/my-store/promotions",
     *     tags={"Store"},
     *     summary="Add a promotion for the authenticated user's store",
     *     description="Add a promotion for the authenticated user's store.",
     *     operationId="addPromotionForMyStore",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"promotion_percentage"},
     *             @OA\Property(property="promotion_percentage", type="number", format="float", example=15.5, description="Promotion percentage")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Promotion added successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Promotion added successfully")
     *         )
     *     ),
     *     @OA\Response(response=400, description="Bad Request"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=404, description="Store not found")
     * )
     */
    public function addPromotionForMyStore(Request $request): JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $store->promotion_percentage = $request->promotion_percentage;
        $store->has_promotion = true;
        $store->save();

        return response()->json(['message' => 'Promotion added successfully'], 200);
    }


    /**
     * @OA\Delete(
     *     path="/api/stores/my-store/promotions",
     *     tags={"Store"},
     *     summary="Remove a promotion from the authenticated user's store",
     *     description="Remove a promotion from the authenticated user's store.",
     *     operationId="removePromotionForMyStore",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Promotion removed successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Promotion removed successfully")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Store not found")
     * )
     */
    public function removePromotionForMyStore(): JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $store->promotion_percentage = 0;
        $store->has_promotion = false;
        $store->save();

        return response()->json(['message' => 'Promotion removed successfully'], 200);
    }

    public function redirectToApp($storeId)
    {
        return view("redirect-page", ['storeId' => $storeId]);
    }

    /**
     * Get the location of the authenticated user's store.
     *
     * @OA\Get(
     *     path="/api/stores/my-store/location",
     *     tags={"Store"},
     *     summary="Get store location",
     *     description="Returns the location of the authenticated user's store.",
     *     operationId="getStoreLocation",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="address", type="string"),
     *             @OA\Property(property="latitude", type="number", format="float"),
     *             @OA\Property(property="longitude", type="number", format="float"),
     *             @OA\Property(property="created_at", type="string", format="date-time"),
     *             @OA\Property(property="updated_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Store not found")
     * )
     */
    public function getStoreLocation(): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $location = $store->location;

        return response()->json($location, 200);
    }

    public function postStoreLocation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'address' => 'required|string',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'name' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Validation error'], 400);
        }

        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $location = Location::query()->create($request->all());
        $store->location()->associate($location);

        return response()->json(['message' => 'Location posted successfully'], 200);
    }

    public function editStoreLocation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'address' => 'required|string|max:255',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Bad Request', 'errors' => $validator->errors()], 400);
        }

        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $location = $store->location ?: new Location();
        $location->fill($request->all());
        $location->save();

        if (!$store->location_id) {
            $store->location()->associate($location);
            $store->save();
        }

        return response()->json(['message' => 'Location posted successfully'], 200);
    }

    public function promoteStore(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'store_id' => 'required|exists:store,id',
            'position' => 'required|integer|min:1',
        ]);

        $store = Store::findOrFail($request->store_id);

        if ($store->is_promoted) {
            return back()->with('error', 'This store is already promoted.');
        }

        $existingStoreWithPosition = Store::where('promotion_position', $request->position)
            ->where('is_promoted', true)
            ->first();

        DB::transaction(function () use ($request, $existingStoreWithPosition) {
            if ($existingStoreWithPosition) {
                $storesToShift = Store::where('promotion_position', '>=', $request->position)
                    ->where('is_promoted', true)
                    ->orderBy('promotion_position', 'asc')
                    ->get();

                foreach ($storesToShift as $storeToShift) {
                    $storeToShift->promotion_position += 1;
                    $storeToShift->save();
                }
            }

            $store = Store::findOrFail($request->store_id);
            $store->is_promoted = true;
            $store->promotion_position = $request->position;
            $store->save();
        });

        return back()->with('success', 'Store promoted successfully');
    }

    public function updatePromotionPositions(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'positions' => 'required|array',
            'positions.*.store_id' => 'required|exists:store,id',
            'positions.*.position' => 'required|integer|min:1',
        ]);

        $positions = collect($request->positions)->pluck('position')->toArray();
        $duplicatePositions = array_diff_assoc($positions, array_unique($positions));

        if (!empty($duplicatePositions)) {
            return back()->with('error', 'Duplicate positions detected. Each store must have a unique position.');
        }

        DB::transaction(function () use ($request) {
            $storeIds = collect($request->positions)->pluck('store_id')->toArray();

            Store::whereIn('id', $storeIds)->update([
                'promotion_position' => DB::raw('promotion_position + 10000')
            ]);

            foreach ($request->positions as $positionData) {
                Store::where('id', $positionData['store_id'])
                    ->update(['promotion_position' => $positionData['position']]);
            }
        });

        return back()->with('success', 'Promotion positions updated successfully');
    }

    public function removePromotion(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'store_id' => 'required|exists:store,id',
        ]);

        $store = Store::findOrFail($request->store_id);

        if (!$store->is_promoted) {
            return back()->with('error', 'This store is not currently promoted.');
        }

        $currentPosition = $store->promotion_position;

        DB::transaction(function () use ($store, $currentPosition) {
            $store->is_promoted = false;
            $store->promotion_position = null;
            $store->save();

            if ($currentPosition) {
                $storesToReorder = Store::where('promotion_position', '>', $currentPosition)
                    ->where('is_promoted', true)
                    ->orderBy('promotion_position', 'asc')
                    ->get();

                foreach ($storesToReorder as $storeToReorder) {
                    $storeToReorder->promotion_position -= 1;
                    $storeToReorder->save();
                }
            }
        });

        return back()->with('success', 'Promotion removed successfully');
    }

    /**
     * Toggle store open/closed status
     *
     * @OA\Post(
     *     path="/api/stores/toggle-status",
     *     tags={"Store"},
     *     summary="Toggle store open/closed status",
     *     description="Open or close a store",
     *     operationId="toggleStoreStatus",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"is_open"},
     *             @OA\Property(property="is_open", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Status updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Store status updated successfully"),
     *             @OA\Property(property="is_open", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=404, description="Store not found")
     * )
     */
    public function toggleStoreStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'is_open' => 'required|boolean',
        ]);

        
        if ($validator->fails()) {
            return response()->json(['message' => 'Validation error', 'errors' => $validator->errors()], 422);
        }

        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $store->is_open = $request->is_open;
        $store->save();

        return response()->json([
            'message' => 'Store status updated successfully',
            'is_open' => $store->is_open
        ], 200);
    }

    /**
     * Calculate boutique score for a store based on various metrics.
     *
     * @param Store $store
     * @return float
     */
    private function calculateBoutiqueScore($store): float
    {
        $score = 0;

        // 1. Followers score (40% weight) - max 40 points
        $followersCount = $store->followers_count ?? 0;
        if ($followersCount > 100) {
            $score += 40;
        } elseif ($followersCount > 50) {
            $score += 35;
        } elseif ($followersCount > 20) {
            $score += 30;
        } elseif ($followersCount > 10) {
            $score += 25;
        } elseif ($followersCount > 5) {
            $score += 20;
        } elseif ($followersCount > 0) {
            $score += 10;
        }

        // 2. Items count score (25% weight) - max 25 points
        $itemsCount = $store->items_count ?? 0;
        if ($itemsCount > 50) {
            $score += 25;
        } elseif ($itemsCount > 30) {
            $score += 22;
        } elseif ($itemsCount > 20) {
            $score += 18;
        } elseif ($itemsCount > 10) {
            $score += 15;
        } elseif ($itemsCount > 5) {
            $score += 10;
        } elseif ($itemsCount > 0) {
            $score += 5;
        }

        // 3. Store activity/recency score (20% weight) - max 20 points
        if ($store->created_at) {
            $daysSinceCreated = $store->created_at->diffInDays(now());
            if ($daysSinceCreated <= 30) {
                $score += 20; // New store bonus
            } elseif ($daysSinceCreated <= 90) {
                $score += 15;
            } elseif ($daysSinceCreated <= 180) {
                $score += 10;
            } elseif ($daysSinceCreated <= 365) {
                $score += 5;
            }
        } else {
            // If no created_at date, give a neutral score
            $score += 5;
        }

        // 4. Store completeness score (10% weight) - max 10 points
        $completenessScore = 0;
        if (!empty($store->description)) $completenessScore += 2;
        if ($store->images && $store->images->count() > 0) $completenessScore += 3;
        if ($store->location_id) $completenessScore += 2;
        if ($store->opening_time && $store->closing_time) $completenessScore += 2;
        if ($store->type_id) $completenessScore += 1;
        $score += $completenessScore;

        // 5. Promotion bonus (5% weight) - max 5 points
        if ($store->has_promotion) {
            $score += 5;
        }

        return round($score, 2);
    }

    /**
     * Check if store matches user's gender preferences.
     * This is a simplified implementation - you may want to enhance this
     * based on store categories, target audience, etc.
     *
     * @param Store $store
     * @param string $userGender
     * @return bool
     */
    private function isStoreGenderMatch($store, string $userGender): bool
    {
        // For now, we'll use a simple heuristic based on store favorite categories
        // You can enhance this logic based on your business requirements

        if (!$userGender || !in_array($userGender, ['male', 'female'])) {
            return false;
        }

        // Get store's favorite categories
        $favoriteCategories = $store->favoriteCategories ?? collect();

        if ($favoriteCategories->isEmpty()) {
            return false; // No preference data available
        }

        // Define gender-specific category patterns (you can expand this)
        $maleCategories = [
            'men', 'homme', 'masculin', 'male', 'garcon', 'boy',
            'sport', 'tech', 'gaming', 'auto', 'tools', 'outils'
        ];

        $femaleCategories = [
            'women', 'femme', 'féminin', 'female', 'fille', 'girl',
            'beauty', 'beauté', 'makeup', 'fashion', 'mode', 'jewelry', 'bijoux'
        ];

        $targetCategories = $userGender === 'male' ? $maleCategories : $femaleCategories;

        // Check if any of the store's favorite categories match user's gender
        foreach ($favoriteCategories as $category) {
            $categoryName = strtolower($category->title_en ?? $category->title_fr ?? '');

            foreach ($targetCategories as $targetCategory) {
                if (strpos($categoryName, $targetCategory) !== false) {
                    return true;
                }
            }
        }

        return false;
    }
}
