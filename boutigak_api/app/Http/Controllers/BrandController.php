<?php

namespace App\Http\Controllers;

use App\Models\Brand;
use App\Models\Category;
use Exception;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class BrandController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/brands",
     *     tags={"Brand"},
     *     summary="Get all brands",
     *     description="Retrieve a list of all brands.",
     *     @OA\Parameter(
     *         name="category_id",
     *         in="query",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="The ID of the category to filter brands by."
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Brand Name"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T00:00:00Z")
     *             )
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $category_id = $request->query('category_id');

        $brands = Brand::where('category_id', $category_id)->get();

        return response()->json($brands, 200);
    }

    /**
     * @OA\Post(
     *     path="/api/brands",
     *     tags={"Brand"},
     *     summary="Create a new brand",
     *     description="Create a new brand with a name.",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name", "category_id"},
     *             @OA\Property(property="name", type="string", example="New Brand"),
     *             @OA\Property(property="category_id", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Brand created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="New Brand"),
     *             @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T00:00:00Z")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad request"
     *     ),
     *     @OA\Response(
     *         response=409,
     *         description="Conflict - Brand already exists"
     *     )
     * )
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|integer',
            'gender' => 'required|in:male,female,both',
        ]);

        // Check if the brand name already exists
        if (Brand::where('name', $request->input('name'))->exists()) {
            return response()->json(['error' => 'Brand already exists'], 409);
        }

        $brand = new Brand();
        $brand->name = $request->input('name');
        $brand->category_id = $request->input('category_id');
        $brand->gender = $request->input('gender');
        $brand->save();

        return response()->json(['id' => $brand->id, 'name' => $brand->name, 'created_at' => $brand->created_at], 201);
    }

    /**
     * @throws Exception
     */
    public function indexBo(Request $request): \Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\JsonResponse|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        if ($request->ajax()) {
            $data = Brand::with('category')->get();
            return DataTables::of($data)
                ->addColumn('category', function ($row) {
                    return $row->category ? $row->category->title_en : 'N/A';
                })
                ->addColumn('actions', function ($row) {
                    $btn = '<a href="' . route('brands.edit', $row->id) . '" class="edit btn btn-warning btn-sm me-1">Edit</a>';
                    $btn .= '<button type="button" class="delete btn btn-danger btn-sm" data-id="' . $row->id . '">
                    <i class="fa fa-trash"></i> Delete
                 </button>';
                    return $btn;
                })
                ->rawColumns(['actions'])
                ->make(true);
        }

        return view('backoffice.brand.index');
    }

    public function create(): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        $categories = Category::all();
        return view('backoffice.brand.create', compact('categories'));
    }

    public function storeBo(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|integer',
            'gender' => 'required|in:male,female,both',
        ]);

        // Check if the brand name already exists
        if (Brand::where('name', $request->input('name'))->exists()) {
            return redirect()->route('brands.index')->with('error', 'Brand already exists');
        }

        $brand = new Brand();
        $brand->name = $request->input('name');
        $brand->category_id = $request->input('category_id');
        $brand->gender = $request->input('gender');
        $brand->save();

        return redirect()->route('brands.index')->with('success', 'Brand created successfully');
    }

    public function edit($id): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        $brand = Brand::find($id);
        $categories = Category::all();
        return view('backoffice.brand.edit', compact('brand', 'categories'));
    }

    public function updateBo(Request $request, $id): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|integer',
            'gender' => 'required|in:male,female,both',
        ]);

        $brand = Brand::find($id);
        $brand->name = $request->input('name');
        $brand->category_id = $request->input('category_id');
        $brand->gender = $request->input('gender');
        $brand->save();

        return redirect()->route('brands.index')->with('success', 'Brand updated successfully');

    }

    public function destroyBo($id): \Illuminate\Http\RedirectResponse
    {
        $brand = Brand::find($id);
        $brand->delete();

        return redirect()->route('brands.index')->with('success', 'Brand deleted successfully');
    }
}
