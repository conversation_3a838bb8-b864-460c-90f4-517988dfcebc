<?php

namespace App\Http\Controllers;

use App\Models\Store;
use App\Models\StoreSocialMediaLink;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

/**
 * @OA\Tag(
 *     name="Store Social Media Links",
 *     description="API endpoints for managing store social media links"
 * )
 */
class StoreSocialMediaLinkController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/stores/{store_id}/social-media-links",
     *     summary="Get all social media links for a store",
     *     tags={"Store Social Media Links"},
     *     @OA\Parameter(
     *         name="store_id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="store_id", type="integer", example=1),
     *                     @OA\Property(property="platform", type="string", enum={"whatsapp", "facebook", "snapchat", "tiktok"}, example="whatsapp"),
     *                     @OA\Property(property="link", type="string", example="https://wa.me/1234567890"),
     *                     @OA\Property(property="is_active", type="boolean", example=true),
     *                     @OA\Property(property="created_at", type="string", format="date-time"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store not found"
     *     )
     * )
     */
    public function index($storeId): JsonResponse
    {
        $store = Store::find($storeId);
        
        if (!$store) {
            return response()->json([
                'success' => false,
                'message' => 'Store not found'
            ], 404);
        }

        $socialMediaLinks = $store->socialMediaLinks;

        return response()->json([
            'success' => true,
            'data' => $socialMediaLinks
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/stores/{store_id}/social-media-links",
     *     summary="Create a new social media link for a store",
     *     tags={"Store Social Media Links"},
     *     @OA\Parameter(
     *         name="store_id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"platform", "link"},
     *             @OA\Property(property="platform", type="string", enum={"whatsapp", "facebook", "snapchat", "tiktok"}, example="whatsapp"),
     *             @OA\Property(property="link", type="string", example="https://wa.me/1234567890"),
     *             @OA\Property(property="is_active", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Social media link created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Social media link created successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store not found"
     *     )
     * )
     */
    public function store(Request $request, $storeId): JsonResponse
    {
        $store = Store::find($storeId);
        
        if (!$store) {
            return response()->json([
                'success' => false,
                'message' => 'Store not found'
            ], 404);
        }

        $request->validate([
            'platform' => ['required', Rule::in(['whatsapp', 'facebook', 'snapchat', 'tiktok'])],
            'link' => 'required|string|url',
            'is_active' => 'boolean'
        ]);

        $socialMediaLink = StoreSocialMediaLink::create([
            'store_id' => $storeId,
            'platform' => $request->platform,
            'link' => $request->link,
            'is_active' => $request->is_active ?? true
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Social media link created successfully',
            'data' => $socialMediaLink
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/stores/{store_id}/social-media-links/{id}",
     *     summary="Get a specific social media link",
     *     tags={"Store Social Media Links"},
     *     @OA\Parameter(
     *         name="store_id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Social media link ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Social media link not found"
     *     )
     * )
     */
    public function show($storeId, $id): JsonResponse
    {
        $socialMediaLink = StoreSocialMediaLink::where('store_id', $storeId)
            ->where('id', $id)
            ->first();

        if (!$socialMediaLink) {
            return response()->json([
                'success' => false,
                'message' => 'Social media link not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $socialMediaLink
        ]);
    }

    /**
     * @OA\Put(
     *     path="/api/stores/{store_id}/social-media-links/{id}",
     *     summary="Update a social media link",
     *     tags={"Store Social Media Links"},
     *     @OA\Parameter(
     *         name="store_id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Social media link ID"
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="platform", type="string", enum={"whatsapp", "facebook", "snapchat", "tiktok"}, example="facebook"),
     *             @OA\Property(property="link", type="string", example="https://facebook.com/mystore"),
     *             @OA\Property(property="is_active", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Social media link updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Social media link updated successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Social media link not found"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function update(Request $request, $storeId, $id): JsonResponse
    {
        $socialMediaLink = StoreSocialMediaLink::where('store_id', $storeId)
            ->where('id', $id)
            ->first();

        if (!$socialMediaLink) {
            return response()->json([
                'success' => false,
                'message' => 'Social media link not found'
            ], 404);
        }

        $request->validate([
            'platform' => ['sometimes', Rule::in(['whatsapp', 'facebook', 'snapchat', 'tiktok'])],
            'link' => 'sometimes|string|url',
            'is_active' => 'sometimes|boolean'
        ]);

        $socialMediaLink->update($request->only(['platform', 'link', 'is_active']));

        return response()->json([
            'success' => true,
            'message' => 'Social media link updated successfully',
            'data' => $socialMediaLink->fresh()
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/api/stores/{store_id}/social-media-links/{id}",
     *     summary="Delete a social media link",
     *     tags={"Store Social Media Links"},
     *     @OA\Parameter(
     *         name="store_id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Store ID"
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Social media link ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Social media link deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Social media link deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Social media link not found"
     *     )
     * )
     */
    public function destroy($storeId, $id): JsonResponse
    {
        $socialMediaLink = StoreSocialMediaLink::where('store_id', $storeId)
            ->where('id', $id)
            ->first();

        if (!$socialMediaLink) {
            return response()->json([
                'success' => false,
                'message' => 'Social media link not found'
            ], 404);
        }

        $socialMediaLink->delete();

        return response()->json([
            'success' => true,
            'message' => 'Social media link deleted successfully'
        ]);
    }
}
