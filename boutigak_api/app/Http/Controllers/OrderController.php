<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\Item;
use App\Models\User;
use App\Models\Order;
use App\Models\Store;
use App\Models\OrderItem;
use App\Models\ItemPayment;
use App\Models\Notification;

use App\Models\OrderPayment;
use App\services\FCMService;
use App\services\ImageOptimizationService;
use Illuminate\Http\Request;
use App\Models\DeliveryCharge;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;
use App\Models\OrderModification;


class OrderController extends Controller
{


    protected FCMService $firebaseService;
    protected ImageOptimizationService $imageOptimizationService;


    public function __construct(FCMService $firebaseService, ImageOptimizationService $imageOptimizationService)
    {
        $this->firebaseService = $firebaseService;
        $this->imageOptimizationService = $imageOptimizationService;
    }


    function getLocalizedNotificationMessages(string $type, array $params = []): array
    {
        // Set default values for all parameters
        $defaultParams = [
            'customer_name' => '',
            'store_name' => '',
            'reason' => '',
            'status' => '',
        ];

        // Merge provided params with defaults
        $params = array_merge($defaultParams, array_filter($params));

        // Helper function for Arabic text formatting
        $formatArabic = function (string $text, array $values = []): string {
            return vsprintf(str_replace('%s', '%s', $text), array_map(fn($v) => $v ?: '', $values));
        };

        $messages = [
            'new_order' => [
                'en' => [
                    'title' => 'New Order Received',
                    'body' => sprintf("You have received a new order from %s", $params['customer_name'])
                ],
                'fr' => [
                    'title' => 'Nouvelle Commande Reçue',
                    'body' => sprintf("Vous avez reçu une nouvelle commande de %s", $params['customer_name'])
                ],
                'ar' => [
                    'title' => 'طلب جديد',
                    'body' => $formatArabic("%s لقد تلقيت طلبًا جديدًا من", [$params['customer_name']])
                ]
            ],
            'order_modified' => [
                'en' => [
                    'title' => 'Order Modified',
                    'body' => sprintf("Your order has been modified by %s. Reason: %s",
                        $params['store_name'],
                        $params['reason']
                    )
                ],
                'fr' => [
                    'title' => 'Commande Modifiée',
                    'body' => sprintf("Votre commande a été modifiée par %s. Raison: %s",
                        $params['store_name'],
                        $params['reason']
                    )
                ],
                'ar' => [
                    'title' => 'تم تعديل الطلب',
                    'body' => $formatArabic("%s :السبب .%s تم تعديل طلبك بواسطة",
                        [$params['reason'], $params['store_name']]
                    )
                ]
            ],
            'order_status_updated' => [
                'en' => [
                    'title' => 'Order Status Updated',
                    'body' => sprintf("Your order status has been updated to: %s", $params['status'])
                ],
                'fr' => [
                    'title' => 'Statut de Commande Mis à Jour',
                    'body' => sprintf("Le statut de votre commande a été mis à jour vers: %s", $params['status'])
                ],
                'ar' => [
                    'title' => 'تم تحديث حالة الطلب',
                    'body' => $formatArabic("%s :تم تحديث حالة طلبك إلى", [$params['status']])
                ]
            ],
            'order_auto_cancelled' => [
                'en' => [
                    'title' => 'Order Automatically Cancelled',
                    'body' => sprintf("Your order has been automatically cancelled because %s did not respond within 30 minutes", $params['store_name'])
                ],
                'fr' => [
                    'title' => 'Commande Automatiquement Annulée',
                    'body' => sprintf("Votre commande a été automatiquement annulée car %s n'a pas répondu dans les 30 minutes", $params['store_name'])
                ],
                'ar' => [
                    'title' => 'تم إلغاء الطلب تلقائيًا',
                    'body' => $formatArabic("تم إلغاء طلبك تلقائيًا لأن %s لم يستجب خلال 30 دقيقة", [$params['store_name']])
                ]
            ]
        ];

        Log::debug('Notification parameters', [
            'type' => $type,
            'params' => $params
        ]);

        return $messages[$type] ?? $messages['new_order'];
    }

    function sendLocalizedNotification(User $user, string $type, array $params = []): void
    {
        // Set default values for parameters
        $params = array_merge([
            'customer_name' => '',
            'store_name' => '',
            'reason' => '',
            'status' => '',
            'order_id' => null
        ], array_filter($params));

        $token = $user->deviceToken->token ?? null;
        $userLang = $user->lang ?? 'en';

        try {
            // Get all language versions
            $messages = $this->getLocalizedNotificationMessages($type, $params);

            // Get user's preferred language for push notification
            $userMessage = $messages[$userLang] ?? $messages['en'];

            // Create notification with all language versions
            $notification = Notification::create([
                'user_id' => $user->id,
                'type' => $type,
                'order_id' => $params['order_id'],
                'is_read' => false,
                // Store titles in all languages
                'title_en' => $messages['en']['title'],
                'title_fr' => $messages['fr']['title'],
                'title_ar' => $messages['ar']['title'],
                // Store messages in all languages
                'message_en' => $messages['en']['body'],
                'message_fr' => $messages['fr']['body'],
                'message_ar' => $messages['ar']['body'],
                // Store current language version for backwards compatibility
                // 'title' => $userMessage['title'],
                // 'message' => $userMessage['body'],
                // Store parameters for future reference
                'params' => json_encode($params)
            ]);

            // Send push notification if device token exists
            if ($token) {
                $this->firebaseService->sendNotification(
                    $token,
                    $userMessage['title'],
                    $userMessage['body']
                );

                Log::info('Notification sent and stored successfully', [
                    'user_id' => $user->id,
                    'language' => $userLang,
                    'type' => $type,
                    'notification_id' => $notification->id,
                    'order_id' => $params['order_id']
                ]);
            } else {
                Log::warning('No device token found for user, notification stored in database only', [
                    'user_id' => $user->id
                ]);
            }
        } catch (Exception $e) {
            Log::error('Failed to process notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'type' => $type,
                'params' => $params,
                'order_id' => $params['order_id']
            ]);
        }
    }

    /**
     * @throws Exception
     */
    public function index(Request $request): \Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\JsonResponse|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        if ($request->ajax()) {
            $query = Order::with('store', 'items', 'user');

            if ($request->has('store_id') && $request->store_id != '') {
                $query->where('store_id', $request->store_id);
            }

            if ($request->has('item_id') && $request->item_id != '') {
                $query->whereHas('items', function ($q) use ($request) {
                    $q->where('item_id', $request->item_id);
                });
            }

            return DataTables::of($query)
                ->addColumn('actions', function ($row) {
                    return '<a href="' . route('orders.show', $row->id) . '" class="btn btn-primary btn-sm me-1">Show</a>
                <button type="button" class="btn btn-secondary btn-sm change-status" data-id="' . $row->id . '" data-toggle="modal" data-target="#changeStatusModal">
                    Change Status
                </button>';
                })
                ->rawColumns(['actions'])
                ->make(true);
        }

        $stores = Store::all();
        $items = Item::all();

        return view('backoffice.orders.index', compact('stores', 'items'));
    }

    public function show($id)
    {
        $order = Order::with(['user', 'store', 'items', 'orderItems.originalItem.images'])->findOrFail($id);
        
        // Add formatted items to the view data
        $formattedItems = $this->formatOrderItems($order);
        
        return view('backoffice.orders.show', compact('order', 'formattedItems'));
    }


    /**
     * Create a new order.
     *
     * @OA\Post(
     *     path="/api/orders",
     *     tags={"Order"},
     *     summary="Create a new order",
     *     description="Creates a new order for the authenticated user.",
     *     operationId="createOrder",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="store_id", type="integer"),
     *             @OA\Property(property="items", type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="item_id", type="integer"),
     *                     @OA\Property(property="quantity", type="integer")
     *                 )
     *             ),
     *             @OA\Property(property="delivery_charge_id", type="integer"),
     *             @OA\Property(property="location_id", type="integer", nullable=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Order created successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="user_id", type="integer"),
     *             @OA\Property(property="store_id", type="integer"),
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="delivery_charge", type="number", format="float"),
     *             @OA\Property(property="location_id", type="integer", nullable=true),
     *             @OA\Property(property="created_at", type="string", format="date-time"),
     *             @OA\Property(property="updated_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(response=400, description="Invalid input"),
     *     @OA\Response(response=500, description="Internal server error")
     * )
     */
    public function createOrder(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'store_id' => 'required|integer|exists:store,id',
            'items' => 'required|array',
            'items.*.item_id' => 'required|integer|exists:item,id',
            'items.*.quantity' => 'required|integer|min:1',
            'delivery_charge_id' => 'required|integer|exists:delivery_charges,id',
            'is_cash_on_delivery' => 'required|boolean',
            'location_id' => 'required|integer|exists:location,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        try {
            $user = Auth::user();

            // Check if the user has a pending order in the same store
            $pendingOrder = Order::query()
                ->where('user_id', $user->id)
                ->where('store_id', $request->store_id)
                ->where('status', 'PENDING')
                ->first();

            if ($pendingOrder) {
                return response()->json(['message' => 'You already have a pending order in this store.'], 400);
            }

            $deliveryCharge = DeliveryCharge::query()->find($request->delivery_charge_id);
            $store = Store::find($request->store_id);

            DB::beginTransaction();
            
            $order = Order::query()->create([
                'user_id' => $user->id,
                'store_id' => $request->store_id,
                'status' => 'PENDING',
                'delivery_charge' => $deliveryCharge->amount,
                'location_id' => $request->location_id,
                'is_cash_on_delivery' => $request->is_cash_on_delivery,
            ]);

            // Store items in both pivot table and dedicated order_items table

            Log::info('Creating order items', [
                'order_id' => $order->id,
                'items' => json_encode($request->items)
            ]);

            foreach ($request->items as $itemData) {
                $item = Item::query()->with(['brand', 'category', 'store', 'categoryDetails'])->find($itemData['item_id']);
                $price = $item->price;

                // Get original price before promotion calculation
                $originalPrice = $item->has_promotion && $item->promotion_percentage > 0
                    ? $item->getOriginal('price')
                    : $price;

                // Prepare category details
                $categoryDetails = $item->categoryDetails->map(function ($detail) {
                    return [
                        'label_en' => $detail->label_en,
                        'label_ar' => $detail->label_ar,
                        'label_fr' => $detail->label_fr,
                        'value' => $detail->value
                    ];
                })->toArray();

                // Create a complete duplicate in order_items table
                OrderItem::create([
                    'order_id' => $order->id,
                    'item_id' => $item->id,
                    'name' => $item->title, // Keep for backward compatibility
                    'title' => $item->title,
                    'title_ar' => $item->title_ar,
                    'description' => $item->description ?? '',
                    'description_ar' => $item->description_ar,
                    'price' => $price,
                    'original_price' => $originalPrice,
                    'quantity' => $itemData['quantity'],
                    'image_url' => $item->images->first()->media->url ?? null,
                    'total' => $price * $itemData['quantity'],
                    'condition' => $item->condition,
                    'brand_id' => $item->brand_id,
                    'category_id' => $item->category_id,
                    'store_id' => $item->store_id,
                    'brand_name' => $item->brand->name ?? null,
                    'category_name' => $item->category->title_en ?? null,
                    'store_name' => $item->store->name ?? null,
                    'category_details' => $categoryDetails,
                    'has_promotion' => $item->has_promotion,
                    'promotion_percentage' => $item->promotion_percentage
                ]);
            }
            
            DB::commit();

            // Send notification to store owner with payment method info
            if ($store && $store->user) {
                $this->sendLocalizedNotification(
                    $store->user,
                    'new_order',
                    [
                        'order_id' => $order->id,
                        'payment_method' => $request->is_cash_on_delivery ? 'Cash on delivery' : 'Online payment',
                        'customer_name' => $user->firstname . ' ' . $user->lastname
                    ]
                );
            }

            return response()->json(['message' => 'Order created successfully', 'order' => $order], 201);
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Internal server error', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/order-store",
     *     tags={"Store"},
     *     summary="Get orders for stores owned by the authenticated user",
     *     description="Retrieve all orders for the stores owned by the authenticated user.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="store_id", type="integer", example=1),
     *                 @OA\Property(property="user_id", type="integer", example=1),
     *                 @OA\Property(property="status", type="string", example="pending"),
     *                 @OA\Property(property="delivery_charge", type="number", format="float", example=5.00),
     *                 @OA\Property(property="location_id", type="integer", example=1),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-10-13T02:20:22.000000Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-10-13T02:20:22.000000Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthenticated"),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function getStoreOrders(Request $request): JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'No store found for the authenticated user'], 404);
        }

        $orders = Order::query()->where('store_id', $store->id)->with(['items', 'user', 'orderItems.originalItem.images'])
        ->orderBy('created_at', 'desc')->get();

        $response = $orders->map(function ($order) {
            // Use OrderItem data instead of original items
            $totalOrders = $order->orderItems->sum(function ($orderItem) {
                return $orderItem->price * $orderItem->quantity;
            });

            return [
                'id' => $order->id,
                'store_id' => $order->store_id,
                'status' => $order->status,
                'delivery_charge' => $order->delivery_charge,
                'delivery_charge_id' => $order->delivery_charge_id,
                'location_id' => $order->location_id,
                'userfirstname' => $order->user->firstname,
                'userlastname' => $order->user->lastname,
                'created_at' => $order->created_at,
                'updated_at' => $order->updated_at,
                'totalorders' => $totalOrders,
                'is_cash_on_delivery' => $order->is_cash_on_delivery,

                'items' => $this->formatOrderItems($order, $user->lang ?? 'en'),

            ];
        });

        return response()->json($response);
    }

    /**
     * @OA\Put(
     *     path="/api/orders-update-status",
     *     tags={"Order"},
     *     summary="Update order status",
     *     description="Update the status of an order. Status can be PENDING, ACCEPTED, CANCELLED, WAITING_PAYMENT, DELIVERED.",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="status", type="string", example="ACCEPTED")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Order status updated successfully")
     *         )
     *     ),
     *     @OA\Response(response=400, description="Invalid input"),
     *     @OA\Response(response=401, description="Unauthenticated"),
     *     @OA\Response(response=403, description="Forbidden"),
     *     @OA\Response(response=404, description="Order not found"),
     *     @OA\Response(response=500, description="Internal server error")
     * )
     */
    public function updateOrderStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'status' => 'required|string|in:PENDING,ACCEPTED,REJECTED,CANCELLED,DELIVERED',
            'items' => 'array|required_if:status,ACCEPTED',
            'items.*.item_id' => 'required_if:status,ACCEPTED|integer',
            'items.*.quantity' => 'required_if:status,ACCEPTED|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $user = Auth::user();
        $order = Order::query()->with(['orderItems.originalItem.images', 'modifications'])->find($request->id);

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        $store = $order->store;

        if ($store->user_id !== $user->id) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        try {
            DB::beginTransaction();

            $orderModified = false;
            $modificationDetails = [];

            // If status is ACCEPTED and items are modified
            if ($request->status === 'ACCEPTED' && $request->has('items')) {
                // Get original items for comparison
                $originalItems = $order->orderItems->keyBy('id');
                $newItems = collect($request->items)->keyBy('item_id');

                // Clear existing modifications
                $order->modifications()->delete();

                // Check for removed items
                foreach ($originalItems as $itemId => $originalItem) {
                    if (!$newItems->has($itemId)) {
                        // Use OrderItem data (preserved at order creation time)
                        $itemName = $originalItem->getTitle($user->lang ?? 'en');

                        // Log the original item data for debugging
                        Log::info('Creating removal modification', [
                            'item_id' => $itemId,
                            'item_name' => $itemName,
                            'quantity' => $originalItem->quantity,
                            'price' => $originalItem->price
                        ]);

                        // Create modification record for removed item
                        OrderModification::create([
                            'order_id' => $order->id,
                            'type' => 'removed',
                            'order_item_id' => $itemId,
                            'item_name' => $itemName,
                            'old_quantity' => $originalItem->quantity,
                            'old_price' => $originalItem->price,
                            'reason' => 'Item not available'
                        ]);
                        
                        $modificationDetails[] = [
                            'type' => 'removed',
                            'item_name' => $itemName,
                            'old_quantity' => $originalItem->quantity,
                            'old_price' => $originalItem->price
                        ];
                        $orderModified = true;
                    }
                }

                // Check for quantity changes and new items
                foreach ($newItems as $itemId => $newItem) {
                    if ($originalItems->has($itemId)) {
                        $originalQuantity = $originalItems[$itemId]->quantity;
                        $newQuantity = $newItem['quantity'];
                        if ($originalQuantity != $newQuantity) {
                            // Use OrderItem data (preserved at order creation time)
                            $itemName = $originalItems[$itemId]->getTitle($user->lang ?? 'en');
                            $oldPrice = $originalItems[$itemId]->price;
                            
                            // Log the quantity change for debugging
                            Log::info('Creating quantity change modification', [
                                'item_id' => $itemId,
                                'item_name' => $itemName,
                                'old_quantity' => $originalQuantity,
                                'new_quantity' => $newQuantity,
                                'old_price' => $oldPrice
                            ]);
                            
                            // Create modification record for quantity change
                            OrderModification::create([
                                'order_id' => $order->id,
                                'type' => 'quantity_changed',
                                'order_item_id' => $itemId,
                                'item_name' => $itemName,
                                'old_quantity' => $originalQuantity,
                                'new_quantity' => $newQuantity,
                                'old_price' => $oldPrice,
                                'new_price' => $oldPrice,
                                'reason' => $newQuantity < $originalQuantity ? 'Limited stock available' : 'Stock available'
                            ]);
                            
                            $modificationDetails[] = [
                                'type' => 'quantity_changed',
                                'item_name' => $itemName,
                                'old_quantity' => $originalQuantity,
                                'new_quantity' => $newQuantity,
                                'old_price' => $oldPrice,
                                'new_price' => $oldPrice
                            ];
                            $orderModified = true;
                        }
                    } else {
                        // This is a new item (shouldn't happen in normal flow, but handle it)
                        $itemModel = Item::find($itemId);
                        if ($itemModel) {
                            // Use localized title for new items
                            $userLang = $user->lang ?? 'en';
                            $itemName = ($userLang === 'ar' && !empty($itemModel->title_ar))
                                ? $itemModel->title_ar
                                : $itemModel->title;

                            OrderModification::create([
                                'order_id' => $order->id,
                                'type' => 'added',
                                'order_item_id' => null, // New item, no OrderItem ID yet
                                'item_name' => $itemName,
                                'new_quantity' => $newItem['quantity'],
                                'new_price' => $itemModel->price,
                                'reason' => 'Item added as replacement'
                            ]);

                            $modificationDetails[] = [
                                'type' => 'added',
                                'item_name' => $itemName,
                                'new_quantity' => $newItem['quantity'],
                                'new_price' => $itemModel->price
                            ];
                            $orderModified = true;
                        }
                    }
                }

                // Mark order as modified if changes were detected
                if ($orderModified) {
                    $order->modified_by_store = true;
                    
                    // Generate modification reasons in all languages
                    $modificationReasonEn = $this->generateModificationReason($modificationDetails, 'en');
                    $modificationReasonFr = $this->generateModificationReason($modificationDetails, 'fr');
                    $modificationReasonAr = $this->generateModificationReason($modificationDetails, 'ar');
                    
                    $order->modification_reason_en = $modificationReasonEn;
                    $order->modification_reason_fr = $modificationReasonFr;
                    $order->modification_reason_ar = $modificationReasonAr;
                }

                // Update OrderItems preserving historical data
                $totalAmount = 0;

                // Remove OrderItems for removed items (already tracked in modifications above)
                foreach ($originalItems as $itemId => $originalItem) {
                    if (!$newItems->has($itemId)) {
                        $originalItem->delete();
                    }
                }

                // Update quantities for existing items and calculate total
                foreach ($newItems as $itemId => $newItem) {
                    if ($originalItems->has($itemId)) {
                        // Update existing OrderItem quantity and total
                        $orderItem = $originalItems[$itemId];
                        $orderItem->quantity = $newItem['quantity'];
                        $orderItem->total = $orderItem->price * $newItem['quantity'];
                        $orderItem->save();

                        $totalAmount += $orderItem->total;
                    } else {
                        // This is a genuinely new item (rare case) - create from current Item data
                        $itemModel = Item::query()->with(['brand', 'category', 'store', 'categoryDetails'])->find($itemId);
                        if ($itemModel) {
                            $itemPrice = $itemModel->price;
                            $itemTotal = $itemPrice * $newItem['quantity'];
                            $totalAmount += $itemTotal;

                            // Get original price before promotion calculation
                            $originalPrice = $itemModel->has_promotion && $itemModel->promotion_percentage > 0
                                ? $itemModel->getOriginal('price')
                                : $itemPrice;

                            // Prepare category details
                            $categoryDetails = $itemModel->categoryDetails->map(function ($detail) {
                                return [
                                    'label_en' => $detail->label_en,
                                    'label_ar' => $detail->label_ar,
                                    'label_fr' => $detail->label_fr,
                                    'value' => $detail->value
                                ];
                            })->toArray();

                            // Create new OrderItem for genuinely new item
                            OrderItem::create([
                                'order_id' => $order->id,
                                'item_id' => $itemModel->id,
                                'name' => $itemModel->title, // Keep for backward compatibility
                                'title' => $itemModel->title,
                                'title_ar' => $itemModel->title_ar,
                                'description' => $itemModel->description ?? '',
                                'description_ar' => $itemModel->description_ar,
                                'price' => $itemPrice,
                                'original_price' => $originalPrice,
                                'quantity' => $newItem['quantity'],
                                'image_url' => $itemModel->images->first()->media->url ?? null,
                                'total' => $itemTotal,
                                'condition' => $itemModel->condition,
                                'brand_id' => $itemModel->brand_id,
                                'category_id' => $itemModel->category_id,
                                'store_id' => $itemModel->store_id,
                                'brand_name' => $itemModel->brand->name ?? null,
                                'category_name' => $itemModel->category->title_en ?? null,
                                'store_name' => $itemModel->store->name ?? null,
                                'category_details' => $categoryDetails,
                                'has_promotion' => $itemModel->has_promotion,
                                'promotion_percentage' => $itemModel->promotion_percentage
                            ]);
                        }
                    }
                }

                // Update order total
                $order->total = $totalAmount + ($order->delivery_charge ?? 0);
            }

            $order->status = $request->status;
            $order->save();

            DB::commit();

            if ($order->user) {
                if ($orderModified && !empty($modificationDetails)) {
                    $this->sendLocalizedNotification(
                        $order->user,
                        'order_modified',
                        [
                            'order_id' => $order->id,
                            'store_name' => $store->name,
                            'reason' => $this->generateModificationReason($modificationDetails, 'en')
                        ]
                    );
                }

                $this->sendLocalizedNotification(
                    $order->user,
                    'order_status_updated',
                    [
                        'order_id' => $order->id,
                        'status' => $request->status,
                        'store_name' => $store->name
                    ]
                );
            }

            return response()->json([
                'message' => 'Order updated successfully',
                'order' => [
                    'id' => $order->id,
                    'status' => $order->status,
                    'modified_by_store' => $order->modified_by_store,
                    'modification_reason_en' => $order->modification_reason_en,
                    'modification_reason_fr' => $order->modification_reason_fr,
                    'modification_reason_ar' => $order->modification_reason_ar,
                    'modifications' => $order->modifications->map(function($mod) {
                        return [
                            'type' => $mod->type,
                            'item_name' => $mod->item_name,
                            'old_quantity' => $mod->old_quantity,
                            'new_quantity' => $mod->new_quantity,
                            'old_price' => $mod->old_price,
                            'new_price' => $mod->new_price,
                            'reason' => $mod->reason
                        ];
                    }),
                    'items' => $this->formatOrderItems($order, $user->lang ?? 'en')
                ]
            ], 200);

        } catch (Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Internal server error',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/orders/{id}",
     *     tags={"Order"},
     *     summary="Update order details",
     *     description="Update the details of an order. Only the owner of the store can update the order.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Order ID"
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="status", type="string", example="ACCEPTED"),
     *             @OA\Property(property="delivery_charge_id", type="integer", example=1),
     *             @OA\Property(property="location_id", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Order updated successfully")
     *         )
     *     ),
     *     @OA\Response(response=400, description="Invalid input"),
     *     @OA\Response(response=401, description="Unauthenticated"),
     *     @OA\Response(response=403, description="Forbidden"),
     *     @OA\Response(response=404, description="Order not found"),
     *     @OA\Response(response=500, description="Internal server error")
     * )
     */
    public function updateOrder(Request $request, $id): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|string|in:PENDING,ACCEPTED,CANCELLED,WAITING_PAYMENT,DELIVERED',
            'delivery_charge_id' => 'sometimes|integer|exists:delivery_charges,id',
            'location_id' => 'sometimes|integer|exists:location,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $user = Auth::user();
        $order = Order::find($id);

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        $store = $order->store;

        if ($store->user_id !== $user->id) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        if ($request->has('delivery_charge_id')) {
            $deliveryCharge = DeliveryCharge::find($request->delivery_charge_id);
            $order->delivery_charge = $deliveryCharge->amount;
        }

        $order->update($request->only(['status', 'location_id']));

        return response()->json(['message' => 'Order updated successfully'], 200);
    }

    /**
     * Get orders for the authenticated user.
     *
     * @OA\Get(
     *     path="/api/orders/mine",
     *     tags={"Order"},
     *     summary="Get orders for the authenticated user",
     *     description="Returns a list of orders for the authenticated user.",
     *     operationId="getOrders",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="total_price", type="number", format="float"),
     *                 @OA\Property(property="status", type="string"),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time"),
     *                 @OA\Property(property="items", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer"),
     *                         @OA\Property(property="title", type="string"),
     *                         @OA\Property(property="price", type="number", format="float"),
     *                         @OA\Property(property="quantity", type="integer")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthenticated")
     * )
     */
    public function getUserOrders(Request $request): JsonResponse
    {
        $user = Auth::user();

        // Get pagination parameters
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 10);

        $orders = Order::query()->where('user_id', $user->id)
            ->with(['user', 'items', 'store', 'modifications', 'orderPayment', 'orderItems.originalItem.images'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        $response = $orders->getCollection()->map(function ($order) {
            // Use OrderItem data instead of original items
            $totalOrders = $order->orderItems->sum(function ($orderItem) {
                return $orderItem->price * $orderItem->quantity;
            });

            return [
                'id' => $order->id,
                'userphone' => $order->store->phone,
                'store_id' => $order->store_id,
                'store_name' => $order->store->name,
                'store_image' => $order->store->images[0]->media->url ?? null,
                'status' => $order->status,
                'modified_by_store' => $order->modified_by_store,
                'modification_reason_en' => $order->modification_reason_en,
                'modification_reason_fr' => $order->modification_reason_fr,
                'modification_reason_ar' => $order->modification_reason_ar,
                'modifications' => $order->modifications->map(function($mod) {
                    return [
                        'type' => $mod->type,
                        'item_name' => $mod->item_name,
                        'old_quantity' => $mod->old_quantity,
                        'new_quantity' => $mod->new_quantity,
                        'old_price' => $mod->old_price,
                        'new_price' => $mod->new_price,
                        'reason' => $mod->reason
                    ];
                }),
                'delivery_charge' => $order->delivery_charge,
                'delivery_charge_id' => $order->delivery_charge_id,
                'location_id' => $order->location_id,
                'userfirstname' => $order->user->firstname,
                'userlastname' => $order->user->lastname,
                'created_at' => $order->created_at,
                'updated_at' => $order->updated_at,
                'totalorders' => $totalOrders,
                'items' => $this->formatOrderItems($order, $user->lang ?? 'en'),
                'is_paid' => $order->is_paid,
                'is_cash_on_delivery' => $order->is_cash_on_delivery,
                'payment_screenshot' => $order->orderPayment->photo_url ?? null,
            ];
        });

        // Return paginated response with orders data only for mobile compatibility
        return response()->json($response);
    }

    /**
     * @OA\Put(
     *     path="/api/orders/{id}/edit",
     *     tags={"Order"},
     *     summary="Edit order items",
     *     description="Edit the items of an order if a product is not available or the quantity ordered is not complete.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Order ID"
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="items", type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="item_id", type="integer"),
     *                     @OA\Property(property="quantity", type="integer")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Order items updated successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Order items updated successfully"),
     *             @OA\Property(property="cloned_order", type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="user_id", type="integer"),
     *                 @OA\Property(property="store_id", type="integer"),
     *                 @OA\Property(property="status", type="string"),
     *                 @OA\Property(property="delivery_charge", type="number", format="float"),
     *                 @OA\Property(property="location_id", type="integer", nullable=true),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=400, description="Invalid input"),
     *     @OA\Response(response=401, description="Unauthenticated"),
     *     @OA\Response(response=403, description="Forbidden"),
     *     @OA\Response(response=404, description="Order not found"),
     *     @OA\Response(response=500, description="Internal server error")
     * )
     */
    public function editUserOrder(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.item_id' => 'required|integer|exists:item,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }
        try {
            $user = Auth::user();

            $order = Order::query()->find($id);

            if (!$order) {
                return response()->json(['message' => 'Order not found'], 404);
            }

            $deliveryCharge = DeliveryCharge::query()->find($request->delivery_charge_id);
            $store = Store::find($request->store_id);
            if ($order->store_id !== $store->id) {
                return response()->json(['message' => 'Forbidden'], 403);
            }

            $clonedOrder = $this->cloneOrder($order);

            return response()->json(['message' => 'Order items updated successfully', 'cloned_order' => $clonedOrder], 200);
        } catch (Exception $e) {
            return response()->json(['message' => 'Internal server error', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Clone an order.
     *
     * @OA\Post(
     *     path="/api/orders/{id}/clone",
     *     tags={"Order"},
     *     summary="Clone an order",
     *     description="Creates a clone of the specified order.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Order ID"
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Order cloned successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="user_id", type="integer"),
     *             @OA\Property(property="store_id", type="integer"),
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="delivery_charge", type="number", format="float"),
     *             @OA\Property(property="location_id", type="integer", nullable=true),
     *             @OA\Property(property="created_at", type="string", format="date-time"),
     *             @OA\Property(property="updated_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Order not found"),
     *     @OA\Response(response=500, description="Internal server error")
     * )
     */
    private function cloneOrder(Order $order): Order
    {
        $clonedOrder = $order->replicate();
        $clonedOrder->status = 'CLONED';
        $clonedOrder->save();

        // Clone OrderItems with complete information (no need to clone original items relationship)
        foreach ($order->orderItems as $orderItem) {
            OrderItem::create([
                'order_id' => $clonedOrder->id,
                'item_id' => $orderItem->item_id,
                'name' => $orderItem->name,
                'title' => $orderItem->title,
                'title_ar' => $orderItem->title_ar,
                'description' => $orderItem->description,
                'description_ar' => $orderItem->description_ar,
                'price' => $orderItem->price,
                'original_price' => $orderItem->original_price,
                'quantity' => $orderItem->quantity,
                'image_url' => $orderItem->image_url,
                'total' => $orderItem->total,
                'condition' => $orderItem->condition,
                'brand_id' => $orderItem->brand_id,
                'category_id' => $orderItem->category_id,
                'store_id' => $orderItem->store_id,
                'brand_name' => $orderItem->brand_name,
                'category_name' => $orderItem->category_name,
                'store_name' => $orderItem->store_name,
                'category_details' => $orderItem->category_details,
                'has_promotion' => $orderItem->has_promotion,
                'promotion_percentage' => $orderItem->promotion_percentage
            ]);
        }

        return $clonedOrder;
    }

    public function orderPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required',
            'provider_id' => 'required',
            'amount' => 'required|numeric|min:0',
            'payment_image' => 'required|image|mimes:jpeg,png,jpg,gif,webp',
        ]);

        Log::info('validator errors ' . json_encode($validator->errors()));
        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            DB::beginTransaction();

            // Use ImageOptimizationService to optimize and store the payment image
            $optimizedImageData = $this->imageOptimizationService->optimizeAndStore(
                $request->file('payment_image'),
                'order_payments_screen_shots',   
            );

            Log::info('Payment image optimization completed', [
                'original_file' => $request->file('payment_image')->getClientOriginalName(),
                'optimized_path' => $optimizedImageData['path'],
                'optimized_url' => $optimizedImageData['url']
            ]);

            $payment = OrderPayment::create([
                'order_id' => $request->order_id,
                'provider_id' => $request->provider_id,
                'amount' => $request->amount,
                'photo_url' => $optimizedImageData['url']
            ]);

            // add order is paid true
            $order = Order::find($request->order_id);
            $order->is_paid = true;
            $order->save();

            DB::commit();

            return response()->json([
                'message' => 'Payment added successfully',
                'payment' => $payment,
                'image_url' => $optimizedImageData['url'] // Return the public URL for frontend use
            ], 201);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Payment creation failed', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to process payment'], 500);
        }
    }

    public function changeStatus(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'status' => 'required|string|in:PENDING,PROCESSING,COMPLETED,CANCELLED',
        ]);

        $order = Order::query()->find((int)$request->order_id);

        if (!$order) {
            return redirect()->back()->with('error', 'Order not found');
        }

        $order->status = $request->status;
        $order->save();

        return redirect()->back()->with('success', 'Order status updated successfully');
    }

    private function formatOrderItems($order, $userLanguage = 'en')
    {
        if ($order->orderItems->count() > 0) {
            return $order->orderItems->map(function ($orderItem) use ($userLanguage) {
                return [
                    'item_id' => $orderItem->id,
                    'quantity' => $orderItem->quantity,
                    'price' => $orderItem->price,
                    'original_price' => $orderItem->original_price,
                    'total' => $orderItem->total,

                    'name' => $orderItem->name, 
                    'title' => $orderItem->getTitle($userLanguage),
                    'title_ar' => $orderItem->title_ar,
                    'description' => $orderItem->getDescription($userLanguage),
                    'description_ar' => $orderItem->description_ar,

                    'condition' => $orderItem->condition,
                    'brand_id' => $orderItem->brand_id,
                    'category_id' => $orderItem->category_id,
                    'store_id' => $orderItem->store_id,
                    'brand_name' => $orderItem->brand_name,
                    'category_name' => $orderItem->category_name,
                    'store_name' => $orderItem->store_name,

                    // Category details with localization
                    'category_details' => $orderItem->getLocalizedCategoryDetails($userLanguage),

                    // Promotion info
                    'has_promotion' => $orderItem->has_promotion,
                    'promotion_percentage' => $orderItem->promotion_percentage,

                    // Images
                    'image_url' => $orderItem->image_url,
                    'images' => $orderItem->originalItem && $orderItem->originalItem->images
                        ? $orderItem->originalItem->images->map(function ($image) {
                            return $image->url;
                        })->toArray()
                        : [],

                    // Timestamps
                    'created_at' => $orderItem->created_at,
                    'updated_at' => $orderItem->updated_at,
                ];
            });
        }

        return [];
    }

    private function generateModificationReason(array $modificationDetails, string $language): string
    {
        $translatedDetails = [];
        foreach ($modificationDetails as $detail) {
            $translatedDetails[] = $this->translateModificationReason($detail, $language);
        }
        return implode('. ', $translatedDetails);
    }

    private function translateModificationReason(array $modificationDetail, string $language): string
    {
        $type = $modificationDetail['type'];
        $itemName = $modificationDetail['item_name'];
        
        $translations = [
            'fr' => [
                'removed' => 'Article supprimé',
                'quantity_changed' => 'Quantité modifiée',
                'added' => 'Article ajouté',
                'from' => 'de',
                'to' => 'à',
                'quantity' => 'quantité'
            ],
            'ar' => [
                'removed' => 'تم حذف المنتج',
                'quantity_changed' => 'تم تغيير الكمية',
                'added' => 'تم إضافة المنتج',
                'from' => 'من',
                'to' => 'إلى',
                'quantity' => 'الكمية'
            ],
            'en' => [
                'removed' => 'Item removed',
                'quantity_changed' => 'Quantity changed',
                'added' => 'Item added',
                'from' => 'from',
                'to' => 'to',
                'quantity' => 'quantity'
            ]
        ];

        $lang = $translations[$language] ?? $translations['en'];

        switch ($type) {
            case 'removed':
                return "{$lang['removed']}: $itemName";
                
            case 'quantity_changed':
                $oldQty = $modificationDetail['old_quantity'];
                $newQty = $modificationDetail['new_quantity'];
                return "{$lang['quantity_changed']}: $itemName ({$lang['from']} $oldQty {$lang['to']} $newQty)";
                
            case 'added':
                $newQty = $modificationDetail['new_quantity'];
                return "{$lang['added']}: $itemName ($newQty {$lang['quantity']})";
                
            default:
                return $itemName;
        }
    }

    // Update getOrderById method to use formatOrderItems
    public function getOrderById(Request $request, $id): JsonResponse
    {
        $user = Auth::user();
        $order = Order::query()->with(['items', 'store', 'user', 'orderItems.originalItem.images'])->find($id);

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        // Check if the user is the owner of the order or the store owner
        if ($order->user_id !== $user->id && (!$user->store || $order->store_id !== $user->store->id)) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Use OrderItem data instead of original items
        $totalOrders = $order->orderItems->sum(function ($orderItem) {
            return $orderItem->price * $orderItem->quantity;
        });

        $response = [
            'id' => $order->id,
            'store_id' => $order->store_id,
            'store_name' => $order->store->name,
            'store_image' => $order->store->images[0]->media->url ?? null,
            'status' => $order->status,
            'modified_by_store' => $order->modified_by_store,
            'modification_reason_en' => $order->modification_reason_en,
            'modification_reason_fr' => $order->modification_reason_fr,
            'modification_reason_ar' => $order->modification_reason_ar,
            'delivery_charge' => $order->delivery_charge,
            'delivery_charge_id' => $order->delivery_charge_id,
            'location_id' => $order->location_id,
            'userfirstname' => $order->user->firstname,
            'userlastname' => $order->user->lastname,
            'created_at' => $order->created_at,
            'updated_at' => $order->updated_at,
            'totalorders' => $totalOrders,
            'is_cash_on_delivery' => $order->is_cash_on_delivery,

            'items' => $this->formatOrderItems($order, $user->lang ?? 'en'),
        ];

        return response()->json($response);
    }
}
