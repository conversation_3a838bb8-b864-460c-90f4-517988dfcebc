<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Store;
use App\Models\Notification;
use App\services\FCMService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    protected FCMService $firebaseService;

    public function __construct(FCMService $firebaseService)
    {
        $this->firebaseService = $firebaseService;
    }

    /**
     * Send a push notification.
     *
     * @OA\Post(
     *     path="/api/notifications/send",
     *     tags={"Notifications"},
     *     summary="Send a push notification",
     *     description="Send a push notification to a specific device token",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"token", "title", "body"},
     *             @OA\Property(property="token", type="string", example="device_token_here"),
     *             @OA\Property(property="title", type="string", example="Notification Title"),
     *             @OA\Property(property="body", type="string", example="Notification Body")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Notification sent successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Notification sent successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Error sending notification",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="Failed to send notification")
     *         )
     *     )
     * )
     */
    public function sendPushNotification(Request $request): JsonResponse
    {
        try {
            $token = $request->input('token');
            $title = $request->input('title');
            $body = $request->input('body');

            $this->firebaseService->sendNotification($token, $title, $body);

            return response()->json(['message' => 'Notification sent successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to send push notification: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to send notification'], 500);
        }
    }

  

      /**
     * @OA\Get(
     *     path="/api/notifications",
     *     tags={"Notifications"},
     *     summary="Get user notifications in their preferred language",
     *     description="Retrieve all notifications for the authenticated user with localized content",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of notifications in user's preferred language",
     *         @OA\JsonContent(
     *             @OA\Property(property="current_page", type="integer"),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer"),
     *                     @OA\Property(property="user_id", type="integer"),
     *                     @OA\Property(property="title", type="string"),
     *                     @OA\Property(property="message", type="string"),
     *                     @OA\Property(property="type", type="string"),
     *                     @OA\Property(property="is_read", type="boolean"),
     *                     @OA\Property(property="created_at", type="string", format="date-time"),
     *                     @OA\Property(property="store_data", type="object", nullable=true)
     *                 )
     *             ),
     *             @OA\Property(property="pagination_metadata", type="object")
     *         )
     *     )
     * )
     */
    public function getUserNotifications(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $perPage = $request->input('per_page', 15);
            $userLang = $user->lang ?? 'fr'; 

            $notifications = Notification::where('user_id', $user->id)
                ->with(['user.store' => function ($query) {
                    $query->with(['images.media' => function ($query) {
                        $query->select('id', 'url');
                    }]);
                }])
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            // Transform the notifications to include localized content
            $notifications->through(function ($notification) use ($userLang , $user) {
                // Get localized title and message based on user's language
                $notification->title = $notification->{"title_" . $userLang} ?? $notification->title_fr ?? '';
                $notification->message = $notification->{"message_" . $userLang} ?? $notification->message_fr ?? '';



                // add notification image 



                 
                if ($notification->order_id) {
                    $order = Order::find($notification->order_id);
                    $store = $order ? Store::find($order->store_id) : null;
                    $storeImage = $store && $store->images ? $store->images->first() : null;
                    $notification->image = $storeImage;
                } else {
                    $notification->image = null;
                }


                // Remove language-specific fields from response
                unset($notification->title_en);
                unset($notification->title_fr);
                unset($notification->title_ar);
                unset($notification->message_en);
                unset($notification->message_fr);
                unset($notification->message_ar);

                return $notification;
            });

            return response()->json($notifications);
        } catch (\Exception $e) {
            Log::error('Error fetching user notifications: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch notifications'], 500);
        }
    }
}
