<?php

namespace App\services;

use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Illuminate\Support\Facades\Log;

class FCMService
{
    protected $messaging;

    public function __construct()
    {
        try {
            $serviceAccountPath = base_path("boutigak-19dff-firebase-adminsdk.json");

            // Log::info('Service account path: ' . json_encode($serviceAccountPath));

            if (!$serviceAccountPath || !is_readable($serviceAccountPath)) {
                throw new \Exception('Firebase credentials not set or not readable.');
            }

            // Log::info('Service account path is readable: ' . $serviceAccountPath);

            $factory = (new Factory)->withServiceAccount($serviceAccountPath);
            $this->messaging = $factory->createMessaging();

            // Log::info('Firebase Messaging service initialized successfully.');
        } catch (\Exception $e) {
            Log::error('Error initializing Firebase Messaging: ' . $e->getMessage());
        }
    }

    public function sendNotification($token, $title, $body)
    {
        try {
            // // Log the input parameters
            // Log::info('Notification token: ' . json_encode($token));
            // Log::info('Notification title: ' . $title);
            // Log::info('Notification body: ' . $body);

            // Build the message
            $message = CloudMessage::withTarget('token', $token)
                ->withNotification([
                    'title' => $title,
                    'body' => $body,
            ]);

            $message = $message->withApnsConfig([
                "payload" => [
                        "aps" => [
                            "sound" => "default",
                        ]
                    ]
            ]);

            $message = $message->withAndroidConfig([
                "notification" => [
                        "default_sound" => true,
                ]
            ]);

            // Log the constructed message
            // Log::info('Constructed CloudMessage: ' . json_encode($message));

            // Attempt to send the message
            $response = $this->messaging->send($message);

            // Log the response from Firebase
            // Log::info('Firebase response: ' . json_encode($response));
        } catch (\Kreait\Firebase\Exception\MessagingException $e) {
            Log::error('MessagingException: ' . $e->getMessage());
        } catch (\Kreait\Firebase\Exception\FirebaseException $e) {
            Log::error('FirebaseException: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('General Exception: ' . $e->getMessage());
        }
    }

    public function sendMessageNotification($token, $title, $body, $discussionId)
    {
        try {
            $message = CloudMessage::withTarget('token', $token)
                ->withNotification([
                    'title' => $title,
                    'body' => $body,
                ])
                ->withData([
                    'type' => 'message',
                    'discussion_id' => (string)$discussionId,
                ])
                ->withApnsConfig([
                    "payload" => [
                        "aps" => [
                            "sound" => "default",
                        ]
                    ]
                ])
                ->withAndroidConfig([
                    "notification" => [
                        "default_sound" => true,
                    ]
                ]);

            $response = $this->messaging->send($message);
        } catch (\Kreait\Firebase\Exception\MessagingException $e) {
            Log::error('MessagingException: ' . $e->getMessage());
        } catch (\Kreait\Firebase\Exception\FirebaseException $e) {
            Log::error('FirebaseException: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('General Exception: ' . $e->getMessage());
        }
    }
}
