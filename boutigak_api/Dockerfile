FROM php:8.1-apache

# Copy PHP configuration
RUN mv /usr/local/etc/php/php.ini-production /usr/local/etc/php/php.ini

# Configure PHP settings
RUN echo "memory_limit = -1\n" >> /usr/local/etc/php/php.ini
RUN echo "extension=pdo_pgsql\n" \
    "extension=pgsql\n" \
    >> /usr/local/etc/php/php.ini

# Enable Apache modules
RUN a2enmod rewrite headers deflate ssl

# Update package list and install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    libwebp-dev \
    libxpm-dev \
    libmcrypt-dev \
    jpegoptim \
    optipng \
    pngquant \
    gifsicle \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    sudo \
    npm \
    nodejs \
    libpq-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Configure and install GD extension with all image format support
RUN docker-php-ext-configure gd \
    --with-freetype \
    --with-jpeg \
    --with-webp \
    --with-xpm \
    && docker-php-ext-install -j$(nproc) gd

# Install other PHP extensions
RUN docker-php-ext-install \
    pdo \
    pdo_pgsql \
    pgsql \
    zip \
    exif \
    sockets \
    mbstring \
    bcmath

# Install Redis extension
RUN pecl install redis \
    && docker-php-ext-enable redis

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Set working directory
WORKDIR /var/www/html

# Copy composer files first for better caching
COPY composer.json composer.lock* ./

# Remove composer.lock to avoid version conflicts, then install dependencies
RUN rm -f composer.lock && \
    composer install --no-scripts --no-autoloader --no-dev --optimize-autoloader

# Copy application files
COPY . ./

# Generate optimized autoloader
RUN composer dump-autoload --optimize --no-scripts

# Configure Apache virtual host
RUN rm -f /etc/apache2/sites-available/000-default.conf /etc/apache2/sites-enabled/000-default.conf
ADD vhost.docker.conf /etc/apache2/sites-available/vhost.docker.conf
RUN a2ensite vhost.docker.conf

# Create necessary directories
RUN mkdir -p storage/framework/{sessions,views,cache} \
    && mkdir -p storage/logs \
    && mkdir -p storage/app/public \
    && mkdir -p bootstrap/cache

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 775 storage bootstrap/cache

# Expose ports
EXPOSE 80 443

# Switch to www-data user
USER www-data