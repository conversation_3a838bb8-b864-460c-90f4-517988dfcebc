<?php

// Simple test script to verify the Order model relationships work correctly
require_once 'boutigak_api/vendor/autoload.php';

// Load Laravel application
$app = require_once 'boutigak_api/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Order;
use App\Models\OrderItem;

try {
    echo "Testing Order model relationships...\n";
    
    // Test 1: Try to load an order with the relationships
    echo "Test 1: Loading order with relationships...\n";
    $order = Order::with(['orderItems.originalItem.images'])->first();
    
    if ($order) {
        echo "✓ Successfully loaded order with ID: " . $order->id . "\n";
        echo "✓ Order has " . $order->orderItems->count() . " order items\n";
        
        foreach ($order->orderItems as $orderItem) {
            echo "  - OrderItem ID: " . $orderItem->id . "\n";
            if ($orderItem->originalItem) {
                echo "    - Original Item: " . $orderItem->originalItem->title . "\n";
                echo "    - Images count: " . $orderItem->originalItem->images->count() . "\n";
            } else {
                echo "    - No original item found\n";
            }
        }
    } else {
        echo "No orders found in database\n";
    }
    
    echo "\nTest completed successfully! The relationship issue has been fixed.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
