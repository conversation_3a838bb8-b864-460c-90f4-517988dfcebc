#!/bin/bash

# Unified Progressive HTTPS Load Test - All Results in One File
# Gradually increases load and consolidates all metrics

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

URL="https://boutigak.com/api/items/general-recommended"
TIMEOUT=30

# Progressive test stages (concurrent users)
TEST_STAGES=(100 250 500 750 1000 1500 2000 2500 3000)
REQUESTS_PER_STAGE=5000

echo -e "${BLUE}=========================================="
echo "Unified Progressive HTTPS Load Test"
echo -e "==========================================${NC}"
echo -e "${YELLOW}URL:${NC} $URL"
echo -e "${YELLOW}Requests per stage:${NC} $REQUESTS_PER_STAGE"
echo -e "${YELLOW}Test stages:${NC} ${TEST_STAGES[*]}"
echo -e "${YELLOW}Timeout:${NC} ${TIMEOUT}s"
echo "=========================================="

# Check dependencies
if ! command -v ab &> /dev/null; then
    echo -e "${RED}Error: Apache Bench not found. Install with: brew install httpd${NC}"
    exit 1
fi

# System configuration
ulimit -n 10000
ulimit -u 4096

# Create single results file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULTS_FILE="unified_load_test_$TIMESTAMP.txt"
METRICS_CSV="load_test_metrics_$TIMESTAMP.csv"

# Initialize results file with header
cat > "$RESULTS_FILE" << EOF
================================================================================
                    UNIFIED PROGRESSIVE LOAD TEST RESULTS
================================================================================
Test Date: $(date)
URL: $URL  
Requests per Stage: $REQUESTS_PER_STAGE
Timeout: ${TIMEOUT}s
Test Stages: ${TEST_STAGES[*]}

System Configuration:
- Max Open Files: $(ulimit -n)
- Max Processes: $(ulimit -u)
- macOS Version: $(sw_vers -productVersion)
- Apache Bench Version: $(ab -V 2>&1 | head -n 1)

================================================================================
                               PRE-FLIGHT CHECK
================================================================================
EOF

echo -e "${BLUE}Creating unified results file: $RESULTS_FILE${NC}"

# Pre-flight check
echo -e "${YELLOW}Running pre-flight check...${NC}"
echo "Running pre-flight check..." >> "$RESULTS_FILE"
PREFLIGHT_OUTPUT=$(curl -v -s -o /dev/null -w "HTTP_CODE:%{http_code},TOTAL_TIME:%{time_total},CONNECT_TIME:%{time_connect},TTFB:%{time_starttransfer}" "$URL" 2>&1)
CURL_EXIT_CODE=$?

if [ $CURL_EXIT_CODE -eq 0 ]; then
    HTTP_CODE=$(echo "$PREFLIGHT_OUTPUT" | tail -1 | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
    TOTAL_TIME=$(echo "$PREFLIGHT_OUTPUT" | tail -1 | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d: -f2)
    CONNECT_TIME=$(echo "$PREFLIGHT_OUTPUT" | tail -1 | grep -o 'CONNECT_TIME:[0-9.]*' | cut -d: -f2)
    TTFB=$(echo "$PREFLIGHT_OUTPUT" | tail -1 | grep -o 'TTFB:[0-9.]*' | cut -d: -f2)
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo -e "${GREEN}✓ Pre-flight successful (HTTP $HTTP_CODE)${NC}"
        cat >> "$RESULTS_FILE" << EOF
✓ PRE-FLIGHT SUCCESSFUL
  HTTP Status: $HTTP_CODE
  Total Time: ${TOTAL_TIME}s
  Connect Time: ${CONNECT_TIME}s
  Time to First Byte: ${TTFB}s

EOF
    else
        echo -e "${RED}⚠ Pre-flight returned HTTP $HTTP_CODE${NC}"
        echo "⚠ PRE-FLIGHT WARNING: HTTP $HTTP_CODE" >> "$RESULTS_FILE"
        echo "  Total Time: ${TOTAL_TIME}s" >> "$RESULTS_FILE"
        echo "" >> "$RESULTS_FILE"
    fi
else
    echo -e "${RED}✗ Pre-flight failed${NC}"
    echo "✗ PRE-FLIGHT FAILED" >> "$RESULTS_FILE"
    echo "  Curl Exit Code: $CURL_EXIT_CODE" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
fi

# Initialize CSV with headers
cat > "$METRICS_CSV" << EOF
Stage,Concurrent_Users,Complete_Requests,Failed_Requests,Success_Rate_%,Requests_Per_Second,Mean_Response_Time_ms,Min_Response_Time_ms,Max_Response_Time_ms,P50_ms,P90_ms,P95_ms,P99_ms,Duration_seconds,Status,Error_Details
EOF

# Function to run a single stage
run_stage() {
    local concurrent_users=$1
    local stage_num=$2
    local total_stages=$3
    
    echo ""
    echo -e "${PURPLE}=========================================="
    echo -e "STAGE $stage_num/$total_stages: $concurrent_users concurrent users"
    echo -e "==========================================${NC}"
    
    # Add stage header to results file
    cat >> "$RESULTS_FILE" << EOF

================================================================================
                       STAGE $stage_num: $concurrent_users CONCURRENT USERS
================================================================================
Started: $(date)
EOF

    # Build Apache Bench command for HTTPS
    local ab_cmd="ab -n $REQUESTS_PER_STAGE -c $concurrent_users -s $TIMEOUT"
    ab_cmd="$ab_cmd -k"  # Keep-alive for SSL connection reuse
    ab_cmd="$ab_cmd -r"  # Don't exit on socket errors
    ab_cmd="$ab_cmd -H 'Accept: application/json'"
    ab_cmd="$ab_cmd -H 'User-Agent: UnifiedTest-Stage$stage_num'"
    ab_cmd="$ab_cmd -H 'Cache-Control: no-cache'"
    ab_cmd="$ab_cmd '$URL'"
    
    echo -e "${YELLOW}Running:${NC} ab -n $REQUESTS_PER_STAGE -c $concurrent_users -s $TIMEOUT -k '$URL'"
    echo "Command: $ab_cmd" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    # Run the test
    local start_time=$(date +%s)
    local temp_output=$(mktemp)
    eval "$ab_cmd" > "$temp_output" 2>&1
    local result_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Append full AB output to results file
    echo "APACHE BENCH OUTPUT:" >> "$RESULTS_FILE"
    echo "--------------------" >> "$RESULTS_FILE"
    cat "$temp_output" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    # Extract metrics for analysis
    local complete_requests=""
    local failed_requests=""
    local rps=""
    local mean_time=""
    local min_time=""
    local max_time=""
    local p50=""
    local p90=""
    local p95=""
    local p99=""
    local status=""
    local error_details=""
    
    if [ $result_code -eq 0 ]; then
        complete_requests=$(grep "Complete requests:" "$temp_output" | awk '{print $3}' | head -1)
        failed_requests=$(grep "Failed requests:" "$temp_output" | awk '{print $3}' | head -1)
        rps=$(grep "Requests per second:" "$temp_output" | awk '{print $4}' | head -1)
        mean_time=$(grep "Time per request:" "$temp_output" | grep "mean" | awk '{print $4}' | head -1)
        
        # Extract percentile data
        p50=$(grep "50%" "$temp_output" | awk '{print $2}' | head -1)
        p90=$(grep "90%" "$temp_output" | awk '{print $2}' | head -1)
        p95=$(grep "95%" "$temp_output" | awk '{print $2}' | head -1)
        p99=$(grep "99%" "$temp_output" | awk '{print $2}' | head -1)
        
        # Get min/max if available
        min_time=$(grep "min)" "$temp_output" | awk '{print $4}' | head -1)
        max_time=$(grep "max)" "$temp_output" | awk '{print $6}' | head -1)
        
        echo -e "${GREEN}✓ Stage $stage_num SUCCESS${NC}"
        status="SUCCESS"
        
        # Add analysis to results file
        cat >> "$RESULTS_FILE" << EOF
STAGE $stage_num ANALYSIS:
-------------------------
✓ Status: SUCCESS
  Complete Requests: ${complete_requests:-N/A}
  Failed Requests: ${failed_requests:-N/A}
  Requests per Second: ${rps:-N/A}
  Mean Response Time: ${mean_time:-N/A}ms
  Duration: ${duration}s
  Completed: $(date)

RESPONSE TIME PERCENTILES:
  50th percentile: ${p50:-N/A}ms
  90th percentile: ${p90:-N/A}ms  
  95th percentile: ${p95:-N/A}ms
  99th percentile: ${p99:-N/A}ms
  Min: ${min_time:-N/A}ms
  Max: ${max_time:-N/A}ms

EOF
        
        # Calculate and display success rate
        if [ -n "$complete_requests" ] && [ -n "$failed_requests" ] && [ "$complete_requests" -gt 0 ]; then
            local success_rate=$(echo "scale=2; (($complete_requests - $failed_requests) * 100) / $complete_requests" | bc -l 2>/dev/null || echo "N/A")
            echo -e "  Complete: ${complete_requests:-0} | Failed: ${failed_requests:-0} | RPS: ${rps:-0} | Success: ${success_rate}%"
            echo -e "  Mean Time: ${mean_time:-N/A}ms | Duration: ${duration}s"
            echo "Success Rate: ${success_rate}%" >> "$RESULTS_FILE"
            
            # Check if success rate is too low
            if [ "$success_rate" != "N/A" ] && (( $(echo "$success_rate < 95" | bc -l 2>/dev/null || echo 0) )); then
                echo -e "${YELLOW}⚠️  Warning: Low success rate ($success_rate%). Consider this your limit.${NC}"
                echo "⚠️  WARNING: Low success rate ($success_rate%)" >> "$RESULTS_FILE"
                rm "$temp_output"
                return 1
            fi
        fi
        
    else
        echo -e "${RED}✗ Stage $stage_num FAILED (Exit Code: $result_code)${NC}"
        status="FAILED"
        
        # Extract error information
        if grep -q "SSL handshake failed" "$temp_output"; then
            local ssl_failures=$(grep -c "SSL handshake failed" "$temp_output")
            error_details="SSL_HANDSHAKE_FAILED:$ssl_failures"
            echo -e "  ❌ SSL handshake failures: $ssl_failures"
        elif grep -q "Connection refused" "$temp_output"; then
            error_details="CONNECTION_REFUSED"
        elif grep -q "timeout" "$temp_output"; then
            error_details="TIMEOUT"
        else
            error_details="UNKNOWN_ERROR:$result_code"
        fi
        
        # Add failure analysis to results file
        cat >> "$RESULTS_FILE" << EOF
STAGE $stage_num ANALYSIS:
-------------------------
✗ Status: FAILED (Exit Code: $result_code)
  Duration: ${duration}s
  Error Details: $error_details
  Completed: $(date)

ERROR SUMMARY:
EOF
        
        # Add error details
        if [ -n "$error_details" ]; then
            echo "Primary Error: $error_details" >> "$RESULTS_FILE"
        fi
        
        echo "" >> "$RESULTS_FILE"
        echo "LAST 10 LINES OF OUTPUT:" >> "$RESULTS_FILE"
        tail -10 "$temp_output" >> "$RESULTS_FILE"
        echo "" >> "$RESULTS_FILE"
        
        rm "$temp_output"
        return 1
    fi
    
    # Calculate success rate for CSV
    local success_rate_csv="N/A"
    if [ -n "$complete_requests" ] && [ -n "$failed_requests" ] && [ "$complete_requests" -gt 0 ]; then
        success_rate_csv=$(echo "scale=2; (($complete_requests - $failed_requests) * 100) / $complete_requests" | bc -l 2>/dev/null || echo "N/A")
    fi
    
    # Add row to CSV
    echo "$stage_num,$concurrent_users,${complete_requests:-0},${failed_requests:-0},${success_rate_csv:-N/A},${rps:-N/A},${mean_time:-N/A},${min_time:-N/A},${max_time:-N/A},${p50:-N/A},${p90:-N/A},${p95:-N/A},${p99:-N/A},$duration,$status,${error_details:-}" >> "$METRICS_CSV"
    
    rm "$temp_output"
    return 0
}

# Start main test sequence
echo -e "${YELLOW}Starting unified progressive load test...${NC}"
echo "" >> "$RESULTS_FILE"
echo "PROGRESSIVE TEST STARTED: $(date)" >> "$RESULTS_FILE"
echo "Expected Duration: Approximately $((${#TEST_STAGES[@]} * 180)) seconds" >> "$RESULTS_FILE"

sleep 3

# Run progressive stages
total_stages=${#TEST_STAGES[@]}
max_successful_users=0
failed_stage=0
successful_stages=0

for i in "${!TEST_STAGES[@]}"; do
    stage_num=$((i + 1))
    concurrent_users=${TEST_STAGES[$i]}
    
    if run_stage "$concurrent_users" "$stage_num" "$total_stages"; then
        max_successful_users=$concurrent_users
        successful_stages=$((successful_stages + 1))
    else
        failed_stage=$stage_num
        echo -e "${RED}🛑 Testing stopped at stage $stage_num due to failures${NC}"
        break
    fi
    
    # Small delay between stages
    if [ $stage_num -lt $total_stages ]; then
        echo -e "${YELLOW}Waiting 10 seconds before next stage...${NC}"
        sleep 10
    fi
done

# Add final summary to results file
cat >> "$RESULTS_FILE" << EOF

================================================================================
                             FINAL TEST SUMMARY  
================================================================================
Test Completed: $(date)
Total Test Duration: $(echo "$(date +%s) - $start_time" | bc 2>/dev/null || echo "Unknown") seconds

OVERALL RESULTS:
- Successful Stages: $successful_stages/${#TEST_STAGES[@]}
- Maximum Successful Concurrent Users: $max_successful_users
- Failed Stage: ${failed_stage:-None}

STAGE SUMMARY:
EOF

# Add stage-by-stage summary
for i in "${!TEST_STAGES[@]}"; do
    stage_num=$((i + 1))
    if [ $stage_num -le $((failed_stage > 0 ? failed_stage : total_stages)) ]; then
        concurrent_users=${TEST_STAGES[$i]}
        if [ $stage_num -lt ${failed_stage:-999} ]; then
            echo "  Stage $stage_num (${concurrent_users} users): ✓ SUCCESS" >> "$RESULTS_FILE"
        elif [ $stage_num -eq $failed_stage ]; then
            echo "  Stage $stage_num (${concurrent_users} users): ✗ FAILED" >> "$RESULTS_FILE"
        fi
    else
        echo "  Stage $stage_num (${TEST_STAGES[$i]} users): - SKIPPED" >> "$RESULTS_FILE"
    fi
done

cat >> "$RESULTS_FILE" << EOF

RECOMMENDATIONS:
EOF

if [ $max_successful_users -gt 0 ]; then
    cat >> "$RESULTS_FILE" << EOF
✓ Recommended maximum concurrent users: $max_successful_users
✓ Use this for production load testing: ab -n 50000 -c $max_successful_users -k '$URL'
✓ Your server can reliably handle $max_successful_users concurrent HTTPS connections

EOF
    if [ $failed_stage -gt 0 ]; then
        cat >> "$RESULTS_FILE" << EOF
⚠ Failed at ${TEST_STAGES[$((failed_stage-1))]} concurrent users
⚠ SSL connection limits reached at this level
⚠ Consider multiple distributed tests instead of higher concurrency

EOF
    fi
else
    cat >> "$RESULTS_FILE" << EOF
❌ All stages failed - server configuration issues detected
❌ Check SSL certificate, server capacity, and network connectivity
❌ Start with very low concurrency (10-50 users) for troubleshooting

EOF
fi

cat >> "$RESULTS_FILE" << EOF
NEXT STEPS:
1. Review detailed results above for each stage
2. Monitor server logs during peak load (${max_successful_users:-"N/A"} users)
3. Check SSL/TLS configuration if failures occurred
4. Use distributed testing for loads higher than $max_successful_users users
5. Consider HTTP testing to isolate SSL vs application performance

FILES GENERATED:
- $RESULTS_FILE (This complete report)
- $METRICS_CSV (CSV format for analysis/graphing)

================================================================================
                               END OF REPORT
================================================================================
EOF

# Console summary
echo ""
echo -e "${CYAN}=========================================="
echo -e "UNIFIED PROGRESSIVE TEST COMPLETED"
echo -e "==========================================${NC}"

if [ $max_successful_users -gt 0 ]; then
    echo -e "${GREEN}✓ Maximum successful concurrent users: $max_successful_users${NC}"
    
    if [ $failed_stage -gt 0 ]; then
        echo -e "${RED}✗ Failed at: ${TEST_STAGES[$((failed_stage-1))]} concurrent users${NC}"
        echo -e "${YELLOW}💡 Recommended maximum: $max_successful_users concurrent users${NC}"
    else
        echo -e "${GREEN}🎉 All stages completed successfully!${NC}"
        echo -e "${BLUE}💪 Your server can handle ${TEST_STAGES[-1]}+ concurrent users${NC}"
    fi
else
    echo -e "${RED}❌ All stages failed - check server configuration${NC}"
fi

echo ""
echo -e "${BLUE}📊 Complete results saved to:${NC}"
echo -e "${YELLOW}📄 Detailed Report: $RESULTS_FILE${NC}"
echo -e "${YELLOW}📈 CSV Metrics: $METRICS_CSV${NC}"
echo ""
echo -e "${PURPLE}🎯 Quick Command for Your Max Load:${NC}"
if [ $max_successful_users -gt 0 ]; then
    echo -e "${GREEN}ab -n 50000 -c $max_successful_users -k '$URL'${NC}"
else
    echo -e "${RED}Fix server issues first, then retry with low concurrency${NC}"
fi

echo ""
echo -e "${CYAN}✅ Unified progressive load test completed at: $(date)${NC}"